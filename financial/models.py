from django.db import models

from financial.enums import CompanyDocumentType, CompanyStatus, CompanyType


class Company(models.Model):
    user = models.OneToOneField('user.User', on_delete=models.CASCADE, related_name='_company')
    status = models.CharField(
        max_length=255,
        choices=CompanyStatus.choices(),
        default=CompanyStatus.PENDING.id
    )
    externalId = models.IntegerField(null=True, blank=True, verbose_name='Id externo')
    onboard = models.BooleanField(default=False, verbose_name='Cadastro Completo na Split')
    type = models.CharField(
        max_length=255,
        choices=CompanyType.choices(),
        default=CompanyType.INDIVIDUAL.id
    )
    companyName = models.CharField(max_length=255, null=True, blank=True)
    companyCnpj = models.CharField(max_length=255, null=True, blank=True)
    companyType = models.CharField(max_length=255, null=True, blank=True)
    averageRevenue = models.DecimalField(max_digits=11, decimal_places=2, null=True, blank=True)

    # Administrator data
    completeName = models.CharField(max_length=255, null=True, blank=True)
    cpf = models.CharField(max_length=11, null=True, blank=True)
    verificationDocument = models.CharField(max_length=255, null=True, blank=True)
    verificationDocumentType = models.CharField(
        max_length=255,
        choices=CompanyDocumentType.choices(),
        null=True,
        blank=True
    )
    birthDate = models.DateField(null=True, blank=True)
    phone = models.CharField(max_length=11, null=True, blank=True)
    motherName = models.CharField(max_length=255, null=True, blank=True)

    # Address data
    cep = models.CharField(max_length=8, null=True, blank=True)
    street = models.CharField(max_length=255, null=True, blank=True)
    number = models.CharField(max_length=255, null=True, blank=True)
    complement = models.CharField(max_length=255, null=True, blank=True)
    neighborhood = models.CharField(max_length=255, null=True, blank=True)
    city = models.CharField(max_length=255, null=True, blank=True)
    state = models.CharField(max_length=2, null=True, blank=True)

    acceptedTerms = models.BooleanField(default=False)
    acceptedTermsDate = models.DateTimeField(null=True, blank=True)

    files = models.ManyToManyField('financial.CompanyImage', blank=True, related_name='company_files')

    # Veriff
    sessionId = models.CharField(max_length=255, null=True, blank=True)
    sessionUrl = models.URLField(max_length=2048, null=True, blank=True)
    sessionToken = models.CharField(max_length=255, null=True, blank=True)
    sessionTokenExpires = models.DateTimeField(null=True, blank=True)
    rejectedReasons = models.JSONField(null=True, blank=True, default=list, verbose_name='Motivos da rejeição')

    # Onboard Split
    onboardUrl = models.URLField(max_length=2048, null=True, blank=True)

    # Administration
    threeDsEnabled = models.BooleanField(
        default=False,
        verbose_name='3DS habilitado',
        help_text='Habilita validação bancária em vendas com cartão.'
    )

    createdAt = models.DateTimeField(auto_now_add=True, verbose_name='Criado em')
    updatedAt = models.DateTimeField(auto_now=True, verbose_name='Atualizado em')

    class Meta:
        ordering = ['-pk']

    def __str__(self):
        return self.companyName or self.user.email

    def save(self, *args, **kwargs):
        from product.models import Product

        save_response = super().save(*args, **kwargs)

        Product.bulk_invalidate_cache(self.user.product_set.all())

        return save_response

    @staticmethod
    def has_changed_to_approved(current_status: str, new_status: str) -> bool:
        valid_statuses_before_approval = [
            CompanyStatus.PRE_APPROVED.id,
            CompanyStatus.RESUBMISSION_REQUESTED.id,
            CompanyStatus.GATEWAY_PENDING.id,
            CompanyStatus.WAITING_DOCUMENTS.id,
        ]

        is_approved = (new_status == CompanyStatus.APPROVED.id)
        is_valid_for_approval = (current_status in valid_statuses_before_approval)

        return is_valid_for_approval and is_approved

class CompanyImage(models.Model):
    company = models.ForeignKey('financial.Company', on_delete=models.CASCADE)
    file = models.FileField(upload_to='documents/')

    def __str__(self) -> str:
        return f'{self.company} - {self.file}'
