from cakto.enums import LabeledEnum


class CompanyStatus(LabeledEnum):
    # name = (id, description)
    PENDING = ('pending', 'Pendente')
    PRE_APPROVED = ('pre_approved', 'Pré-aprovado')
    APPROVED = ('approved', 'Aprovado')
    REJECTED = ('rejected', 'Rejeitado')
    BLOCKED = ('blocked', 'Bloqueado')
    WAITING_DOCUMENTS = ('waiting_documents', 'Aguardando envio dos documentos')
    GATEWAY_PENDING = ('gateway_pending', 'Aguardando aprovação')
    WAITING_URL = ('waiting_url', 'Aguardando Url')
    RESUBMISSION_REQUESTED = ('resubmission_requested', 'Reenvio solicitado')

class CompanyType(LabeledEnum):
    COMPANY = ('company', 'Empresa')
    INDIVIDUAL = ('individual', 'Individual')

class CompanyDocumentType(LabeledEnum):
    RG = ('rg', 'RG')
    CNH = ('cnh', 'CNH')
    PASSPORT = ('passport', 'Passaporte')
