from unittest import mock

import pytest
from django.urls import reverse
from django.utils import timezone
from django.core.cache import cache
from django_otp.plugins.otp_email.models import EmailDevice

from cakto.tests.base import BaseTestCase
from financial.enums import CompanyStatus
from user.models import MFAMethod


class FinancialTestCase(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.headers = cls.build_user_auth_headers(cls.user)

        cls.mfa_method = MFAMethod.objects.create(user=cls.user, type='email', confirmed=True, primary=True)  # type:ignore
        cls.mfa_method.email_device = EmailDevice.objects.create(user=cls.user, name=cls.user.email)  # type:ignore
        cls.mfa_method.email_device.generate_token()  # type:ignore
        cls.mfa_method.save(update_fields=['email_device'])  # type:ignore

        cls.company = cls.user.company
        cls.company.status = CompanyStatus.APPROVED.id
        cls.company.save(update_fields=['status'])

        cls.user.pixKey = '12345678901'
        cls.user.save(update_fields=['pixKey'])

    @pytest.mark.skip
    def test_user_cant_withdraw_without_mfa_code_when_mfa_is_active(self):
        url = reverse('financial:withdraw')

        payload = {'amount': 100, }

        response = self.client.post(url, payload, headers=self.build_user_auth_headers())

        self.assertEqual(response.status_code, 400, response.content.decode())
        self.assertEqual(response.json()['detail'], 'Informe o código de autenticação. "mfa_Code" é obrigatório.')

    def test_user_cant_withdraw_with_wrong_code(self):
        url = reverse('financial:withdraw')

        self.mfa_method.email_device.generate_token()  # type:ignore

        payload = {'amount': 100, 'mfa_code': '123456'}

        response = self.client.post(url, payload, headers=self.build_user_auth_headers())

        self.assertEqual(response.status_code, 400, response.content.decode())
        self.assertEqual(response.json()['detail'], 'Código de autenticação inválido.')

    def test_user_withdraw_pass_with_correct_mfa_code(self):
        url = reverse('financial:withdraw')

        self.mfa_method.email_device.generate_token()  # type:ignore

        payload = {'amount': 100, 'mfa_code': self.mfa_method.email_device.token}  # type:ignore

        with mock.patch('financial.views.SplitPay.createWithdraw') as create_withdraw_mock:
            response_mock = mock.Mock(status_code=200)
            create_withdraw_mock.return_value = response_mock

            response = self.client.post(url, payload, headers=self.build_user_auth_headers())

        create_withdraw_mock.assert_called_once()
        self.assertEqual(response.status_code, 200, response.content.decode())

    def test_user_with_blocked_company_cant_update(self):
        self.company.status = CompanyStatus.BLOCKED.id
        self.company.save(update_fields=['status'])

        url = reverse('financial:company')

        response = self.client.put(url, data={}, headers=self.headers)

        self.assertEqual(response.status_code, 400, response.content.decode())
        self.assertEqual(response.json(), {'detail': 'Conta bloqueada. Não é possível editar dados.'})

    def test_update_company(self):
        url = reverse('financial:company')

        payload = {
            'completeName': 'Testing Name',
            'cpf': '***********',
            'birthDate': '1990-01-01',
            'phone': '***********',
            'motherName': 'Testing Mother',
            'cep': '********',
            'street': 'Rua A',
            'number': '10',
            'neighborhood': 'Centro',
            'city': 'São Paulo',
            'state': 'SP',
            'acceptedTerms': True,
        }

        onboard_mock = mock.patch('financial.views.onboardAccount').start()

        response = self.client.put(url, data=payload, headers=self.headers, format='json')

        self.assertEqual(response.status_code, 200, response.content.decode())

        onboard_mock.assert_called_once_with(self.company, idwallSdkToken=None)

        self.company.refresh_from_db()

        self.assertEqual(self.company.completeName, payload['completeName'])
        self.assertEqual(self.company.cpf, payload['cpf'])
        self.assertEqual(self.company.phone, payload['phone'])
        self.assertEqual(self.company.motherName, payload['motherName'])
        self.assertEqual(self.company.cep, payload['cep'])
        self.assertEqual(self.company.street, payload['street'])
        self.assertEqual(self.company.number, payload['number'])
        self.assertEqual(self.company.neighborhood, payload['neighborhood'])
        self.assertEqual(self.company.city, payload['city'])
        self.assertEqual(self.company.state, payload['state'])
        self.assertEqual(
            self.company.birthDate,
            timezone.datetime.strptime(payload['birthDate'], '%Y-%m-%d').date()
        )
        self.assertEqual(self.company.acceptedTerms, payload['acceptedTerms'])
        self.assertAlmostEqual(
            self.company.acceptedTermsDate.timestamp(),  # type:ignore
            timezone.now().timestamp(),
            delta=3
        )

    def test_update_company_calls_onboard_when_idwallSdkToken(self):
        url = reverse('financial:company')

        payload = {
            'completeName': 'Testing Name',
            'cpf': '***********',
            'birthDate': '1990-01-01',
            'phone': '***********',
            'motherName': 'Testing Mother',
            'cep': '********',
            'street': 'Rua A',
            'number': '10',
            'neighborhood': 'Centro',
            'city': 'São Paulo',
            'state': 'SP',
            'acceptedTerms': True,
            'idwallSdkToken': 'TestToken',
        }

        onboard_mock = mock.patch('financial.views.onboardAccount').start()

        response = self.client.put(url, data=payload, headers=self.headers, format='json')

        self.assertEqual(response.status_code, 200, response.content.decode())

        onboard_mock.assert_called_once_with(self.company, idwallSdkToken=payload['idwallSdkToken'])

    def test_update_company_does_not_change_acceptedTermsDate_when_it_already_exists(self):
        url = reverse('financial:company')

        self.company.acceptedTerms = True
        self.company.acceptedTermsDate = timezone.now() - timezone.timedelta(days=1)
        self.company.save(update_fields=['acceptedTerms', 'acceptedTermsDate'])

        payload = {
            'completeName': 'Testing Name',
            'cpf': '***********',
            'birthDate': '1990-01-01',
            'phone': '***********',
            'motherName': 'Testing Mother',
            'cep': '********',
            'street': 'Rua A',
            'number': '10',
            'neighborhood': 'Centro',
            'city': 'São Paulo',
            'state': 'SP',
            'acceptedTerms': True,
        }

        mock.patch('financial.views.onboardAccount').start()

        response = self.client.put(url, data=payload, headers=self.headers, format='json')

        self.assertEqual(response.status_code, 200, response.content.decode())

        self.company.refresh_from_db()

        self.assertEqual(self.company.acceptedTerms, payload['acceptedTerms'])
        self.assertNotAlmostEqual(
            self.company.acceptedTermsDate.timestamp(),  # type:ignore
            timezone.now().timestamp(),
            delta=10,
            msg='acceptedTermsDate should not have been updated'
        )

    def test_user_can_update_company_with_any_status_but_blocked(self):
        url = reverse('financial:company')

        payload = {
            'completeName': 'Testing Name',
            'cpf': '***********',
            'birthDate': '1990-01-01',
            'phone': '***********',
            'motherName': 'Testing Mother',
            'cep': '********',
            'street': 'Rua A',
            'number': '10',
            'neighborhood': 'Centro',
            'city': 'São Paulo',
            'state': 'SP',
        }

        statuses = CompanyStatus.as_id_list()
        statuses.remove(CompanyStatus.BLOCKED.id)

        mock.patch('financial.views.onboardAccount').start()

        for _status in statuses:
            with self.subTest(status=_status):
                self.company.status = _status
                self.company.completeName = 'Old Name'
                self.company.save(update_fields=['status', 'completeName'])

                response = self.client.put(url, data=payload, headers=self.headers, format='json')

                self.assertEqual(response.status_code, 200, response.content.decode())

                self.company.refresh_from_db()

                self.assertEqual(self.company.completeName, payload['completeName'])

    @mock.patch('financial.views.SplitPay.get_cielo_three_ds_token')
    def test_cielo_three_ds_token_success(self, mock_get_token):
        mock_response = mock.Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {'access_token': 'abc123'}
        mock_get_token.return_value = mock_response

        url = reverse('financial:cielo_3ds_token')
        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json(), {'access_token': 'abc123'})
        mock_get_token.assert_called_once()

    @mock.patch('financial.views.SplitPay.get_cielo_three_ds_token')
    def test_cielo_three_ds_token_connection_error(self, mock_get_token):
        mock_get_token.side_effect = Exception("Erro ao conectar com Split")

        url = reverse('financial:cielo_3ds_token')
        response = self.client.get(url)

        self.assertEqual(response.status_code, 502)
        self.assertEqual(response.json(), {"error": "Falha ao conectar com o servidor da Split"})
        mock_get_token.assert_called_once()


class PlanFeeTestCase(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.user.emailValidated = True
        cls.user.whatsappValidated = True
        cls.user.is_staff = True
        cls.user.company.externalId = 12345
        cls.user.company.save()
        cls.user.save()

        cls.user_headers = cls.build_user_auth_headers(cls.user)

        cls.url_list_plans = reverse('financial:list_plan_fees')
        cls.url_choose_plan = reverse('financial:choose_plan')
        cls.url_current_plan = reverse('financial:current_plan')

    def test_list_plans_success_and_cache(self):
        with mock.patch('financial.views.SplitPay.list_plan_fees') as mock_list:
            mock_response = mock.Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = [{'id': 1, 'name': 'D10'}]
            mock_list.return_value = mock_response

            response = self.client.get(self.url_list_plans, headers=self.user_headers)
            self.assertEqual(response.status_code, 200)
            self.assertEqual(response.json(), [{'id': 1, 'name': 'D10'}])
            self.assertEqual(mock_list.call_count, 1)

            response = self.client.get(self.url_list_plans, headers=self.user_headers)
            self.assertEqual(response.status_code, 200)
            self.assertEqual(response.json(), [{'id': 1, 'name': 'D10'}])
            self.assertEqual(mock_list.call_count, 1)

    def test_list_plans_error(self):
        with mock.patch('financial.views.SplitPay.list_plan_fees') as mock_list:
            mock_response = mock.Mock()
            mock_response.status_code = 500
            mock_response.json.return_value = {'detail': 'Erro interno no split'}
            mock_list.return_value = mock_response

            response = self.client.get(self.url_list_plans, headers=self.user_headers)
            self.assertEqual(response.status_code, 500)
            self.assertIn('Erro interno no split', response.content.decode())

    @mock.patch('financial.views.SplitPay.list_plan_fees')
    def test_choose_plan_plan_type_not_found_on_split(self, mock_list):
        mock_list.return_value = mock.Mock(
            status_code=200,
            json=lambda: []
        )

        payload = {'plan_type': 'XYZ'}
        response = self.client.post(
            self.url_choose_plan,
            data=payload,
            headers=self.user_headers,
            format='json'
        )

        self.assertEqual(response.status_code, 404)
        self.assertIn('Plano XYZ não encontrado', response.content.decode())

    @mock.patch('financial.views.SplitPay.list_plan_fees')
    def test_choose_plan_split_error(self, mock_list):
        mock_response = mock.Mock()
        mock_response.status_code = 500
        mock_response.json.return_value = {'detail': 'Erro no split list'}
        mock_list.return_value = mock_response

        payload = {'plan_type': 'D10'}
        response = self.client.post(
            self.url_choose_plan,
            data=payload,
            headers=self.user_headers,
            format='json'
        )

        self.assertEqual(response.status_code, 500)
        self.assertIn('Erro no split list', response.content.decode())

    @mock.patch('financial.views.SplitPay.list_plan_fees')
    def test_choose_plan_not_found(self, mock_list):
        mock_list.return_value = mock.Mock(
            status_code=200,
            json=lambda: []
        )
        payload = {'plan_type': 'D10'}

        response = self.client.post(
            self.url_choose_plan,
            data=payload,
            headers=self.user_headers,
            format='json'
        )

        self.assertEqual(response.status_code, 404)
        self.assertIn('não encontrado', response.content.decode().lower())

    @mock.patch('financial.views.SplitPay.getAccount')
    @mock.patch('financial.views.SplitPay.update_user_plan_fee')
    @mock.patch('financial.views.SplitPay.list_plan_fees')
    def test_choose_plan_success(self, mock_list_plan_fees, mock_update_user_plan_fee, mock_getAccount):
        mock_plan_list_response = mock.Mock()
        mock_plan_list_response.status_code = 200
        mock_plan_list_response.json.return_value = [{'id': 101, 'name': 'D10'}]
        mock_list_plan_fees.return_value = mock_plan_list_response

        mock_getAccount_response = mock.Mock()
        mock_getAccount_response.status_code = 200
        mock_getAccount_response.json.return_value = {'plan_fee': None}
        mock_getAccount.return_value = mock_getAccount_response

        mock_update_user_plan_fee.return_value = mock.Mock(
            status_code=200,
            text='OK'
        )

        payload = {'plan_type': 'D10'}
        response = self.client.post(
            self.url_choose_plan,
            data=payload,
            headers=self.user_headers,
            format='json'
        )

        self.assertEqual(response.status_code, 200)
        self.assertIn('Plano D10 selecionado com sucesso', response.content.decode())

        expected_external_id = self.user.company.externalId
        mock_getAccount.assert_called_once_with(expected_external_id)
        mock_update_user_plan_fee.assert_called_once_with(expected_external_id, 101)

    @mock.patch('financial.views.SplitPay.getAccount')
    @mock.patch('financial.views.SplitPay.update_user_plan_fee')
    @mock.patch('financial.views.SplitPay.list_plan_fees')
    def test_choose_plan_user_not_found(self, mock_list_plan_fees, mock_update_user_plan_fee, mock_getAccount):
        mock_list_plan_fees.return_value = mock.Mock(
            status_code=200,
            json=lambda: [{'id': 999, 'name': 'D10'}]
        )

        mock_getAccount_response = mock.Mock()
        mock_getAccount_response.status_code = 200
        mock_getAccount_response.json.return_value = {'plan_fee': None}
        mock_getAccount.return_value = mock_getAccount_response

        mock_update_user_plan_fee.return_value = mock.Mock(
            status_code=404,
            text='Conta não encontrada na Split'
        )

        payload = {'plan_type': 'D10'}
        response = self.client.post(
            self.url_choose_plan,
            data=payload,
            headers=self.user_headers,
            format='json'
        )

        self.assertEqual(response.status_code, 404)
        self.assertIn('Conta não encontrada na Split', response.content.decode())

        expected_external_id = self.user.company.externalId
        mock_getAccount.assert_called_once_with(expected_external_id)
        mock_update_user_plan_fee.assert_called_once_with(expected_external_id, 999)

    @mock.patch('financial.views.SplitPay.getAccount')
    @mock.patch('financial.views.SplitPay.update_user_plan_fee')
    @mock.patch('financial.views.SplitPay.list_plan_fees')
    def test_choose_plan_update_error(self, mock_list_plan_fees, mock_update_user_plan_fee, mock_getAccount):
        resp_list = mock.Mock()
        resp_list.status_code = 200
        resp_list.json.return_value = [{'id': 888, 'name': 'D10'}]
        mock_list_plan_fees.return_value = resp_list

        mock_getAccount_response = mock.Mock()
        mock_getAccount_response.status_code = 200
        mock_getAccount_response.json.return_value = {'plan_fee': None}
        mock_getAccount.return_value = mock_getAccount_response

        error_resp = mock.Mock()
        error_resp.status_code = 500
        error_resp.json.return_value = {'detail': 'Erro inesperado ao atualizar'}
        error_resp.text = 'Erro inesperado ao atualizar'
        mock_update_user_plan_fee.return_value = error_resp

        payload = {'plan_type': 'D10'}
        response = self.client.post(
            self.url_choose_plan,
            data=payload,
            headers=self.user_headers,
            format='json'
        )

        self.assertEqual(response.status_code, 500)
        self.assertIn('Erro inesperado ao atualizar', response.content.decode())

        expected_external_id = self.user.company.externalId
        mock_getAccount.assert_called_once_with(expected_external_id)
        mock_update_user_plan_fee.assert_called_once_with(expected_external_id, 888)

    @mock.patch('financial.views.SplitPay.list_plan_fees')
    def test_choose_plan_d2_requires_approval(self, mock_list):
        mock_list.return_value = mock.Mock(
            status_code=200,
            json=lambda: [{'id': 777, 'name': 'D2'}]
        )

        payload = {'plan_type': 'D2'}
        response = self.client.post(
            self.url_choose_plan,
            data=payload,
            headers=self.user_headers,
            format='json'
        )

        self.assertEqual(response.status_code, 200)
        self.assertIn('requer aprovação', response.content.decode().lower())

    @mock.patch('financial.views.SplitPay.get_plan_fee_by_id')
    @mock.patch('financial.views.SplitPay.getAccount')
    def test_current_plan_success(self, mock_getAccount, mock_get_plan_fee_by_id):
        mock_getAccount.return_value = mock.Mock(
            status_code=200,
            json=lambda: {'plan_fee': 7}
        )

        mock_get_plan_fee_by_id.return_value = mock.Mock(
            status_code=200,
            json=lambda: {'id': 7, 'creditCardPercentage': 6.99}
        )

        response = self.client.get(self.url_current_plan, headers=self.user_headers)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json().get("id"), 7)
        self.assertEqual(response.json().get("creditCardPercentage"), 6.99)

        expected_external_id = self.user.company.externalId
        mock_getAccount.assert_called_once_with(expected_external_id)

    @mock.patch('financial.views.SplitPay.getAccount')
    def test_current_plan_user_without_plan(self, mock_getAccount):
        mock_getAccount.return_value = mock.Mock(
            status_code=200,
            json=lambda: {'plan_fee': None}
        )

        response = self.client.get(self.url_current_plan, headers=self.user_headers)
        self.assertEqual(response.status_code, 200)
        self.assertIn("não possui plano", response.content.decode().lower())

        expected_external_id = self.user.company.externalId
        mock_getAccount.assert_called_once_with(expected_external_id)

    @mock.patch('financial.views.SplitPay.getAccount')
    def test_current_plan_user_split_error(self, mock_getAccount):
        mock_getAccount.return_value = mock.Mock(
            status_code=500,
            json=mock.Mock(return_value={'detail': 'Erro ao buscar usuário'})
        )

        response = self.client.get(self.url_current_plan, headers=self.user_headers)
        self.assertEqual(response.status_code, 500)
        self.assertIn('Erro ao buscar usuário', response.content.decode())

        expected_external_id = self.user.company.externalId
        mock_getAccount.assert_called_once_with(expected_external_id)
