from datetime import datetime, timezone
from unittest import mock

from django.urls import reverse

from cakto.tests.base import BaseTestCase
from financial.enums import CompanyStatus


class ResetVerificationTestCase(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.create_paymentMethods()

        cls.admin_user = cls.create_user(is_superuser=True, is_staff=True)

    def test_should_reset_account_if_status_not_blocked(self):
        url = reverse('financial:reset-verification', kwargs={'user_id': self.user.id})

        statuses = CompanyStatus.as_id_list()
        statuses.remove(CompanyStatus.BLOCKED.id)

        for status in statuses:
            with self.subTest(status=status):
                self.user._company.status = status
                self.user._company.save()

                with mock.patch('financial.views.SplitPay.reset_verification') as reset_verification_split:  # noqa: E501
                    response_mock = mock.Mock(status_code=200)
                    reset_verification_split.return_value = response_mock
                    headers = self.build_user_auth_headers(user=self.admin_user)

                    response = self.client.post(url, headers=headers)
                    response_json = response.json()
                    reset_verification_split.assert_called_once()
                    self.assertEqual(response.status_code, 200, response.content.decode())
                    self.assertEqual(response_json['detail'], 'Verificação resetada com sucesso!')

                    self.user._company.refresh_from_db()
                    self.assertEqual(self.user._company.status, CompanyStatus.RESUBMISSION_REQUESTED.id)
                    self.assertEqual(self.user._company.onboardUrl, None)
                    self.assertEqual(self.user._company.sessionId, None)
                    self.assertEqual(self.user._company.sessionUrl, None)
                    self.assertEqual(self.user._company.sessionToken, None)
                    self.assertEqual(self.user._company.sessionTokenExpires, None)

    def test_should_not_reset_account_if_status_is_blocked(self):
        url = reverse('financial:reset-verification', kwargs={'user_id': self.user.id})

        actual_date = datetime.now(tz=timezone.utc)
        self.user._company.status = 'blocked'
        self.user._company.onboardUrl = 'onboardUrl'
        self.user._company.sessionId = 'sessionId'
        self.user._company.sessionUrl = 'sessionUrl'
        self.user._company.sessionToken = 'sessionToken'
        self.user._company.sessionTokenExpires = actual_date
        self.user._company.save()

        with mock.patch('financial.views.SplitPay.reset_verification') as reset_verification_split:  # noqa: E501
            response_mock = mock.Mock(status_code=200)
            reset_verification_split.return_value = response_mock
            headers = self.build_user_auth_headers(user=self.admin_user)

            response = self.client.post(url, headers=headers)
            response_json = response.json()
            reset_verification_split.assert_not_called()
            self.assertEqual(response.status_code, 400, response.content.decode())
            self.assertEqual(response_json['detail'], 'Conta bloqueada. Não é possível resetar.')

            self.user._company.refresh_from_db()
            self.assertEqual(self.user._company.status, 'blocked')
            self.assertEqual(self.user._company.onboardUrl, 'onboardUrl')
            self.assertEqual(self.user._company.sessionId, 'sessionId')
            self.assertEqual(self.user._company.sessionUrl, 'sessionUrl')
            self.assertEqual(self.user._company.sessionToken, 'sessionToken')
            self.assertEqual(self.user._company.sessionTokenExpires, actual_date)
