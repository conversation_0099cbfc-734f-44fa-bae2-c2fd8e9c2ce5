import os

import json
import requests
from hashlib import md5

from django.core.cache import cache
from django.shortcuts import get_object_or_404
from django.utils import timezone
from rest_framework import status, views, viewsets
from rest_framework.decorators import action
from rest_framework.parsers import MultiPartParser
from rest_framework.permissions import IsAdminUser, AllowAny
from rest_framework.response import Response

from financial.enums import CompanyStatus
from financial.utils import onboardAccount, sync_company_status_with_split
from gateway.sdk import SplitPay
from gateway.utils import get_client_ip, handle_split_response
from user.models import MFAMethod, User

from .models import Company
from .serializers import CompanyPublicSerializer, CompanyUpdateSerializer
from system_log.utils import log_action
from cakto.throttling import CaktoScopedRateThrottle


class BalanceViewSet(viewsets.ViewSet):
    scope = 'financial'

    def list(self, request):
        company = request.user.company
        split = SplitPay()
        res = split.getBalance(company.externalId)
        if not status.is_success(res.status_code):
            raise Exception(f'externalId: {company.externalId}', res.text)

        data = res.json()
        return Response({
            'balance': data.get('balance', 0),
            'pending': data.get('pendingBalance', 0) + data.get('reservedBalance', 0),
            'antecipateAmount': data.get('anticipationBalance', 0),
        })

    @action(detail=False, methods=['post'])
    def withdraw(self, request):
        split = SplitPay()
        user: User = request.user
        amount = float(request.data.get('amount'))
        mfa_code = request.data.get('mfa_code', '')
        companyIsApproved = user.company.status == CompanyStatus.APPROVED.id

        # if not mfa_code:
        #     return Response({'detail': 'Informe o código de autenticação. "mfa_Code" é obrigatório.'}, status=status.HTTP_400_BAD_REQUEST)
        if not companyIsApproved:
            return Response({'detail': 'É necessário verificar sua identidade antes de sacar.'}, status=status.HTTP_400_BAD_REQUEST)
        if not user.pixKey:
            return Response({'detail': 'Cadastre uma chave pix.'}, status=status.HTTP_400_BAD_REQUEST)
        if not amount:
            return Response({'detail': 'Informe o valor.'}, status=status.HTTP_400_BAD_REQUEST)
        if amount < 10:
            return Response({'detail': 'O valor mínimo para saque é de R$ 10,00.'}, status=status.HTTP_400_BAD_REQUEST)

        if mfa_code and not MFAMethod.verify_user_token(user, mfa_code):
            return Response({'detail': 'Código de autenticação inválido.'}, status=status.HTTP_400_BAD_REQUEST)

        response = split.createWithdraw(
            user.company.externalId,
            amount=amount,
            ip=get_client_ip(request)
        )

        if status.is_client_error(response.status_code):
            detail = response.json().get('detail')
            messages = ['Esta conta está bloqueada']
            if detail in messages:
                sync_company_status_with_split(user.company)
            return Response(response.json(), status=response.status_code)

        if not status.is_success(response.status_code):
            raise Exception(response.text)

        return Response({'detail': 'Saque solicitado com sucesso!'})

    @action(detail=False, methods=['post'])
    def anticipate_balance(self, request):
        split = SplitPay()
        user: User = request.user
        amount = float(request.data.get('amount', 0) or 0)
        mfa_code = request.data.get('mfa_code', '')

        if not mfa_code:
            return Response({'detail': 'Informe o código de autenticação. "mfa_Code" é obrigatório.'}, status=status.HTTP_400_BAD_REQUEST)

        if user.company.status != CompanyStatus.APPROVED.id:
            return Response({'detail': 'É necessário verificar sua identidade antes de solicitar antecipação.'}, status=status.HTTP_400_BAD_REQUEST)

        if not amount or amount <= 0:
            return Response({'detail': 'Informe o valor da antecipação.'}, status=status.HTTP_400_BAD_REQUEST)

        if not MFAMethod.verify_user_token(user, mfa_code):
            return Response({'detail': 'Código de autenticação inválido.'}, status=status.HTTP_400_BAD_REQUEST)

        response = split.createWithdraw(
            user.company.externalId,
            amount=amount,
            type='antecipation',
            ip=get_client_ip(request)
        )

        if not status.is_success(response.status_code):
            if status.is_client_error(response.status_code):
                detail = response.json().get('detail')
                messages = ['Esta conta está bloqueada']
                if detail in messages:
                    sync_company_status_with_split(user.company)
            raise Exception(response.text)

        return Response({'detail': 'Antecipação solicitada com sucesso!'})

class PixAPI(viewsets.ViewSet):
    scope = 'financial'

    def list(self, request):
        return Response({
            'pix': request.user.pixKey
        })

    @action(detail=False, methods=['post'])
    def create(self, request):
        pix = request.data.get('pix')
        if not pix:
            return Response({'detail': 'Informe o pix'}, status=status.HTTP_400_BAD_REQUEST)

        split = SplitPay()
        response = split.onboardAccount(request.user.company.externalId, pixKey=pix)
        if not status.is_success(response.status_code):
            raise Exception(response.text)

        request.user.pixKey = pix
        request.user.save()
        return Response({'message': 'Pix cadastrado com sucesso!'})

class FeeAPI(viewsets.ViewSet):
    scope = 'financial'

    def get_user_fees(self, request):
        split = SplitPay()
        response = split.getUserFees(request.user.company.externalId)
        if not status.is_success(response.status_code):
            raise Exception(response.text)

        return Response(response.json())

    def get_withdraw_fee(self, request):
        split = SplitPay()
        response = split.getWithdrawalFee(request.user.company.externalId)
        if not status.is_success(response.status_code):
            raise Exception(response.text)
        return Response(response.json())

class CompanyViewSet(viewsets.ModelViewSet):
    scope = 'financial'
    serializer_class = CompanyPublicSerializer
    model = Company
    ordering = '-pk'

    def get_object(self):
        return self.request.user.company  # type:ignore

    def update(self, request, *args, **kwargs):
        company = self.get_object()

        if company.status == CompanyStatus.BLOCKED.id:
            return Response({'detail': 'Conta bloqueada. Não é possível editar dados.'}, status=status.HTTP_400_BAD_REQUEST)

        serializer = CompanyUpdateSerializer(company, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()

        idwall_token = serializer.validated_data.get('idwallSdkToken')  # type:ignore
        company.refresh_from_db()
        onboardAccount(company, idwallSdkToken=idwall_token)

        return Response(self.get_serializer(company).data)

    @action(detail=True, methods=['put'], parser_classes=[MultiPartParser])
    def add_files(self, request, *args, **kwargs):
        company = self.get_object()
        if company.status == CompanyStatus.BLOCKED.id:
            return Response({'detail': 'Conta bloqueada. Não é possível adicionar arquivos.'}, status=status.HTTP_400_BAD_REQUEST)

        if len(request.FILES.getlist('files')) > 5:
            return Response({'detail': 'Máximo de 5 arquivos atingido.'}, status=status.HTTP_400_BAD_REQUEST)

        received_files = request.FILES.getlist('files')
        files = [('files', (file.name, file.read())) for file in received_files]

        split = SplitPay()
        response = split.sendDocuments(company.externalId, files=files)

        if not status.is_success(response.status_code):
            return Response({'detail': 'Erro ao enviar arquivos.'}, status=response.status_code)

        return Response({'detail': 'Arquivos adicionados com sucesso.'})

    @action(detail=True, methods=['put'])
    def delete_file(self, request, *args, **kwargs):
        company = self.get_object()

        if company.status == CompanyStatus.BLOCKED.id:
            return Response({'detail': 'Conta bloqueada. Não é possível remover arquivos'}, status=status.HTTP_400_BAD_REQUEST)

        file_instance = company.files.filter(pk=self.kwargs['pk']).first()

        if file_instance is not None:
            file_instance.file.delete(save=False)
            file_instance.delete()
            company.save()
            return Response({'detail': 'Arquivo removido com sucesso.'})
        return Response({'detail': 'Não encontrado.'}, status=status.HTTP_404_NOT_FOUND)

class CompanyAdminAPI(viewsets.GenericViewSet):
    scope = 'financial'
    permission_classes = [IsAdminUser]

    @action(detail=True, methods=['post'])
    def reset_verification(self, request, *args, **kwargs):
        user_id = kwargs.get('user_id')
        company = get_object_or_404(Company.objects.filter(user=user_id))

        if company.status == CompanyStatus.BLOCKED.id:
            return Response(
                {'detail': 'Conta bloqueada. Não é possível resetar.'},
                status=status.HTTP_400_BAD_REQUEST
            )

        split = SplitPay()
        response = split.reset_verification(account_id=company.externalId)
        if response.status_code != 200:
            try:
                data = response.json()
            except ValueError:
                data = {'detail': 'Erro desconhecido ao resetar verificação.'}
            return Response(data, status=response.status_code)

        company.status = CompanyStatus.RESUBMISSION_REQUESTED.id
        company.onboardUrl = None
        company.sessionId = None
        company.sessionUrl = None
        company.sessionToken = None
        company.sessionTokenExpires = None
        company.rejectedReasons = []
        company.onboard = False
        company.save()

        # Log da ação -> reset de contas
        log_action(
            user=request.user,
            action='account_reset',
            details=f"Admin {request.user.email} resetou a verificação da empresa do usuário {company.user.email}",
            affected_user=company.user
        )

        return Response({'detail': 'Verificação resetada com sucesso!'})

    @action(detail=True, methods=['put'], parser_classes=[MultiPartParser])
    def add_files(self, request, *args, **kwargs):
        company = get_object_or_404(Company.objects.filter(user=kwargs.get('user_id')))

        received_files = request.FILES.getlist('files')
        files = [('files', (file.name, file.read())) for file in received_files]

        split = SplitPay()
        response = split.sendDocuments(company.externalId, files=files)

        if status.is_client_error(response.status_code):
            return Response(response.json(), status=response.status_code)

        if status.is_server_error(response.status_code):
            raise Exception(f'Error sending files | externalId: {company.externalId}', response.text)

        return Response({'detail': 'Arquivos adicionados com sucesso.'})

class VerificationAPIView(views.APIView):
    scope = 'financial'

    def post(self, request):
        company = request.user.company

        if company.status == 'rejected':
            return Response({'detail': 'Verificação de identidade reprovada.'}, status=status.HTTP_400_BAD_REQUEST)

        if company.status == 'blocked':
            return Response({'detail': 'Conta bloqueada.'}, status=status.HTTP_400_BAD_REQUEST)

        if company.sessionTokenExpires and (company.sessionTokenExpires <= timezone.now()):
            company.sessionId = None
            company.sessionUrl = None
            company.sessionToken = None
            company.save()

        if request.user.totalSales() <= 0:
            return Response({'detail': 'É necessário ter alguma venda aprovada antes de fazer a validação'}, status=status.HTTP_400_BAD_REQUEST)

        if company.sessionUrl:
            return Response({'url': company.sessionUrl})

        if company.status not in ['waiting_documents', 'resubmission_requested']:
            return Response({'detail': 'É necessário enviar os dados completos antes de fazer a validação'}, status=status.HTTP_400_BAD_REQUEST)

        if not company.verificationDocument or not company.verificationDocumentType:
            company.status = 'pending'
            company.save()
            return Response({'detail': 'Informe o documento e o tipo de documento para validação.'}, status=status.HTTP_400_BAD_REQUEST)

        response = self.get_verifications_data(request, company)

        if status.is_success(response.status_code):
            data = response.json().get('verification', {})

            company.sessionId = data.get('id')
            company.sessionUrl = data.get('url')
            company.sessionToken = data.get('sessionToken')
            company.sessionTokenExpires = timezone.now() + timezone.timedelta(days=6, hours=22)
            company.save()

            return Response({'url': company.sessionUrl})

        raise Exception(response.text)

    def get_verifications_data(self, request, company):
        url = 'https://stationapi.veriff.com/v1/sessions/'

        document_type = ''
        if company.verificationDocumentType == 'rg':
            document_type = 'ID_CARD'
        elif company.verificationDocumentType == 'cnh':
            document_type = 'DRIVERS_LICENSE'
        elif company.verificationDocumentType == 'passport':
            document_type = 'PASSPORT'

        payload = {
            'verification': {
                'callback': 'https://app.cakto.com.br/dashboard/financial?tab=identity',
                'person': {
                    'firstName': ' '.join(company.completeName.split()[:-1]),
                    'lastName': company.completeName.split()[-1],
                    'idNumber': company.cpf,
                    'dateOfBirth': company.birthDate.strftime('%Y-%m-%d'),
                },
                'document': {
                    'number': company.verificationDocument,
                    'type': document_type,
                    'country': 'BR'
                },
                'vendorData': str(company.externalId),
                'features': ['selfid']
            }
        }

        headers = {
            'X-AUTH-CLIENT': os.getenv('VERIFF_API_TOKEN', ''),
            'Content-Type': 'application/json'
        }

        return requests.request('POST', url, json=payload, headers=headers)


class PlanFeeAPI(viewsets.ViewSet):
    scope = 'financial'

    def list_plans(self, request):
        # Coleta parâmetros de filtro
        name_filter = request.query_params.get("name")
        id_filter = request.query_params.get("id")

        # Cria chave de cache baseada nos parâmetros
        filters_key = json.dumps({'name': name_filter, 'id': id_filter}, sort_keys=True)
        cache_key = f'plan_fees::{md5(filters_key.encode()).hexdigest()}'

        # Tenta recuperar do cache
        cached_response = cache.get(cache_key)
        if cached_response:
            return Response(cached_response)

        # Consulta a API do Split
        split = SplitPay()
        resp = split.list_plan_fees()

        # Valida se houve erro
        error_response = handle_split_response(resp)
        if error_response:
            return error_response

        plan_fees = resp.json()

        # Filtro local, se aplicável
        if name_filter:
            plan_fees = [plan for plan in plan_fees if plan.get("name") == name_filter]

        if id_filter:
            try:
                id_filter = int(id_filter)
                plan_fees = [plan for plan in plan_fees if plan.get("id") == id_filter]
            except ValueError:
                return Response(
                    {"detail": "O parâmetro id deve ser um número inteiro."},
                    status=status.HTTP_400_BAD_REQUEST
                )

        # Atualiza o cache
        cache.set(cache_key, plan_fees, timeout=60 * 60)

        return Response(plan_fees, status=resp.status_code)

    def choose_plan(self, request):
        # Captura o plan_type informado
        plan_type = request.data.get('plan_type')
        if not plan_type:
            return Response({'detail': 'plan_type é obrigatório.'}, status=status.HTTP_400_BAD_REQUEST)

        # Chama a API do Split para buscar o plano
        split = SplitPay()
        plan_fees_resp = split.list_plan_fees(query_params={"search": plan_type})
        error_response = handle_split_response(plan_fees_resp)
        if error_response:
            return error_response

        plan_fees = plan_fees_resp.json()
        plan_fee = next((p for p in plan_fees if p.get("name") == plan_type), None)
        if not plan_fee:
            return Response({'detail': f'Plano {plan_type} não encontrado.'}, status=status.HTTP_404_NOT_FOUND)

        # Se o plan_type for D2: apenas aviso que requer aprovação
        # TODO: implementar fluxo de solicitação de aprovação via Gateway
        if plan_type == 'D2':
            return Response(
                {'detail': 'O plano D2 requer aprovação e não pode ser escolhido diretamente.'},
                status=status.HTTP_200_OK
            )

        external_id = request.user.company.externalId
        if not external_id:
            return Response(
                {'detail': 'Usuário não possui conta cadastrada na Split (externalId ausente).'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Verifica se já está com o mesmo plano
        account_resp = split.getAccount(external_id)
        error_response = handle_split_response(account_resp)
        if error_response:
            return error_response

        current_plan_fee = account_resp.json().get("plan_fee")
        if current_plan_fee == plan_fee["id"]:
            return Response({'detail': f'O plano {plan_type} já está configurado.'}, status=status.HTTP_200_OK)

        # PATCH com o novo plan_fee
        update_resp = split.update_user_plan_fee(external_id, plan_fee["id"])
        if update_resp.status_code == 404:
            return Response({'detail': 'Conta não encontrada na Split.'}, status=status.HTTP_404_NOT_FOUND)
        if update_resp.status_code != 200:
            return Response(update_resp.text, status=update_resp.status_code)

        return Response({'detail': f'Plano {plan_type} selecionado com sucesso'}, status=status.HTTP_200_OK)

    def current_plan(self, request):
        split = SplitPay()

        # Recupera o externalId local da Company
        seller_id = getattr(request.user._company, 'externalId', None)
        if not seller_id:
            return Response(
                {'detail': 'Conta do usuário não vinculada à Split.'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Busca os dados da conta na Split para obter o plan_fee atua
        seller_resp = split.getAccount(seller_id)
        error_response = handle_split_response(seller_resp)
        if error_response:
            return error_response

        seller_data = seller_resp.json()
        plan_fee_id = seller_data.get("plan_fee")

        # Verifica se o usuário possui um plano vinculado
        if plan_fee_id is None:
            return Response({"detail": "Usuário não possui plano vinculado."}, status=status.HTTP_200_OK)

        # Busca os detalhes do plano vinculado
        plan_fee_resp = split.get_plan_fee_by_id(plan_fee_id)
        error_response = handle_split_response(plan_fee_resp)
        if error_response:
            return error_response

        return Response(plan_fee_resp.json(), status=status.HTTP_200_OK)


class CieloThreeDsTokenAPIView(views.APIView):
    permission_classes = [AllowAny]
    throttle_classes = [CaktoScopedRateThrottle]
    throttle_scope = 'threeDsToken'

    def get(self, request):
        split = SplitPay()
        try:
            response = split.get_cielo_three_ds_token()
        except Exception:
            return Response(
                {"error": "Falha ao conectar com o servidor da Split"},
                status=status.HTTP_502_BAD_GATEWAY
            )

        return Response(response.json(), status=response.status_code)
