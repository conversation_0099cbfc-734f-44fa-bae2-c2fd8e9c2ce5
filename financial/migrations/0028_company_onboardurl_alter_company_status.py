# Generated by Django 4.2.5 on 2024-06-18 20:09

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('financial', '0027_company_averagerevenue_company_companytype'),
    ]

    operations = [
        migrations.AddField(
            model_name='company',
            name='onboardUrl',
            field=models.URLField(blank=True, max_length=2048, null=True),
        ),
        migrations.AlterField(
            model_name='company',
            name='status',
            field=models.CharField(choices=[('pending', 'Pendente'), ('waiting_documents', 'Aguardando envio dos documentos'), ('gateway_pending', 'Aguardando aprovação'), ('waiting_url', 'Aguardando Url'), ('resubmission_requested', 'Reenvio solicitado'), ('approved', 'Aprovado'), ('rejected', 'Rejeitado'), ('blocked', 'Bloqueado')], default='pending', max_length=255),
        ),
    ]
