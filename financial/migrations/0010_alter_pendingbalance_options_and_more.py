# Generated by Django 4.2.5 on 2024-01-23 19:38

from decimal import Decimal
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('financial', '0009_pendingbalance_payment'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='pendingbalance',
            options={'verbose_name': 'Saldo Pendentente', 'verbose_name_plural': 'Saldos Pendententes'},
        ),
        migrations.RemoveField(
            model_name='pendingbalance',
            name='antecipated_amount',
        ),
        migrations.AddField(
            model_name='pendingbalance',
            name='remainingAmount',
            field=models.DecimalField(decimal_places=2, default=Decimal('0'), max_digits=10, verbose_name='Valor Restante(Não antecipado)'),
        ),
        migrations.AlterField(
            model_name='pendingbalance',
            name='status',
            field=models.Char<PERSON>ield(choices=[('released', 'Liberado'), ('waiting_release_date', 'Aguardando Liberação'), ('refunded', 'Cancelado por reembolso'), ('chargeback', 'Cancelado por chargeback'), ('in_protest', 'Cancelado por protesto'), ('anticipated', 'Valor Antecipado Totalmente'), ('partially_anticipated', 'Valor Antecipado Parcialmente')], default='waiting_release_date', max_length=60),
        ),
        migrations.CreateModel(
            name='Withdrawal',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Total')),
                ('caktoWithdrawFee', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='Taxa do saque')),
                ('anticipatedAmount', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='Valor antecipado')),
                ('anticipateFee', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='Taxa de Antecipação')),
                ('externalId', models.CharField(max_length=255, null=True, verbose_name='Id externo da transferência')),
                ('status', models.CharField(choices=[('pending', 'Pendente'), ('bank_processing', 'Processamento do Banco'), ('success', 'Sucesso'), ('failed', 'Falha')], default='pending', max_length=255, verbose_name='Status')),
                ('createdAt', models.DateTimeField(auto_now_add=True, verbose_name='Criado em')),
                ('updatedAt', models.DateTimeField(auto_now=True, verbose_name='Atualizado em')),
                ('transferredAt', models.DateTimeField(blank=True, null=True, verbose_name='Efetivado em')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='withdrawals', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Saque',
                'verbose_name_plural': 'Saques',
            },
        ),
        migrations.CreateModel(
            name='Anticipation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Valor')),
                ('pendingBalance', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='anticipations', to='financial.pendingbalance')),
                ('withdrawal', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='anticipations', to='financial.withdrawal')),
            ],
        ),
        migrations.AlterField(
            model_name='pendingbalance',
            name='withdrawals',
            field=models.ManyToManyField(blank=True, related_name='pending_balances', to='financial.withdrawal'),
        ),
    ]
