# Generated by Django 4.2.5 on 2024-04-25 21:34

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('financial', '0024_alter_company_status'),
    ]

    operations = [
        migrations.AddField(
            model_name='company',
            name='sessionId',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='company',
            name='sessionToken',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='company',
            name='sessionTokenExpires',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='company',
            name='sessionUrl',
            field=models.URLField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='company',
            name='verificationDocument',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.Add<PERSON>ield(
            model_name='company',
            name='verificationDocumentType',
            field=models.CharField(blank=True, choices=[('rg', 'RG'), ('cnh', 'CNH'), ('passport', 'Passaporte')], max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='company',
            name='status',
            field=models.CharField(choices=[('pending', 'Pendente'), ('waiting_documents', 'Aguardando envio dos documentos'), ('gateway_pending', 'Aguardando aprovação'), ('resubmission_requested', 'Reenvio solicitado'), ('approved', 'Aprovado'), ('rejected', 'Rejeitado'), ('blocked', 'Bloqueado')], default='pending', max_length=255),
        ),
    ]
