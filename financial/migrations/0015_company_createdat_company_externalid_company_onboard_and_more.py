# Generated by Django 4.2.5 on 2024-01-31 19:42

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('financial', '0014_delete_bankaccount'),
    ]

    operations = [
        migrations.AddField(
            model_name='company',
            name='createdAt',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now, verbose_name='Criado em'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='company',
            name='externalId',
            field=models.IntegerField(blank=True, null=True, verbose_name='Id externo'),
        ),
        migrations.AddField(
            model_name='company',
            name='onboard',
            field=models.BooleanField(default=False, verbose_name='Cadastro Completo na Split'),
        ),
        migrations.AddField(
            model_name='company',
            name='updatedAt',
            field=models.DateTimeField(auto_now=True, verbose_name='Atualizado em'),
        ),
    ]
