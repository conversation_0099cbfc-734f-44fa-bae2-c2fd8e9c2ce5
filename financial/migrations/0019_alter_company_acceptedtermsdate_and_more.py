# Generated by Django 4.2.5 on 2024-03-04 15:52

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('financial', '0018_alter_anticipation_options'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='company',
            name='acceptedTermsDate',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='company',
            name='birthDate',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='company',
            name='cep',
            field=models.CharField(blank=True, max_length=8, null=True),
        ),
        migrations.AlterField(
            model_name='company',
            name='city',
            field=models.Char<PERSON>ield(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='company',
            name='completeName',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name='company',
            name='cpf',
            field=models.Char<PERSON>ield(blank=True, max_length=11, null=True),
        ),
        migrations.AlterField(
            model_name='company',
            name='motherN<PERSON>',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='company',
            name='neighborhood',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='company',
            name='number',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='company',
            name='phone',
            field=models.CharField(blank=True, max_length=11, null=True),
        ),
        migrations.AlterField(
            model_name='company',
            name='state',
            field=models.CharField(blank=True, max_length=2, null=True),
        ),
        migrations.AlterField(
            model_name='company',
            name='street',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
    ]
