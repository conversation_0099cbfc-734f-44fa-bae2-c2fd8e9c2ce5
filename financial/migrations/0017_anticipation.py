# Generated by Django 4.2.5 on 2024-02-22 14:59

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('financial', '0016_remove_pendingbalance_withdrawals_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Anticipation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Valor Antecipado')),
                ('fee', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='Taxa de Antecipação')),
                ('createdAt', models.DateTimeField(auto_now_add=True, verbose_name='Criado em')),
                ('pending', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='anticipations', to='financial.pendingbalance', verbose_name='Saldo Pendente')),
            ],
        ),
    ]
