# Generated by Django 4.2.5 on 2024-01-26 00:42

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('financial', '0011_alter_pendingbalance_remainingamount'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='company',
            name='isApproved',
        ),
        migrations.AddField(
            model_name='company',
            name='status',
            field=models.CharField(choices=[('pending', 'Pendente'), ('approved', 'Aprovado'), ('rejected', 'Rejeitado'), ('waiting_documents', 'Aguardando envio dos documentos')], default='pending', max_length=255),
        ),
        migrations.CreateModel(
            name='BankAccount',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('externalRecipientId', models.Char<PERSON>ield(blank=True, max_length=255, null=True, verbose_name='Id externo do recebedor')),
                ('bankCode', models.CharField(blank=True, max_length=40, null=True, verbose_name='Código do Banco')),
                ('agencyNumber', models.CharField(blank=True, max_length=40, null=True, verbose_name='Número da agência')),
                ('accountNumber', models.CharField(blank=True, max_length=100, null=True, verbose_name='Número da conta')),
                ('accountDigit', models.CharField(blank=True, max_length=40, null=True, verbose_name='Digíto da conta')),
                ('type', models.CharField(choices=[('conta_corrente', 'Conta Corrente'), ('conta_poupanca', 'Conta Poupança')], default='conta_corrente', max_length=30, verbose_name='Tipo')),
                ('createdAt', models.DateTimeField(auto_now_add=True, verbose_name='Criado em')),
                ('updatedAt', models.DateTimeField(auto_now=True, verbose_name='Atualizado em')),
                ('company', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='bank_accounts', to='financial.company')),
            ],
            options={
                'verbose_name': 'Conta Bancária',
                'verbose_name_plural': 'Contas Bancárias',
            },
        ),
    ]
