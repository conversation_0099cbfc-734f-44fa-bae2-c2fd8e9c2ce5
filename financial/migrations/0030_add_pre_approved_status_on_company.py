# Generated by Django 4.2.5 on 2025-02-25 18:14

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('financial', '0029_company_rejectedreasons'),
    ]

    operations = [
        migrations.AlterField(
            model_name='company',
            name='status',
            field=models.CharField(
                choices=[
                    ('pending', 'Pendente'),
                    ('pre_approved', 'Pré-aprovado'),
                    ('approved', 'Aprovado'),
                    ('rejected', 'Rejeitado'),
                    ('blocked', 'Bloqueado'),
                    ('waiting_documents', 'Aguardando envio dos documentos'),
                    ('gateway_pending', 'Aguardando aprovação'),
                    ('waiting_url', 'Aguardando Url'),
                    ('resubmission_requested',
                     'Reenvio solicitado')
                ],
                default='pending',
                max_length=255
            ),
        ),
    ]
