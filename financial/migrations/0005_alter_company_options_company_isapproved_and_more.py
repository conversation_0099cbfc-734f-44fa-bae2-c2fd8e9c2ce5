# Generated by Django 4.2.5 on 2023-12-27 18:13

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('financial', '0004_alter_pendingbalance_status'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='company',
            options={'ordering': ['-pk']},
        ),
        migrations.AddField(
            model_name='company',
            name='isApproved',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='company',
            name='user',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.PROTECT, related_name='companies', to=settings.AUTH_USER_MODEL),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='company',
            name='complement',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.RemoveField(
            model_name='company',
            name='files',
        ),
        migrations.CreateModel(
            name='CompanyImage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file', models.FileField(upload_to='documents/')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='financial.company')),
            ],
        ),
        migrations.AddField(
            model_name='company',
            name='files',
            field=models.ManyToManyField(blank=True, related_name='company_files', to='financial.companyimage'),
        ),
    ]
