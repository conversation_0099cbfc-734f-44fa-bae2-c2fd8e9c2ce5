# Generated by Django 4.2.5 on 2023-12-11 02:53

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('financial', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Company',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('type', models.CharField(choices=[('company', 'Empresa'), ('individual', 'Individual')], default='individual', max_length=255)),
                ('companyName', models.CharField(blank=True, max_length=255, null=True)),
                ('companyCnpj', models.CharField(blank=True, max_length=255, null=True)),
                ('completeName', models.CharField(max_length=255)),
                ('cpf', models.CharField(max_length=11)),
                ('birthDate', models.DateField()),
                ('phone', models.Char<PERSON><PERSON>(max_length=11)),
                ('motherName', models.Char<PERSON><PERSON>(max_length=255)),
                ('cep', models.Char<PERSON>ield(max_length=8)),
                ('street', models.Char<PERSON>ield(max_length=255)),
                ('number', models.CharField(max_length=255)),
                ('complement', models.CharField(max_length=255)),
                ('neighborhood', models.CharField(max_length=255)),
                ('city', models.CharField(max_length=255)),
                ('state', models.CharField(max_length=2)),
                ('acceptedTerms', models.BooleanField(default=False)),
                ('acceptedTermsDate', models.DateTimeField(default=django.utils.timezone.now)),
                ('files', models.FileField(upload_to='documents/')),
            ],
        ),
    ]
