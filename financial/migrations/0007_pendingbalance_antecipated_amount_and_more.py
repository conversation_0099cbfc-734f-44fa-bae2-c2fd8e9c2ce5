# Generated by Django 4.2.5 on 2024-01-19 17:31

from decimal import Decimal
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0022_alter_withdrawal_options'),
        ('gateway', '0031_period'),
        ('financial', '0006_alter_company_user'),
    ]

    operations = [
        migrations.AddField(
            model_name='pendingbalance',
            name='antecipated_amount',
            field=models.DecimalField(decimal_places=2, default=Decimal('0'), max_digits=10),
        ),
        migrations.AddField(
            model_name='pendingbalance',
            name='isStuck',
            field=models.BooleanField(db_index=True, default=False, verbose_name='Retenção de Segurança'),
        ),
        migrations.AddField(
            model_name='pendingbalance',
            name='withdrawals',
            field=models.ManyToManyField(blank=True, related_name='pending_balances', to='user.withdrawal'),
        ),
        migrations.<PERSON>er<PERSON>ield(
            model_name='pendingbalance',
            name='order',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='gateway.order'),
        ),
    ]
