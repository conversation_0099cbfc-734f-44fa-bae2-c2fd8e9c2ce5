# Generated by Django 4.2.5 on 2023-12-21 23:26

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('gateway', '0024_subscription_externalplanid_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('financial', '0002_company'),
    ]

    operations = [
        migrations.AddField(
            model_name='pendingbalance',
            name='order',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.PROTECT, to='gateway.order'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='pendingbalance',
            name='status',
            field=models.CharField(choices=[('released', 'Liberado'), ('waiting_release_date', 'Aguardando Liberação'), ('refunded', 'Cancelado por reembolso'), ('chargeback', 'Cancelado por chargeback'), ('in_protest', 'Cancelado por protesto'), ('canceled', 'Cancelado')], default='waiting_release_date', max_length=60),
        ),
        migrations.AlterField(
            model_name='pendingbalance',
            name='release_date',
            field=models.DateTimeField(verbose_name='Data de Liberação'),
        ),
        migrations.AlterField(
            model_name='pendingbalance',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='pending_balance', to=settings.AUTH_USER_MODEL),
        ),
    ]
