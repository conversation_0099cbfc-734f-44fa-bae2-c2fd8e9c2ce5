# Generated by Django 4.2.5 on 2024-03-08 22:43

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('financial', '0019_alter_company_acceptedtermsdate_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='pendingbalance',
            name='order',
        ),
        migrations.RemoveField(
            model_name='pendingbalance',
            name='payment',
        ),
        migrations.RemoveField(
            model_name='pendingbalance',
            name='user',
        ),
        migrations.RemoveField(
            model_name='withdrawal',
            name='user',
        ),
        migrations.AlterField(
            model_name='company',
            name='user',
            field=models.OneToOneField(on_delete=django.db.models.deletion.PROTECT, related_name='_company', to=settings.AUTH_USER_MODEL),
        ),
        migrations.DeleteModel(
            name='Anticipation',
        ),
        migrations.DeleteModel(
            name='PendingBalance',
        ),
        migrations.DeleteModel(
            name='Withdrawal',
        ),
    ]
