from django.urls import path
from financial import views

app_name = 'financial'

urlpatterns = [
    path('financial/company/', views.CompanyViewSet.as_view({'get': 'retrieve', 'put': 'update'}), name='company'),
    path('financial/company/add_files/', views.CompanyViewSet.as_view({'put': 'add_files'}), name='company-add-files'),
    path('financial/company/delete_file/<int:pk>/', views.CompanyViewSet.as_view({'delete': 'delete_file'}), name='company-remove-file'),
    path('financial/balance/', views.BalanceViewSet.as_view({'get': 'list'}), name='balance'),
    path('financial/withdraw/', views.BalanceViewSet.as_view({'post': 'withdraw'}), name='withdraw'),
    path('financial/anticipate/', views.BalanceViewSet.as_view({'post': 'anticipate_balance'}), name='anticipate-balance'),
    path('financial/pix/', views.PixAPI.as_view({'get': 'list', 'post': 'create'}), name='pix'),
    path('financial/fee/', views.FeeAPI.as_view({'get': 'get_user_fees'}), name='user_fees'),
    path('financial/withdraw_fee/', views.FeeAPI.as_view({'get': 'get_withdraw_fee'}), name='withdraw_fee'),
    path('financial/verification/', views.VerificationAPIView.as_view(), name='verification'),

    # Admin
    path('admin/financial/reset_verification/<int:user_id>/', views.CompanyAdminAPI.as_view({'post': 'reset_verification'}), name='reset-verification'),
    path('admin/financial/add_files/<int:user_id>/', views.CompanyAdminAPI.as_view({'put': 'add_files'}), name='admin-company-add-files'),

    # Plan Fee
    path(
        'financial/plan_fee/list_plans/',
        views.PlanFeeAPI.as_view({'get': 'list_plans'}),
        name='list_plan_fees'
    ),
    path(
        'financial/plan_fee/choose_plan/',
        views.PlanFeeAPI.as_view({'post': 'choose_plan'}),
        name='choose_plan'
    ),

    path(
        'financial/plan_fee/current_plan/',
        views.PlanFeeAPI.as_view({'get': 'current_plan'}),
        name='current_plan'
    ),

    path(
        'financial/cielo/3ds/token/',
        views.CieloThreeDsTokenAPIView.as_view(),
        name='cielo_3ds_token'
    ),
]
