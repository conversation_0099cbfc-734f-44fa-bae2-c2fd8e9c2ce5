import random
import string
from typing import Literal

import requests
from django.core.cache import cache
from django.utils import timezone
from django_rq import job
from rest_framework import status
from rest_framework.exceptions import ValidationError
from rq import Retry
from django.contrib.auth import get_user_model

from apps.services.event_manager import dispatch_app_events, dispatch_event
from email_service.mail import (dispatch_approved_mail, send_order_refunded_customer_email,
                                send_order_refunded_producer_email, send_subscription_canceled_email,
                                send_subscription_renewal_payment_mail, send_subscription_renewal_refused_payment_mail, send_subscription_renewed_email)
from financial.enums import CompanyStatus
from financial.models import Company
from gateway.models import Order, Payment, Subscription
from gateway.sdk import SplitPay
from members.sdk.discord import remove_discord_access_from_order
from members.sdk.instagram import remove_instagram_access_from_order
from members.sdk.members_v2 import add_membersV2_course_to_user, remove_membersV2_user_access
from members.sdk.members_v2_webhook import send_purchase_webhook_to_members_v2
from members.sdk.telegram import remove_telegram_access_from_order
from members.utils import add_user_course_access, remove_user_course_access
from notifications.models import NotificationType
from notifications.utils import is_notification_enabled
from product.enums import ProductType
from product.models import DeliveryAccess, Product, ProductDelivery
from product.utils import calculate_product_metrics, update_checkout_sales_count
from user.models import MFAMethod, User
from user.notification import send_notification
from user.sdk.crm import send_crm_chargeback_event, send_crm_refund_event, send_crm_sale_event
from user.utils import create_user_awards, get_or_create_user
from gateway.services.email_marketing_service import send_user_to_email_marketing


def handle_approval(order: Order, payment: Payment, webhook_data: dict = {}):
    is_subscription_payment = order.type == ProductType.SUBSCRIPTION.id

    if is_subscription_payment:
        order.subscription.update_recurrence_metrics()

    is_new_subscription = order.subscription and order.subscription.current_period in [0, 1]

    if is_new_subscription is True:
        handle_new_subscription.delay(order=order, payment=payment, webhook_data=webhook_data)

    product: Product = order.product

    if product.statusAdmin == 'stand_by':
        product.statusAdmin = 'pending'
        product.save(update_fields=['statusAdmin'])

    user = get_or_create_user(order.customer.email, cellphone=order.customer.phone, emailValidated=True, is_customer=True)
    # Set recovery token if user has no password
    user.handle_otp_token()

    if is_new_subscription is False:
        handle_subscription_renewed(order=order, payment=payment, webhook_data=webhook_data)
    else:
        dispatch_event('purchase_approved', order=order, payment=payment)
        # send_new_password = not user.has_usable_password() and product.has_internal_contentDelivery()
        dispatch_approved_mail.delay(order=order, user=user, send_new_password=False)

    handle_purchase_course_access(order=order, user=user)

    handle_commissionedUsers_purchase_approval(order)

    handle_purchase_approved_notification(
        order,
        is_subscription_payment,
        is_new_subscription  # type: ignore
    )

    calculate_product_metrics.delay(product)

    update_checkout_sales_count.delay(offer=order.offer, checkout=order.checkout)  # type: ignore

    send_crm_sale_event.delay(order=order)

    send_purchase_webhook_to_members_v2.delay(order=order, payment=payment)

    send_user_to_email_marketing(order.product.user)

def handle_commissionedUsers_purchase_approval(order):
    cache_keys_to_delete = []

    for commissionedUser in order.commissionedUsers.all():
        create_user_awards.delay(user=commissionedUser)
        cache_keys_to_delete.append(f'{User.totalSales_cache_prefix}{commissionedUser.pk}')
        cache_keys_to_delete.append(f'{User.nextRank_cache_prefix}{commissionedUser.pk}')
        cache_keys_to_delete.append(f'{User.nextAward_cache_prefix}{commissionedUser.pk}')

        if not commissionedUser.is_producer:
            commissionedUser.is_producer = True
            commissionedUser.save(update_fields=['is_producer'])
            MFAMethod.get_email_method(
                user=commissionedUser,
                confirmed=True,
            )

    cache.delete_many(cache_keys_to_delete)

def handle_purchase_approved_notification(order: Order, is_subscription_payment: bool, is_new_subscription: bool):
    paymentMethod_pk_to_name = {
        'credit_card': 'Cartão',
        'threeDs': 'Cartão',
        'pix': 'Pix',
        'boleto': 'Boleto',
        'picpay': 'PicPay',
        'googlepay': 'Google Pay',
        'applepay': 'Apple Pay',
        'openfinance_nubank': 'Nubank',
    }

    paymentMethodName = paymentMethod_pk_to_name.get(order.paymentMethod.pk) or order.paymentMethodName.split()[0].title()

    if is_subscription_payment:
        approved_renewed = 'aprovada' if is_new_subscription else 'renovada'
        title = f'Assinatura {approved_renewed} no {paymentMethodName}!'
    else:
        title = f'Venda aprovada no {paymentMethodName}!'

    offer_types = [
        ('upsell', 'Upsell'),
        ('downsell', 'Downsell'),
        ('orderbump', 'Orderbump'),
    ]

    def remove_exclamation_mark(title):
        return title[:-1]

    for offer_type, offer_name in offer_types:
        if order.offer_type == offer_type:
            title = remove_exclamation_mark(title)
            title += f' - {offer_name}!'
            break

    for user in order.commissionedUsers.all():
        if is_notification_enabled(user, NotificationType.ORDER_APPROVED):
            commission = sum(split.totalAmount for split in order.splits.filter(user=user))
            send_notification(user, title, 'Sua comissão: {amount}', commission)

@job('default', retry=Retry(max=3, interval=[5, 10, 30]))
def add_cakto_members_v1_access_job(user: User, product: Product, access_time: str):
    response, success = add_user_course_access(user=user, product=product, access_time=access_time)
    if not success:
        raise Exception('Error adding user course access:', {response.content.decode()})
    return 'User course access added', response.content.decode()

def handle_purchase_course_access(order: Order, user: User):
    product: Product = order.product

    if product.has_contentDelivery('cakto'):
        access_time = order.offer.calculate_access_time()
        access_time = access_time.isoformat() if isinstance(access_time, timezone.datetime) else access_time

        add_cakto_members_v1_access_job.delay(
            user=user,
            product=product,
            access_time=access_time,
        )

        if product.membersV2Id:
            add_membersV2_course_to_user.delay(user=user, product=product, expires_at=access_time)

    add_external_accesses.delay(order=order, user=user)

@job
def add_external_accesses(order: Order, user: User):
    product: Product = order.product

    external_types = ['telegram', 'instagram_pp', 'instagram_cf', 'discord']

    for productDelivery in product.deliveries.filter(status='active', contentDelivery__type__in=external_types):
        deliveryAccess = productDelivery.accesses.filter(user=user).first()

        if deliveryAccess and (deliveryAccess.status == 'expired' or deliveryAccess.status == 'canceled'):
            deliveryAccess.delete()
            deliveryAccess = None

        if not deliveryAccess:
            create_default_deliveryAccess(productDelivery=productDelivery, order=order, user=user)
        else:
            update_deliveryAccess_expiresAt(deliveryAccess)

def update_deliveryAccess_expiresAt(deliveryAccess: DeliveryAccess) -> None:
    current_expiresAt = deliveryAccess.expiresAt

    access_time: timezone.datetime | Literal['lifetime'] = deliveryAccess.calculate_access_time()

    if access_time == 'lifetime':
        deliveryAccess.expiresAt = None
    else:
        deliveryAccess.expiresAt = access_time

    if deliveryAccess.expiresAt != current_expiresAt:
        deliveryAccess.save(update_fields=['expiresAt'])

def create_default_deliveryAccess(productDelivery: ProductDelivery, order: Order, user: User) -> DeliveryAccess | None:
    deliveryAccess = DeliveryAccess(order=order, user=user, productDelivery=productDelivery, status='waiting_config')

    if productDelivery.contentDelivery.type == 'telegram':
        deliveryAccess.fields = {'access_code': gen_deliveryAccess_unique_access_code()}

    deliveryAccess.save()
    return deliveryAccess

def handle_refused(order: Order, payment: Payment, webhook_data: dict = {}):
    dispatch_app_events.delay('purchase_refused', order=order, payment=payment)

def handle_refund(order: Order, payment: Payment, webhook_data: dict = {}):
    dispatch_event('refund', order=order, payment=payment)

    send_order_refunded_customer_email.delay(order)

    send_order_refunded_producer_email.delay(order)

    send_crm_refund_event.delay(order=order)

    calculate_product_metrics.delay(order.product)

    remove_content_access_based_on_order(order)

    update_checkout_sales_count.delay(offer=order.offer, checkout=order.checkout)  # type: ignore

def handle_med(order: Order, payment: Payment, webhook_data: dict = {}):
    send_order_refunded_customer_email.delay(order)

    send_order_refunded_producer_email.delay(order)

    update_checkout_sales_count.delay(offer=order.offer, checkout=order.checkout)  # type: ignore

    # TODO: Enviar evento de med
    # TODO: Remover acesso ao conteúdo

def handle_chargeback(order: Order, payment: Payment, webhook_data: dict = {}):
    dispatch_event('chargeback', order=order, payment=payment)

    send_order_refunded_customer_email.delay(order)

    send_order_refunded_producer_email.delay(order)

    calculate_product_metrics.delay(order.product)

    send_crm_chargeback_event.delay(order=order)

    remove_content_access_based_on_order(order)

    update_checkout_sales_count.delay(offer=order.offer, checkout=order.checkout)  # type: ignore

def remove_content_access_based_on_order(order: Order) -> None:
    User = get_user_model()

    user = User.objects.filter(email=order.customer.email).only("pk").first()

    if user:
        if user == order.product.user:
            return

        has_other_paid_order = Order.objects.filter(
            customer__email=order.customer.email,
            status="paid",
            product=order.product
        ).exists()

        if has_other_paid_order:
            return

    if order.product.has_contentDelivery('cakto'):
        handle_members_course_removal_access.delay(order)

    remove_instagram_access_from_order.delay(order)

    remove_telegram_access_from_order.delay(order)

    remove_discord_access_from_order.delay(order)

@job('default', retry=Retry(max=3, interval=[5, 10, 30]))
def handle_members_course_removal_access(order: Order):
    user = User.objects.filter(email=order.customer.email).only('pk').first()
    if not user:
        return 'User not found'
    if user == order.product.user:
        return 'User is the product owner. Course access not removed.'

    have_another_order_paid = Order.objects.filter(customer__email=order.customer.email, status='paid', product=order.product).exists()
    if not have_another_order_paid:
        response, removed = remove_user_course_access(str(user.pk), order.product.pk)
        remove_membersV2_user_access(user=user, product=order.product)
        if removed:
            return 'User course access removed'
        return response

    return 'User has another paid order for the same product. Course access not removed.'

@job('default', retry=Retry(max=3, interval=60))
def handle_subcription_renewal_payment(order: Order, payment: Payment, webhook_data: dict = {}):
    email_sended = send_subscription_renewal_payment_mail(order=order, payment=payment)
    if not email_sended:
        raise Exception('Email not sended')

def handle_subscription_renewal_refused_payment(order: Order, payment: Payment, webhook_data: dict = {}):
    dispatch_event('subscription_renewal_refused', order=order, payment=payment)

    send_subscription_renewal_refused_payment_mail.delay(
        subscription=order.subscription,
        order=order,
    )

@job('default', retry=Retry(max=3, interval=[5, 10, 30]))
def handle_new_subscription(order: Order, payment: Payment, webhook_data: dict = {}):
    from gateway.services.payment_factory import PaymentStrategyFactory
    from gateway.strategies.base import PaymentStrategy

    payment_strategy: PaymentStrategy = PaymentStrategyFactory.get_strategy(order.paymentMethodType)

    response = payment_strategy.create_subscription(order)

    dispatch_event('subscription_created', payment=payment, order=order)

    return response

def handle_subscription_renewed(order: Order, payment: Payment, webhook_data: dict = {}):
    subscription: Subscription = order.subscription  # type: ignore
    if subscription is None:
        return

    dispatch_event('subscription_renewed', payment=payment, order=order)

    send_subscription_renewed_email.delay(order=order, payment=payment)

def handle_subscription_canceled(subscription: Subscription, webhook_data: dict = {}):
    payment: Payment = subscription.payments.order_by('createdAt').last()

    subscription.next_payment_date = None
    subscription.update_recurrence_metrics(commit=False)
    subscription.save()

    order: Order = subscription.orders.order_by('-createdAt').first()

    dispatch_event('subscription_canceled', payment=payment, order=order)

    send_subscription_canceled_email.delay(subscription)

def updateSplitEmail(user: User, new_email: str) -> requests.Response | None:
    split = SplitPay()
    response = split.onboardAccount(user.company.externalId, email=new_email)

    if status.is_client_error(response.status_code):
        raise ValidationError(response.json())
    elif not status.is_success(response.status_code):
        raise Exception('Error on change e-mail -> ' + response.text)

    return response

def onboardAccount(company: Company, **kwargs) -> requests.Response | None:
    """Sends a patch request to SplitPay API and update company data with the response.

    Args:
        company (Company): Company instance.
        **kwargs: Additional data to be sent to SplitPay API.

    Raises:
        ValidationError: mesage as response.json() if status code is 4xx.
        Exception: message as response.text if status code is not 2xx or 4xx.

    Returns:
        requests.Response | None: Response from SplitPay API.
    """

    split = SplitPay()
    name_parts = (company.completeName or '').strip().split()

    payload = {
        'email': company.user.email,
        'first_name': name_parts[0] if name_parts else '',
        'last_name': ' '.join(name_parts[1:]),
        'cpf': company.cpf,
        'docType': company.verificationDocumentType,
        'docNumber': company.verificationDocument,
        'cellphone': company.phone,
        'contactPhone': company.phone,
        'contactEmail': company.user.email,
        'averageRevenue': float(company.averageRevenue) if company.averageRevenue else None,
        'companyType': company.companyType,
        'birthDate': company.birthDate.strftime('%Y-%m-%d') if company.birthDate else None,
        'motherName': company.motherName,
        'commercialName': company.companyName,  # optional
        'companyLegalName': company.companyName,  # optional
        'cnpj': company.companyCnpj,  # optional
        'neighborhood': company.neighborhood,  # optional
        'address': company.street,  # optional
        'number': company.number,  # optional
        'state': company.state,  # optional
        'cep': company.cep,  # optional
        'city': company.city,  # optional
        'country': None,  # optional
        'postalCode': None,  # optional
        'user_agreement_accepted': company.acceptedTerms,
    }
    payload.update(kwargs)
    response = split.onboardAccount(company.externalId, **payload)

    if status.is_success(response.status_code):
        _sync_company_data_with_split(company, response.json())
        return response

    elif status.is_client_error(response.status_code):
        raise ValidationError(response.json(), code=response.status_code)

    raise Exception('Onboard failed -> ' + f'company_id: {company.pk}' + response.text)

def _sync_company_data_with_split(company: Company, split_account_data: dict):
    data = split_account_data

    company.onboard = data.get('externalProfileCompleted', company.onboard)

    split_status = data.get('status')
    if split_status in CompanyStatus.as_id_list():
        company.status = split_status

    if data.get('first_name'):
        company.completeName = data.get('first_name', '') + ' ' + data.get('last_name', '')

    company.cpf = data.get('cpf', company.cpf)
    company.verificationDocumentType = data.get('docType', company.verificationDocumentType)
    company.verificationDocument = data.get('docNumber', company.verificationDocument)
    company.phone = data.get('cellphone', data.get('contactPhone', company.phone))
    company.averageRevenue = float(data.get('averageRevenue', company.averageRevenue) or 0)
    company.companyType = data.get('companyType', company.companyType)
    company.birthDate = data.get('birthDate', company.birthDate)
    company.motherName = data.get('motherName', company.motherName)
    company.companyName = data.get('companyLegalName', data.get('commercialName', company.companyName))
    company.companyCnpj = data.get('cnpj', company.companyCnpj)
    company.neighborhood = data.get('neighborhood', company.neighborhood)
    company.street = data.get('address', company.street)
    company.number = data.get('number', company.number)
    company.state = data.get('state', company.state)
    company.cep = data.get('cep', company.cep)
    company.city = data.get('city', company.city)
    company.acceptedTerms = bool(data.get('user_agreement_accepted', company.acceptedTerms))

    company.save()

def validate_age(birthDate: timezone.datetime, min_age: int = 18) -> bool:
    today = timezone.now().date()

    age = today.year - birthDate.year
    monthDifference = today.month - birthDate.month
    dayDifference = today.day - birthDate.day

    if (monthDifference < 0 or (monthDifference == 0 and dayDifference < 0)):
        age -= 1

    return age >= min_age

def gen_deliveryAccess_unique_access_code() -> str:
    def gen_code():
        return ''.join(random.choices(string.ascii_uppercase + string.digits + string.ascii_lowercase, k=6))

    code = gen_code()
    deliveryAccesses = DeliveryAccess.objects.filter(fields__access_code=code).exists()

    while deliveryAccesses:
        code = gen_code()
        deliveryAccesses = DeliveryAccess.objects.filter(fields__access_code=code).exists()

    return code

def fetch_company_status(company: Company):
    split = SplitPay()
    response = split.getAccount(company.externalId)
    if not status.is_success(response.status_code):
        raise Exception(response.text)
    return response.json().get('status')

def sync_company_status_with_split(company):
    split_status = fetch_company_status(company)

    if company.status == split_status:
        return

    cakto_old_status = company.status
    if split_status in CompanyStatus.as_id_list():
        company.status = split_status
    else:
        return

    company.save(update_fields=['status'])
    print(f'Company where desynchronized! user:{company.user.email:<40} | externalId:{company.externalId:<7} | cakto_old_status:{cakto_old_status:<22} | split_status:{split_status}')
