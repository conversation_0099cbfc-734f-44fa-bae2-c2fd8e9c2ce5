from django.contrib import admin

from .models import Company


@admin.register(Company)
class CompanyAdmin(admin.ModelAdmin):
    list_display = ('companyName', 'user', 'type', 'companyType', 'averageRevenue', 'status')
    search_fields = ('externalId', 'user__email', 'companyName', 'companyCnpj', 'completeName', 'cpf', 'phone')
    list_filter = ('status', 'type', 'companyType', 'acceptedTerms', 'acceptedTermsDate', 'state', 'createdAt', 'updatedAt', )
    list_display_links = ('companyName', 'user')
    readonly_fields = ['user', 'files', 'createdAt', 'updatedAt']
