from django.core.management.base import BaseCommand

from rest_framework import status
from financial.models import Company
from gateway.sdk.splitpay import SplitPay

class Command(BaseCommand):
    help = 'Send all document files of companies to split server.'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Sending documents files...'))

        splitpay = SplitPay()
        errors = []
        for company in Company.objects.filter(externalId__isnull=False).prefetch_related('files'):
            if company.files.all():
                files = []
                for file in company.files.all():
                    files.append(('files', (file.file.name, file.file.read())))
                try:
                    response = splitpay.sendDocuments(files=files, account_id=company.externalId)
                    if not status.is_success(response.status_code):
                        errors.append({
                            'userEmail': company.user.email,
                            'id': company.externalId,
                            'reponse': response.text
                        })
                except Exception as e:
                    errors.append({
                        'userEmail': company.user.email,
                        'id': company.externalId,
                        'error': str(e)
                    })

        if errors:
            self.stdout.write(self.style.ERROR('Errors sending documents files:'))
            self.stdout.write(self.style.WARNING(str(errors)))
            self.stdout.write(self.style.ERROR('Errors above occur when sending documents.'))
        else:
            self.stdout.write(self.style.SUCCESS('Documents files sent successfully.'))
