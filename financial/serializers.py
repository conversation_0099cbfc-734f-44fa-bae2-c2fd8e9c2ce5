import os

from django.utils import timezone
from rest_framework import serializers
from rest_framework.exceptions import ValidationError

from financial.enums import CompanyType
from financial.utils import validate_age
from user.serializers import UserReadOnlySerializer

from .models import Company, CompanyImage


class CompanyImageSerializer(serializers.ModelSerializer):
    class Meta:
        model = CompanyImage
        fields = ('id', 'company', 'file')
        read_only_fields = ('id', )

    def validate_file(self, value):
        # Lista de tipos de arquivos permitidos
        allowed_extensions = ('.jpg', '.jpeg', '.png', '.pdf')
        # Extrai a extensão do arquivo
        ext = os.path.splitext(value.name)[1].lower()
        if ext not in allowed_extensions:
            raise serializers.ValidationError("Tipo de arquivo não suportado. Permitido: jpg, jpeg, png, pdf.")
        return value

class CompanySerializerFull(serializers.ModelSerializer):
    files = CompanyImageSerializer(read_only=True, many=True)
    completeName = serializers.CharField(required=True)
    cpf = serializers.CharField(required=True)
    birthDate = serializers.DateField(required=True)
    phone = serializers.CharField(required=True)
    motherName = serializers.CharField(required=True)
    cep = serializers.CharField(required=True)
    street = serializers.CharField(required=True)
    number = serializers.CharField(required=True)
    neighborhood = serializers.CharField(required=True)
    city = serializers.CharField(required=True)
    state = serializers.CharField(required=True)

    class Meta:
        model = Company
        fields = '__all__'

class CompanyUpdateSerializer(serializers.ModelSerializer):
    user = serializers.CharField(source='user.email', read_only=True)
    idwallSdkToken = serializers.CharField(required=False, allow_null=True)

    class Meta:
        model = Company
        fields = [
            'user',
            'status',

            'type',
            'companyName',
            'companyCnpj',
            'companyType',
            'averageRevenue',
            'completeName',
            'motherName',
            'birthDate',
            'phone',

            'cpf',
            'verificationDocument',
            'verificationDocumentType',

            'cep',
            'street',
            'number',
            'complement',
            'neighborhood',
            'city',
            'state',

            'acceptedTerms',
            'acceptedTermsDate',

            'idwallSdkToken',

            'createdAt',
            'updatedAt',
        ]

    def sanitize_cnpj(self, cnpj: str) -> str:
        return cnpj.strip().replace('.', '').replace('/', '').replace('-', '')

    def to_internal_value(self, data):
        cnpj = self.sanitize_cnpj(data.get('cnpj', ''))

        if cnpj:
            data['companyCnpj'] = cnpj
            data['type'] = CompanyType.COMPANY.id

        company = self.instance

        data.pop('acceptedTermsDate', None)
        if company and data.get('acceptedTerms') and not company.acceptedTermsDate:
            data['acceptedTermsDate'] = timezone.now()

        return super().to_internal_value(data)

    def validate_birthDate(self, value):
        if validate_age(value, 18) is False:  # type:ignore
            raise ValidationError({
                'detail': 'É necessário ter 18 anos ou mais para se cadastrar.'
            })
        return value

class CompanyPublicSerializer(serializers.ModelSerializer):
    user = UserReadOnlySerializer()
    files = CompanyImageSerializer(many=True)
    pixKey = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = Company
        fields = [
            'user',
            'status',
            'type',
            'companyName',
            'companyCnpj',
            'companyType',
            'averageRevenue',
            'completeName',
            'cpf',
            'pixKey',
            'verificationDocument',
            'verificationDocumentType',
            'birthDate',
            'phone',
            'motherName',
            'cep',
            'street',
            'number',
            'complement',
            'neighborhood',
            'city',
            'state',
            'acceptedTerms',
            'acceptedTermsDate',
            'files',
            'rejectedReasons',
            'onboardUrl',
            'createdAt',
            'updatedAt',
        ]

    def get_pixKey(self, obj):
        return obj.user.pixKey
