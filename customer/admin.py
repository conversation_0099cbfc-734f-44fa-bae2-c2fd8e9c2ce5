from django.contrib import admin

from customer.models import Address, Card, Customer, CustomerHistory
from gateway.admin import OrderInline


@admin.register(Customer)
class CustomerAdmin(admin.ModelAdmin):
    model = Customer
    list_display = ('name', 'email', 'docType', 'docNumber', 'ip', 'createdAt')
    search_fields = ('name', 'email', 'docType', 'docNumber', 'phone', 'ip', 'fingerprint')
    readonly_fields = ['createdAt', 'updatedAt', 'ip', 'fingerprint', 'id']
    list_display_links = ('name',)
    inlines = [OrderInline]

@admin.register(CustomerHistory)
class CustomerHistoryAdmin(admin.ModelAdmin):
    model = CustomerHistory
    list_display = ('id', 'customer', 'name', 'email', 'docType', 'docNumber', 'ip', 'createdAt', 'fingerprint')
    search_fields = (
        'id', 'name', 'email', 'docType', 'docNumber', 'phone', 'ip',
        'customer__email', 'customer__docNumber', 'customer__phone', 'customer__ip', 'customer__fingerprint'
    )
    readonly_fields = [f.name for f in model._meta.concrete_fields]

@admin.register(Card)
class CardAdmin(admin.ModelAdmin):
    model = Card
    exclude = ('token', 'fingerprint',)
    list_display = ('id', 'customer', 'holderName', 'lastDigits', 'brand', 'fingerprint', 'createdAt')
    search_fields = (
        'id', 'holderName', 'lastDigits', 'brand', 'fingerprint', 'createdAt',
        'customer__email', 'customer__docNumber', 'customer__phone', 'customer__ip', 'customer__fingerprint'
    )
    readonly_fields = [f.name for f in model._meta.concrete_fields]

@admin.register(Address)
class AddressAdmin(admin.ModelAdmin):
    model = Address
    list_display = ('customer', 'city', 'state', 'zipcode')
    search_fields = ('id', 'customer__email', 'customer__docNumber')
    readonly_fields = [f.name for f in model._meta.concrete_fields]
    inlines = [OrderInline]
