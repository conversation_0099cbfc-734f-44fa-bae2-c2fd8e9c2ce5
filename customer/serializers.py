from django.db import transaction
from rest_framework import serializers

from cakto.serializers import CustomFlexFieldsSerializer
from customer.models import Address, Card, Customer
from customer.utils import CustomerPaymentProcessor
from user.models import User


class CustomerOrderSerializer(serializers.ModelSerializer):
    class Meta:
        model = Customer
        fields = ['name', 'email', 'birthDate', 'phone', 'docType', 'docNumber']

class CustomerProcessPaymentSerializer(serializers.ModelSerializer):
    fingerprint = serializers.CharField(required=False)

    class Meta:
        model = Customer
        fields = [
            'id',
            'name',
            'birthDate',
            'email',
            'phone',
            'docType',
            'docNumber',
            'ip',
            'fingerprint',
        ]
        read_only_fields = ['id', 'createdAt', 'updatedAt']

class CustomerPublicSerializer(serializers.ModelSerializer):
    class Meta:
        model = Customer
        fields = ['name', 'birthDate', 'email', 'phone', 'docNumber']
        read_only_fields = ['name', 'email', 'phone', 'docNumber']

class CardPublicSerializer(serializers.ModelSerializer):
    class Meta:
        model = Card
        fields = ['holderName', 'lastDigits', 'brand']
        read_only_fields = ['holderName', 'lastDigits', 'brand']

class CustomerAdminFlexSerializer(CustomFlexFieldsSerializer):
    class Meta:
        model = Customer
        fields = '__all__'

class CustomerOwnerFlexSerializer(CustomFlexFieldsSerializer):
    class Meta:
        model = Customer
        fields = [
            'id',
            'name',
            'email',
            'phone',
            'docType',
            'docNumber',
            'ip',
            'fingerprint',
            'createdAt',
            'updatedAt',
        ]

class CustomerPublicFlexSerializer(CustomFlexFieldsSerializer):
    class Meta:
        model = Customer
        fields = [
            'name',
            'email',
            'phone',
            'birthDate',
            'docNumber',
            'docType',
        ]

class CardAdminFlexSerializer(CustomFlexFieldsSerializer):
    class Meta:
        model = Card
        fields = '__all__'

class CardPublicFlexSerializer(CustomFlexFieldsSerializer):
    class Meta:
        model = Card
        fields = [
            'customer',
            'holderName',
            'lastDigits',
            'brand',
        ]
        expandable_fields = {
            'customer': CustomerPublicFlexSerializer,
        }

class AddressFullSerializer(CustomFlexFieldsSerializer):
    class Meta:
        model = Address
        fields = [
            'id',
            'customer',
            'country',
            'state',
            'city',
            'zipcode',
            'street',
            'neighborhood',
            'complement',
            'number',
            'is_default',
            'createdAt',
            'updatedAt',
        ]
        expandable_fields = {
            'customer': CustomerOrderSerializer,
        }
        read_only_fields = [
            'id',
            'customer',
            'createdAt',
            'updatedAt',
        ]

    def create(self, validated_data):
        user: User = self.__get_user_from_context()

        with transaction.atomic():
            # Handles the possibility of the user haven't any purchase yet
            # wich will cause the customer to not exist
            customer = self.__create_or_get_customer(user)
            validated_data['customer'] = customer
            return super().create(validated_data)

    def __create_or_get_customer(self, user):
        customer_data = {
            'email': user.email,
            'name': user.get_full_name() or user.username,
        }

        customer_processor = CustomerPaymentProcessor(customer_data=customer_data)
        customer = customer_processor.get_or_new_customer()

        return customer

    def __get_user_from_context(self) -> User:
        user: User = self.context['request'].user
        if not user.is_authenticated:
            raise serializers.ValidationError("User must be authenticated to create an address.")
        return user

class AddressProcessPaymentSerializer(serializers.ModelSerializer):
    class Meta:
        model = Address
        fields = [
            'country',
            'state',
            'city',
            'zipcode',
            'street',
            'neighborhood',
            'complement',
            'number',
            'is_default',
        ]
