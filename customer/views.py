from django.db import transaction
from django.db.models.deletion import ProtectedError
from rest_framework import status, viewsets
from rest_framework.response import Response

from customer.models import Address
from customer.serializers import AddressFullSerializer


class AddressAPIView(viewsets.ModelViewSet):
    serializer_class = AddressFullSerializer

    def get_queryset(self):
        return Address.objects.filter(customer__email=self.request.user.email)

    def perform_destroy(self, instance):
        try:
            instance.delete()
        except ProtectedError:
            instance.deleted = True
            instance.save(update_fields=['deleted'])

    def update(self, request, *args, **kwargs):
        address: Address = self.get_object()

        serializer = self.get_serializer(address, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)

        has_changes = self.__has_any_field_changed(address, serializer)

        if has_changes and address.orders.exists():
            # If the address has been used in orders, we copy it instead of updating
            # so we don't break the historical data of the orders.
            # ->> This is a point of attention, as it is an unexpected behavior, it will
            # return a address with the data sended but a new ID.
            with transaction.atomic():
                new_address = self.__copy_address(address)
                serializer = self.get_serializer(new_address, data=request.data, partial=True)
                serializer.is_valid(raise_exception=True)
                serializer.save()
                return Response(serializer.data, status=status.HTTP_201_CREATED)
        else:
            serializer.save()
            return Response(serializer.data, status=status.HTTP_200_OK)

    def __copy_address(self, address: Address) -> Address:
        address.deleted = True
        address.save(update_fields=['deleted'])
        address.id = None  # Reset ID to create a new instance
        address.deleted = False  # Reset deleted status for the new instance
        return address

    def __has_any_field_changed(self, address: Address, serializer) -> bool:
        address_current_values = {
            # omitting 'complement' and 'is_default' as there is no problem
            # updating them in the address
            'country': address.country,
            'state': address.state,
            'city': address.city,
            'zipcode': address.zipcode,
            'street': address.street,
            'neighborhood': address.neighborhood,
            'number': address.number,
        }

        received_values = serializer.validated_data.items()

        for field, value in received_values:
            if address_current_values.get(field) != value:
                return True
        return False
