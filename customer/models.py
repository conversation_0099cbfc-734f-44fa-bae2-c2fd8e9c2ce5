from django.db import models


class Customer(models.Model):
    name = models.Char<PERSON><PERSON>(max_length=255)
    email = models.EmailField()
    phone = models.CharField(max_length=255)
    birthDate = models.DateField('Data de Nascimento', null=True, blank=True)
    docNumber = models.CharField(max_length=255, help_text='CPF ou CNPJ')
    docType = models.Char<PERSON>ield(
        max_length=255,
        choices=(
            ('cpf', 'CPF'),
            ('cnpj', 'CNPJ')
        ),
        default='cpf',
        help_text='Tipo de documento (cpf ou cnpj)'
    )
    ip = models.CharField(max_length=255, null=True, blank=True, help_text='IP do usuário')
    fingerprint = models.CharField(max_length=255, help_text='Fingerprint do browser do usuário')

    createdAt = models.DateTimeField(auto_now_add=True)
    updatedAt = models.DateTimeField(auto_now=True)
    themembers_uuid = models.Char<PERSON><PERSON>(
        max_length=36,
        null=True,
        blank=True,
        unique=False,
    )

    class Meta:
        verbose_name = 'Cliente'
        verbose_name_plural = 'Clientes'

    def __str__(self):
        return self.name

class CustomerHistory(models.Model):
    customer = models.ForeignKey('customer.Customer', on_delete=models.CASCADE)
    name = models.CharField(max_length=255)
    email = models.EmailField()
    phone = models.CharField(max_length=255)
    birthDate = models.DateField('Data de Nascimento', null=True, blank=True)
    docNumber = models.CharField(max_length=255, help_text='CPF ou CNPJ')
    docType = models.CharField(
        max_length=255,
        choices=(
            ('cpf', 'CPF'),
            ('cnpj', 'CNPJ')
        ),
        default='cpf',
        help_text='Tipo de documento (cpf ou cnpj)'
    )
    ip = models.CharField(max_length=255, null=True, blank=True, help_text='IP do usuário')
    fingerprint = models.CharField(max_length=255, help_text='Fingerprint do browser do usuário')
    createdAt = models.DateTimeField(auto_now_add=True)
    updatedAt = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'Histórico de Cliente'
        verbose_name_plural = 'Histórico de Clientes'

    def __str__(self):
        return self.name

class Card(models.Model):
    customer = models.ForeignKey('customer.Customer', on_delete=models.CASCADE)
    token = models.CharField(max_length=255)
    holderName = models.CharField(max_length=255)
    lastDigits = models.CharField(max_length=255)
    brand = models.CharField(max_length=255, null=True, blank=True)
    fingerprint = models.CharField(max_length=255, null=True, blank=True)
    createdAt = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.holderName

class AddressManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(deleted=False)

class DeletedAddressManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(deleted=True)

class Address(models.Model):
    objects = AddressManager()  # should be the first manager so it is the default
    deleted_objects = DeletedAddressManager()

    customer = models.ForeignKey(Customer, on_delete=models.CASCADE, related_name='addresses')
    country = models.CharField(
        max_length=2,
        verbose_name='País',
        help_text='Código do país no formato ISO 3166-1 Alpha-2 (ex: BR para Brasil)',
    )
    state = models.CharField(
        max_length=2,
        verbose_name='Estado',
        help_text='Código do estado no formato ISO 3166-2 Alpha-2 (ex: SP para São Paulo)',
    )
    city = models.CharField(
        max_length=255,
        verbose_name='Cidade',
        help_text='Nome da cidade do cliente',
    )
    zipcode = models.CharField(
        max_length=30,
        verbose_name='CEP',
        help_text='CEP do cliente',
    )
    street = models.CharField(
        max_length=255,
        verbose_name='Rua',
        help_text='Nome da rua do cliente',
    )
    neighborhood = models.CharField(
        max_length=255,
        verbose_name='Bairro',
        help_text='Nome do bairro do cliente',
        blank=True,
        null=True,
    )
    complement = models.CharField(
        max_length=255,
        verbose_name='Complemento',
        help_text='Complemento do endereço do cliente',
        blank=True,
        null=True,
    )
    number = models.CharField(
        max_length=255,
        verbose_name='Número',
        help_text='Número do endereço do cliente',
    )
    is_default = models.BooleanField(
        default=False,
        db_index=True,
        verbose_name='Endereço Principal',
        help_text='Define se este é o endereço principal do cliente'
    )
    deleted = models.BooleanField(
        default=False,
        db_index=True,
        verbose_name='Excluído',
        help_text='Define se o endereço foi excluído'
    )

    createdAt = models.DateTimeField(
        db_index=True,
        auto_now_add=True,
        verbose_name='Criado em',
    )
    updatedAt = models.DateTimeField(
        auto_now=True,
        verbose_name='Atualizado em',
    )

    class Meta:
        verbose_name = 'Endereço'
        verbose_name_plural = 'Endereços'
        ordering = ['is_default', '-createdAt']

    def __str__(self):
        return f"{self.street}, {self.number} - {self.city}/{self.state}"

    def save(self, *args, **kwargs):
        self.normalize_fields()

        if self.is_default:
            self.clear_other_default_addresses()

        super().save(*args, **kwargs)

    def clear_other_default_addresses(self) -> None:
        Address.objects\
            .filter(customer=self.customer, is_default=True)\
            .exclude(id=self.id)\
            .update(is_default=False)

    def normalize_fields(self) -> None:
        # Normalize country and state codes to uppercase
        if self.country:
            self.country = self.country.upper()
        if self.state:
            self.state = self.state.upper()

        # Normalize zipcode removing any non-alphanumeric characters
        if self.zipcode:
            self.zipcode = self.normalize_zipcode(self.zipcode)

    @staticmethod
    def normalize_zipcode(zipcode: str) -> str:
        return ''.join(filter(str.isalnum, zipcode))
