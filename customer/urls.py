from django.urls import path

from customer import views

app_name = 'customer'

urlpatterns = [
    path(
        'customer/address/',
        views.AddressAPIView.as_view({'get': 'list', 'post': 'create'}),
        name='addresses'
    ),
    path(
        'customer/address/<str:pk>/',
        views.AddressAPIView.as_view({
            'get': 'retrieve',
            'put': 'update',
            'delete': 'destroy',
        }),
        name='address-detail'
    ),
]
