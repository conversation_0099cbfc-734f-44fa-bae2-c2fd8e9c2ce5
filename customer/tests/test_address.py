from django.urls import reverse
from rest_framework.exceptions import ValidationError

from cakto.tests.base import BaseTestCase
from customer.models import Address, Customer
from customer.serializers import AddressProcessPaymentSerializer
from customer.utils import AddressPaymentProcessor


class AddressTest(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()

        cls.headers = cls.build_user_auth_headers(cls.user)

        cls.customer = cls.create_customer(email=cls.user.email)

        cls.address = Address.objects.create(
            customer=cls.customer,
            country='BR',
            state='SP',
            city='São Paulo',
            zipcode='01000000',
            street='Avenida Paulista',
            neighborhood='Centro',
            complement='Apto 123',
            number='456',
            is_default=True
        )

    def test_AddressProcessPaymentSerializer_raises_error_with_wrong_country_code(self):
        with self.assertRaises(
            ValidationError,
        ) as exception:
            AddressProcessPaymentSerializer(data={
                'country': 'XYZ',  # Invalid country code
                'state': 'SP',
                'city': 'São Paulo',
                'zipcode': '01000-000',
                'street': 'Avenida Paulista',
                'complement': 'Apto 123',
                'number': '456',
            }).is_valid(raise_exception=True)
        self.assertEqual(
            str(exception.exception.detail['country'][0]),  # type:ignore
            'Certifique-se de que este campo não tenha mais de 2 caracteres.'
        )

    def test_AddressProcessPaymentSerializer_raises_error_with_wrong_state_code(self):
        with self.assertRaises(
            ValidationError,
        ) as exception:
            AddressProcessPaymentSerializer(data={
                'country': 'BR',
                'state': 'XYZ',  # Invalid country code
                'city': 'São Paulo',
                'zipcode': '01000-000',
                'street': 'Avenida Paulista',
                'complement': 'Apto 123',
                'number': '456',
            }).is_valid(raise_exception=True)
        self.assertEqual(
            str(exception.exception.detail['state'][0]),  # type:ignore
            'Certifique-se de que este campo não tenha mais de 2 caracteres.'
        )

    def test_address_save_normalizes_country_and_state_to_upper_case(self):
        self.address.country = 'us'
        self.address.state = 'ar'
        self.address.save()

        self.address.refresh_from_db()

        self.assertEqual(self.address.country, 'US')
        self.assertEqual(self.address.state, 'AR')

    def test_address_save_normalizes_zipcode(self):
        self.address.zipcode = '01000-000'
        self.address.save()

        self.address.refresh_from_db()

        # Check that the zipcode is normalized to just alphanumeric characters
        self.assertEqual(self.address.zipcode, '01000000')

    def test_address_save_as_default_removes_default_flag_from_other_addresses(self):
        # Create another address for the same customer
        self.address.is_default = True
        self.address.save()

        # Create a new address and set it as default
        address_2 = Address.objects.create(
            customer=self.customer,
            country='BR',
            state='RJ',
            city='Rio de Janeiro',
            zipcode='20000-000',
            street='Rua do Mercado',
            complement='Sala 45',
            number='789',
            is_default=False  # Initially not default
        )

        # Set the new address as default
        address_2.is_default = True

        # Call
        address_2.save()

        self.address.refresh_from_db()

        # Check that the previous address is no longer default
        self.assertFalse(self.address.is_default)

    def test_AddressPaymentProcessor_get_or_new_address_returns_none_when_address_data_is_none(self):
        processor = AddressPaymentProcessor(
            customer=self.customer,
            address_data=None
        )

        address = processor.get_or_new_address()

        self.assertIsNone(address)

    def test_AddressPaymentProcessor_get_or_new_address_creates_new_address(self):
        Address.objects.all().delete()  # Clear existing addresses

        data = {
            'country': 'BR',
            'state': 'SP',
            'city': 'São Paulo',
            'zipcode': '01000000',
            'street': 'Avenida Paulista',
            'neighborhood': 'Centro',
            'complement': 'Apto 123',
            'number': '456',
            'is_default': True
        }

        processor = AddressPaymentProcessor(
            customer=self.customer,
            address_data=data
        )

        address: Address = processor.get_or_new_address()  # type:ignore

        self.assertIsNotNone(address)
        self.assertEqual(Address.objects.count(), 1)
        self.assertEqual(address.customer, self.customer)
        self.assertEqual(address.country, data['country'].upper())
        self.assertEqual(address.state, data['state'].upper())
        self.assertEqual(address.city, data['city'])
        self.assertEqual(address.zipcode, data['zipcode'])
        self.assertEqual(address.street, data['street'])
        self.assertEqual(address.neighborhood, data['neighborhood'])
        self.assertEqual(address.complement, data['complement'])
        self.assertEqual(address.number, data['number'])
        self.assertTrue(address.is_default)

    def test_AddressPaymentProcessor_get_or_new_address_updates_existing_address(self):
        Address.objects.all().delete()  # Clear existing addresses

        data = {
            'country': 'BR',
            'state': 'SP',
            'city': 'São Paulo',
            'zipcode': '01000000',
            'street': 'Avenida Paulista',
            'neighborhood': 'Centro',
            'complement': 'Apto 123',
            'number': '456',
            'is_default': True
        }

        # Create a Address
        existing_address = Address.objects.create(
            customer=self.customer,
            country=data['country'].upper(),
            state=data['state'].upper(),
            city=data['city'],
            street=data['street'],
            number=data['number'],
            neighborhood=None,  # No neighborhood initially
            zipcode='88888888',  # Different zipcode
            complement='Some different complement',  # Different complement
            is_default=False  # Initially not default
        )

        processor = AddressPaymentProcessor(
            customer=self.customer,
            address_data=data
        )

        address: Address = processor.get_or_new_address()  # type:ignore

        self.assertIsNotNone(address)
        self.assertEqual(Address.objects.count(), 1)
        self.assertEqual(address, existing_address)
        self.assertEqual(address.customer, self.customer)
        self.assertEqual(address.country, data['country'].upper())
        self.assertEqual(address.state, data['state'].upper())
        self.assertEqual(address.city, data['city'])
        self.assertEqual(address.street, data['street'])
        self.assertEqual(address.number, data['number'])

        self.assertEqual(address.neighborhood, data['neighborhood'])
        self.assertEqual(address.zipcode, data['zipcode'])  # Updated zipcode
        self.assertEqual(address.complement, data['complement'])  # Updated complement
        self.assertTrue(address.is_default)  # Updated to default

    def test_address_create_view_with_already_created_customer(self):
        Address.objects.all().delete()  # Clear existing addresses

        data = {
            'country': 'BR',
            'state': 'SP',
            'city': 'São Paulo',
            'zipcode': '01000-000',
            'street': 'Avenida Paulista',
            'neighborhood': 'Centro',
            'complement': 'Apto 123',
            'number': '456',
            'is_default': True
        }

        url = reverse('customer:addresses')

        # Call
        response = self.client.post(url, data, format='json', headers=self.headers)

        self.assertEqual(response.status_code, 201, response.content.decode())

        self.assertEqual(Address.objects.count(), 1)
        address: Address = Address.objects.first()  # type:ignore

        self.assertEqual(address.customer, self.customer)
        self.assertEqual(address.country, data['country'].upper())
        self.assertEqual(address.state, data['state'].upper())
        self.assertEqual(address.city, data['city'])
        self.assertEqual(address.zipcode, '01000000')  # Normalized zipcode
        self.assertEqual(address.street, data['street'])
        self.assertEqual(address.neighborhood, data['neighborhood'])
        self.assertEqual(address.complement, data['complement'])
        self.assertEqual(address.number, data['number'])
        self.assertTrue(address.is_default)

    def test_address_create_view_with_no_created_customer(self):
        Customer.objects.all().delete()  # Clear existing customers
        Address.objects.all().delete()  # Clear existing addresses

        data = {
            'country': 'BR',
            'state': 'SP',
            'city': 'São Paulo',
            'zipcode': '01000-000',
            'street': 'Avenida Paulista',
            'complement': 'Apto 123',
            'number': '456',
            'is_default': True
        }

        url = reverse('customer:addresses')

        # Call
        response = self.client.post(url, data, format='json', headers=self.headers)

        self.assertEqual(response.status_code, 201, response.content.decode())

        self.assertEqual(Address.objects.count(), 1)
        address: Address = Address.objects.first()  # type:ignore

        customer = Customer.objects.filter(email=self.user.email).first()

        self.assertIsNotNone(customer, 'Customer should be created')
        self.assertEqual(address.customer, customer)

    def test_address_update_view(self):
        Address.objects.exclude(pk=self.address.pk).delete()  # Clear other addresses

        # Set up data in the address
        self.address.country = 'BR'
        self.address.state = 'SP'
        self.address.city = 'São Paulo'
        self.address.zipcode = '01000000'
        self.address.street = 'Avenida Paulista'
        self.address.neighborhood = 'Centro'
        self.address.complement = 'Apto 123'
        self.address.number = '456'
        self.address.is_default = True
        self.address.save()

        # Prepare data for update
        data = {
            'country': 'AR',
            'state': 'MG',
            'city': 'Belo Horizonte',
            'zipcode': '02000-000',  # This will be normalized
            'street': 'Rua da Liberdade',
            'neighborhood': 'Boa Vista',
            'complement': 'Casa 789',
            'number': '101',
            'is_default': False  # Change default status
        }

        url = reverse('customer:address-detail', kwargs={'pk': self.address.pk})

        # Call
        response = self.client.put(url, data, format='json', headers=self.headers)

        self.assertEqual(response.status_code, 200, response.content.decode())

        self.address.refresh_from_db()
        self.assertEqual(Address.objects.count(), 1)
        self.assertEqual(self.address.country, data['country'].upper())
        self.assertEqual(self.address.state, data['state'].upper())
        self.assertEqual(self.address.city, data['city'])
        self.assertEqual(self.address.zipcode, '02000000')  # Normalized zipcode
        self.assertEqual(self.address.street, data['street'])
        self.assertEqual(self.address.neighborhood, data['neighborhood'])
        self.assertEqual(self.address.complement, data['complement'])
        self.assertEqual(self.address.number, data['number'])
        self.assertFalse(self.address.is_default)  # Updated to not default

    def test_address_delete_view(self):
        Address.objects.exclude(pk=self.address.pk).delete()  # Clear other addresses

        url = reverse('customer:address-detail', kwargs={'pk': self.address.pk})

        # Call
        response = self.client.delete(url, format='json', headers=self.headers)

        self.assertEqual(response.status_code, 204, response.content.decode())
        self.assertEqual(Address.objects.count(), 0, 'Address should be deleted')

    def test_address_list_view(self):
        Address.objects.exclude(pk=self.address.pk).delete()

        url = reverse('customer:addresses')

        # Call
        response = self.client.get(url, format='json', headers=self.headers)
        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertEqual(len(response.data['results']), 1)

        result = response.data['results'][0]
        self.assertEqual(result['customer'], self.customer.pk)
        self.assertEqual(result['country'], self.address.country)
        self.assertEqual(result['state'], self.address.state)
        self.assertEqual(result['city'], self.address.city)
        self.assertEqual(result['zipcode'], self.address.zipcode)
        self.assertEqual(result['street'], self.address.street)
        self.assertEqual(result['neighborhood'], self.address.neighborhood)
        self.assertEqual(result['complement'], self.address.complement)
        self.assertEqual(result['number'], self.address.number)
        self.assertEqual(result['is_default'], self.address.is_default)
        self.assertIn('createdAt', result)
        self.assertIn('updatedAt', result)

    def test_address_detail_view(self):
        url = reverse('customer:address-detail', kwargs={'pk': self.address.pk})

        # Call
        response = self.client.get(url, format='json', headers=self.headers)
        self.assertEqual(response.status_code, 200, response.content.decode())

        result = response.data
        self.assertEqual(result['customer'], self.customer.pk)
        self.assertEqual(result['country'], self.address.country)
        self.assertEqual(result['state'], self.address.state)
        self.assertEqual(result['city'], self.address.city)
        self.assertEqual(result['zipcode'], self.address.zipcode)
        self.assertEqual(result['street'], self.address.street)
        self.assertEqual(result['neighborhood'], self.address.neighborhood)
        self.assertEqual(result['complement'], self.address.complement)
        self.assertEqual(result['number'], self.address.number)
        self.assertEqual(result['is_default'], self.address.is_default)
        self.assertIn('createdAt', result)
        self.assertIn('updatedAt', result)

    def test_user_cant_access_address_of_another_user(self):
        # Create another user and customer
        another_user = self.create_user()
        self.create_customer(email=another_user.email)

        url = reverse('customer:address-detail', kwargs={'pk': self.address.pk})
        user_auth_headers = self.build_user_auth_headers(another_user)

        # Call with another user's headers
        # GET
        response = self.client.get(url, format='json', headers=user_auth_headers)
        self.assertEqual(response.status_code, 404, response.content.decode())
        # PUT
        response = self.client.put(url, data={}, format='json', headers=user_auth_headers)
        self.assertEqual(response.status_code, 404, response.content.decode())
        # DELETE
        response = self.client.delete(url, format='json', headers=user_auth_headers)
        self.assertEqual(response.status_code, 404, response.content.decode())
        # LIST
        url = reverse('customer:addresses')
        response = self.client.get(url, format='json', headers=user_auth_headers)
        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertEqual(len(response.data['results']), 0)

    def test_AddressPaymentProcessor_num_queries_when_creating(self):
        Address.objects.all().delete()  # Clear existing addresses

        address_data = {
            'country': 'BR',
            'state': 'SP',
            'city': 'São Paulo',
            'zipcode': '01000-000',
            'street': 'Avenida Paulista',
            'complement': 'Apto 123',
            'number': '456',
            'is_default': True,
        }

        processor = AddressPaymentProcessor(
            customer=self.customer,
            address_data=address_data,
        )

        # 2 queries for opening transactions
        # 1 for select the address - wich will not be found
        # 1 for create the address
        # 1 for update another addresses to not default - because `is_default` is True
        # 2 for releasing the transactions
        with self.assertNumQueries(7):
            processor.get_or_new_address()

    def test_AddressPaymentProcessor_num_queries_when_updating(self):
        address_data = {
            'country': 'BR',
            'state': 'SP',
            'city': 'São Paulo',
            'zipcode': '01000-000',
            'street': 'Avenida Paulista',
            'complement': 'Apto 123',
            'number': '456',
            'is_default': False,
        }

        processor = AddressPaymentProcessor(
            customer=self.customer,
            address_data=address_data,
        )

        # 1 queries for opening transaction
        # 1 for select the address
        # 1 for update the address
        # 1 for releasing the transaction
        with self.assertNumQueries(4):
            processor.get_or_new_address()

    def test_deleted_addess_not_showed_on_objects(self):
        self.address.deleted = True
        self.address.save()

        self.assertEqual(
            Address.objects.count(),
            0,
            'Deleted address should not be returned by Address.objects'
        )

    def test_deleting_address_with_order_actually_marks_it_as_deleted(self):
        self.address.deleted = False
        self.address.save()

        self.create_paymentMethods()
        # Create an order to ensure the address is linked to an order
        # This will raise a ProtectedError when attempting to delete the address
        self.create_order(customer=self.customer, address=self.address)

        url = reverse('customer:address-detail', kwargs={'pk': self.address.pk})

        # Call
        response = self.client.delete(url, format='json', headers=self.headers)

        self.assertEqual(response.status_code, 204, response.content.decode())
        self.assertEqual(Address.deleted_objects.count(), 1, 'Address should exist but marked as deleted')

        self.address.refresh_from_db()
        self.assertTrue(self.address.deleted, 'Address should be marked as deleted')

    def test_update_address_with_order_marks_it_as_deleted_an_returns_a_new_one(self):
        self.address.deleted = False
        self.address.country = 'US'
        self.address.save()

        self.create_paymentMethods()
        # Create an order to ensure the address is linked to an order
        # This will raise a ProtectedError when attempting to update the address
        self.create_order(customer=self.customer, address=self.address)

        url = reverse('customer:address-detail', kwargs={'pk': self.address.pk})

        data = {'country': 'BR'}

        # Call
        response = self.client.put(url, data, format='json', headers=self.headers)

        self.assertEqual(response.status_code, 201, response.content.decode())
        self.assertEqual(Address.deleted_objects.count(), 1, 'Old address should be marked as deleted')
        self.assertEqual(Address.objects.count(), 1, 'Address should be created')

        new_address: Address = Address.objects.first()  # type:ignore

        self.assertNotEqual(new_address.pk, self.address.pk)
        self.assertEqual(new_address.country, 'BR')
        self.assertEqual(new_address.customer, self.address.customer)
        self.assertEqual(new_address.state, self.address.state)
        self.assertEqual(new_address.city, self.address.city)
        self.assertEqual(new_address.zipcode, self.address.zipcode)
        self.assertEqual(new_address.street, self.address.street)
        self.assertEqual(new_address.neighborhood, self.address.neighborhood)
        self.assertEqual(new_address.complement, self.address.complement)
        self.assertEqual(new_address.number, self.address.number)
        self.assertEqual(new_address.is_default, self.address.is_default)
        self.assertFalse(new_address.deleted, 'New address should not be marked as deleted')

    def test_update_address_without_order_updates_it_without_duplicating(self):
        Address.objects.all().delete()  # Clear existing addresses
        Address.deleted_objects.all().delete()  # Clear deleted addresses

        address = Address.objects.create(
            customer=self.customer,
            country='US',
            state='CA',
            city='Los Angeles',
            zipcode='90001',
            street='Sunset Boulevard',
            neighborhood='Hollywood',
            complement='Suite 100',
            number='123',
            is_default=True
        )

        address_pk = address.pk  # Store the original primary key

        url = reverse('customer:address-detail', kwargs={'pk': address.pk})

        data = {'country': 'BR'}

        # Call
        response = self.client.put(url, data, format='json', headers=self.headers)

        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertEqual(Address.deleted_objects.count(), 0, 'No address should be marked as deleted')
        self.assertEqual(Address.objects.count(), 1, 'Address should not be duplicated')

        updated_address: Address = Address.objects.first()  # type:ignore
        updated_address.refresh_from_db()
        self.assertEqual(updated_address.country, 'BR')
        self.assertEqual(updated_address.pk, address_pk)
