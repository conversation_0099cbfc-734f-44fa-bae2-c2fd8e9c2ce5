# Generated by Django 4.2.5 on 2025-06-30 18:26

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('customer', '0011_customerhistory_birthdate'),
    ]

    operations = [
        migrations.CreateModel(
            name='Address',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('country', models.Char<PERSON>ield(help_text='Código do país no formato ISO 3166-1 Alpha-2 (ex: BR para Brasil)', max_length=2, verbose_name='País')),
                ('state', models.CharField(help_text='Código do estado no formato ISO 3166-2 Alpha-2 (ex: SP para São Paulo)', max_length=2, verbose_name='Estado')),
                ('city', models.CharField(help_text='Nome da cidade do cliente', max_length=255, verbose_name='Cidade')),
                ('zipcode', models.Char<PERSON><PERSON>(help_text='CEP do cliente', max_length=30, verbose_name='CEP')),
                ('street', models.Char<PERSON>ield(help_text='Nome da rua do cliente', max_length=255, verbose_name='Rua')),
                ('neighborhood', models.CharField(blank=True, help_text='Nome do bairro do cliente', max_length=255, null=True, verbose_name='Bairro')),
                ('complement', models.CharField(blank=True, help_text='Complemento do endereço do cliente', max_length=255, null=True, verbose_name='Complemento')),
                ('number', models.CharField(help_text='Número do endereço do cliente', max_length=255, verbose_name='Número')),
                ('is_default', models.BooleanField(db_index=True, default=False, help_text='Define se este é o endereço principal do cliente', verbose_name='Endereço Principal')),
                ('createdAt', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='Criado em')),
                ('updatedAt', models.DateTimeField(auto_now=True, verbose_name='Atualizado em')),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='addresses', to='customer.customer')),
            ],
            options={
                'verbose_name': 'Endereço',
                'verbose_name_plural': 'Endereços',
                'ordering': ['is_default', '-createdAt'],
            },
        ),
    ]
