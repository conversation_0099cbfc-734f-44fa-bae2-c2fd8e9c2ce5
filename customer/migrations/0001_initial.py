# Generated by Django 4.2.5 on 2023-09-28 22:12

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Customer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.<PERSON>r<PERSON>ield(max_length=255)),
                ('email', models.EmailField(max_length=254)),
                ('phone', models.Char<PERSON>ield(max_length=255)),
                ('document', models.Char<PERSON>ield(max_length=255)),
                ('ip', models.<PERSON><PERSON><PERSON><PERSON>(max_length=255)),
            ],
        ),
    ]
