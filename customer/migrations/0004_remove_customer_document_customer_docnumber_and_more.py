# Generated by Django 4.2.5 on 2023-10-24 15:57

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('customer', '0003_remove_customer_products'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='customer',
            name='document',
        ),
        migrations.AddField(
            model_name='customer',
            name='docNumber',
            field=models.CharField(default=1, help_text='CPF ou CNPJ', max_length=255),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='customer',
            name='docType',
            field=models.CharField(choices=[('cpf', 'CPF'), ('cnpj', 'CNPJ')], default='cpf', help_text='Tipo de documento (cpf ou cnpj)', max_length=255),
        ),
        migrations.AddField(
            model_name='customer',
            name='fingerprint',
            field=models.CharField(default=1, help_text='Fingerprint do browser do usuário', max_length=255),
            preserve_default=False,
        ),
        migrations.<PERSON><PERSON><PERSON>ield(
            model_name='customer',
            name='ip',
            field=models.<PERSON><PERSON><PERSON>ield(help_text='IP do usuário', max_length=255),
        ),
    ]
