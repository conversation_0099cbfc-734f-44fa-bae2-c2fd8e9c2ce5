# Generated by Django 4.2.5 on 2024-03-14 13:15

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('customer', '0007_card_fingerprint'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='customer',
            options={'verbose_name': 'Cliente', 'verbose_name_plural': 'Clientes'},
        ),
        migrations.AddField(
            model_name='customer',
            name='createdAt',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='customer',
            name='updatedAt',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.CreateModel(
            name='CustomerHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON><PERSON>(max_length=255)),
                ('email', models.Email<PERSON>ield(max_length=254)),
                ('phone', models.CharField(max_length=255)),
                ('docNumber', models.CharField(help_text='CPF ou CNPJ', max_length=255)),
                ('docType', models.CharField(choices=[('cpf', 'CPF'), ('cnpj', 'CNPJ')], default='cpf', help_text='Tipo de documento (cpf ou cnpj)', max_length=255)),
                ('ip', models.CharField(blank=True, help_text='IP do usuário', max_length=255, null=True)),
                ('fingerprint', models.CharField(help_text='Fingerprint do browser do usuário', max_length=255)),
                ('createdAt', models.DateTimeField(auto_now_add=True)),
                ('updatedAt', models.DateTimeField(auto_now=True)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='customer.customer')),
            ],
            options={
                'verbose_name': 'Histórico de Cliente',
                'verbose_name_plural': 'Histórico de Clientes',
            },
        ),
    ]
