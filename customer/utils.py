
from customer.models import Address, Customer, CustomerHistory


class CustomerPaymentProcessor:
    def __init__(self, customer_data: dict | None = None, customer_ip: str | None = None):
        self.customer_data: dict = customer_data or {}
        self.customer_ip: str = customer_ip

    def get_or_new_customer(self) -> Customer:
        self.__clean_data()

        customer, created = self.__get_or_create_customer()

        if created:
            self.__create_customer_history(customer)
        else:
            self.__update_customer_instance(customer)

        return customer

    def __update_customer_instance(self, customer):
        create_history = False
        for key, value in self.customer_data.items():
            if getattr(customer, key) != value:
                create_history = True
            setattr(customer, key, value)
        if create_history:
            self.__create_customer_history(customer)
        customer.save()

    def __create_customer_history(self, customer):
        CustomerHistory.objects.create(customer=customer, **self.customer_data)

    def __get_or_create_customer(self):
        try:
            customer, created = Customer.objects.get_or_create(
                email__iexact=self.customer_data.get('email'),
                defaults=self.customer_data  # type:ignore
            )
        except Customer.MultipleObjectsReturned:
            customer: Customer = Customer.objects.filter(
                email__iexact=self.customer_data.get('email')
            ).order_by('-createdAt').first()  # type:ignore
            created = False
        return customer, created

    def __clean_data(self):
        # Remove 'id' from customer_data if it exists (in case of webhook comming from split)
        self.customer_data.pop('id', None)

        if self.customer_ip:
            self.customer_data['ip'] = self.customer_ip

        self.customer_data['email'] = self.customer_data.get('email', '').lower()

class AddressPaymentProcessor:
    def __init__(self, customer: Customer, address_data: dict | None = None):
        self.address_data: dict = address_data or {}
        self.customer: Customer = customer

    def get_or_new_address(self) -> Address | None:
        if not self.address_data:
            return None

        defaults = self.__get_defaults()

        address, _ = self.__update_or_create(defaults)

        return address

    def __update_or_create(self, defaults):
        return Address.objects.update_or_create(
            customer=self.customer,
            country=defaults['country'],
            state=defaults['state'],
            city=defaults['city'],
            street=defaults['street'],
            number=defaults['number'],
            deleted=False,
            defaults=defaults,
        )

    def __get_defaults(self):
        zipcode = Address.normalize_zipcode(self.address_data.get('zipcode', ''))
        return {
            'country': self.address_data.get('country', '').upper(),
            'state': self.address_data.get('state', '').upper(),
            'city': self.address_data.get('city', ''),
            'zipcode': zipcode,
            'street': self.address_data.get('street', ''),
            'neighborhood': self.address_data.get('neighborhood', ''),
            'complement': self.address_data.get('complement', ''),
            'number': self.address_data.get('number', ''),
            'is_default': bool(self.address_data.get('is_default', False)),
        }
