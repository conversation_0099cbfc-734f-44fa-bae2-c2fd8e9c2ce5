import asyncio
import logging
import time

import aiohttp

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# URL = 'https://api.cakto.com.br'
# OFFER_ID = '5Xwz7L3'
URL = 'http://localhost:8001'
OFFER_ID = '5mrU526'
# Define the URL
url = f'{URL}/api/product/checkout/{OFFER_ID}/'

# Define a counter to keep track of successful responses
success_counter = 0

# Define a counter to keep track of total requests
total_requests = 0

# Define a semaphore to limit concurrency
semaphore = asyncio.Semaphore(200)

# Define the asynchronous function to send requests
async def send_request(start_time):
    global success_counter
    global total_requests
    async with semaphore:
        async with aiohttp.ClientSession() as session:
            while True:
                try:
                    request_start_time = time.time()
                    async with session.get(url) as response:
                        request_end_time = time.time()
                        if response.status == 200:
                            success_counter += 1
                            total_requests += 1
                            time_taken = request_end_time - request_start_time
                            time_since_start = request_end_time - start_time
                            req_per_second = total_requests / time_since_start
                            logging.info(
                                f"Success #{success_counter}: Received response with status {response.status}, Time taken: {time_taken:.2f} seconds, Total requests: {total_requests}, Time since start: {time_since_start:.2f} seconds, Requests per second: {req_per_second:.2f}")
                except aiohttp.ClientError as e:
                    logging.error(f"Error: {e}")

# Run the coroutines concurrently
async def main():
    global total_requests

    # Define the number of requests
    requests_per_second = 2000

    while True:
        start_time = time.time()
        coroutines = [send_request(start_time) for _ in range(requests_per_second)]
        await asyncio.gather(*coroutines)

# Run the event loop
if __name__ == "__main__":
    logging.info("Starting the script...")
    asyncio.run(main())
    logging.info("Script execution completed.")
