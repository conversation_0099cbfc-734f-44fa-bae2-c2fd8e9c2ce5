from django.db import models

class Image(models.Model):
    product = models.ForeignKey('product.Product', on_delete=models.CASCADE, related_name='images')
    file = models.ImageField(upload_to='images/')
    createdAt = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.file.url if self.file else 'Sem arquivo'

class CheckoutAbandonment(models.Model):
    class Meta:
        verbose_name = 'Abandono de checkout'
        verbose_name_plural = 'Abandonos de chekout'
        ordering = ['-createdAt']

    offer = models.ForeignKey('product.Offer', on_delete=models.CASCADE)
    customerName = models.CharField(max_length=255, null=True, blank=True)
    customerEmail = models.CharField(max_length=255, blank=True, null=True)
    customerCpf = models.CharField(max_length=11, blank=True, null=True)
    customerCnpj = models.CharField(max_length=14, blank=True, null=True)
    customerCellphone = models.CharField(max_length=255, blank=True, null=True)
    checkoutUrl = models.TextField(blank=True, null=True)
    createdAt = models.DateTimeField(auto_now_add=True, verbose_name='Criado em')
