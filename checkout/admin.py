from django.contrib import admin
from .models import Image, CheckoutAbandonment

admin.site.register(Image)

@admin.register(CheckoutAbandonment)
class CheckoutAbandonmentAdmin(admin.ModelAdmin):
    list_display = ('offer', 'customerName', 'customerEmail', 'customerCellphone', 'createdAt')
    search_fields = (
        'offer__id', 'offer__externalId', 'offer__name',
        'customerName', 'customerEmail', 'customerCellphone', 'customerCpf', 'customerCnpj',
        'checkoutUrl',
    )
    readonly_fields = ('createdAt', )
    list_filter = ('createdAt', )
    ordering = ('-createdAt', )
