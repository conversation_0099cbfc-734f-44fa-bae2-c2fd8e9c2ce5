import re

from rest_framework import serializers

from email_service.mail import get_product_supportEmail
from product.models import Checkout, Offer, Product
from product.serializers import OfferPublicSerializer, OfferSerializer, ProductSerializer

from .models import CheckoutAbandonment, Image


class ImageSerializer(serializers.ModelSerializer):
    class Meta:
        model = Image
        fields = ('id', 'file', 'createdAt',)
        read_only_fields = ('createdAt', )

class CheckoutSerializer(serializers.ModelSerializer):
    product = ProductSerializer(read_only=True)
    offers = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = Checkout
        fields = ('id', 'product', 'name', 'config', 'offers', 'default', 'updatedAt', 'createdAt', 'visits',)
        read_only_fields = ('createdAt', 'visits')

    def get_offers(self, checkout):
        return OfferSerializer(checkout.offers.filter(status__in=['active', 'disabled']), many=True).data

    def to_internal_value(self, data):
        res = super().to_internal_value(data)
        if data.get('offers'):
            res['offers'] = data['offers']
        return res

    def get_valid_offers(self, offer_ids: list[str], product: Product):
        if offer_ids:
            return Offer.objects.filter(
                id__in=offer_ids,
                product=product,
                status__in=['active', 'disabled'],
            )
        return []

    def create(self, validated_data):
        offers = validated_data.pop('offers', [])

        checkout = super().create(validated_data)

        if offers:
            offers = self.get_valid_offers(offers, validated_data['product'])
            checkout.offers.set(offers)

        return checkout

    def update(self, instance, validated_data):
        res = super().update(instance, validated_data)

        offers = self.get_valid_offers(validated_data.get('offers'), instance.product)
        if offers:
            instance.offers.set(offers)

        return res

class CheckoutAbandonmentCreateSerializer(serializers.ModelSerializer):
    customerEmail = serializers.EmailField(required=True)

    class Meta:
        model = CheckoutAbandonment
        fields = (
            'offer',
            'customerName',
            'customerEmail',
            'customerCpf',
            'customerCnpj',
            'customerCellphone',
            'checkoutUrl',
            'createdAt',
        )
        read_only_fields = ('createdAt', )

    def validate_customerName(self, value):
        if len(value) < 4:
            raise serializers.ValidationError('Nome do cliente é obrigatório')
        return value

    def validate_customerCellphone(self, value):
        regex = r'^(?:(?:\+|00)?(55)\s?)?(?:\(?([1-9][0-9])\)?\s?)?(?:((?:9\d|[2-9])\d{3})\-?(\d{4}))$'
        if not re.match(regex, value):
            raise serializers.ValidationError('Celular inválido.')
        return value

class CheckoutAbandonmentPublicSerializer(serializers.ModelSerializer):
    offer = OfferPublicSerializer(read_only=True)
    product = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = CheckoutAbandonment
        fields = (
            'offer',
            'product',
            'customerName',
            'customerEmail',
            'customerCellphone',
            'checkoutUrl',
            'createdAt',
        )
        read_only_fields = ('createdAt', )

    def get_product(self, checkout_abandonment):
        product = checkout_abandonment.offer.product
        return {
            'name': product.name,
            'id': product.id,
            'short_id': product.short_id,
            'supportEmail': get_product_supportEmail(product),
            'type': product.type,
            'invoiceDescription': product.invoiceDescription,
        }

class CheckoutAbandonmentExportSerializer(serializers.ModelSerializer):
    offerName = serializers.CharField(source='offer.name', label='Oferta')
    offerPrice = serializers.CharField(source='offer.price', label='Preço da Oferta')
    offerId = serializers.CharField(source='offer.id', label='ID da Oferta')
    customerName = serializers.CharField(label='Nome do Cliente')
    customerEmail = serializers.CharField(label='Email do Cliente')
    customerCellphone = serializers.CharField(label='Celular do Cliente')
    checkoutUrl = serializers.CharField(label='URL do Checkout')
    createdAt = serializers.DateTimeField(label='Data')

    class Meta:
        model = CheckoutAbandonment
        fields = (
            'offerName',
            'offerPrice',
            'offerId',
            'customerName',
            'customerEmail',
            'customerCellphone',
            'checkoutUrl',
            'createdAt',
        )
        read_only_fields = ('createdAt', )
