import pytest
from django.core.cache import cache
from django.urls import reverse
from rest_framework.test import APIClient
from starlette.status import HTTP_201_CREATED

from product.models import Checkout, <PERSON>, Offer, Product


@pytest.mark.django_db
def test_creates_link_for_checkout(
    logged_in_user: APIClient,
    product: Product,
    offer: Offer,
    checkout: Checkout,
):
    url = reverse('checkout-api', kwargs={'product': product.id})
    data = {
        'name': 'New Checkout',
        'offers': [offer.id]
    }
    response = logged_in_user.post(url, data, format='json')
    assert response.status_code == HTTP_201_CREATED
    assert Link.objects.filter(product=product, checkout__name='New Checkout').exists()


@pytest.mark.django_db
def test_does_not_creates_link_for_checkout_without_offers(
    logged_in_user: APIClient,
    product: Product,
    checkout: Checkout,
):
    url = reverse('checkout-api', kwargs={'product': product.id})
    data = {
        'name': 'New Checkout'
    }
    response = logged_in_user.post(url, data, format='json')
    assert response.status_code == HTTP_201_CREATED
    assert Link.objects.filter(product=product, checkout__name='New Checkout').exists() is False


@pytest.mark.django_db
def test_does_not_create_link_for_invalid_offer(
    logged_in_user: APIClient,
    product: Product,
    checkout: Checkout,
):
    url = reverse('checkout-api', kwargs={'product': product.id})
    data = {
        'name': 'New Checkout',
        'offers': [999]  # Invalid offer ID
    }
    response = logged_in_user.post(url, data, format='json')
    assert response.status_code == HTTP_201_CREATED
    assert Link.objects.filter(product=product, checkout__name='New Checkout').exists() is False

@pytest.mark.django_db
def test_allow_same_offer_to_two_checkouts(
    logged_in_user: APIClient,
    product: Product,
    offer: Offer,
    checkout: Checkout,
):
    assert Checkout.objects.filter(offers=offer).exists()
    assert Checkout.objects.filter(offers=offer).count() == 1

    url = reverse('checkout-api', kwargs={'product': product.id})
    data = {
        'name': 'New Checkout',
        'offers': [offer.id]
    }
    response = logged_in_user.post(url, data, format='json')
    assert response.status_code == HTTP_201_CREATED
    assert Link.objects.filter(product=product, checkout__name='New Checkout').exists()

    assert Checkout.objects.filter(offers=offer).count() == 2

@pytest.mark.django_db
def test_updating_checkout_invalidate_cache(
    logged_in_user: APIClient,
    checkout: Checkout,
):
    offer = checkout.offers.first()
    cache.set(Offer.cache_prefix + f'{offer.id}_{checkout.id}', 'Cached', timeout=60)  # type:ignore

    url = reverse('checkout-retrieve-api', kwargs={'id': checkout.id})
    data = {'name': 'Updated Checkout'}

    response = logged_in_user.patch(url, data, format='json')

    assert response.status_code == 200, response.content.decode()
    assert cache.get(Offer.cache_prefix + f'{offer.id}_{checkout.id}') is None, 'Cache should be cleared'  # type:ignore

@pytest.mark.django_db
def test_checkout_link_with_offer(
    logged_in_user: APIClient,
    offer: Offer
):
    url = reverse('checkout-api', kwargs={'product': offer.product.id})
    data = {
        'name': 'New Checkout',
        'offers': [offer.id]
    }

    response = logged_in_user.post(url, data, format='json')

    assert response.status_code == HTTP_201_CREATED, response.content.decode()
    assert Checkout.objects.count() == 1

    checkout = Checkout.objects.first()
    link = checkout.link_set.first()  # type:ignore

    # The ending of the checkout url is used to cache the checkout data
    # so when changing the format of the url, make sure to check if the cache is invalidated
    assert link.url.endswith(f'{offer.id}_{checkout.id}'), 'Link should end in the format {offer_id}_{checkout_id}'  # type:ignore

@pytest.mark.django_db
def test_update_checkout_adds_links_for_new_offers(
    logged_in_user: APIClient,
    product,
    offer,
    checkout: Checkout,
):
    # cria uma segunda oferta válida para o mesmo produto
    new_offer = Offer.objects.create(
        product=product,
        name='New Offer',
        price=99.9,
        status='active',
        type='single',
    )

    assert checkout.offers.count() == 1
    assert Link.objects.filter(checkout=checkout, offer=new_offer).exists() is False

    # PATCH acrescentando a nova oferta
    url = reverse('checkout-retrieve-api', kwargs={'id': checkout.id})
    data = {'offers': [offer.id, new_offer.id], 'name': checkout.name}
    resp = logged_in_user.patch(url, data, format='json')
    assert resp.status_code == 200, resp.content.decode()

    checkout.refresh_from_db()
    assert checkout.offers.count() == 2
    assert Link.objects.filter(checkout=checkout, offer=new_offer).exists()

@pytest.mark.django_db
def test_update_checkout_removes_links_for_deleted_offers(
    logged_in_user: APIClient,
    product,
    offer,
    checkout: Checkout,
):
    checkout.offers.set([offer])
    checkout.save()

    # Cria link para a oferta original
    Link.objects.update_or_create(
        product=product,
        checkout=checkout,
        offer=offer,
        defaults={
            "name": f"Link para checkout {checkout.name}",
            "type": "checkout",
            "url": "fake-url",
        },
    )
    assert Link.objects.filter(checkout=checkout, offer=offer).exists()

    # Cria uma segunda oferta
    replacement_offer = Offer.objects.create(
        product=product,
        name="Replacement",
        price=50,
        status="active",
        type="single",
    )

    # PATCH substituindo a lista de ofertas (remove a original)
    url = reverse("checkout-retrieve-api", kwargs={"id": checkout.id})
    data = {
        "name": checkout.name,
        "offers": [replacement_offer.id],
    }
    resp = logged_in_user.patch(url, data, format="json")
    assert resp.status_code == 200, resp.content.decode()

    checkout.refresh_from_db()
    # Oferta antiga saiu, nova entrou
    assert list(checkout.offers.all()) == [replacement_offer]

    # Link antigo deve sumir
    assert Link.objects.filter(checkout=checkout, offer=offer).exists() is False

@pytest.mark.django_db
def test_update_checkout_renames_links_when_checkout_name_changes(
    logged_in_user: APIClient,
    checkout: Checkout,
):
    # pelo menos 1 link existe previamente
    offer = checkout.offers.first()
    Link.objects.update_or_create(
        product=checkout.product,
        checkout=checkout,
        offer=offer,
        defaults={
            "name": f"Link para checkout {checkout.name}",
            "type": "checkout",
            "url": "fake-url",
        }
    )

    old_name = checkout.name
    new_name = 'Checkout Renomeado'
    old_link_qs = Link.objects.filter(checkout=checkout)
    assert old_link_qs.exists()
    assert old_link_qs.first().name.startswith(f'Link para checkout {old_name}')

    # PATCH alterando o nome
    url = reverse('checkout-retrieve-api', kwargs={'id': checkout.id})
    data = {
        'name': new_name,
        'offers': [offer.id]
    }
    resp = logged_in_user.patch(url, data, format='json')
    assert resp.status_code == 200, resp.content.decode()

    checkout.refresh_from_db()
    for link in Link.objects.filter(checkout=checkout):
        assert link.name == f'Link para checkout {new_name}'
