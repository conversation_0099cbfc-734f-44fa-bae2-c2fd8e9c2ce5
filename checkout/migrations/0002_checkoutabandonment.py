# Generated by Django 4.2.5 on 2024-01-05 17:52

from django.db import migrations, models
import django.db.models.deletion
import user.fields


class Migration(migrations.Migration):

    dependencies = [
        ('product', '0047_offer_status'),
        ('checkout', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='CheckoutAbandonment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('customerName', models.CharField(blank=True, max_length=255, null=True)),
                ('customerEmail', user.fields.LowercaseEmailField(blank=True, max_length=254, null=True)),
                ('customerCpf', models.CharField(blank=True, max_length=11, null=True)),
                ('customerCnpj', models.CharField(blank=True, max_length=14, null=True)),
                ('customerCellphone', models.CharField(blank=True, max_length=255, null=True)),
                ('createdAt', models.DateTimeField(auto_now_add=True, verbose_name='Criado em')),
                ('offer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='product.offer')),
            ],
        ),
    ]
