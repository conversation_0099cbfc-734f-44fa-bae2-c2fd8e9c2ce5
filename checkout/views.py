import json
import uuid
from typing import Iterable

from django.core.cache import cache
from django.db import transaction
from django.http import JsonResponse
from django.shortcuts import get_object_or_404
from django.views.decorators.csrf import csrf_exempt
from rest_framework import filters, generics, status, viewsets
from rest_framework.decorators import action
from rest_framework.response import Response

from apps.services.event_manager import dispatch_app_events
from main import unpack_offer_id_and_checkout_id
from product.models import Checkout, Link, Offer, Product
from product.utils import send_offer_data_to_compliance_analysis

from .models import Image
from .serializers import CheckoutAbandonmentCreateSerializer, CheckoutSerializer, ImageSerializer


class GalleryApi(viewsets.ModelViewSet):
    scope = 'products'
    serializer_class = ImageSerializer

    def get_queryset(self):
        return Image.objects.filter(product=self.kwargs['product'], product__user=self.request.user)

    def create(self, request, *args, **kwargs):
        # Change filename
        if hasattr(request.data, '_mutable'):
            request.data._mutable = True
        request.data['file']._name = str(uuid.uuid4()) + '.' + request.data['file'].name.split('.')[-1]
        if hasattr(request.data, '_mutable'):
            request.data._mutable = False

        serializer = ImageSerializer(data=request.data, context={'request': request})
        serializer.is_valid(raise_exception=True)
        serializer.save(product=get_object_or_404(Product.objects.filter(id=self.kwargs['product'])))
        return Response(serializer.data, status=status.HTTP_201_CREATED)

class CheckoutMixim:
    def create_link_for_checkout(
            self,
            product: Product,
            checkout: Checkout,
            offers: Iterable[Offer] | None = None
        ) -> None:
        if not offers:
            return

        for offer in offers:
            url = Link.generate_link_url(
                offer=offer,
                checkout_id=str(checkout.id)
            )

            Link.objects.update_or_create(
                product=product,
                checkout=checkout,
                offer=offer,
                defaults={
                    "name": f"Link para checkout {checkout.name}",
                    "type": "checkout",
                    "url": url,
                },
            )


    def handle_other_checkouts(self, serializer, product, checkout):
        """Remove default from other checkouts if the new checkout is default"""
        if serializer.data.get('default'):  # type:ignore
            Checkout.objects.filter(product=product).exclude(pk=checkout.pk).update(default=False)

    def get_offers_cache_keys(self, offers: Iterable[Offer]) -> list[str]:
        cache_keys = []
        for offer in offers:
            cache_keys.extend(offer.get_cache_keys())
        return cache_keys

class CheckoutAPI(viewsets.ModelViewSet, CheckoutMixim):
    scope = 'products'
    serializer_class = CheckoutSerializer
    filter_backends = (filters.SearchFilter, )
    search_fields = ('name', 'product__id', 'product__name', 'offers__id', 'offers__name', )

    def get_queryset(self):
        return Checkout.objects.filter(product=self.kwargs['product'], product__user=self.request.user)

    def create(self, request, *args, **kwargs):
        product = get_object_or_404(Product.objects.filter(id=self.kwargs['product']))

        serializer = self.get_serializer(data=request.data, context={'request': request})
        serializer.is_valid(raise_exception=True)

        checkout = serializer.save(product=product)

        offers = checkout.offers.all()

        cache_keys = self.get_offers_cache_keys(offers)
        cache.delete_many(set(cache_keys))

        self.handle_other_checkouts(serializer, product, checkout)
        self.create_link_for_checkout(product, checkout, offers)

        return Response(self.get_serializer(instance=checkout).data, status=status.HTTP_201_CREATED)

class CheckoutRetrieveAPI(generics.RetrieveUpdateDestroyAPIView, CheckoutMixim):
    scope = 'products'
    serializer_class = CheckoutSerializer

    def get_object(self):
        return get_object_or_404(Checkout.objects.filter(id=self.kwargs['id'], product__user=self.request.user))

    def update(self, request, *args, **kwargs):
        checkout = self.get_object()

        if checkout.default and request.data.get('default') is False:
            return Response({'detail': 'Não é possível desmarcar o checkout padrão, defina outro checkout com esta opção.'}, status=status.HTTP_400_BAD_REQUEST)

        # snapshot das ofertas antes de editar
        old_offer_ids = set(checkout.offers.values_list("id", flat=True))

        serializer = CheckoutSerializer(checkout, data=request.data, context={'request': request})
        serializer.is_valid(raise_exception=True)

        with transaction.atomic():
            serializer.save()
            checkout.refresh_from_db(fields=["name"])

            # snapshot pós-update
            new_offer_ids = set(checkout.offers.values_list("id", flat=True))

            added_ids    = new_offer_ids - old_offer_ids
            removed_ids  = old_offer_ids - new_offer_ids

            # cria links para novas ofertas
            if added_ids:
                added_offers = Offer.objects.filter(id__in=added_ids)
                self.create_link_for_checkout(checkout.product, checkout, added_offers)

            # remove links órfãos
            if removed_ids:
                Link.objects.filter(checkout=checkout, offer_id__in=removed_ids).delete()

            # se o nome do checkout mudou, sincroniza nos links restantes
            Link.objects.filter(checkout=checkout).update(
                name=f"Link para checkout {checkout.name}"
            )

        # invalida cache de todas as ofertas impactadas
        touched_offers = Offer.objects.filter(id__in=(old_offer_ids | new_offer_ids))
        cache.delete_many(set(self.get_offers_cache_keys(touched_offers)))

        self.handle_other_checkouts(serializer, checkout.product, checkout)

        send_offer_data_to_compliance_analysis(offers=checkout.offers.all())  # type: ignore

        return Response(self.get_serializer(instance=checkout).data, status=status.HTTP_200_OK)

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()

        if instance.default:
            return Response({'detail': 'Não é possível excluir o checkout padrão.'}, status=status.HTTP_400_BAD_REQUEST)

        cache_keys = self.get_offers_cache_keys(instance.offers.all())

        self.perform_destroy(instance)

        cache.delete_many(cache_keys)
        return Response(status=status.HTTP_200_OK)

class CheckoutActionsAPI(viewsets.ViewSet):
    scope = 'products'
    serializer_class = CheckoutSerializer

    def get_object(self):
        return get_object_or_404(Checkout.objects.filter(id=self.kwargs['id'], product__user=self.request.user))

    @action(detail=True, methods=['post'])
    def duplicate(self, request, *args, **kwargs):
        instance = self.get_object()
        instance.pk = None
        instance.default = False
        instance.save()
        serializer = CheckoutSerializer(instance, context={'request': request})
        return Response(serializer.data, status=status.HTTP_201_CREATED)


@csrf_exempt
def create_checkout_abandonment(request, *args, **kwargs):
    data = json.loads(request.body.decode('utf-8'))

    data['offer'], _ = unpack_offer_id_and_checkout_id(kwargs.get('id', ''))
    data['customerCpf'] = data.get('customerCpf', '')\
        .strip().replace('.', '').replace('-', '')
    data['customerCnpj'] = data.get('customerCpf', '')\
        .strip().replace('.', '').replace('-', '').replace('/', '')
    data['customerCellphone'] = data.get('customerCellphone', '')\
        .strip().replace('(', '').replace(')', '').replace('-', '').replace(' ', '')

    serializer = CheckoutAbandonmentCreateSerializer(data=data)

    if serializer.is_valid():
        checkout_abandonment = serializer.save()
        dispatch_app_events.delay('checkout_abandonment', checkout_abandonment=checkout_abandonment, offer=checkout_abandonment.offer)  # type:ignore
        return JsonResponse(serializer.data)

    return JsonResponse({'detail': 'Erro ao criar abandono de checkout', 'errors': serializer.errors}, status=status.HTTP_400_BAD_REQUEST)
