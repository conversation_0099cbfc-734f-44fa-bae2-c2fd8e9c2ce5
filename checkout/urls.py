# checkout/urls.py
from django.urls import path
import checkout.views as views

urlpatterns = [
    path('product/builder/checkout/<str:id>/', views.CheckoutRetrieveAPI.as_view(), name='checkout-retrieve-api'),
    path('product/builder/checkout/<str:id>/duplicate/', views.CheckoutActionsAPI.as_view({'post': 'duplicate'})),
    path('product/<str:product>/checkouts/', views.CheckoutAPI.as_view({'get': 'list', 'post': 'create'}), name='checkout-api'),
    path('gallery/upload/<str:product>/', views.GalleryApi.as_view({'post': 'create'})),
    path('gallery/<str:product>/', views.GalleryApi.as_view({'get': 'list'})),
    path('gallery/<str:product>/<int:pk>/', views.GalleryApi.as_view({'delete': 'destroy'})),
    path('checkout/<str:id>/abandonment/', views.create_checkout_abandonment),
]
