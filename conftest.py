import pytest
from product.models import Product, Offer, Checkout
from model_bakery import baker
from faker import Faker
from rest_framework.test import APIClient
from user.models import User

faker = Faker(locale="pt_BR")


@pytest.fixture
def api_client() -> APIClient:
    return APIClient()


@pytest.fixture
def user():
    return baker.make(
        "user.User",
        email=faker.email(),
        username=faker.user_name(),
        password="P@ssW0rd",
        is_active=True,
        emailValidated=True,
    )


@pytest.fixture
def logged_in_user(api_client: APIClient, user: User) -> APIClient:
    api_client.force_authenticate(user)
    return api_client


@pytest.fixture
def product(user: User) -> Product:
    return baker.make(
        "product.Product",
        name="Test Product",
        user=user,
    )


@pytest.fixture
def offer(product: Product) -> Offer:
    return baker.make(
        "product.Offer",
        name="Test Offer",
        product=product,
        status="active"
    )


@pytest.fixture
def checkout(product: Product, offer: Offer) -> Checkout:
    checkout: Checkout = baker.make(
        "product.Checkout",
        name="Test Checkout",
        product=product,
        default=True,
        config={"checkout": "First Checkout"},
    )
    checkout.offers.add(offer)

    return checkout

@pytest.fixture
def default_checkout_without_offer(product: Product) -> Checkout:
    return baker.make(
        "product.Checkout",
        name="Test Checkout",
        product=product,
        default=True,
        config={"checkout": "Test Checkout"},
    )

@pytest.fixture
def second_offer_checkout(product: Product, offer: Offer) -> Checkout:
    second_checkout: Checkout = baker.make(
        "product.Checkout",
        name="Second Checkout",
        product=product,
        default=True,
        config={"checkout": "Second Checkout"},
    )
    second_checkout.offers.add(offer)

    return second_checkout
