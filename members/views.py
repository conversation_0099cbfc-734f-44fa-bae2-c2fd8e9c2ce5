import hashlib
import os
import re
import uuid

import boto3
import requests
import telegram
from django.db import transaction
from django.shortcuts import get_object_or_404
from django.utils import timezone
from rest_framework import filters, status, viewsets
from rest_framework.decorators import action
from rest_framework.parsers import MultiPartParser
from rest_framework.permissions import <PERSON>ow<PERSON><PERSON>, IsAdminUser, IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.viewsets import ModelViewSet

from email_service.mail import send_new_course_access_email, send_password_reset_email
from financial.utils import get_or_create_user
from gateway.models import Order
from members.auth_manager import MemberUserManager, ResponseError
from members.models import MemberFile, Video
from members.sdk.discord import Discord
from members.sdk.members_v2 import (MembersV2BannersSDK, MembersV2CourseSDK, MembersV2LessonsSDK, MembersV2ModulesSDK,
                                    add_membersV2_course_to_user, get_user_membersV2_token,
                                    remove_membersV2_user_access)
from members.sdk.telegram import Telegram
from members.serializers import (GiveCourseAccessSerializer, MembersV2GiveAccessSerializer, MemberV2BannerSerializer,
                                 MemberV2CourseSerializer, MemberV2ImageSerializer, MemberV2LessonFileSerializer,
                                 MemberV2LessonFileUploadSerializer, MemberV2LessonSerializer, MemberV2ModuleSerializer,
                                 VideoSerializer)
from members.utils import (add_user_course_access, create_course, do_request, do_request_as_admin,
                           generate_expiration_time, get_response_content, remove_user_course_access)
from product.models import DeliveryAccess, Product
from product.serializers import DeliveryAccessPublicSerializer
from user.models import User
from user.permissions import ScopePermission


def get_s3_client():
    return boto3.client(
        's3',
        aws_access_key_id=os.getenv('MEMBERS_AWS_ACCESS_KEY_ID'),
        aws_secret_access_key=os.getenv('MEMBERS_AWS_SECRET_ACCESS_KEY'),
        region_name=os.getenv('MEMBERS_AWS_S3_REGION_NAME'),
        endpoint_url=os.getenv('MEMBERS_AWS_S3_ENDPOINT_URL')
    )

class MemberAccessTokenAPIView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            user = request.user
            session_key = f'{user.id}_members_api_user_token'
            session_token = request.session.get(session_key) or None
            token = MemberUserManager().validade_or_get_token(user=request.user, token=session_token)
            request.session[session_key] = token
            request.session.save()
        except ResponseError as e:
            return e.args[0]
        return Response({'accessToken': token})

class AdminMemberAccessTokenAPIView(APIView):
    scope = 'members'
    permission_classes = [IsAdminUser]

    def get(self, request, *args, **kwargs):
        user = get_object_or_404(User.objects.filter(email__iexact=request.query_params.get('userEmail')))
        MemberUserManager().member_admin.set_token()
        token = MemberUserManager().auth_user(user)
        return Response({'accessToken': token})

class CourseListAPIView(APIView):
    scope = 'members'
    permission_classes = [IsAuthenticated, ScopePermission]

    def get(self, request, *args, **kwargs):
        response = do_request(self.request, '/cursos/', 'get')

        data = response.data if response.data else []

        # remove sensible data
        for item in data:
            item.pop('userId', None)

        external_ids = []

        self.discord_login_url = Discord().get_discord_oauth_customer(self.request)

        if status.is_success(response.status_code) and data:
            external_ids = [item['externalId'] for item in data]
            products = Product.objects.filter(pk__in=external_ids).only('pk', 'status')
            self.remove_deleted_products(data, products)
            self.populate_externalChannels(data, external_ids, products)

        self.append_only_externalChannels(data, external_ids)

        return Response(data, status=response.status_code)

    def get_serializer_context(self):
        return {'discord_login_url': self.discord_login_url}

    def remove_deleted_products(self, data, products):
        product_dict = {product.pk: product for product in products}
        for i, item in enumerate(data):
            product = product_dict.get(item['externalId'])
            if product:
                item['status'] = product.status
            if product and product.status == 'deleted':
                data.pop(i)

    def populate_externalChannels(self, data, external_ids, products):
        product_dict = {product.pk: product for product in products}

        delivery_accesses = (
            DeliveryAccess.objects
            .filter(user=self.request.user, productDelivery__product__in=external_ids)
            .prefetch_related('productDelivery__contentDelivery')
        )

        delivery_access_dict: dict[str, list[DeliveryAccess]] = {}

        for access in delivery_accesses:
            product_id = access.productDelivery.product_id
            if product_id not in delivery_access_dict:
                delivery_access_dict[product_id] = []
            delivery_access_dict[product_id].append(access)

        for item in data:
            product = product_dict.get(item['externalId'])
            if product:
                product_delivery_accesses = delivery_access_dict.get(product.pk, [])
                serializer = DeliveryAccessPublicSerializer(product_delivery_accesses, many=True, context=self.get_serializer_context())
                item['externalChannels'] = serializer.data

    def append_only_externalChannels(self, data, external_ids):
        only_external_delivery_accesses = (
            DeliveryAccess.objects
            .filter(user=self.request.user,)
            .prefetch_related('productDelivery__contentDelivery')
            .select_related('productDelivery__product')
            .exclude(productDelivery__product__in=external_ids)
        )

        to_add: dict[str, list[DeliveryAccess]] = {}

        for access in only_external_delivery_accesses:
            product_id = access.productDelivery.product_id
            if product_id not in to_add:
                to_add[product_id] = []
            to_add[product_id].append(access)

        for product_id, accesses in to_add.items():
            serializer = DeliveryAccessPublicSerializer(accesses, many=True, context=self.get_serializer_context())
            data.append({
                'externalId': product_id,
                'capa': accesses[0].productDelivery.product.image.url if accesses[0].productDelivery.product.image else None,
                'onlyExternal': True,
                'nome': accesses[0].productDelivery.product.name,
                'externalChannels': serializer.data,
            })

class CourseAPIView(viewsets.ViewSet):
    scope = 'members'

    def get(self, request, *args, **kwargs):
        product = get_object_or_404(Product, pk=self.kwargs.get('id'))
        if product.membersId is None:
            response, created = create_course(product)
            if not created:
                return response
        response = do_request(self.request, f'/cursos/{product.pk}/', 'get')

        if response.data and status.is_success(response.status_code):
            owner = True if (product.user == self.request.user) else False
            response.data.update({'owner': owner})

        return response

    def put(self, request, *args, **kwargs):
        product = get_object_or_404(Product, pk=self.kwargs.get('id'), user=self.request.user)
        return do_request(self.request, f'/cursos/{product.id}/', 'put')

    def delete(self, request, *args, **kwargs):
        product = get_object_or_404(Product, pk=self.kwargs.get('id'), user=self.request.user)
        response = do_request_as_admin(self.request, f'/cursos/{product.id}/', 'delete')
        if status.is_success(response.status_code):
            product.membersId = None
            product.save()
        return response

    @action(detail=False, methods=['post'])
    def add_course_access_to_user(self, request, *args, **kwargs):
        product = get_object_or_404(Product, pk=self.kwargs.get('id'), user=self.request.user, status='active')
        serializer = GiveCourseAccessSerializer(data=self.request.data)  # type:ignore
        serializer.is_valid(raise_exception=True)
        data: dict = serializer.validated_data  # type:ignore

        user = get_or_create_user(data['email'], emailValidated=True)
        response, created = add_user_course_access(user, product, str(data.get('access_time')))
        if created:
            send_new_course_access_email(user, product)
            return Response({'detail': 'Acesso ao curso concedido com sucesso.'}, status=status.HTTP_200_OK)

        raise Exception(
            'Erro ao conceder acesso ao curso.\n'
            f'members response -> {get_response_content(response)}\n'
            f'request data -> {self.request.data}'  # type:ignore
        )

    @action(detail=False, methods=['post'])
    def remove_course_access_to_user(self, request, *args, **kwargs):
        product = get_object_or_404(Product, pk=self.kwargs.get('id'), user=self.request.user, status='active')
        user = get_object_or_404(User, email=self.request.data.get('email'))
        response, removed = remove_user_course_access(user_id=user.pk, product_id=product.pk)

        if not removed and status.is_client_error(response.status_code):
            return Response(response.data, status=response.status_code)

        if removed:
            return Response({'detail': 'Acesso ao curso removido com sucesso.'}, status=status.HTTP_200_OK)

        raise Exception(
            'Erro ao remover acesso ao curso.\n'
            f'members response -> {get_response_content(response)}\n'
            f'request data -> {self.request.data}'  # type:ignore
        )

class UserCourseActionsAPIView(viewsets.GenericViewSet):
    @action(detail=False, methods=['post'])
    def send_new_password_link(self, request, *args, **kwargs):
        email = self.request.data.get('email')

        customer_exists = Order.objects.filter(customer__email=email, status='paid', product__user=self.request.user).exists()
        if not customer_exists:
            return Response({'detail': 'Usuário não encontrado.'}, status=status.HTTP_404_NOT_FOUND)

        user = get_object_or_404(User, email=email)
        user.set_otp_token(timezone.now() + timezone.timedelta(days=1))

        email_sent = send_password_reset_email(user)
        if not email_sent:
            raise Exception(f'Error sending password reset email to {user.email}')
        return Response({'detail': 'Email enviado com sucesso!'})

class CourseListUsersAPIView(APIView):
    scope = 'members'

    def get(self, *args, **kwargs):
        product = get_object_or_404(Product, pk=self.kwargs.get('product_id'), user=self.request.user)
        return do_request(self.request, f'/cursos/{product.id}/users/', 'get')

class ModuleCreateUpdateDeleteAPIView(APIView):
    scope = 'members'

    def post(self, request, *args, **kwargs):
        product = get_object_or_404(Product, pk=self.kwargs.get('product_id'), user=self.request.user)
        return do_request(self.request, f'/cursos/{product.id}/modulos', 'post')

    def put(self, request, *args, **kwargs):
        product = get_object_or_404(Product, pk=self.kwargs.get('product_id'), user=self.request.user)
        return do_request(self.request, f'/cursos/{product.id}/modulos', 'put')

    def delete(self, request, *args, **kwargs):
        product = get_object_or_404(Product, pk=self.kwargs.get('product_id'), user=self.request.user)
        return do_request(self.request, f'/cursos/{product.id}/modulos', 'delete')

class LessonsListAPIView(APIView):
    scope = 'members'

    def get(self, request, *args, **kwargs):
        return do_request(self.request, '/aulas/', 'get')

class LessonsRetrieveAPIView(viewsets.GenericViewSet):
    scope = 'members'

    def get(self, request, *args, **kwargs):
        lesson_id = self.kwargs.get('id')
        return do_request(self.request, f'/aulas/{lesson_id}/', 'get')

    @action(methods=['get'], detail=True)
    def lesson_watch_data(self, request, *args, **kwargs):
        lesson_id = self.kwargs.get('id')
        return do_request(self.request, f'/aulas/{lesson_id}/assistir', 'get')

    @action(detail=False, methods=['put'], parser_classes=[MultiPartParser])
    def upload_files(self, request, *args, **kwargs):
        received_files = request.FILES.getlist('files')
        s3 = get_s3_client()
        files_uploaded = []

        # limit each file size to 1 GB
        for file in received_files:
            if file.size > 1_000_000_000:
                return Response({'detail': 'Arquivo muito grande.'}, status=status.HTTP_400_BAD_REQUEST)

        for file in received_files:
            file_name = str(uuid.uuid4()).replace('-', '')[:15] + '-' + file.name
            MemberFile.objects.create(file_key=file_name, user=self.request.user)
            with transaction.atomic():
                s3.upload_fileobj(
                    file.file,
                    os.getenv('MEMBERS_AWS_STORAGE_BUCKET_NAME'),
                    file_name,
                    ExtraArgs={'ACL': 'private'}
                )
                files_uploaded.append(file_name)
        return Response({'detail': 'Arquivos enviados com sucesso.', 'files': files_uploaded})

    @action(detail=False, methods=['delete'])
    def delete_file(self, request, *args, **kwargs):
        file_key = request.data.get('fileKey')
        if not file_key:
            return Response({'detail': 'fileKey é obrigatório.'}, status=status.HTTP_400_BAD_REQUEST)

        instance = MemberFile.objects.filter(file_key=file_key, user=self.request.user)
        if not instance.exists():
            return Response({'detail': 'Erro ao exluir.'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        s3 = get_s3_client()
        s3.delete_object(
            os.getenv('MEMBERS_AWS_STORAGE_BUCKET_NAME'),
            Key=file_key
        )

        instance.delete()
        return Response({'detail': 'Arquivo removido com sucesso.'})

    @action(detail=False, methods=['delete'])
    def get_pre_signed_url(self, request, *args, **kwargs):
        file_key = request.query_params.get('fileKey')
        if not file_key:
            return Response({'detail': 'fileKey é obrigatório.'}, status=status.HTTP_400_BAD_REQUEST)

        instance = MemberFile.objects.filter(file_key=file_key, user=self.request.user)
        if not instance.exists():
            return Response({'detail': 'Erro ao gerar url.'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        else:
            s3 = get_s3_client()
            url = s3.generate_presigned_url(
                'get_object',
                Params={
                    'Bucket': os.getenv('MEMBERS_AWS_STORAGE_BUCKET_NAME'),
                    'Key': file_key
                },
                ExpiresIn=3600
            )
            return Response({'url': url})

class LessonsCreateUpdateDeleteAPIView(APIView):
    scope = 'members'

    def post(self, request, *args, **kwargs):
        product = get_object_or_404(Product, pk=self.kwargs.get('product_id'), user=self.request.user)
        self.request.data.update({'externalId': product.id})  # type:ignore
        return do_request(self.request, '/aulas/', 'post')

    def put(self, request, *args, **kwargs):
        lesson_id = self.kwargs.get('id')
        get_object_or_404(Product, pk=self.kwargs.get('product_id'), user=self.request.user)
        return do_request(self.request, f'/aulas/{lesson_id}/', 'put')

    def delete(self, request, *args, **kwargs):
        lesson_id = self.kwargs.get('id')
        get_object_or_404(Product, pk=self.kwargs.get('product_id'), user=self.request.user)
        return do_request(self.request, f'/aulas/{lesson_id}/', 'delete')

class VideoAPIView(ModelViewSet):
    model = Video
    scope = 'members'
    serializer_class = VideoSerializer
    filter_backends = [filters.SearchFilter]
    search_fields = ['title']

    access_headers = {
        "accept": "application/json",
        "content-type": "application/*+json",
        "AccessKey": os.getenv('VIDEO_HOST_APIKEY', '')
    }
    url = f"https://video.bunnycdn.com/library/{os.getenv('VIDEO_LIBRARY_ID')}/videos"

    def get_queryset(self, *args, **kwargs):
        return Video.objects.filter(user=self.request.user)

    def create(self, request, *args, **kwargs):
        title = request.data.get('videoTitle')

        response = requests.post(self.url, json={'title': title}, headers=self.access_headers)
        response_content = response.json()

        if status.is_success(response.status_code):
            id = response_content.get("guid")
            authorizationExpire = generate_expiration_time()
            signature = f"{os.getenv('VIDEO_LIBRARY_ID')}{os.getenv('VIDEO_HOST_APIKEY')}{authorizationExpire}{id}"
            signature = hashlib.sha256(signature.encode('utf-8')).hexdigest()

            data = {
                'id': id,
                'title': response_content.get("title"),
                'authorizationSignature': signature,
                'authorizationExpire': authorizationExpire,
                'libraryId': os.getenv('VIDEO_LIBRARY_ID'),
            }
            Video.objects.create(
                id=data['id'],
                user=self.request.user,
                title=data['title']
            )
            return Response(data, status=status.HTTP_200_OK)
        return Response(response_content, status=response.status_code)

    def retrieve(self, *args, **kwargs):
        instance = self.get_object()
        url = self.url + f'/{instance.id}'
        response = requests.get(url, headers=self.access_headers)
        if status.is_success(response.status_code):
            data: dict = response.json()
            [data.pop(item, None) for item in ['guid', 'status', 'title', 'dateUploaded']]
            new_data = {
                'id': instance.id,
                'title': instance.title,
                'status': instance.status,
                'uploadedAt': instance.uploadedAt
            }
            new_data.update(data)
            return Response(new_data)
        return Response(response.json(), status=response.status_code)

    def destroy(self, *args, **kwargs):
        instance = self.get_object()
        url = self.url + f'/{instance.id}'
        response = requests.delete(url, headers=self.access_headers)
        if status.is_success(response.status_code):
            instance.delete()
            return Response(status=status.HTTP_200_OK)
        return Response(response.json(), status=response.status_code)

    @action(detail=True, methods=['post'])
    def set_thumbnail(self, *args, **kwargs):
        instance = self.get_object()
        url = self.url + f'/{instance.id}/thumbnail'
        url += f"?thumbnailUrl={self.request.data.get('thumnailUrl')}"  # type:ignore
        response = requests.post(url, headers=self.access_headers)

        if status.is_success(response.status_code):
            url = self.url + f'/{instance.id}'
            retrieve_resposne = requests.get(url, headers=self.access_headers)

            if status.is_success(retrieve_resposne.status_code):
                thumb_name = retrieve_resposne.json().get('thumbnailFileName', 'thumbnail.jpg')
                instance.thumbnail = thumb_name
                instance.save()
                return Response({'thumbnail': instance.thumbnailUrl})
            return Response(retrieve_resposne.json(), status=retrieve_resposne.status_code)

        return Response(response.json(), status=response.status_code)

class VideoWebhook(ModelViewSet):
    model = Video
    permission_classes = [AllowAny, ]
    throttle_classes = []

    # https://docs.bunny.net/docs/stream-webhook
    video_status = {
        '0': 'queued',
        '1': 'processing',
        '2': 'encoding',
        '3': 'finished',
        '4': 'videoPlayable',
        '5': 'encodingFailed',
        '6': 'uploadStarted',
        '7': 'uploadFinished',
        '8': 'uploadFailed',
        '9': 'captionsGenerated',
        '10': 'titleOrDescriptionGenerated',
    }

    def post(self, request, *args, **kwargs):
        data = request.data.copy()
        status = str(data.get('Status'))
        video = Video.objects.filter(id=data.get('VideoGuid')).only('status', 'uploadedAt').first()

        if (video is not None) and (video.status != 'finished'):
            video.status = self.video_status[status]
            if (status in ['3', '7', '4']) and (video.uploadedAt is None):
                video.uploadedAt = timezone.now()
            video.save()

        return Response()

class TelegramWebhook(APIView):
    permission_classes = [AllowAny]
    throttle_classes = []

    def post(self, request, *args, **kwargs):
        secret_token = os.getenv('TELEGRAM_SECRET', '')
        received_token = request.headers.get('X-Telegram-Bot-Api-Secret-Token')

        if received_token != secret_token:
            return Response({"success": True})

        self.telegram_sdk = Telegram()
        update = self.telegram_sdk.toUpdate(request.data)

        callback_query = getattr(update, 'callback_query', None)
        if callback_query is not None:
            return self.telegram_sdk.handle_callback(callback_query)

        message = getattr(update, 'message', None) or getattr(update, 'channel_post', None)
        if update and message:
            self.handle_message(message)

        return Response({"success": True})

    def handle_message(self, message: telegram.Message):
        new_chat_title = getattr(message, 'new_chat_title', None)

        if new_chat_title is not None:
            return self.telegram_sdk.handle_new_chat_title(message)

        if not message.text:
            return Response({"success": True})

        if message.chat.type == 'group' or message.chat.type == 'supergroup' or message.chat.type == 'channel':
            if re.match(r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$', message.text):
                return self.telegram_sdk.process_productDelivery_config(message)
        elif message.chat.type == 'private':
            self.handle_private_message(message)

    def handle_private_message(self, message: telegram.Message):
        if not message.from_user or not message.text:
            return Response({"success": True})

        if re.match(r'^/start ([0-9A-z]){6}$', message.text) or re.match(r'^([0-9A-z]){6}$', message.text):
            return self.telegram_sdk.handle_customer_access(message)
        elif re.match(r'^/start$', message.text):
            return self.telegram_sdk.handle_start(message)
        else:
            return self.telegram_sdk.handle_unknown_command(message)

class MembersV2CourseAPIView(viewsets.GenericViewSet, MembersV2CourseSDK):
    scope = 'members'

    def list(self, request, *args, **kwargs):
        return self.list_courses(user=request.user)

    def retrieve(self, request, *args, **kwargs):
        product_id = self.kwargs.get('product_id')
        return self.retrieve_course(user=request.user, product_id=product_id)

    def create(self, request, *args, **kwargs):
        product_id = self.kwargs.get('product_id')
        return self.create_course(user=request.user, product_id=product_id)

    def update(self, request, *args, **kwargs):
        product_id = self.kwargs.get('product_id')
        serializer = MemberV2CourseSerializer(data=self.request.data)
        serializer.is_valid(raise_exception=True)

        return self.update_course(
            user=request.user,
            product_id=product_id,
            data=serializer.validated_data,  # type:ignore
        )

    def destroy(self, request, *args, **kwargs):
        product_id = self.kwargs.get('product_id')
        return self.delete_course(user=request.user, product_id=product_id)

    def upload_logo(self, request, *args, **kwargs):
        product_id = self.kwargs.get('product_id')
        serializer = MemberV2ImageSerializer(data=request.FILES)
        serializer.is_valid(raise_exception=True)

        return self.upload_course_logo(
            user=request.user,
            product_id=product_id,
            logo=serializer.validated_data['image'],  # type:ignore
        )

    def delete_logo(self, request, *args, **kwargs):
        product_id = self.kwargs.get('product_id')
        return self.delete_course_logo(user=request.user, product_id=product_id)

    def add_user_access(self, request, *args, **kwargs):
        product = self._get_product_instance_by_id(product_id=self.kwargs.get('product_id'), user=request.user)

        serializer = MembersV2GiveAccessSerializer(data=self.request.data)
        serializer.is_valid(raise_exception=True)

        expires_at = serializer.validated_data.get('finalTime')  # type:ignore

        add_membersV2_course_to_user(user=request.user, product=product, expires_at=expires_at)

        return Response({'detail': 'Acesso ao curso concedido com sucesso.'}, status=status.HTTP_200_OK)

    def remove_user_access(self, request, *args, **kwargs):
        product = self._get_product_instance_by_id(product_id=self.kwargs.get('product_id'), user=request.user)

        user_email_to_remove = self.request.query_params.get('email')
        user_to_remove = User.objects.exclude(id=request.user.id).filter(email=user_email_to_remove).first()
        if not user_to_remove:
            return Response({'detail': 'Usuário não encontrado.'}, status=status.HTTP_404_NOT_FOUND)

        remove_membersV2_user_access(user=user_to_remove, product=product)

        return Response({'detail': 'Acesso ao curso removido com sucesso.'}, status=status.HTTP_200_OK)

class MembersV2BannersAPIView(viewsets.GenericViewSet, MembersV2BannersSDK):
    scope = 'members'

    def list_banners(self, request, *args, **kwargs):
        product_id = self.kwargs.get('product_id')
        return self.list_course_banners(user=request.user, product_id=product_id)

    def create_banner(self, request, *args, **kwargs):
        product_id = self.kwargs.get('product_id')
        serializer = MemberV2BannerSerializer(data=self.request.data)
        serializer.is_valid(raise_exception=True)

        return self.create_course_banner(
            user=request.user,
            product_id=product_id,
            data=serializer.validated_data,  # type:ignore
        )

    def update_banner(self, request, *args, **kwargs):
        product_id = self.kwargs.get('product_id')
        banner_id = self.kwargs.get('banner_id')
        serializer = MemberV2BannerSerializer(data=self.request.data)
        serializer.is_valid(raise_exception=True)

        return self.update_course_banner(
            user=request.user,
            product_id=product_id,
            banner_id=banner_id,
            data=serializer.validated_data,  # type:ignore
        )

    def delete_banner(self, request, *args, **kwargs):
        product_id = self.kwargs.get('product_id')
        banner_id = self.kwargs.get('banner_id')

        return self.delete_course_banner(
            user=request.user,
            product_id=product_id,
            banner_id=banner_id,
        )

    def upload_banner_image(self, request, *args, **kwargs):
        product_id = self.kwargs.get('product_id')
        banner_id = self.kwargs.get('banner_id')
        serializer = MemberV2ImageSerializer(data=request.FILES)
        serializer.is_valid(raise_exception=True)

        return self.upload_course_banner_file(
            user=request.user,
            product_id=product_id,
            banner_id=banner_id,
            banner=serializer.validated_data['image'],  # type:ignore
        )

    def delete_banner_image(self, request, *args, **kwargs):
        product_id = self.kwargs.get('product_id')
        banner_id = self.kwargs.get('banner_id')

        return self.delete_course_banner_file(
            user=request.user,
            product_id=product_id,
            banner_id=banner_id,
        )

class MembersV2ModulesAPIView(viewsets.GenericViewSet, MembersV2ModulesSDK):
    scope = 'members'

    def create(self, request, *args, **kwargs):
        product_id = self.kwargs.get('product_id')
        serializer = MemberV2ModuleSerializer(data=self.request.data)
        serializer.is_valid(raise_exception=True)

        return self.create_module(
            user=request.user,  # type:ignore
            product_id=product_id,
            data=serializer.validated_data,  # type:ignore
        )

    def udpate(self, request, *args, **kwargs):
        product_id = self.kwargs.get('product_id')
        module_id = self.kwargs.get('module_id')
        serializer = MemberV2ModuleSerializer(data=self.request.data)
        serializer.is_valid(raise_exception=True)

        return self.update_module(
            user=request.user,  # type:ignore
            product_id=product_id,
            module_id=module_id,
            data=serializer.validated_data,  # type:ignore
        )

    def destroy(self, request, *args, **kwargs):
        product_id = self.kwargs.get('product_id')
        module_id = self.kwargs.get('module_id')
        return self.delete_module(
            user=request.user,  # type:ignore
            product_id=product_id,
            module_id=module_id,
        )

    def upload_cover(self, request, *args, **kwargs):
        product_id = self.kwargs.get('product_id')
        module_id = self.kwargs.get('module_id')

        serializer = MemberV2ImageSerializer(data=request.FILES)
        serializer.is_valid(raise_exception=True)

        return self.upload_module_cover(
            user=request.user,  # type:ignore
            product_id=product_id,
            module_id=module_id,
            cover=serializer.validated_data['image'],  # type:ignore
        )

    def delete_cover(self, request, *args, **kwargs):
        product_id = self.kwargs.get('product_id')
        module_id = self.kwargs.get('module_id')
        return self.delete_module_cover(
            user=request.user,  # type:ignore
            product_id=product_id,
            module_id=module_id,
        )

class MembersV2UserTokenAPIView(APIView):
    scope = 'members'

    def get(self, request, *args, **kwargs):
        return Response({'token': get_user_membersV2_token(user=self.request.user, get_cached_token=False)})  # type:ignore

class MembersV2LessonsAPIView(viewsets.GenericViewSet, MembersV2LessonsSDK):
    scope = 'members'

    def list_lessons(self, request, *args, **kwargs):
        module_id = self.kwargs.get('module_id')
        return self.list_module_lessons(user=request.user, module_id=module_id)

    def create_lesson(self, request, *args, **kwargs):
        serializer = MemberV2LessonSerializer(data=self.request.data)
        serializer.is_valid(raise_exception=True)
        product_id = self.kwargs.get('product_id')
        module_id = self.kwargs.get('module_id')

        return self.create_module_lesson(
            user=request.user,
            product_id=product_id,
            module_id=module_id,
            data=serializer.validated_data,  # type:ignore
        )

    def update_lesson(self, request, *args, **kwargs):
        serializer = MemberV2LessonSerializer(data=self.request.data)
        serializer.is_valid(raise_exception=True)
        product_id = self.kwargs.get('product_id')

        return self.update_module_lesson(
            user=request.user,
            product_id=product_id,
            lesson_id=self.kwargs.get('lesson_id'),
            data=serializer.validated_data,  # type:ignore
        )

    def delete_lesson(self, request, *args, **kwargs):
        product_id = self.kwargs.get('product_id')
        lesson_id = self.kwargs.get('lesson_id')

        return self.delete_module_lesson(
            user=request.user,
            product_id=product_id,
            lesson_id=lesson_id,
        )

    def list_files(self, request, *args, **kwargs):
        lesson_id = self.kwargs.get('lesson_id')
        return self.list_lesson_files(user=request.user, lesson_id=lesson_id,)

    def create_file(self, request, *args, **kwargs):
        product_id = self.kwargs.get('product_id')
        lesson_id = self.kwargs.get('lesson_id')

        serializer = MemberV2LessonFileSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        return self.create_lesson_file(
            user=request.user,
            product_id=product_id,
            lesson_id=lesson_id,
            data=serializer.validated_data,  # type:ignore
        )

    def update_file_data(self, request, *args, **kwargs):
        product_id = self.kwargs.get('product_id')
        file_id = self.kwargs.get('file_id')

        serializer = MemberV2LessonFileSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        return self.update_lesson_file_data(
            user=request.user,
            product_id=product_id,
            file_id=file_id,
            data=serializer.validated_data,  # type:ignore
        )

    def upload_file_item(self, request, *args, **kwargs):
        product_id = self.kwargs.get('product_id')
        file_id = self.kwargs.get('file_id')

        serializer = MemberV2LessonFileUploadSerializer(data=request.FILES)
        serializer.is_valid(raise_exception=True)

        return self.upload_lesson_file_item(
            user=request.user,
            product_id=product_id,
            file_id=file_id,
            file_item=serializer.validated_data['file'],  # type:ignore
        )

    def delete_file(self, request, *args, **kwargs):
        product_id = self.kwargs.get('product_id')
        file_id = self.kwargs.get('file_id')

        return self.delete_lesson_file(
            user=request.user,
            product_id=product_id,
            file_id=file_id,
        )
