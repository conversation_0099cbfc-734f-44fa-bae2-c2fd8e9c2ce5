import os
import time
from datetime import datetime, timedelta

import requests
from django.core.cache import cache
from django.utils import timezone
from django_rq import job
from rest_framework import status
from rest_framework.response import Response
from rq import Retry

from members.auth_manager import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, MemberUserManager
from product.models import Product
from user.models import User

MEMBERS_URL = os.getenv('MEMBERS_URL', '')

def _get_admin_response(endpoint, data):
    MembersAdminManager().set_token()
    headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + MembersAdminManager().token,  # type:ignore
    }
    url = f'{MEMBERS_URL.rstrip("/")}/{endpoint.lstrip("/")}'
    response = requests.post(url=url, json=data, headers=headers)

    if status.is_client_error(response.status_code):
        # if error reset token and try again
        cache.delete('members_admin_token')
        MembersAdminManager().set_token()
        headers.update({'Authorization': 'Bearer ' + MembersAdminManager().token})  # type:ignore
        response = requests.post(url=url, json=data, headers=headers)
    return response

def _get_response(request, http_method, endpoint, admin=False):
    if admin:
        MembersAdminManager().set_token()
        token = MemberUserManager().member_admin.token
    else:
        user = request.user
        session_key = f'{user.id}_members_api_user_token'
        session_token = request.session.get(session_key) or None
        token = MemberUserManager().validade_or_get_token(user=request.user, token=session_token)
        request.session[session_key] = token
        request.session.save()

    headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + token,  # type:ignore
    }

    if request.query_params:
        params = list(request.query_params.items())
        endpoint += '?'
        endpoint += ''.join([f'{k}={v}&' for k, v in params[0:-1]])
        endpoint += '='.join(params[-1])

    request_attrs = {
        'url': f'{MEMBERS_URL}{endpoint}',
        'headers': headers,
    }

    if request.data:
        request_attrs.update({'json': request.data})

    return getattr(requests, http_method)(**request_attrs)

def get_response_content(response):
    if isinstance(response, Response):
        return response.data
    elif 'application/json' in response.headers['Content-Type']:  # type:ignore
        return response.json()
    else:
        return {'detail': response.content}

def do_request_as_admin(*args, **kwargs):
    kwargs.update({'admin': True})
    return do_request(*args, **kwargs)

def do_request(request, endpoint, http_method, admin=False):
    response = _get_response(request, http_method, endpoint, admin)
    if isinstance(response, Response):
        return response

    if status.is_client_error(response.status_code):
        # reset all tokens and try again
        cache.delete('members_admin_token')
        user = request.user
        session_key = f'{user.id}_members_api_user_token'
        request.session[session_key] = None
        request.session.save()
        response = _get_response(request, http_method, endpoint, admin)

    response_content = get_response_content(response)

    if status.is_success(response.status_code):
        return Response(response_content, status=status.HTTP_200_OK)
    return Response(response_content, status=response.status_code)

def create_course(product: Product):
    data = {
        'nome': product.name,
        'externalId': product.pk,
        'userId': product.user.pk,
    }
    MemberUserManager().auth_user(product.user)
    response = _get_admin_response('cursos/', data)
    data = get_response_content(response)

    if status.is_success(response.status_code) and data:
        product.membersId = data['id']  # type:ignore
        product.save()
        date = datetime.strptime('01-01-2299', '%d-%m-%Y')
        date.replace(tzinfo=timezone.get_current_timezone())
        create_response, created = add_user_course_access(
            user=product.user,
            product=product,
            access_time='lifetime',
        )
        if created:
            return response, True
        return create_response, False
    return response, False

@job('default', retry=Retry(max=3, interval=[5, 10, 30]))
def add_user_course_access(user: User, product: Product, access_time=None, **kwargs) -> tuple[requests.Response, bool]:
    product.refresh_from_db()
    user.refresh_from_db()
    MemberUserManager().auth_user(user)
    if product.membersId is None:
        response, created = create_course(product)
        if not created:
            return response, False

    data = {
        'userId': str(user.pk),
        'cursoId': product.id,
        'acesso': access_time,
    }
    response = _get_admin_response('usuarios/addCourseToUser/', data)

    if status.is_success(response.status_code):
        return response, True
    return response, False

@job
def remove_user_course_access(user_id, product_id):
    product = Product.objects.filter(pk=product_id).first()
    if product is None:
        return Response({'detail': 'Produto não encontrado.'}, status=status.HTTP_404_NOT_FOUND), False
    if product.user.pk == user_id:
        return Response({'detail': 'Usuário é dono do produto.'}, status=status.HTTP_400_BAD_REQUEST), False

    data = {
        'userId': str(user_id),
        'cursoId': product_id,
    }
    response = _get_admin_response('usuarios/removeCourseFromUser/', data)
    if status.is_success(response.status_code):
        return response, True
    return response, False

def generate_expiration_time():
    current_time = datetime.now()
    expiration_time = current_time + timedelta(hours=1)
    expiration_timestamp = int(time.mktime(expiration_time.timetuple()))
    return expiration_timestamp


class RequestDataManager:
    def __init__(self, request) -> None:
        self.request = request
        self.data: dict = {}
        self.store_data()

    def store_data(self):
        self.data = {k: self.request.data.pop(k) for k in self.request.data.keys()}

    def restore_data(self):
        self.request.data.clear()
        self.request.data.update(self.data) if self.data else None
