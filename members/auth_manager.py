import base64
import json
import os
from datetime import datetime

from django.core.cache import cache
import requests
from rest_framework import status
from rest_framework.response import Response

MEMBERS_URL = os.getenv('MEMBERS_URL')


def token_exp_date_is_valid(token):
    try:
        chunks = token.split('.')
        payload_base64 = chunks[1]
        payload_bytes = base64.urlsafe_b64decode(payload_base64 + '==')
        payload = json.loads(payload_bytes)

        exp = payload.get('exp')
        if exp is None:
            return False

        exp_date = datetime.utcfromtimestamp(exp)
        return datetime.utcnow() < exp_date

    except Exception:
        return False


class ResponseError(Exception):
    ...

class Singleton():
    def __new__(cls):
        if not hasattr(cls, 'instance'):
            cls.instance = super(Singleton, cls).__new__(cls)
        return cls.instance

class MembersAdminManager(Singleton):
    def __init__(self) -> None:
        self.password = os.getenv('MEMBERS_ADMIN_PASSWORD')
        self.set_token()

    def auth_admin(self) -> None:
        headers = {'Content-Type': 'application/json'}
        data = {'senha': self.password}
        login_admin_response = requests.post(f'{MEMBERS_URL}/usuarios/loginAdmin', json=data, headers=headers)
        if status.is_success(login_admin_response.status_code):
            cache.set('members_admin_token', login_admin_response.json()['token'])
            return
        else:
            create_admin_response = requests.post(f'{MEMBERS_URL}/usuarios/acessoAdmin', json=data, headers=headers)
            if status.is_success(create_admin_response.status_code):
                login_admin_response = requests.post(f'{MEMBERS_URL}/usuarios/loginAdmin', json=data, headers=headers)
                if status.is_success(login_admin_response.status_code):
                    cache.set('members_admin_token', login_admin_response.json()['token'])
                    return
        raise ResponseError(
            'Erro ao autenticar o admin na área de membros.',
            f'create_admin_response: {create_admin_response.status_code}{create_admin_response.text}',
            f'login_response: {login_admin_response.status_code}{login_admin_response.text}',
        )

    def set_token(self) -> None:
        token = cache.get('members_admin_token')
        if token is not None:
            if token_exp_date_is_valid(token):
                return
        self.auth_admin()

    @property
    def token(self) -> str:
        self.set_token()
        return cache.get('members_admin_token')

class MemberUserManager(Singleton):
    def __init__(self) -> None:
        self.member_admin = MembersAdminManager()

    @property
    def headers(self):
        return {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + self.member_admin.token,  # type:ignore
        }

    def _register_user(self, user) -> None:
        data = {
            'email': user.email,
            'idExterno': str(user.pk),
        }
        if user.picture:
            data.update({'foto': user.picture.url})  # type:ignore

        requests.post(f'{MEMBERS_URL}/usuarios/createUser', json=data, headers=self.headers)

    def auth_user(self, user) -> str:
        data = {'userId': str(user.pk)}

        login_response = requests.post(f'{MEMBERS_URL}/usuarios/loginUser', json=data, headers=self.headers)
        if status.is_success(login_response.status_code):
            return login_response.json()['token']

        elif status.is_client_error(login_response.status_code):
            self._register_user(user)
            login_response = requests.post(f'{MEMBERS_URL}/usuarios/loginUser', json=data, headers=self.headers)
            if login_response.status_code == status.HTTP_200_OK:
                return login_response.json()['token']
        raise ResponseError(
            'Erro ao autenticar usuário na área de membros.',
            f'code: {login_response.status_code}',
            f'response: {login_response.text}',
        )

    def validade_or_get_token(self, user, token=None) -> str | Response:
        if token:
            if token_exp_date_is_valid(token) and isinstance(token, str):
                return token
        self.member_admin.set_token()
        token = self.auth_user(user)
        return token

    def update_user(self, user, new_email=None, new_foto_url=None, new_id=None):
        data = {'userId': str(user.pk)}
        data.update({'email': new_email}) if new_email else None
        data.update({'foto': new_foto_url}) if new_foto_url else None
        data.update({'idExterno': str(new_id)}) if new_id else None

        response = requests.post(f'{MEMBERS_URL}/usuarios/editUser', json=data, headers=self.headers)
        if status.is_success(response.status_code):
            return True
        return Response({'detail': 'Não foi possível atualizar o usuário'}, status=response.status_code)

    def delete_user(self, user):
        data = {'userId': str(user.pk)}

        response = requests.post(f'{MEMBERS_URL}/usuarios/deleteUser', json=data, headers=self.headers)
        if status.is_success(response.status_code):
            return Response({'detail': 'Usuário deletado com sucesso.'}, status=status.HTTP_200_OK)
        return Response({'detail': 'Não foi possível deletar o usuário'}, status=response.status_code)
