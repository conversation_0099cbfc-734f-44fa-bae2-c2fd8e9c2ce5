import json
from unittest import mock

import responses
import responses.matchers
from django.conf import settings
from django.core.cache import cache
from django.core.files.uploadedfile import InMemoryUploadedFile
from django.test import override_settings
from django.urls import reverse
from django.utils import timezone
from rest_framework import status
from rest_framework.exceptions import ValidationError
from rest_framework.response import Response

from cakto.tests.base import BaseTestCase
from financial.utils import handle_chargeback, handle_members_course_removal_access, handle_purchase_course_access
from members.sdk.members_v2 import (TOKEN_CACHE_KEY_PREFIX, CustomMembersV2Session, MembersV2BannersSDK,
                                    MembersV2BaseSDK, MembersV2CourseSDK, MembersV2LessonsSDK, MembersV2ModulesSDK,
                                    add_membersV2_course_to_user, get_user_membersV2_token,
                                    parse_and_validate_expiration, remove_membersV2_user_access,
                                    update_membersV2_user_info)
from members.serializers import (MembersV2GiveAccessSerializer, MemberV2BannerSerializer, MemberV2CourseSerializer,
                                 MemberV2ImageSerializer, MemberV2LessonFileSerializer,
                                 MemberV2LessonFileUploadSerializer, MemberV2LessonSerializer, MemberV2ModuleSerializer)
from product.models import ContentDelivery, PaymentMethod

MEMBERS_V2_BASE_URL = settings.MEMBERS_V2_BASE_URL or 'https://members-v2.cakto.com'
MEMBERS_V2_API_TOKEN = settings.MEMBERS_V2_API_TOKEN or '1234567890'

USER_TOKEN_MOCK = 'token_user_12345'


@override_settings(STORAGES={
    'default': {
        'BACKEND': 'django.core.files.storage.InMemoryStorage',
    },
})
class MembersV2SDKTests(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()

        cls.course_sdk = MembersV2CourseSDK()
        cls.banners_sdk = MembersV2BannersSDK()
        cls.modules_sdk = MembersV2ModulesSDK()
        cls.lessons_sdk = MembersV2LessonsSDK()
        cls.base_sdk = MembersV2BaseSDK()

        cls.user.membersV2Id = 'id_user_9876'
        cls.user.save(update_fields=['membersV2Id'])

        cls.product = cls.create_product(user=cls.user, membersV2Id='id_product_1234')

    def mock__get_user_headers(self):
        _get_user_headers_mock = mock.patch(
            'members.sdk.members_v2.MembersV2BaseSDK._get_user_headers'
        ).start()
        _get_user_headers_mock.return_value = {'Authorization': USER_TOKEN_MOCK}
        return _get_user_headers_mock

    def mock__get_cached_user_token(self):
        return mock.patch('members.sdk.members_v2.MembersV2BaseSDK._get_cached_user_token').start()

    def mock__login_user(self):
        return mock.patch('members.sdk.members_v2.MembersV2BaseSDK._login_user').start()

    def test__build_url(self):
        url = self.base_sdk._build_url('/members')
        self.assertEqual(url, f'{MEMBERS_V2_BASE_URL}/members')

    def test__get_admin_headers(self):
        headers = self.base_sdk._get_admin_headers()
        self.assertEqual(headers, {'Authorization': MEMBERS_V2_API_TOKEN})

    def test__get_user_headers(self):
        _get_cached_user_token_mock = self.mock__get_cached_user_token()
        _get_cached_user_token_mock.return_value = expected_token = 'token_user_12345'

        headers = self.base_sdk._get_user_headers(self.user)

        self.assertEqual(headers, {'Authorization': expected_token})

    @responses.activate
    def test__login_user(self):
        login_response_mock = responses.add(
            responses.POST,
            f'{MEMBERS_V2_BASE_URL}/loginUser/{self.user.membersV2Id}',
            json={'token': 'token_user_12345'},
        )

        token = self.base_sdk._login_user(self.user)

        self.assertEqual(token, 'token_user_12345')
        self.assertTrue(login_response_mock.call_count == 1)
        call = login_response_mock.calls[0]
        self.assertEqual(call.request.headers['Authorization'], MEMBERS_V2_API_TOKEN)

    def test__get_cached_user_token(self):
        cache_key = f'{TOKEN_CACHE_KEY_PREFIX}{self.user.membersV2Id}'

        expected_token = 'token_user_12345'

        cache.set(cache_key, expected_token, timeout=60)

        token = self.base_sdk._get_cached_user_token(self.user)

        self.assertEqual(token, expected_token)

    def test__get_cached_user_token__no_cache(self):
        cache.clear()

        login_user_mock = self.mock__login_user()
        login_user_mock.return_value = expected_token = 'token_user_12345'

        token = self.base_sdk._get_cached_user_token(self.user)

        self.assertEqual(token, expected_token)

    def test__process_user_registration(self):
        self.user.membersV2Id = None
        self.user.save(update_fields=['membersV2Id'])

        _create_user_mock = mock.patch(
            'members.sdk.members_v2.MembersV2BaseSDK._create_user'
        ).start()
        response_mock = self.get_response_mock(
            content={'id': 'id_user_9876'},
            status=200,
        )
        _create_user_mock.return_value = response_mock

        self.base_sdk._process_user_registration(self.user)

        self.user.refresh_from_db()
        self.assertEqual(self.user.membersV2Id, 'id_user_9876')

    @responses.activate
    def test__create_user(self):
        create_user_response_mock = responses.add(
            responses.POST,
            f'{MEMBERS_V2_BASE_URL}/user',
            json={'id': 'id_user_9876'},
        )

        expected_body_request = {
            'email': self.user.email,
            'name': ' '.join([self.user.first_name, self.user.last_name]),
            'password': mock.ANY,
        }

        response = self.base_sdk._create_user(self.user)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json(), {'id': 'id_user_9876'})

        self.assertTrue(create_user_response_mock.call_count == 1)
        call = create_user_response_mock.calls[0]
        self.assertEqual(call.request.headers['Authorization'], MEMBERS_V2_API_TOKEN)
        self.assertEqual(json.loads(call.request.body), expected_body_request)  # type:ignore

    def test__get_product_instance_by_id(self):
        product = self.base_sdk._get_product_instance_by_id(
            product_id=self.product.id,
            user=self.user
        )

        self.assertEqual(product, self.product)

    def test__get_product_instance_by_id__not_found(self):
        with self.assertRaises(ValidationError, msg={'detail': 'Área de membros não encontrada.'}):
            self.base_sdk._get_product_instance_by_id(
                product_id='999',
                user=self.user
            )

    def test__get_product_instance_by_id_does_not_return_other_user_product(self):
        with self.assertRaises(ValidationError, msg={'detail': 'Área de membros não encontrada.'}):
            self.base_sdk._get_product_instance_by_id(
                product_id=self.product.id,
                user=self.create_user()
            )

    def test__get_product_membersV2Id_with_product_instance(self):
        product_membersV2Id = self.base_sdk._get_product_membersV2Id(self.product)

        self.assertEqual(product_membersV2Id, self.product.membersV2Id)

    def test__get_product_membersV2Id_with_search_params(self):
        product_membersV2Id = self.base_sdk._get_product_membersV2Id(
            search_params=(self.product.id, self.user)
        )

        self.assertEqual(product_membersV2Id, self.product.membersV2Id)

    def test__get_product_membersV2Id_without_product_membersV2Id(self):
        self.product.membersV2Id = None
        self.product.save(update_fields=['membersV2Id'])

        with self.assertRaises(ValidationError, msg={'detail': 'Área de membros não encontrada.'}):
            self.base_sdk._get_product_membersV2Id(self.product)

    @responses.activate
    def test__add_course_to_user(self):
        expires_at = timezone.now() + timezone.timedelta(days=10)

        expected_payload = {
            'cursoId': self.product.membersV2Id,
            'finalTime': expires_at.isoformat(),
        }

        add_course_response_mock = responses.add(
            responses.POST,
            f'{MEMBERS_V2_BASE_URL}/admin/addCursoToUser/{self.user.membersV2Id}',
            json={'test': 'test'},
        )

        self.base_sdk._add_course_to_user(
            user=self.user,
            product_membersV2Id=self.product.membersV2Id,  # type:ignore
            expires_at=expires_at,
        )

        self.assertTrue(add_course_response_mock.call_count == 1)
        call = add_course_response_mock.calls[0]
        self.assertEqual(call.request.headers['Authorization'], MEMBERS_V2_API_TOKEN)
        self.assertEqual(json.loads(call.request.body), expected_payload)  # type:ignore

    @responses.activate
    def test__add_course_to_user__without_expires_at(self):
        expected_payload = {'cursoId': self.product.membersV2Id}

        add_course_response_mock = responses.add(
            responses.POST,
            f'{MEMBERS_V2_BASE_URL}/admin/addCursoToUser/{self.user.membersV2Id}',
            json={'test': 'test'},
        )

        self.base_sdk._add_course_to_user(
            user=self.user,
            product_membersV2Id=self.product.membersV2Id,  # type:ignore
        )

        call = add_course_response_mock.calls[0]
        self.assertEqual(json.loads(call.request.body), expected_payload)  # type:ignore

    def test__add_course_to_user__with_past_expires_at(self):
        expires_at = timezone.now() - timezone.timedelta(days=10)

        with self.assertRaises(ValidationError, msg={
            'detail': 'Data de expiração inválida. Deve ser maior que a data atual ou null para acesso por tempo indeterminado.'
        }):
            self.base_sdk._add_course_to_user(
                user=self.user,
                product_membersV2Id=self.product.membersV2Id,  # type:ignore
                expires_at=expires_at,
            )

    def test__remove_course_from_user(self):
        _add_course_to_user_mock = mock.patch(
            'members.sdk.members_v2.MembersV2BaseSDK._add_course_to_user'
        ).start()

        self.base_sdk._remove_course_from_user(
            user=self.user,
            product_membersV2Id=self.product.membersV2Id,  # type:ignore
        )

        _add_course_to_user_mock.assert_called_once_with(
            user=self.user,
            product_membersV2Id=self.product.membersV2Id,  # type:ignore
            expires_at=mock.ANY,
        )
        expires_at_call = _add_course_to_user_mock.call_args.kwargs['expires_at']
        self.assertAlmostEqual(expires_at_call, timezone.now(), delta=timezone.timedelta(seconds=1))

    @responses.activate
    def test_update_user_info(self):
        self.user.first_name = 'Test'
        self.user.last_name = 'User'
        self.user.save(update_fields=['first_name', 'last_name'])

        self.mock__get_user_headers()

        update_user_response_mock = responses.add(
            responses.PUT,
            f'{MEMBERS_V2_BASE_URL}/user/{self.user.membersV2Id}',
            json={'test': 'test'},
        )

        expected_payload = {
            'nome': ' '.join([self.user.first_name, self.user.last_name]),
            'email': self.user.email,
        }

        self.base_sdk.update_user_info(self.user)

        self.assertTrue(update_user_response_mock.call_count == 1)
        call = update_user_response_mock.calls[0]
        self.assertEqual(call.request.headers['Authorization'], USER_TOKEN_MOCK)
        self.assertEqual(json.loads(call.request.body), expected_payload)  # type:ignore

    @responses.activate
    def test_list_courses(self):
        self.mock__get_user_headers()

        list_courses_response_mock = responses.add(
            responses.GET,
            f'{MEMBERS_V2_BASE_URL}/user/getAllCursosByUser/{self.user.membersV2Id}',
            json={'test': 'test'},
        )

        self.course_sdk.list_courses(self.user)

        self.assertTrue(list_courses_response_mock.call_count == 1)
        call = list_courses_response_mock.calls[0]
        self.assertEqual(call.request.headers['Authorization'], USER_TOKEN_MOCK)

    @responses.activate
    def test_retrieve_course(self):
        self.mock__get_user_headers()

        retrieve_course_response_mock = responses.add(
            responses.GET,
            f'{MEMBERS_V2_BASE_URL}/user/getAllModulosByUser/{self.product.membersV2Id}',
            json=[{'id': '123', 'name': 'Test Module'}],
        )

        response = self.course_sdk.retrieve_course(self.user, self.product.id)

        self.assertEqual(response.data, {'name': 'Test Module'})
        self.assertTrue(retrieve_course_response_mock.call_count == 1)
        call = retrieve_course_response_mock.calls[0]
        self.assertEqual(call.request.headers['Authorization'], USER_TOKEN_MOCK)

    @responses.activate
    def test_create_course(self):
        product = self.create_product(user=self.user)

        add_course_to_user_mock = mock.patch(
            'members.sdk.members_v2.MembersV2BaseSDK._add_course_to_user'
        ).start()

        create_course_response_mock = responses.add(
            responses.POST,
            f'{MEMBERS_V2_BASE_URL}/admin/curso/create',
            json={'id': 'new_course_id'},
        )

        response = self.course_sdk.create_course(self.user, product.id)

        self.assertEqual(response.data, {'detail': 'Área de membros criada com sucesso'})

        self.assertTrue(create_course_response_mock.call_count == 1)
        call = create_course_response_mock.calls[0]
        self.assertEqual(call.request.headers['Authorization'], MEMBERS_V2_API_TOKEN)
        self.assertEqual(json.loads(call.request.body), {'nome': product.name})  # type:ignore

        product.refresh_from_db()
        self.assertEqual(product.membersV2Id, 'new_course_id')

        add_course_to_user_mock.assert_called_once_with(self.user, 'new_course_id')

    def test_create_course_already_exists(self):
        self.product.membersV2Id = '123'
        self.product.save(update_fields=['membersV2Id'])

        response = self.course_sdk.create_course(self.user, self.product.id)

        self.assertEqual(response.data, {'detail': 'Área de membros já existente.'})

    @responses.activate
    def test__create_member_course(self):
        product = self.create_product(user=self.user)

        create_course_response_mock = responses.add(
            responses.POST,
            f'{MEMBERS_V2_BASE_URL}/admin/curso/create',
            json={'id': 'new_course_id'},
        )

        membersV2Id = self.course_sdk._create_member_course(product)

        self.assertEqual(membersV2Id, 'new_course_id')

        product.refresh_from_db()
        self.assertEqual(product.membersV2Id, 'new_course_id')

        call = create_course_response_mock.calls[0]
        self.assertEqual(json.loads(call.request.body), {'nome': product.name})  # type:ignore

    @responses.activate
    def test_update_course(self):
        update_data = {'nome': 'Updated Course Name'}

        update_course_response_mock = responses.add(
            responses.PUT,
            f'{MEMBERS_V2_BASE_URL}/admin/curso/update/{self.product.membersV2Id}',
            json={'id': self.product.membersV2Id, 'nome': 'Updated Course Name'},
        )

        self.course_sdk.update_course(self.user, self.product.id, update_data)

        self.assertTrue(update_course_response_mock.call_count == 1)
        call = update_course_response_mock.calls[0]
        self.assertEqual(call.request.headers['Authorization'], MEMBERS_V2_API_TOKEN)
        self.assertEqual(json.loads(call.request.body), update_data)  # type:ignore

    @responses.activate
    def test_upload_course_logo(self):
        file_content = b'test content'
        file = self.create_test_image(name='test_file.png', content_type='image/png', content=file_content)

        upload_logo_response_mock = responses.add(
            responses.POST,
            f'{MEMBERS_V2_BASE_URL}/admin/uploadLogoCurso/{self.product.membersV2Id}',
            status=200,
            match=[responses.matchers.multipart_matcher(files={'file': (file.name, file_content, file.content_type)})],
        )

        response = self.course_sdk.upload_course_logo(self.user, self.product.id, file)

        self.assertEqual(response.data, {'detail': 'Logo enviada com sucesso'})
        self.assertTrue(upload_logo_response_mock.call_count == 1)

        call = upload_logo_response_mock.calls[0]
        self.assertEqual(call.request.headers['Authorization'], MEMBERS_V2_API_TOKEN)

    @responses.activate
    def test_delete_course_logo(self):
        delete_logo_response_mock = responses.add(
            responses.DELETE,
            f'{MEMBERS_V2_BASE_URL}/admin/deleteLogoCurso/{self.product.membersV2Id}',
            json={'success': True},
        )

        response = self.course_sdk.delete_course_logo(self.user, self.product.id)

        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertEqual(response.data, {'detail': 'Logo deletada com sucesso'})

        self.assertTrue(delete_logo_response_mock.call_count == 1)
        call = delete_logo_response_mock.calls[0]
        self.assertEqual(call.request.headers['Authorization'], MEMBERS_V2_API_TOKEN)

    @responses.activate
    def test_delete_course(self):
        delete_course_response_mock = responses.add(
            responses.DELETE,
            f'{MEMBERS_V2_BASE_URL}/admin/curso/delete/{self.product.membersV2Id}',
            json={'success': True},
        )

        response = self.course_sdk.delete_course(self.user, self.product.id)

        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertEqual(response.data, {'detail': 'Área de membros deletada com sucesso'})

        self.assertTrue(delete_course_response_mock.call_count == 1)
        call = delete_course_response_mock.calls[0]
        self.assertEqual(call.request.headers['Authorization'], MEMBERS_V2_API_TOKEN)

        self.product.refresh_from_db()
        self.assertIsNone(self.product.membersV2Id)

    @responses.activate
    def test_list_course_banners(self):
        list_banners_response_mock = responses.add(
            responses.GET,
            f'{MEMBERS_V2_BASE_URL}/admin/getAllBannerCurso/{self.product.membersV2Id}',
            json=[{'id': 'banner1', 'cursoId': self.product.membersV2Id}],
        )

        response = self.banners_sdk.list_course_banners(self.user, self.product.id)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data, [{'id': 'banner1'}])

        self.assertTrue(list_banners_response_mock.call_count == 1)
        call = list_banners_response_mock.calls[0]
        self.assertEqual(call.request.headers['Authorization'], MEMBERS_V2_API_TOKEN)

    @responses.activate
    def test_create_course_banner(self):
        create_banner_response_mock = responses.add(
            responses.POST,
            f'{MEMBERS_V2_BASE_URL}/admin/bannerCurso',
            json={'id': 'banner1', 'cursoId': self.product.membersV2Id},
        )

        banner_data = {'name': 'Test Banner'}

        response = self.banners_sdk.create_course_banner(self.user, self.product.id, banner_data)

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data, {'id': 'banner1'})

        self.assertTrue(create_banner_response_mock.call_count == 1)
        call = create_banner_response_mock.calls[0]
        self.assertEqual(call.request.headers['Authorization'], MEMBERS_V2_API_TOKEN)
        self.assertEqual(json.loads(call.request.body), {'name': 'Test Banner', 'cursoId': self.product.membersV2Id})  # type:ignore

    @responses.activate
    def test_update_course_banner(self):
        update_banner_response_mock = responses.add(
            responses.PUT,
            f'{MEMBERS_V2_BASE_URL}/admin/cursoBanner/banner1/curso/{self.product.membersV2Id}',
            json={'id': 'banner1', 'name': 'Updated Banner'},
        )

        banner_data = {'name': 'Updated Banner'}

        response = self.banners_sdk.update_course_banner(self.user, self.product.id, 'banner1', banner_data)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data, {'id': 'banner1', 'name': 'Updated Banner'})

        self.assertTrue(update_banner_response_mock.call_count == 1)
        call = update_banner_response_mock.calls[0]
        self.assertEqual(call.request.headers['Authorization'], MEMBERS_V2_API_TOKEN)
        self.assertEqual(json.loads(call.request.body), banner_data)  # type:ignore

    @responses.activate
    def test_delete_course_banner(self):
        delete_banner_response_mock = responses.add(
            responses.DELETE,
            f'{MEMBERS_V2_BASE_URL}/admin/bannerCurso/delete/banner1/curso/{self.product.membersV2Id}',
            json={'success': True},
        )

        response = self.banners_sdk.delete_course_banner(self.user, self.product.id, 'banner1')

        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertEqual(response.data, {'detail': 'Banner deletado com sucesso'})

        self.assertTrue(delete_banner_response_mock.call_count == 1)
        call = delete_banner_response_mock.calls[0]
        self.assertEqual(call.request.headers['Authorization'], MEMBERS_V2_API_TOKEN)

    @responses.activate
    def test_upload_course_banner_file(self):
        file_content = b'test content'
        file = self.create_test_image(name='test_file.png', content_type='image/png', content=file_content)
        upload_banner_response_mock = responses.add(
            responses.POST,
            f'{MEMBERS_V2_BASE_URL}/admin/uploadImageBannerCurso/banner1/curso/{self.product.membersV2Id}',
            status=200,
            match=[responses.matchers.multipart_matcher(files={'file': (file.name, file_content, file.content_type)})],
        )

        response = self.banners_sdk.upload_course_banner_file(self.user, self.product.id, 'banner1', file)

        self.assertEqual(response.data, {'detail': 'Imagem do banner enviado com sucesso'})
        self.assertTrue(upload_banner_response_mock.call_count == 1)
        call = upload_banner_response_mock.calls[0]
        self.assertEqual(call.request.headers['Authorization'], MEMBERS_V2_API_TOKEN)

    @responses.activate
    def test_delete_course_banner_file(self):
        delete_banner_file_response_mock = responses.add(
            responses.DELETE,
            f'{MEMBERS_V2_BASE_URL}/admin/bannerCursoRemoveImage/delete/banner1/curso/{self.product.membersV2Id}',
            json={'success': True},
        )

        response = self.banners_sdk.delete_course_banner_file(self.user, self.product.id, 'banner1')

        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertEqual(response.data, {'detail': 'Imagem do banner deletado com sucesso'})

        self.assertTrue(delete_banner_file_response_mock.call_count == 1)
        call = delete_banner_file_response_mock.calls[0]
        self.assertEqual(call.request.headers['Authorization'], MEMBERS_V2_API_TOKEN)

    @responses.activate
    def test_create_module(self):
        create_module_response_mock = responses.add(
            responses.POST,
            f'{MEMBERS_V2_BASE_URL}/admin/modulo/create',
            json={'id': 'module1', 'cursoId': self.product.membersV2Id, 'name': 'Test Module'},
        )

        module_data = {'name': 'Test Module'}

        response = self.modules_sdk.create_module(self.user, self.product.id, module_data)

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data, {'id': 'module1', 'name': 'Test Module'})

        self.assertTrue(create_module_response_mock.call_count == 1)
        call = create_module_response_mock.calls[0]
        self.assertEqual(call.request.headers['Authorization'], MEMBERS_V2_API_TOKEN)
        self.assertEqual(json.loads(call.request.body), {'name': 'Test Module', 'cursoId': self.product.membersV2Id})  # type:ignore

    @responses.activate
    def test_delete_module(self):
        delete_module_response_mock = responses.add(
            responses.DELETE,
            f'{MEMBERS_V2_BASE_URL}/admin/modulo/delete/module1/curso/{self.product.membersV2Id}',
            json={'success': True},
        )

        response = self.modules_sdk.delete_module(self.user, self.product.id, 'module1')

        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertEqual(response.data, {'detail': 'Módulo deletado com sucesso'})

        self.assertTrue(delete_module_response_mock.call_count == 1)
        call = delete_module_response_mock.calls[0]
        self.assertEqual(call.request.headers['Authorization'], MEMBERS_V2_API_TOKEN)

    @responses.activate
    def test_update_module(self):
        update_module_response_mock = responses.add(
            responses.PUT,
            f'{MEMBERS_V2_BASE_URL}/admin/modulo/update/module1/curso/{self.product.membersV2Id}',
            json={'id': 'module1', 'name': 'Updated Module'},
        )

        module_data = {'name': 'Updated Module'}

        response = self.modules_sdk.update_module(self.user, self.product.id, 'module1', module_data)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data, {'id': 'module1', 'name': 'Updated Module'})

        self.assertTrue(update_module_response_mock.call_count == 1)
        call = update_module_response_mock.calls[0]
        self.assertEqual(call.request.headers['Authorization'], MEMBERS_V2_API_TOKEN)
        self.assertEqual(json.loads(call.request.body), module_data)  # type:ignore

    @responses.activate
    def test_upload_module_cover(self):
        file_content = b'test content'
        file = self.create_test_image(name='test_file.png', content_type='image/png', content=file_content)

        upload_cover_response_mock = responses.add(
            responses.POST,
            f'{MEMBERS_V2_BASE_URL}/admin/uploadCapaModulo/module1/curso/{self.product.membersV2Id}',
            status=200,
            match=[responses.matchers.multipart_matcher(files={'file': (file.name, file_content, file.content_type)})],
        )

        response = self.modules_sdk.upload_module_cover(self.user, self.product.id, 'module1', file)

        self.assertEqual(response.data, {'detail': 'Capa enviada com sucesso'})
        self.assertTrue(upload_cover_response_mock.call_count == 1)
        call = upload_cover_response_mock.calls[0]
        self.assertEqual(call.request.headers['Authorization'], MEMBERS_V2_API_TOKEN)

    @responses.activate
    def test_delete_module_cover(self):
        delete_cover_response_mock = responses.add(
            responses.DELETE,
            f'{MEMBERS_V2_BASE_URL}/admin/deleteCapaModulo/module1/curso/{self.product.membersV2Id}',
            json={'success': True},
        )

        response = self.modules_sdk.delete_module_cover(self.user, self.product.id, 'module1')

        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertEqual(response.data, {'detail': 'Capa deletada com sucesso'})

        self.assertTrue(delete_cover_response_mock.call_count == 1)
        call = delete_cover_response_mock.calls[0]
        self.assertEqual(call.request.headers['Authorization'], MEMBERS_V2_API_TOKEN)

    @responses.activate
    def test_list_module_lessons(self):
        self.mock__get_user_headers()

        list_lessons_response_mock = responses.add(
            responses.GET,
            f'{MEMBERS_V2_BASE_URL}/user/aulas/module1/{self.user.membersV2Id}',
            json=[{'id': 'lesson1', 'name': 'Test Lesson'}],
        )

        response = self.lessons_sdk.list_module_lessons(self.user, 'module1')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data, [{'id': 'lesson1', 'name': 'Test Lesson'}])

        self.assertTrue(list_lessons_response_mock.call_count == 1)
        call = list_lessons_response_mock.calls[0]
        self.assertEqual(call.request.headers['Authorization'], USER_TOKEN_MOCK)

    @responses.activate
    def test_create_module_lesson(self):
        create_lesson_response_mock = responses.add(
            responses.POST,
            f'{MEMBERS_V2_BASE_URL}/admin/aula/create',
            json={'id': 'lesson1', 'moduloId': 'module1', 'name': 'Test Lesson'},
        )

        lesson_data = {'name': 'Test Lesson'}

        expected_response = {
            'name': 'Test Lesson',
            'cursoId': self.product.membersV2Id,
            'moduloId': 'module1'
        }

        response = self.lessons_sdk.create_module_lesson(self.user, self.product.id, 'module1', lesson_data)

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data, {'id': 'lesson1', 'moduloId': 'module1', 'name': 'Test Lesson'})

        self.assertTrue(create_lesson_response_mock.call_count == 1)
        call = create_lesson_response_mock.calls[0]
        self.assertEqual(call.request.headers['Authorization'], MEMBERS_V2_API_TOKEN)
        self.assertEqual(json.loads(call.request.body), expected_response)  # type:ignore

    @responses.activate
    def test_update_module_lesson(self):
        update_lesson_response_mock = responses.add(
            responses.PUT,
            f'{MEMBERS_V2_BASE_URL}/admin/aula/update/lesson1/curso/{self.product.membersV2Id}',
            json={'id': 'lesson1', 'name': 'Updated Lesson'},
        )

        lesson_data = {'name': 'Updated Lesson'}

        response = self.lessons_sdk.update_module_lesson(self.user, self.product.id, 'lesson1', lesson_data)

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data, {'id': 'lesson1', 'name': 'Updated Lesson'})

        self.assertTrue(update_lesson_response_mock.call_count == 1)
        call = update_lesson_response_mock.calls[0]
        self.assertEqual(call.request.headers['Authorization'], MEMBERS_V2_API_TOKEN)
        self.assertEqual(json.loads(call.request.body), lesson_data)  # type:ignore

    @responses.activate
    def test_delete_module_lesson(self):
        delete_lesson_response_mock = responses.add(
            responses.DELETE,
            f'{MEMBERS_V2_BASE_URL}/admin/aula/delete/lesson1/curso/{self.product.membersV2Id}',
            json={'success': True},
        )

        response = self.lessons_sdk.delete_module_lesson(self.user, self.product.id, 'lesson1')

        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertEqual(response.data, {'detail': 'Aula deletada com sucesso'})

        self.assertTrue(delete_lesson_response_mock.call_count == 1)
        call = delete_lesson_response_mock.calls[0]
        self.assertEqual(call.request.headers['Authorization'], MEMBERS_V2_API_TOKEN)

    @responses.activate
    def test_list_lesson_files(self):
        self.mock__get_user_headers()

        list_files_response_mock = responses.add(
            responses.GET,
            f'{MEMBERS_V2_BASE_URL}/user/getAllArquivosAulaforUser/lesson1',
            json=[{'id': 'file1', 'name': 'Test File'}],
        )

        response = self.lessons_sdk.list_lesson_files(self.user, 'lesson1')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data, [{'id': 'file1', 'name': 'Test File'}])

        self.assertTrue(list_files_response_mock.call_count == 1)
        call = list_files_response_mock.calls[0]
        self.assertEqual(call.request.headers['Authorization'], USER_TOKEN_MOCK)

    @responses.activate
    def test_create_lesson_file(self):
        create_file_response_mock = responses.add(
            responses.POST,
            f'{MEMBERS_V2_BASE_URL}/admin/createArquivo/lesson1/curso/{self.product.membersV2Id}',
            json={'id': 'file1', 'name': 'Test File'},
        )

        file_data = {'name': 'Test File'}

        response = self.lessons_sdk.create_lesson_file(self.user, self.product.id, 'lesson1', file_data)

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data, {'id': 'file1', 'name': 'Test File'})

        self.assertTrue(create_file_response_mock.call_count == 1)
        call = create_file_response_mock.calls[0]
        self.assertEqual(call.request.headers['Authorization'], MEMBERS_V2_API_TOKEN)
        self.assertEqual(json.loads(call.request.body), file_data)  # type:ignore

    @responses.activate
    def test_update_lesson_file_data(self):
        update_file_response_mock = responses.add(
            responses.PUT,
            f'{MEMBERS_V2_BASE_URL}/admin/updateDadosArquivoAula/file1/curso/{self.product.membersV2Id}',
            json={'id': 'file1', 'name': 'Updated File'},
        )

        file_data = {'name': 'Updated File'}

        response = self.lessons_sdk.update_lesson_file_data(self.user, self.product.id, 'file1', file_data)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data, {'id': 'file1', 'name': 'Updated File'})

        self.assertTrue(update_file_response_mock.call_count == 1)
        call = update_file_response_mock.calls[0]
        self.assertEqual(call.request.headers['Authorization'], MEMBERS_V2_API_TOKEN)
        self.assertEqual(json.loads(call.request.body), file_data)  # type:ignore

    @responses.activate
    def test_upload_lesson_file_item(self):
        file_content = b'test content'
        file = self.create_test_image(name='test_file.png', content_type='image/png', content=file_content)
        upload_file_response_mock = responses.add(
            responses.POST,
            f'{MEMBERS_V2_BASE_URL}/admin/uploadArquivoAula/file1/curso/{self.product.membersV2Id}',
            status=200,
            match=[responses.matchers.multipart_matcher(files={'file': (file.name, file_content, file.content_type)})],
            json='Uploaded',
        )

        response = self.lessons_sdk.upload_lesson_file_item(self.user, self.product.id, 'file1', file)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data, 'Uploaded')

        self.assertTrue(upload_file_response_mock.call_count == 1)
        call = upload_file_response_mock.calls[0]
        self.assertEqual(call.request.headers['Authorization'], MEMBERS_V2_API_TOKEN)

    @responses.activate
    def test_delete_lesson_file(self):
        delete_file_response_mock = responses.add(
            responses.DELETE,
            f'{MEMBERS_V2_BASE_URL}/admin/deleteArquivoAula/delete/file1/curso/{self.product.membersV2Id}',
            json={'success': True},
        )

        response = self.lessons_sdk.delete_lesson_file(self.user, self.product.id, 'file1')

        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertEqual(response.data, {'detail': 'Arquivo deletado com sucesso'})

        self.assertTrue(delete_file_response_mock.call_count == 1)
        call = delete_file_response_mock.calls[0]
        self.assertEqual(call.request.headers['Authorization'], MEMBERS_V2_API_TOKEN)

    def test_update_membersV2_user_info(self):
        update_user_info_mock = mock.patch('members.sdk.members_v2.MembersV2BaseSDK.update_user_info').start()

        update_membersV2_user_info(self.user)

        update_user_info_mock.assert_called_once_with(self.user)

    def test_add_membersV2_course_to_user(self):
        _add_course_to_user_mock = mock.patch(
            'members.sdk.members_v2.MembersV2BaseSDK._add_course_to_user'
        ).start()

        parse_date_mock = mock.patch(
            'members.sdk.members_v2.parse_and_validate_expiration'
        ).start()

        parse_date_mock.return_value = expires_at = timezone.now() + timezone.timedelta(days=10)

        add_membersV2_course_to_user(self.user, self.product)

        _add_course_to_user_mock.assert_called_once_with(self.user, self.product.membersV2Id, expires_at=expires_at)

    def test_parse_and_validate_expiration_with_correct_date(self):
        expires_at = timezone.now() + timezone.timedelta(days=10)

        parsed_date = parse_and_validate_expiration(expires_at)

        self.assertEqual(parsed_date, expires_at)

    def test_parse_and_validate_expiration_with_wrong_str_format(self):
        with self.assertRaises(
            ValidationError,
            msg={'detail': 'Data de expiração inválida. Formato deve ser ISO 8601, ex: 2021-12-31T23:59:59-03:00'}
        ):
            parse_and_validate_expiration('2021-01-01T000000')

    def test_parse_and_validate_expiration_with_past_date(self):
        with self.assertRaises(
            ValidationError,
            msg={'detail': 'Data de expiração inválida. Deve ser maior que a data atual ou null para acesso por tempo indeterminado.'}
        ):
            parse_and_validate_expiration(timezone.now() - timezone.timedelta(days=1))

    def test_parse_and_validate_expiration_with_null_date(self):
        parsed_date = parse_and_validate_expiration(None)

        self.assertIsNone(parsed_date)

    def test_parse_and_validate_expiration_with_not_aware_date(self):
        expires_at = (timezone.now() + timezone.timedelta(days=10)).strftime('%Y-%m-%d %H:%M:%S')

        parsed_date = parse_and_validate_expiration(expires_at)

        expected_date = timezone.make_aware(timezone.datetime.fromisoformat(expires_at))

        self.assertEqual(parsed_date, expected_date)

    def test_remove_membersV2_user_access(self):
        _remove_course_from_user_mock = mock.patch(
            'members.sdk.members_v2.MembersV2BaseSDK._remove_course_from_user'
        ).start()

        product = self.create_product(membersV2Id='123')

        remove_membersV2_user_access(self.user, product)

        _remove_course_from_user_mock.assert_called_once_with(self.user, product.membersV2Id)

    def test_remove_membersV2_user_access__without_product_membersV2Id(self):
        _remove_course_from_user_mock = mock.patch(
            'members.sdk.members_v2.MembersV2BaseSDK._remove_course_from_user'
        ).start()

        product = self.create_product(membersV2Id=None)

        response = remove_membersV2_user_access(self.user, product)

        _remove_course_from_user_mock.assert_not_called()
        self.assertEqual(response, 'Product does not have a Members V2 ID.')

    def test_remove_membersV2_user_access__without_user_membersV2Id(self):
        _remove_course_from_user_mock = mock.patch(
            'members.sdk.members_v2.MembersV2BaseSDK._remove_course_from_user'
        ).start()

        product = self.create_product(membersV2Id='123')

        self.user.membersV2Id = None
        self.user.save(update_fields=['membersV2Id'])

        response = remove_membersV2_user_access(self.user, product)

        _remove_course_from_user_mock.assert_not_called()
        self.assertEqual(response, 'User does not have a Members V2 ID.')

    def test_remove_membersV2_user_access__without_user_equals_product_owner_user(self):
        _remove_course_from_user_mock = mock.patch(
            'members.sdk.members_v2.MembersV2BaseSDK._remove_course_from_user'
        ).start()

        product = self.create_product(user=self.user, membersV2Id='123')

        response = remove_membersV2_user_access(self.user, product)

        _remove_course_from_user_mock.assert_not_called()
        self.assertEqual(response, 'User is the owner of the product.')

    def test_get_user_membersV2_token_cached(self):
        _get_cached_user_token_mock = self.mock__get_cached_user_token()
        _get_cached_user_token_mock.return_value = 'token1'

        response = get_user_membersV2_token(self.user, get_cached_token=True)

        self.assertEqual(response, 'token1')

    def test_get_user_membersV2_token_not_cached(self):
        _login_user_mock = self.mock__login_user()
        _login_user_mock.return_value = 'token2'

        response = get_user_membersV2_token(self.user, get_cached_token=False)

        self.assertEqual(response, 'token2')

class MembersV2SerializersTests(BaseTestCase):
    def test_MemberV2CourseSerializer(self):
        data = {'nome': 'Test Course'}
        serializer = MemberV2CourseSerializer(data=data)
        self.assertTrue(serializer.is_valid())
        self.assertEqual(serializer.validated_data, data)

        serializer = MemberV2CourseSerializer(data={})
        self.assertFalse(serializer.is_valid())

    def test_MemberV2CourseSerializer_with_invalid_data(self):
        serializer = MemberV2CourseSerializer(data={})
        self.assertFalse(serializer.is_valid())
        self.assertIn('nome', serializer.errors)

    def test_MemberV2ImageSerializer(self):
        image_file = self.create_test_image()
        data = {'image': image_file}
        serializer = MemberV2ImageSerializer(data=data)
        self.assertTrue(serializer.is_valid(), serializer.errors)
        self.assertEqual(serializer.validated_data['image'], image_file)  # type:ignore

    def test_MemberV2ImageSerializer_with_invalid_size(self):
        image_file = self.create_large_test_image((1024 * 1024 * settings.MEMBERS_V2_MAX_IMAGE_SIZE) + 10)
        data = {'image': image_file}
        serializer = MemberV2ImageSerializer(data=data)
        self.assertFalse(serializer.is_valid())
        self.assertIn('image', serializer.errors)

    def test_MemberV2ImageSerializer_without_data(self):
        serializer = MemberV2ImageSerializer(data={})
        self.assertFalse(serializer.is_valid())
        self.assertIn('image', serializer.errors)

    def test_MemberV2BannerSerializer(self):
        data = {'titulo': 'Test Banner', 'descricao': 'Description', 'posicao': 1}
        serializer = MemberV2BannerSerializer(data=data)
        self.assertTrue(serializer.is_valid())
        self.assertEqual(serializer.validated_data, data)

    def test_MemberV2BannerSerializer_with_wrong_data(self):
        serializer = MemberV2BannerSerializer(data={})
        self.assertFalse(serializer.is_valid())
        self.assertIn('titulo', serializer.errors)
        self.assertNotIn('descricao', serializer.errors)
        self.assertIn('posicao', serializer.errors)

    def test_MemberV2ModuleSerializer(self):
        data = {'nome': 'Test Module', 'posicao': 1}
        serializer = MemberV2ModuleSerializer(data=data)
        self.assertTrue(serializer.is_valid())
        self.assertEqual(serializer.validated_data, data)

    def test_MemberV2ModuleSerializer_with_wrong_data(self):
        serializer = MemberV2ModuleSerializer(data={})
        self.assertFalse(serializer.is_valid())
        self.assertIn('nome', serializer.errors)
        self.assertIn('posicao', serializer.errors)

    def test_MemberV2LessonSerializer(self):
        data = {
            'nome': 'Test Lesson',
            'descricao': 'Description',
            'posicao': 1,
            'duracao': '10:00',
            'urlVideo': 'http://example.com',
            'moduloId': 'module1'
        }
        serializer = MemberV2LessonSerializer(data=data)
        self.assertTrue(serializer.is_valid())
        self.assertEqual(serializer.validated_data, data)

    def test_MemberV2LessonSerializer_with_wrong_data(self):
        serializer = MemberV2LessonSerializer(data={})
        self.assertFalse(serializer.is_valid())
        self.assertIn('nome', serializer.errors)
        self.assertNotIn('descricao', serializer.errors)
        self.assertIn('posicao', serializer.errors)
        self.assertNotIn('duracao', serializer.errors)
        self.assertNotIn('urlVideo', serializer.errors)
        self.assertNotIn('moduloId', serializer.errors)

    def test_MemberV2LessonFileSerializer(self):
        data = {'titulo': 'Test File'}
        serializer = MemberV2LessonFileSerializer(data=data)
        self.assertTrue(serializer.is_valid())
        self.assertEqual(serializer.validated_data, data)

    def test_MemberV2LessonFileSerializer_with_wrong_data(self):
        serializer = MemberV2LessonFileSerializer(data={})
        self.assertFalse(serializer.is_valid())
        self.assertIn('titulo', serializer.errors)

    def test_MemberV2LessonFileUploadSerializer(self):
        file_item = self.create_test_image()
        data = {'file': file_item}
        serializer = MemberV2LessonFileUploadSerializer(data=data)
        self.assertTrue(serializer.is_valid())
        self.assertEqual(serializer.validated_data['file'], file_item)  # type:ignore

    def test_MemberV2LessonFileUploadSerializer_invalid_size(self):
        image_file = self.create_large_test_image((1024 * 1024 * settings.MEMBERS_V2_MAX_IMAGE_SIZE) + 10)
        data = {'file': image_file}
        serializer = MemberV2LessonFileUploadSerializer(data=data)
        self.assertFalse(serializer.is_valid())
        self.assertIn('file', serializer.errors)

    def test_MemberV2LessonFileUploadSerializer_without_data(self):
        serializer = MemberV2LessonFileUploadSerializer(data={})
        self.assertFalse(serializer.is_valid())
        self.assertIn('file', serializer.errors)

    def test_MembersV2GiveAccessSerializer(self):
        data = {'email': '<EMAIL>', 'finalTime': '2023-12-31T23:59:59Z'}
        serializer = MembersV2GiveAccessSerializer(data=data)
        self.assertTrue(serializer.is_valid())
        self.assertEqual(serializer.validated_data['email'], '<EMAIL>')  # type:ignore
        self.assertEqual(serializer.validated_data['finalTime'], timezone.datetime.fromisoformat('2023-12-31T23:59:59Z'))  # type:ignore

    def test_MembersV2GiveAccessSerializer_lifetime(self):
        data = {'email': '<EMAIL>', 'finalTime': None}
        serializer = MembersV2GiveAccessSerializer(data=data)
        self.assertTrue(serializer.is_valid())
        self.assertEqual(serializer.validated_data['email'], '<EMAIL>')  # type:ignore
        self.assertIsNone(serializer.validated_data['finalTime'])  # type:ignore

    def test_MembersV2GiveAccessSerializer_invalid_date(self):
        data = {'email': '<EMAIL>', 'finalTime': 'invalid-date'}
        serializer = MembersV2GiveAccessSerializer(data=data)
        self.assertFalse(serializer.is_valid())
        self.assertIn('finalTime', serializer.errors)

    def test_MembersV2GiveAccessSerializer_without_data(self):
        serializer = MembersV2GiveAccessSerializer(data={})
        self.assertFalse(serializer.is_valid())
        self.assertIn('email', serializer.errors)
        self.assertNotIn('finalTime', serializer.errors)

class MembersV2ViewsTests(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()

        cls.user.membersV2Id = 'id_user_9876'
        cls.user.save(update_fields=['membersV2Id'])

        cls.product = cls.create_product(user=cls.user, membersV2Id='id_product_1234')

    def test_course_list_api_view(self):
        url = reverse('members-v2-list')

        list_courses_mock = mock.patch(
            'members.views.MembersV2CourseSDK.list_courses'
        ).start()
        list_courses_mock.return_value = Response({'test': 'test'})

        response = self.client.get(url, headers=self.build_user_auth_headers(self.user))

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data, {'test': 'test'})
        list_courses_mock.assert_called_once_with(user=self.user)

    def test_course_retrieve_api_view(self):
        url = reverse('members-v2-detail', args=[self.product.id])

        retrieve_course_mock = mock.patch(
            'members.views.MembersV2CourseSDK.retrieve_course'
        ).start()
        retrieve_course_mock.return_value = Response({'name': 'Test Course'})

        response = self.client.get(url, headers=self.build_user_auth_headers(self.user))

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data, {'name': 'Test Course'})
        retrieve_course_mock.assert_called_once_with(user=self.user, product_id=self.product.id)

    def test_course_create_api_view(self):
        url = reverse('members-v2-detail', args=[self.product.id])

        create_course_mock = mock.patch(
            'members.views.MembersV2CourseSDK.create_course'
        ).start()
        create_course_mock.return_value = Response('test_response', status=status.HTTP_201_CREATED)

        response = self.client.post(url, headers=self.build_user_auth_headers(self.user))

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data, 'test_response')
        create_course_mock.assert_called_once_with(user=self.user, product_id=self.product.id)

    def test_course_update_api_view(self):
        url = reverse('members-v2-detail', args=[self.product.id])
        update_data = {'nome': 'Updated Course Name'}

        update_course_mock = mock.patch(
            'members.views.MembersV2CourseSDK.update_course'
        ).start()
        update_course_mock.return_value = Response('test_response')

        response = self.client.put(url, data=update_data, headers=self.build_user_auth_headers(self.user))

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())
        self.assertEqual(response.data, 'test_response')
        update_course_mock.assert_called_once_with(user=self.user, product_id=self.product.id, data=update_data)

    def test_course_destroy_api_view(self):
        url = reverse('members-v2-detail', args=[self.product.id])

        delete_course_mock = mock.patch(
            'members.views.MembersV2CourseSDK.delete_course'
        ).start()
        delete_course_mock.return_value = Response('test_response', status=status.HTTP_204_NO_CONTENT)

        response = self.client.delete(url, headers=self.build_user_auth_headers(self.user))

        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertEqual(response.data, 'test_response')
        delete_course_mock.assert_called_once_with(user=self.user, product_id=self.product.id)

    def test_course_upload_logo_api_view(self):
        url = reverse('members-v2-logo', args=[self.product.id])
        file = self.create_test_image(name='test_file.png', content_type='image/png')

        upload_logo_mock = mock.patch(
            'members.views.MembersV2CourseSDK.upload_course_logo'
        ).start()
        upload_logo_mock.return_value = Response('test_response')

        response = self.client.post(url, data={'image': file}, format='multipart', headers=self.build_user_auth_headers(self.user))

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())
        self.assertEqual(response.data, 'test_response')
        upload_logo_mock.assert_called_once()

        call_args = upload_logo_mock.call_args
        self.assertEqual(call_args.kwargs['user'], self.user)
        self.assertEqual(call_args.kwargs['product_id'], self.product.id)
        self.assertIsInstance(call_args.kwargs['logo'], InMemoryUploadedFile)

    def test_course_delete_logo_api_view(self):
        url = reverse('members-v2-logo', args=[self.product.id])

        delete_logo_mock = mock.patch(
            'members.views.MembersV2CourseSDK.delete_course_logo'
        ).start()
        delete_logo_mock.return_value = Response('test_response', status=status.HTTP_204_NO_CONTENT)

        response = self.client.delete(url, headers=self.build_user_auth_headers(self.user))

        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertEqual(response.data, 'test_response')
        delete_logo_mock.assert_called_once_with(user=self.user, product_id=self.product.id)

    def test_course_add_user_access_api_view(self):
        url = reverse('members-v2-user-access', args=[self.product.id])
        data = {'email': '<EMAIL>', 'finalTime': '2023-12-31T23:59:59Z'}

        expected_expires_at = timezone.datetime.fromisoformat(data['finalTime'])

        add_membersV2_course_to_user_mock = mock.patch(
            'members.views.add_membersV2_course_to_user'
        ).start()

        response = self.client.post(url, data=data, headers=self.build_user_auth_headers(self.user))

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data, {'detail': 'Acesso ao curso concedido com sucesso.'})
        add_membersV2_course_to_user_mock.assert_called_once_with(
            user=self.user,
            product=self.product, expires_at=expected_expires_at
        )

    def test_course_remove_user_access_api_view(self):
        another_user = self.create_user()

        url = reverse('members-v2-user-access', args=[self.product.id]) + f'?email={another_user.email}'

        remove_membersV2_user_access_mock = mock.patch(
            'members.views.remove_membersV2_user_access'
        ).start()

        response = self.client.delete(url, headers=self.build_user_auth_headers(self.user))

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())
        self.assertEqual(response.data, {'detail': 'Acesso ao curso removido com sucesso.'})
        remove_membersV2_user_access_mock.assert_called_once_with(user=another_user, product=self.product)

    def test_list_banners(self):
        url = reverse('members-v2-banners', args=[self.product.id])

        list_course_banners_mock = mock.patch(
            'members.views.MembersV2BannersSDK.list_course_banners'
        ).start()
        list_course_banners_mock.return_value = Response('test_response')

        response = self.client.get(url, headers=self.build_user_auth_headers(self.user))

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data, 'test_response')
        list_course_banners_mock.assert_called_once_with(user=self.user, product_id=self.product.id)

    def test_create_banner(self):
        url = reverse('members-v2-banners', args=[self.product.id])
        banner_data = {'titulo': 'Test Banner', 'descricao': 'Description', 'posicao': 1}

        create_course_banner_mock = mock.patch(
            'members.views.MembersV2BannersSDK.create_course_banner'
        ).start()
        create_course_banner_mock.return_value = Response('test_response', status=status.HTTP_201_CREATED)

        response = self.client.post(url, data=banner_data, headers=self.build_user_auth_headers(self.user))

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data, 'test_response')
        create_course_banner_mock.assert_called_once_with(
            user=self.user,
            product_id=self.product.id,
            data=banner_data
        )

    def test_update_banner(self):
        url = reverse('members-v2-banners-detail', args=[self.product.id, 'banner1'])
        banner_data = {'titulo': 'Updated Banner', 'descricao': 'Updated Description', 'posicao': 2}

        update_course_banner_mock = mock.patch(
            'members.views.MembersV2BannersSDK.update_course_banner'
        ).start()
        update_course_banner_mock.return_value = Response('test_response')

        response = self.client.put(url, data=banner_data, headers=self.build_user_auth_headers(self.user))

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data, 'test_response')
        update_course_banner_mock.assert_called_once_with(
            user=self.user,
            product_id=self.product.id,
            banner_id='banner1',
            data=banner_data
        )

    def test_delete_banner(self):
        url = reverse('members-v2-banners-detail', args=[self.product.id, 'banner1'])

        delete_course_banner_mock = mock.patch(
            'members.views.MembersV2BannersSDK.delete_course_banner'
        ).start()
        delete_course_banner_mock.return_value = Response('test_response', status=status.HTTP_204_NO_CONTENT)

        response = self.client.delete(url, headers=self.build_user_auth_headers(self.user))

        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertEqual(response.data, 'test_response')
        delete_course_banner_mock.assert_called_once_with(
            user=self.user,
            product_id=self.product.id,
            banner_id='banner1'
        )

    def test_upload_banner_image(self):
        url = reverse('members-v2-banners-image', args=[self.product.id, 'banner1'])
        file = self.create_test_image(name='test_file.png', content_type='image/png')

        upload_course_banner_file_mock = mock.patch(
            'members.views.MembersV2BannersSDK.upload_course_banner_file'
        ).start()
        upload_course_banner_file_mock.return_value = Response('test_response')

        response = self.client.post(
            url,
            data={'image': file},
            format='multipart',
            headers=self.build_user_auth_headers(self.user)
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())
        self.assertEqual(response.data, 'test_response')

        call_args = upload_course_banner_file_mock.call_args
        self.assertEqual(call_args.kwargs['user'], self.user)
        self.assertEqual(call_args.kwargs['product_id'], self.product.id)
        self.assertEqual(call_args.kwargs['banner_id'], 'banner1')
        self.assertIsInstance(call_args.kwargs['banner'], InMemoryUploadedFile)

    def test_delete_banner_image(self):
        url = reverse('members-v2-banners-image', args=[self.product.id, 'banner1'])

        delete_course_banner_file_mock = mock.patch(
            'members.views.MembersV2BannersSDK.delete_course_banner_file'
        ).start()
        delete_course_banner_file_mock.return_value = Response('test_response', status=status.HTTP_204_NO_CONTENT)

        response = self.client.delete(url, headers=self.build_user_auth_headers(self.user))

        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertEqual(response.data, 'test_response')
        delete_course_banner_file_mock.assert_called_once_with(
            user=self.user,
            product_id=self.product.id,
            banner_id='banner1'
        )

    def test_create_module(self):
        url = reverse('members-v2-modules', args=[self.product.id])
        module_data = {'nome': 'Test Module', 'posicao': 1}

        create_module_mock = mock.patch(
            'members.views.MembersV2ModulesSDK.create_module'
        ).start()
        create_module_mock.return_value = Response('test_response', status=status.HTTP_201_CREATED)

        response = self.client.post(url, data=module_data, headers=self.build_user_auth_headers(self.user))

        self.assertEqual(response.status_code, status.HTTP_201_CREATED, response.content.decode())
        self.assertEqual(response.data, 'test_response')
        create_module_mock.assert_called_once_with(
            user=self.user,
            product_id=self.product.id,
            data=module_data
        )

    def test_udpate_module(self):
        url = reverse('members-v2-modules-detail', args=[self.product.id, 'module1'])
        module_data = {'nome': 'Updated Module', 'posicao': 2}

        update_module_mock = mock.patch(
            'members.views.MembersV2ModulesSDK.update_module'
        ).start()
        update_module_mock.return_value = Response('test_response')

        response = self.client.put(url, data=module_data, headers=self.build_user_auth_headers(self.user))

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())
        self.assertEqual(response.data, 'test_response')
        update_module_mock.assert_called_once_with(
            user=self.user,
            product_id=self.product.id,
            module_id='module1',
            data=module_data
        )

    def test_delete_module(self):
        url = reverse('members-v2-modules-detail', args=[self.product.id, 'module1'])

        delete_module_mock = mock.patch(
            'members.views.MembersV2ModulesSDK.delete_module'
        ).start()
        delete_module_mock.return_value = Response('test_response', status=status.HTTP_204_NO_CONTENT)

        response = self.client.delete(url, headers=self.build_user_auth_headers(self.user))

        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertEqual(response.data, 'test_response')
        delete_module_mock.assert_called_once_with(
            user=self.user,
            product_id=self.product.id,
            module_id='module1'
        )

    def test_upload_module_cover(self):
        url = reverse('members-v2-modules-cover', args=[self.product.id, 'module1'])
        file = self.create_test_image(name='test_file.png', content_type='image/png')

        upload_module_cover_mock = mock.patch(
            'members.views.MembersV2ModulesSDK.upload_module_cover'
        ).start()
        upload_module_cover_mock.return_value = Response('test_response')

        response = self.client.post(
            url,
            data={'image': file},
            format='multipart',
            headers=self.build_user_auth_headers(self.user)
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())
        self.assertEqual(response.data, 'test_response')

        call_args = upload_module_cover_mock.call_args
        self.assertEqual(call_args.kwargs['user'], self.user)
        self.assertEqual(call_args.kwargs['product_id'], self.product.id)
        self.assertEqual(call_args.kwargs['module_id'], 'module1')
        self.assertIsInstance(call_args.kwargs['cover'], InMemoryUploadedFile)

    def test_delete_module_cover(self):
        url = reverse('members-v2-modules-cover', args=[self.product.id, 'module1'])

        delete_module_cover_mock = mock.patch(
            'members.views.MembersV2ModulesSDK.delete_module_cover'
        ).start()
        delete_module_cover_mock.return_value = Response('test_response', status=status.HTTP_204_NO_CONTENT)

        response = self.client.delete(url, headers=self.build_user_auth_headers(self.user))

        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertEqual(response.data, 'test_response')
        delete_module_cover_mock.assert_called_once_with(
            user=self.user,
            product_id=self.product.id,
            module_id='module1'
        )

    def test_members_v2_token_api(self):
        url = reverse('members-v2-token')

        get_user_membersV2_token_mock = mock.patch(
            'members.views.get_user_membersV2_token'
        ).start()
        get_user_membersV2_token_mock.return_value = 'test_token'

        response = self.client.get(url, headers=self.build_user_auth_headers(self.user))

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data, {'token': 'test_token'})
        get_user_membersV2_token_mock.assert_called_once_with(user=self.user, get_cached_token=False)

    def test_list_lesson(self):
        url = reverse('members-v2-lessons', args=[self.product.id, 'module1'])

        list_lessons_mock = mock.patch(
            'members.views.MembersV2LessonsSDK.list_module_lessons'
        ).start()
        list_lessons_mock.return_value = Response('test_response')

        response = self.client.get(url, headers=self.build_user_auth_headers(self.user))

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data, 'test_response')
        list_lessons_mock.assert_called_once_with(user=self.user, module_id='module1')

    def test_create_lesson(self):
        url = reverse('members-v2-lessons', args=[self.product.id, 'module1'])
        lesson_data = {'nome': 'Test Lesson', 'posicao': 1}

        create_lesson_mock = mock.patch(
            'members.views.MembersV2LessonsSDK.create_module_lesson'
        ).start()
        create_lesson_mock.return_value = Response('test_response', status=status.HTTP_201_CREATED)

        response = self.client.post(url, data=lesson_data, headers=self.build_user_auth_headers(self.user))

        self.assertEqual(response.status_code, status.HTTP_201_CREATED, response.content.decode())
        self.assertEqual(response.data, 'test_response')
        create_lesson_mock.assert_called_once_with(
            user=self.user,
            product_id=self.product.id,
            module_id='module1',
            data=lesson_data
        )

    def test_update_lesson(self):
        url = reverse('members-v2-lessons-detail', args=[self.product.id, 'lesson1'])
        lesson_data = {'nome': 'Updated Lesson', 'posicao': 2}

        update_lesson_mock = mock.patch(
            'members.views.MembersV2LessonsSDK.update_module_lesson'
        ).start()
        update_lesson_mock.return_value = Response('test_response')

        response = self.client.put(url, data=lesson_data, headers=self.build_user_auth_headers(self.user))

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())
        self.assertEqual(response.data, 'test_response')
        update_lesson_mock.assert_called_once_with(
            user=self.user,
            product_id=self.product.id,
            lesson_id='lesson1',
            data=lesson_data
        )

    def test_delete_lesson(self):
        url = reverse('members-v2-lessons-detail', args=[self.product.id, 'lesson1'])

        delete_lesson_mock = mock.patch(
            'members.views.MembersV2LessonsSDK.delete_module_lesson'
        ).start()
        delete_lesson_mock.return_value = Response('test_response', status=status.HTTP_204_NO_CONTENT)

        response = self.client.delete(url, headers=self.build_user_auth_headers(self.user))

        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertEqual(response.data, 'test_response')
        delete_lesson_mock.assert_called_once_with(
            user=self.user,
            product_id=self.product.id,
            lesson_id='lesson1'
        )

    def test_list_lesson_files(self):
        url = reverse('members-v2-lessons-files', args=[self.product.id, 'lesson1'])

        list_files_mock = mock.patch(
            'members.views.MembersV2LessonsSDK.list_lesson_files'
        ).start()
        list_files_mock.return_value = Response('test_response')

        response = self.client.get(url, headers=self.build_user_auth_headers(self.user))

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data, 'test_response')
        list_files_mock.assert_called_once_with(user=self.user, lesson_id='lesson1')

    def test_create_lesson_file(self):
        url = reverse('members-v2-lessons-files', args=[self.product.id, 'lesson1'])
        file_data = {'titulo': 'Test File'}

        create_file_mock = mock.patch(
            'members.views.MembersV2LessonsSDK.create_lesson_file'
        ).start()
        create_file_mock.return_value = Response('test_response', status=status.HTTP_201_CREATED)

        response = self.client.post(url, data=file_data, headers=self.build_user_auth_headers(self.user))

        self.assertEqual(response.status_code, status.HTTP_201_CREATED, response.content.decode())
        self.assertEqual(response.data, 'test_response')
        create_file_mock.assert_called_once_with(
            user=self.user,
            product_id=self.product.id,
            lesson_id='lesson1',
            data=file_data
        )

    def test_update_lesson_file_data(self):
        url = reverse('members-v2-lesson-files-detail', args=[self.product.id, 'file1'])
        file_data = {'titulo': 'Updated File'}

        update_file_mock = mock.patch(
            'members.views.MembersV2LessonsSDK.update_lesson_file_data'
        ).start()
        update_file_mock.return_value = Response('test_response')

        response = self.client.put(url, data=file_data, headers=self.build_user_auth_headers(self.user))

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())
        self.assertEqual(response.data, 'test_response')
        update_file_mock.assert_called_once_with(
            user=self.user,
            product_id=self.product.id,
            file_id='file1',
            data=file_data
        )

    def test_upload_lesson_file_item(self):
        url = reverse('members-v2-lesson-files-upload', args=[self.product.id, 'file1'])
        file = self.create_test_image(name='test_file.png', content_type='image/png')

        upload_file_mock = mock.patch(
            'members.views.MembersV2LessonsSDK.upload_lesson_file_item'
        ).start()
        upload_file_mock.return_value = Response('test_response')

        response = self.client.post(
            url,
            data={'file': file},
            format='multipart',
            headers=self.build_user_auth_headers(self.user)
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())
        self.assertEqual(response.data, 'test_response')

        call_args = upload_file_mock.call_args
        self.assertEqual(call_args.kwargs['user'], self.user)
        self.assertEqual(call_args.kwargs['product_id'], self.product.id)
        self.assertEqual(call_args.kwargs['file_id'], 'file1')
        self.assertIsInstance(call_args.kwargs['file_item'], InMemoryUploadedFile)

    def test_delete_lesson_file_item(self):
        url = reverse('members-v2-lesson-files-detail', args=[self.product.id, 'file1'])

        delete_file_mock = mock.patch(
            'members.views.MembersV2LessonsSDK.delete_lesson_file'
        ).start()
        delete_file_mock.return_value = Response('test_response', status=status.HTTP_204_NO_CONTENT)

        response = self.client.delete(url, headers=self.build_user_auth_headers(self.user))

        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertEqual(response.data, 'test_response')
        delete_file_mock.assert_called_once_with(
            user=self.user,
            product_id=self.product.id,
            file_id='file1'
        )

class MembersV2UtilityTests(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.credit_card, _ = PaymentMethod.objects.get_or_create(type='credit_card', defaults={'name': 'Cartão de Crédito'})

        cls.user.membersV2Id = 'id_user_9876'
        cls.user.save(update_fields=['membersV2Id'])

        cls.product = cls.create_product(user=cls.user, membersV2Id='id_product_1234')
        cls.cakto, _ = ContentDelivery.objects.get_or_create(type='cakto', defaults={'name': 'Área de membros cakto'})
        cls.product.contentDeliveries.add(cls.cakto)

    def test_handle_purchase_course_access_calls_add_membersV2_course_to_user(self):
        add_membersV2_course_to_user_mock = mock.patch(
            'financial.utils.add_membersV2_course_to_user.delay'
        ).start()

        order = self.create_order(product=self.product)
        handle_purchase_course_access(order=order, user=self.user)

        add_membersV2_course_to_user_mock.assert_called_once_with(
            user=self.user,
            product=self.product,
            expires_at=mock.ANY
        )

    def test_handle_remove_user_course_access_calls_remove_membersV2_user_access(self):
        remove_membersV2_user_access_mock = mock.patch(
            'financial.utils.remove_membersV2_user_access'
        ).start()
        remove_member_v1_mock = mock.patch('financial.utils.remove_user_course_access').start()
        remove_member_v1_mock.return_value = None, None

        customer_user = self.create_user()
        customer = self.create_customer(email=customer_user.email)
        order = self.create_order(product=self.product, customer=customer)
        handle_members_course_removal_access(order=order)

        remove_membersV2_user_access_mock.assert_called_once_with(
            user=customer_user,
            product=self.product
        )

    def test_handle_chargeback_calls_remove_membersV2_user_access(self):
        remove_membersV2_user_access_mock = mock.patch(
            'financial.utils.remove_membersV2_user_access'
        ).start()

        mock.patch(
            'financial.utils.handle_members_course_removal_access.delay',
            handle_members_course_removal_access
        ).start()

        mock.patch('financial.utils.dispatch_event').start()

        remove_member_v1_mock = mock.patch('financial.utils.remove_user_course_access').start()
        remove_member_v1_mock.return_value = None, None

        customer_user = self.create_user()
        customer = self.create_customer(email=customer_user.email)
        order = self.create_order(product=self.product, customer=customer)

        handle_chargeback(order=order, payment=mock.MagicMock(), webhook_data={})

        remove_membersV2_user_access_mock.assert_called_once_with(
            user=customer_user,
            product=self.product
        )

class MembersV2CustomMembersV2Session(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()

        cls.user.membersV2Id = 'id_user_9876'
        cls.user.save(update_fields=['membersV2Id'])

    def setUp(self):
        self.session = CustomMembersV2Session(timeout=5)

    @responses.activate
    def test_request_success(self):
        url = f'{MEMBERS_V2_BASE_URL}/test'
        responses.add(responses.GET, url, json={'success': True}, status=200)

        response = self.session.request('GET', url)

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json(), {'success': True})

    @responses.activate
    def test_request_client_error(self):
        url = f'{MEMBERS_V2_BASE_URL}/test'
        responses.add(responses.GET, url, json={'detail': 'Client error'}, status=400)

        with self.assertRaises(ValidationError):
            self.session.request('GET', url)

    @responses.activate
    def test_request_server_error(self):
        url = f'{MEMBERS_V2_BASE_URL}/test'
        responses.add(responses.GET, url, json={'detail': 'Server error'}, status=500)

        with self.assertRaises(Exception):
            self.session.request('GET', url)

    @responses.activate
    def test_request_invalid_token(self):
        url = f'{MEMBERS_V2_BASE_URL}/test'
        responses.add(responses.GET, url, json={'message': 'Token inválido ou erro ao verificar.'}, status=401)

        cache.set(f'{TOKEN_CACHE_KEY_PREFIX}{self.user.membersV2Id}', 'old_token')

        login_user_mock = mock.patch('members.sdk.members_v2.MembersV2BaseSDK._login_user').start()
        login_user_mock.return_value = 'new_token'

        response = self.session.request('GET', url, authUser=self.user)

        self.assertEqual(cache.get(f'{TOKEN_CACHE_KEY_PREFIX}{self.user.membersV2Id}'), 'new_token')
        response.request.headers['Authorization'] = 'new_token'

    @responses.activate
    def test_request_invalid_token_no_auth_user(self):
        url = f'{MEMBERS_V2_BASE_URL}/test'
        responses.add(responses.GET, url, json={'message': 'Token inválido ou erro ao verificar.'}, status=401)

        with self.assertRaises(ValidationError, msg={'message': 'Token inválido ou erro ao verificar.'}):
            self.session.request('GET', url)

    def test_raise_exception_error(self):
        response_mock = mock.Mock()
        response_mock.request.url = f'{MEMBERS_V2_BASE_URL}/test'
        response_mock.status_code = 500
        response_mock.content.decode.return_value = 'Server error'

        with self.assertRaises(Exception) as context:
            self.session._raise_exception_error(response_mock)

        self.assertIn('Error making request to Members V2 on /test', str(context.exception))
        self.assertIn('status_code 500', str(context.exception))
        self.assertIn('response: Server error', str(context.exception))

    def test_raise_validation_error(self):
        response_mock = mock.Mock()
        response_mock.json.return_value = {'detail': 'Validation error'}
        response_mock.status_code = 400

        with self.assertRaises(ValidationError) as context:
            self.session._raise_validation_error(response_mock)

        self.assertEqual(context.exception.detail, {'detail': 'Validation error'})
        self.assertEqual(context.exception.status_code, 400)
