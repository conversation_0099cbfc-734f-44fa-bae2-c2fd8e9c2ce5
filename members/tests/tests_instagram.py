import hashlib
import json
from unittest import mock

import requests
import responses
from django.urls import reverse
from django.utils import timezone

from cakto.tests.base import BaseTestCase
from financial.utils import add_external_accesses, handle_purchase_course_access
from members.sdk.instagram import (
    Instagram,
    remove_instagram_access_from_order,
    remove_instagram_expired_accesses,
    remove_instagram_expired_job,
)
from product.models import ContentDelivery, DeliveryAccess, InstragramApiHistory, ProductDelivery


class TestInstagramSDK(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.create_paymentMethods()

        cls.instagram_pp, _ = ContentDelivery.objects.get_or_create(type='instagram_pp', name='Instagram Perfil Privado')
        cls.instagram_cf, _ = ContentDelivery.objects.get_or_create(type='instagram_cf', name='Instagram Close Friends')
        cls.product = cls.create_product()

    def test_instagram_sdk_url(self):
        instagram = Instagram()
        self.assertIsNotNone(instagram.url)

    def test_instagram_sdk_token(self):
        instagram = Instagram()
        self.assertIsNotNone(instagram.token)

    def test_instagram_sdk_api(self):
        instagram = Instagram()
        self.assertIsInstance(instagram.api, requests.Session)

    def test_instagram_sdk_headers_are_set(self):
        instagram = Instagram()
        self.assertIn('Authorization', instagram.api.headers)

    @responses.activate
    def test_instagram_sdk_login(self):
        instagram = Instagram()

        response_mock = responses.add(
            responses.POST, instagram.url + '/accounts/login',
            json={'test': 'testing'},
            status=200
        )

        instagram.login(username='testinguser', password='testingpassword', user=self.user)  # noqa: S106

        self.assertEqual(
            json.loads(response_mock.calls[0].request.body),  # type: ignore
            {'username': 'testinguser',
             'password': 'testingpassword', }
        )
        self.assertEqual(InstragramApiHistory.objects.count(), 1)
        history = InstragramApiHistory.objects.first()
        self.assertEqual(history.action, 'login')  # type: ignore
        self.assertEqual(
            history.payload,  # type: ignore
            {
                'username': 'testinguser',
                'password': hashlib.sha256('testingpassword'.encode()).hexdigest()
            }
        )
        self.assertEqual(history.response, {'test': 'testing'})  # type: ignore
        self.assertEqual(history.response_status_code, 200)  # type: ignore

    @responses.activate
    def test_instagram_sdk_create_delivery(self):
        product_delivery = self.create_productDelivery_instagram_pp()
        delivery_access = DeliveryAccess.objects.create(
            user=self.create_user(),
            productDelivery=product_delivery,
            order=self.create_order(),
            status='active'
        )

        instagram = Instagram()

        response_mock = responses.add(
            responses.POST, instagram.url + '/requests',
            json={},
            status=200
        )

        create_delivery_response = instagram.create_delivery(
            delivery_access,
            'testingrequester',
            'FOLLOW'
        )

        self.assertEqual(create_delivery_response.status_code, 200, create_delivery_response.text)

        self.assertEqual(
            json.loads(response_mock.calls[0].request.body),  # type: ignore
            {'username': 'testinguser',
             'requester': 'testingrequester',
             'type': 'FOLLOW'}
        )
        self.assertEqual(InstragramApiHistory.objects.count(), 1)
        history = InstragramApiHistory.objects.first()
        self.assertEqual(history.deliveryAccess, delivery_access)  # type: ignore
        self.assertEqual(history.action, 'create_delivery:FOLLOW')  # type:ignore
        self.assertEqual(
            history.payload,  # type: ignore
            {'username': 'testinguser', 'requester': 'testingrequester', 'type': 'FOLLOW'}
        )
        self.assertEqual(history.response, {})  # type:ignore
        self.assertEqual(history.response_status_code, 200)  # type:ignore

    def create_productDelivery_instagram_pp(self, product=None):  # noqa: N802
        return ProductDelivery.objects.create(
            product=product or self.product,
            contentDelivery=self.instagram_pp,
            name='Instagram - Perfil Pivado',
            fields={'username': 'testinguser'},
            status='active'
        )

    def create_productDelivery_instagram_cf(self, product=None):  # noqa: N802
        return ProductDelivery.objects.create(
            product=product or self.product,
            contentDelivery=self.instagram_cf,
            name='Instagram - Instagram Close Friends',
            fields={'username': 'testinguser'}
        )

    def test_instagram_add_to_closed_friends(self):
        instagram = Instagram()
        with mock.patch(
            'members.sdk.instagram.Instagram.create_delivery', mock.Mock()
        ) as create_delivery_mock:
            instagram.add_to_closed_friends(mock.Mock(), 'testingrequester')

        calls = [
            mock.call(mock.ANY, 'testingrequester', 'FOLLOW'),
            mock.call(mock.ANY, 'testingrequester', 'ADD_TO_CLOSED_FRIENDS')
        ]
        create_delivery_mock.assert_has_calls(calls)

    def test_instagram_remove_from_closed_friends(self):
        instagram = Instagram()
        with mock.patch(
            'members.sdk.instagram.Instagram.create_delivery', mock.Mock()
        ) as create_delivery_mock:
            instagram.remove_from_closed_friends(mock.Mock(), 'testingrequester')

        create_delivery_mock.assert_called_once_with(
            mock.ANY,
            'testingrequester',
            'REMOVE_FROM_CLOSED_FRIENDS'
        )

    def test_instagram_add_to_private_account(self):
        instagram = Instagram()
        with mock.patch(
            'members.sdk.instagram.Instagram.create_delivery', mock.Mock()
        ) as create_delivery_mock:
            instagram.add_to_private_account(mock.Mock(), 'testingrequester')

        create_delivery_mock.assert_called_once_with(mock.ANY, 'testingrequester', 'FOLLOW')

    def test_instagram_remove_from_private_account(self):
        instagram = Instagram()
        with mock.patch(
            'members.sdk.instagram.Instagram.create_delivery', mock.Mock()
        ) as create_delivery_mock:
            instagram.remove_from_private_account(mock.Mock(), 'testingrequester')

        create_delivery_mock.assert_called_once_with(mock.ANY, 'testingrequester', 'UNFOLLOW')

    def test_instagram_remove_instagram_expired_access_with_instagram_pp(self):
        product_delivery = self.create_productDelivery_instagram_pp()
        delivery_access = DeliveryAccess.objects.create(
            user=self.create_user(),
            productDelivery=product_delivery,
            order=self.create_order(),
            status='active',
            expiresAt=timezone.now() - timezone.timedelta(days=1),
            fields={'username': 'testinguser'}
        )

        mock.patch(
            'members.sdk.instagram.remove_instagram_expired_job.delay',
            remove_instagram_expired_job
        ).start()
        with mock.patch('django.db.close_old_connections'), \
            mock.patch(
                'members.sdk.instagram.Instagram.remove_from_private_account', mock.Mock()
            ) as remove_from_private_account_mock:
            remove_from_private_account_mock.return_value = mock.Mock(status_code=200)
            remove_instagram_expired_accesses()

        remove_from_private_account_mock.assert_called_once_with(
            deliveryAccess=delivery_access,
            requester='testinguser'
        )

    def test_instagram_remove_instagram_expired_access_with_instagram_cf(self):
        product_delivery = self.create_productDelivery_instagram_cf()
        delivery_access = DeliveryAccess.objects.create(
            user=self.create_user(),
            productDelivery=product_delivery,
            order=self.create_order(),
            status='active',
            expiresAt=timezone.now() - timezone.timedelta(days=1),
            fields={'username': 'testinguser'}
        )

        mock.patch(
            'members.sdk.instagram.remove_instagram_expired_job.delay',
            remove_instagram_expired_job
        ).start()

        with mock.patch('django.db.close_old_connections'), \
            mock.patch(
                'members.sdk.instagram.Instagram.remove_from_closed_friends', mock.Mock()
            ) as remove_from_closed_friends_mock:
            remove_from_closed_friends_mock.return_value = mock.Mock(status_code=200)
            remove_instagram_expired_accesses()

        remove_from_closed_friends_mock.assert_called_once_with(
            deliveryAccess=delivery_access,
            requester='testinguser'
        )

    def test_instagram_remove_instagram_access_from_order_with_instagram_pp(self):
        order = self.create_order(product=self.product)
        product_delivery = self.create_productDelivery_instagram_pp()
        delivery_access = DeliveryAccess.objects.create(
            user=self.create_user(),
            productDelivery=product_delivery,
            order=order,
            status='active',
            expiresAt=timezone.now() - timezone.timedelta(days=1),
            fields={'username': 'testinguser'}
        )

        with mock.patch(
            'members.sdk.instagram.Instagram.remove_from_private_account', mock.Mock()
        ) as remove_from_private_account_mock:
            remove_instagram_access_from_order(order)

        remove_from_private_account_mock.assert_called_once_with(delivery_access, 'testinguser')

    def test_instagram_remove_instagram_access_from_order_with_instagram_cf(self):
        order = self.create_order(product=self.product)
        product_delivery = self.create_productDelivery_instagram_cf()
        delivery_access = DeliveryAccess.objects.create(
            user=self.create_user(),
            productDelivery=product_delivery,
            order=order,
            status='active',
            expiresAt=timezone.now() - timezone.timedelta(days=1),
            fields={'username': 'testinguser'}
        )

        with mock.patch(
            'members.sdk.instagram.Instagram.remove_from_closed_friends', mock.Mock()
        ) as remove_from_closed_friends_mock:
            remove_instagram_access_from_order(order)

        remove_from_closed_friends_mock.assert_called_once_with(delivery_access, 'testinguser')

    @responses.activate
    def test_create_productDelivery_instagram_pp(self):  # noqa: N802
        url = reverse('product-delivery', args=[self.product.pk])
        instagram = Instagram()
        responses.add(
            responses.POST,
            instagram.url + '/accounts/login',
            json={'account': {'username': 'testinguser', 'logged': True}},
            status=200
        )

        payload = {
            'type': 'instagram_pp',
            'username': 'testinguser',
            'password': 'caktoigintegration'
        }

        response = self.client.post(
            url,
            data=payload,
            headers=self.create_headers(self.get_user_access_token(self.product.user))
        )

        self.assertEqual(response.status_code, 200, response.content.decode("utf-8"))
        self.assertEqual(ProductDelivery.objects.count(), 1)
        self.assertEqual(ProductDelivery.objects.first().product, self.product)  # type: ignore
        self.assertEqual(ProductDelivery.objects.first().name, 'Instagram - Perfil Privado')  # type: ignore
        self.assertEqual(ProductDelivery.objects.first().contentDelivery, self.instagram_pp)  # type: ignore
        self.assertEqual(ProductDelivery.objects.first().fields, {'username': payload['username']})  # type: ignore

    @responses.activate
    def test_create_productDelivery_instagram_cf(self):  # noqa: N802
        url = reverse('product-delivery', args=[self.product.pk])
        instagram = Instagram()
        responses.add(
            responses.POST,
            instagram.url + '/accounts/login',
            json={'account': {'username': 'testinguser', 'logged': True}},
            status=200
        )

        payload = {
            'type': 'instagram_cf',
            'username': 'testinguser',
            'password': 'caktoigintegration'
        }

        response = self.client.post(
            url,
            data=payload,
            headers=self.create_headers(self.get_user_access_token(self.product.user))
        )

        self.assertEqual(response.status_code, 200, response.content.decode("utf-8"))
        self.assertEqual(ProductDelivery.objects.count(), 1)
        self.assertEqual(ProductDelivery.objects.first().product, self.product)  # type: ignore
        self.assertEqual(ProductDelivery.objects.first().name, 'Instagram - Close Friends')  # type: ignore
        self.assertEqual(ProductDelivery.objects.first().contentDelivery, self.instagram_cf)  # type: ignore
        self.assertEqual(ProductDelivery.objects.first().fields, {'username': payload['username']})  # type: ignore

    @responses.activate
    def test_login_with_already_logged_instagram_account_that_belongs_to_the_user(self):
        url = reverse('product-delivery', args=[self.product.pk])
        instagram = Instagram()

        self.add_already_logged_response_error(instagram)

        payload = {
            'type': 'instagram_pp',
            'username': 'testinguser',
            'password': 'caktoigintegration'
        }

        delivery_1 = ProductDelivery.objects.create(
            product=self.product,
            contentDelivery=self.instagram_cf,
            name='Instagram - Close Friends',
            fields={'username': payload['username']},
            status='active',
        )

        response = self.client.post(
            url,
            data=payload,
            headers=self.create_headers(self.get_user_access_token(self.product.user))
        )

        delivery_1.refresh_from_db()
        delivery_2 = ProductDelivery.objects.exclude(pk=delivery_1.pk).first()

        self.assertEqual(response.status_code, 200, response.content.decode("utf-8"))
        self.assertEqual(ProductDelivery.objects.count(), 2)
        self.assertEqual(delivery_2.product, self.product)  # type: ignore
        self.assertEqual(delivery_2.name, 'Instagram - Perfil Privado')  # type: ignore
        self.assertEqual(delivery_2.contentDelivery, self.instagram_pp)  # type: ignore
        self.assertEqual(delivery_2.fields, {'username': payload['username']})  # type: ignore

    @responses.activate
    def test_login_with_already_logged_instagram_account_that_not_belongs_to_the_user(self):
        url = reverse('product-delivery', args=[self.product.pk])
        instagram = Instagram()

        self.add_already_logged_response_error(instagram)

        payload = {
            'type': 'instagram_pp',
            'username': 'testinguser',
            'password': 'caktoigintegration'
        }

        another_product = self.create_product()

        ProductDelivery.objects.create(
            product=another_product,
            contentDelivery=self.instagram_cf,
            name='Instagram - Close Friends',
            fields={'username': payload['username']},
            status='active',
        )

        response = self.client.post(
            url,
            data=payload,
            headers=self.create_headers(self.get_user_access_token(self.product.user))
        )

        self.assertEqual(response.status_code, 400, response.content.decode("utf-8"))
        self.assertEqual(response.json(), {'detail': 'Instagram já conectado à outra conta.'})

    def add_already_logged_response_error(self, instagram):
        responses.add(
            responses.POST,
            instagram.url + '/accounts/login',
            json={
                'code': 500,
                'timestamp': '2024-12-30T13:29:58.493Z',
                'path': '/api/v1/accounts/login',
                'status': 'error',
                'message': 'Account already logged in.',
                'trace': 'Error: Account already logged in.....'
            },
            status=500
        )

    def test_customer_DeliveryAccess_are_created_on_handle_purchase_course_access_with_instagram_pp(self): # noqa: E501, N802
        user = self.create_user()
        order = self.create_order(product=self.product)
        self.create_productDelivery_instagram_pp()

        mock.patch('financial.utils.add_external_accesses.delay', add_external_accesses).start()
        handle_purchase_course_access(order, user)

        self.assertEqual(DeliveryAccess.objects.count(), 1)
        delivery_access: DeliveryAccess = DeliveryAccess.objects.first()  # type: ignore
        self.assertEqual(delivery_access.user, user)
        self.assertEqual(delivery_access.order, order)
        self.assertEqual(delivery_access.productDelivery.product, self.product)

    def test_customer_DeliveryAccess_are_created_on_handle_purchase_course_access_with_instagram_cf(self):  # noqa: E501, N802
        user = self.create_user()
        order = self.create_order(product=self.product)
        self.create_productDelivery_instagram_cf()

        mock.patch('financial.utils.add_external_accesses.delay', add_external_accesses).start()
        handle_purchase_course_access(order, user)

        self.assertEqual(DeliveryAccess.objects.count(), 1)
        delivery_access: DeliveryAccess = DeliveryAccess.objects.first()  # type: ignore
        self.assertEqual(delivery_access.user, user)
        self.assertEqual(delivery_access.order, order)
        self.assertEqual(delivery_access.productDelivery.product, self.product)
