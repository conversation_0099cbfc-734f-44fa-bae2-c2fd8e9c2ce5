from django.db import models


class Video(models.Model):
    id = models.CharField(max_length=255, primary_key=True, unique=True)
    user = models.ForeignKey('user.User', on_delete=models.CASCADE)
    title = models.CharField(verbose_name='Títu<PERSON>', max_length=255)
    thumbnail = models.CharField(max_length=255, default='thumbnail.jpg')
    status = models.CharField(
        max_length=255,
        verbose_name='Status',
        choices=(
            ('queued', 'Na fila para codificação.'),
            ('processing', 'Processando'),
            ('encoding', 'Codificando.'),
            ('finished', 'Finalizado.'),
            ('videoPlayable', 'Video reprodutível. A codificação terminou em pelo menos uma resolução.'),
            ('encodingFailed', 'Falha na codificação.'),
            ('uploadStarted', 'Upload iniciado.'),
            ('uploadFinished', 'Upload finalizado.'),
            ('uploadFailed', 'Falha no upload.'),
            ('captionsGenerated', 'Legendas automáticas geradas.'),
            ('titleOrDescriptionGenerated', 'Geração automática do título ou descrição finalizada.'),
        ),
        default='created'
    )
    uploadedAt = models.DateTimeField(null=True, blank=True)

    createdAt = models.DateTimeField(auto_now_add=True)
    updatedAt = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return self.title

    @property
    def thumbnailUrl(self):
        return f'https://thumbnail.cakto.com.br/{self.id}/{self.thumbnail}'

    @property
    def hlsUrl(self):
        if self.status in ['finished', 'videoPlayable', 'captionsGenerated', 'titleOrDescriptionGenerated']:
            return f'https://stream.cakto.com.br/{self.id}/playlist.m3u8'
        return None

class MemberFile(models.Model):
    user = models.ForeignKey('user.User', on_delete=models.CASCADE)
    file_key = models.CharField(max_length=255)
    createdAt = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = 'Arquivo'
        verbose_name_plural = 'Arquivos'
