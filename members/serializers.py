from django.conf import settings
from rest_framework import serializers

from members.models import Video
from user.serializers import UserReadOnlySerializer


class VideoSerializer(serializers.ModelSerializer):
    user = UserReadOnlySerializer()

    class Meta:
        model = Video
        fields = [
            'id',
            'user',
            'title',
            'hlsUrl',
            'thumbnailUrl',
            'status',
            'uploadedAt',
        ]

class GiveCourseAccessSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True)
    access_time = serializers.CharField(required=True)

    class Meta:
        fields = [
            'email',
            'access_time',
        ]

    def validate_access_time(self, value):
        if value != 'lifetime':
            serializers.DateTimeField().run_validation(value)
        return value

class MemberV2CourseSerializer(serializers.Serializer):
    nome = serializers.CharField()

class MemberV2ImageSerializer(serializers.Serializer):
    image = serializers.ImageField()

    def validate_image(self, value):
        MAX_SIZE = settings.MEMBERS_V2_MAX_IMAGE_SIZE
        if value.size > MAX_SIZE * 1024 * 1024:
            raise serializers.ValidationError(f'Tamanho do arquivo deve ser menor que {MAX_SIZE} MB.')
        return value

class MemberV2BannerSerializer(serializers.Serializer):
    titulo = serializers.CharField()
    descricao = serializers.CharField(required=False)
    posicao = serializers.IntegerField()

class MemberV2ModuleSerializer(serializers.Serializer):
    nome = serializers.CharField()
    posicao = serializers.IntegerField()

class MemberV2LessonSerializer(serializers.Serializer):
    nome = serializers.CharField()
    descricao = serializers.CharField(required=False)
    posicao = serializers.IntegerField()
    duracao = serializers.CharField(required=False)
    urlVideo = serializers.CharField(required=False)
    moduloId = serializers.CharField(required=False)

class MemberV2LessonFileSerializer(serializers.Serializer):
    titulo = serializers.CharField()

class MemberV2LessonFileUploadSerializer(serializers.Serializer):
    file = serializers.FileField()

    def validate_file(self, value):
        MAX_SIZE = settings.MEMBERS_V2_MAX_FILE_SIZE
        if value.size > MAX_SIZE * 1024 * 1024:
            raise serializers.ValidationError(f'Tamanho do arquivo deve ser menor que {MAX_SIZE} MB.')
        return value

class MembersV2GiveAccessSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True)
    finalTime = serializers.DateTimeField(required=False, allow_null=True)

    class Meta:
        fields = [
            'email',
            'finalTime',
        ]
