from django.urls import path

from members import views

urlpatterns = [
    # Users
    path('members/users/send_new_password_link/', views.UserCourseActionsAPIView.as_view({'post': 'send_new_password_link'}), name='users-send-new-password-link'),

    # Courses
    path('members/courses/', views.CourseListAPIView.as_view(), name='courses'),
    path('members/courses/token/', views.MemberAccessTokenAPIView.as_view(), name='member-access-token'),
    path('members/courses/<str:id>/', views.CourseAPIView.as_view({'get': 'get', 'put': 'put', 'delete': 'delete'}), name='course'),
    path('members/courses/<str:product_id>/users/', views.CourseListUsersAPIView.as_view(), name='list-course-users'),
    path('members/courses/<str:id>/add-access/', views.CourseAPIView.as_view({'post': 'add_course_access_to_user'}), name='add-course-access-to-user'),
    path('members/courses/<str:id>/remove-access/', views.CourseAPIView.as_view({'post': 'remove_course_access_to_user'}), name='remove-course-access-to-user'),

    # Modules
    path('members/<str:product_id>/modules/', views.ModuleCreateUpdateDeleteAPIView.as_view(), name='modules'),

    # Lessons
    path('members/lessons/', views.LessonsListAPIView.as_view(), name='lessons'),
    path('members/lessons/files/', views.LessonsRetrieveAPIView.as_view({'get': 'get_pre_signed_url', 'post': 'upload_files', 'delete': 'delete_file'}), name='lesson-files'),
    path('members/lessons/<str:id>/', views.LessonsRetrieveAPIView.as_view({'get': 'get'}), name='lesson'),
    path('members/lessons/<str:id>/watch/', views.LessonsRetrieveAPIView.as_view({'get': 'lesson_watch_data'}), name='lesson-watch'),
    path('members/<str:product_id>/lessons/', views.LessonsCreateUpdateDeleteAPIView.as_view(), name='lessons-create'),
    path('members/<str:product_id>/lessons/<str:id>/', views.LessonsCreateUpdateDeleteAPIView.as_view(), name='lesson-update-delete'),

    # Videos
    path('members/videos/', views.VideoAPIView.as_view({'post': 'create', 'get': 'list'}), name='videos'),
    path('members/videos/<str:pk>/', views.VideoAPIView.as_view({'get': 'retrieve', 'delete': 'destroy'}), name='video'),
    path('members/videos/<str:pk>/thumbnail/', views.VideoAPIView.as_view({'post': 'set_thumbnail'}), name='video-thumbnail'),
    path('members/webhooks/videos/', views.VideoWebhook.as_view({'post': 'post'}), name='videos-webhook'),

    # Telegram Webhook
    path('members/telegram/webhook/', views.TelegramWebhook.as_view(), name='telegram-webhook'),

    # Admin
    path('admin/members/token/', views.AdminMemberAccessTokenAPIView.as_view(), name='admin-member-access-token'),

    # Members V2 User Token
    path('members/v2/token/', views.MembersV2UserTokenAPIView.as_view(), name='members-v2-token'),

    # Members V2 Courses
    path('members/v2/courses/', views.MembersV2CourseAPIView.as_view({'get': 'list'}), name='members-v2-list'),
    path('members/v2/<str:product_id>/', views.MembersV2CourseAPIView.as_view({'get': 'retrieve', 'post': 'create', 'put': 'update', 'delete': 'destroy'}), name='members-v2-detail'),
    path('members/v2/<str:product_id>/logo/', views.MembersV2CourseAPIView.as_view({'post': 'upload_logo', 'delete': 'delete_logo'}), name='members-v2-logo'),
    path('members/v2/<str:product_id>/user_access/', views.MembersV2CourseAPIView.as_view({'post': 'add_user_access', 'delete': 'remove_user_access'}), name='members-v2-user-access'),

    # Members V2 Banners
    path('members/v2/<str:product_id>/banners/', views.MembersV2BannersAPIView.as_view({'get': 'list_banners', 'post': 'create_banner'}), name='members-v2-banners'),
    path('members/v2/<str:product_id>/banners/<str:banner_id>/', views.MembersV2BannersAPIView.as_view({'put': 'update_banner', 'delete': 'delete_banner'}), name='members-v2-banners-detail'),
    path('members/v2/<str:product_id>/banners/<str:banner_id>/image/',
         views.MembersV2BannersAPIView.as_view({'post': 'upload_banner_image', 'delete': 'delete_banner_image'}), name='members-v2-banners-image'),

    # Members V2 Modules
    path('members/v2/<str:product_id>/modules/', views.MembersV2ModulesAPIView.as_view({'post': 'create'}), name='members-v2-modules'),
    path('members/v2/<str:product_id>/modules/<str:module_id>/', views.MembersV2ModulesAPIView.as_view({'delete': 'destroy', 'put': 'udpate'}), name='members-v2-modules-detail'),
    path('members/v2/<str:product_id>/modules/<str:module_id>/cover/', views.MembersV2ModulesAPIView.as_view({'post': 'upload_cover', 'delete': 'delete_cover'}), name='members-v2-modules-cover'),

    # Members V2 Lessons
    path('members/v2/<str:product_id>/modules/<str:module_id>/lessons/', views.MembersV2LessonsAPIView.as_view({'get': 'list_lessons', 'post': 'create_lesson'}), name='members-v2-lessons'),
    path('members/v2/<str:product_id>/lessons/<str:lesson_id>/', views.MembersV2LessonsAPIView.as_view({'put': 'update_lesson', 'delete': 'delete_lesson'}), name='members-v2-lessons-detail'),

    # Members V2 Lessons Files
    path('members/v2/<str:product_id>/lessons/<str:lesson_id>/files/', views.MembersV2LessonsAPIView.as_view({'get': 'list_files', 'post': 'create_file'}), name='members-v2-lessons-files'),
    path('members/v2/<str:product_id>/lesson_files/<str:file_id>/', views.MembersV2LessonsAPIView.as_view({'put': 'update_file_data', 'delete': 'delete_file'}), name='members-v2-lesson-files-detail'),
    path('members/v2/<str:product_id>/lesson_files/<str:file_id>/upload/', views.MembersV2LessonsAPIView.as_view({'post': 'upload_file_item'}), name='members-v2-lesson-files-upload'),
]
