import hashlib
import hmac
import json
import logging
from typing import Dict, Any, Optional
from urllib.parse import urljoin

import requests
from django.conf import settings
from django_rq import job
from rq import Retry

from gateway.models import Order, Payment
from product.models import Product

logger = logging.getLogger(__name__)


class MembersV2WebhookService:
    def __init__(self):
        self.webhook_url = settings.CAKTO_MEMBERS_V2_WEBHOOK_URL
        self.api_url = settings.CAKTO_MEMBERS_V2_BASE_URL
        self.api_key = settings.CAKTO_MEMBERS_V2_API_KEY

    def _generate_hmac_signature(self, payload: str) -> str:
        return ""

    def _prepare_purchase_payload(self, order: Order, payment: Payment) -> Dict[str, Any]:
        product = order.product

        payload = {
            "secret": "1340098d-340d-488a-af83-f80e0eaaa773",
            "event": "purchase_approved",
            "data": {
                "id": str(order.id),
                "refId": order.refId,
                "customer": {
                    "name": order.customer.name,
                    "email": order.customer.email,
                    "phone": order.customer.phone,
                    "docNumber": order.customer.docNumber,
                },
                "product": {
                    "name": product.name,
                    "id": str(product.id),
                    "short_id": product.short_id,
                    "supportEmail": product.user.email,
                    "type": product.type,
                    "invoiceDescription": product.description[:100] if product.description else "",
                },
                "status": "approved",
                "baseAmount": float(order.baseAmount),
                "discount": float(order.discount) if order.discount else 0.0,
                "amount": float(order.amount),
                "installments": order.installments,
                "paymentMethod": payment.method,
                "paymentMethodName": payment.get_method_display(),
                "paidAt": payment.paidAt.isoformat() if payment.paidAt else None,
                "createdAt": order.createdAt.isoformat(),
            }
        }

        if payment.method == 'credit_card' and payment.card:
            payload["data"]["card"] = {
                "lastDigits": payment.card.lastDigits,
                "holderName": payment.card.holderName,
                "brand": payment.card.brand,
            }
        elif payment.method == 'boleto' and payment.boleto:
            payload["data"]["boleto"] = {
                "barcode": payment.boleto.barcode,
                "boletoUrl": payment.boleto.boletoUrl,
                "expirationDate": payment.boleto.expirationDate.strftime("%Y-%m-%d") if payment.boleto.expirationDate else None,
            }
        elif payment.method == 'pix' and payment.pix:
            payload["data"]["pix"] = {
                "expirationDate": payment.pix.expirationDate.strftime("%Y-%m-%d") if payment.pix.expirationDate else None,
                "qrCode": payment.pix.qrCode,
            }
        elif payment.method == 'picpay' and payment.picpay:
            payload["data"]["picpay"] = {
                "qrCode": payment.picpay.qrCode,
                "paymentURL": payment.picpay.paymentURL,
                "expirationDate": payment.picpay.expirationDate.strftime("%Y-%m-%d %H:%M:%S%z") if payment.picpay.expirationDate else None,
            }

        if order.affiliate:
            payload["data"]["affiliate"] = order.affiliate.email

        if order.offer:
            payload["data"]["offer"] = {
                "id": order.offer.id,
                "name": order.offer.name,
                "price": float(order.offer.price),
            }
            payload["data"]["offer_type"] = "main"

        if order.commissions.exists():
            payload["data"]["commissions"] = [
                {
                    "user": commission.user.email,
                    "totalAmount": float(commission.totalAmount),
                    "type": commission.type,
                    "percentage": float(commission.percentage),
                }
                for commission in order.commissions.all()
            ]

        return payload

    def send_purchase_webhook(self, order: Order, payment: Payment) -> bool:
        try:
            if not self.webhook_url:
                logger.error("Cakto Members V2 webhook URL not configured")
                return False

            if order.product.contentDelivery != 'cakto':
                logger.info(f"Product {order.product.id} does not use Cakto Members delivery, skipping webhook")
                return True

            payload = self._prepare_purchase_payload(order, payment)

            headers = {
                'Content-Type': 'application/json',
                'User-Agent': 'CaktoBot/1.0',
            }

            logger.info(f"Sending webhook to {self.webhook_url} for order {order.id}")

            response = requests.post(
                self.webhook_url,
                json=payload,
                headers=headers,
                timeout=30
            )

            if response.status_code == 200:
                logger.info(f"Webhook sent successfully for order {order.id}")
                return True
            else:
                logger.error(f"Webhook failed for order {order.id}: {response.status_code} - {response.text}")
                return False

        except Exception as e:
            logger.error(f"Error sending webhook for order {order.id}: {str(e)}")
            return False


# Global instance
members_v2_webhook_service = MembersV2WebhookService()


@job('default', retry=Retry(max=3, interval=[5, 30, 300]))
def send_purchase_webhook_to_members_v2(order: Order, payment: Payment) -> bool:
    """Background job to send purchase webhook to Cakto Members V2"""
    return members_v2_webhook_service.send_purchase_webhook(order, payment)
