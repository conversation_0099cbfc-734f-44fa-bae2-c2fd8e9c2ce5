import hashlib
import os

import django_apscheduler.util
import requests
from django.utils import timezone
from django_rq import job
from requests.exceptions import JSONDecodeError
from rest_framework import status
from rest_framework.exceptions import ValidationError

from apps.utils import get_response_data
from cakto.utils import is_job_scheduled
from product.models import DeliveryAccess, InstragramApiHistory, ProductDelivery
from user.models import User


class Instagram:
    url = os.getenv('INSTAGRAM_BASE_URL', '')
    token = os.getenv('INSTAGRAM_AUTH_TOKEN', '')
    api = requests.Session()
    is_logged_in = None

    def __init__(self) -> None:
        self.api.headers.update({'Authorization': self.token})

    def login(self, username: str, password: str, user: User) -> dict:
        response = self._login_request(username, password)

        response_data = self._parse_api_response(response)

        self._validate_login_response(response_data)

        if 'Account already logged in.' in response_data.get('message', ''):
            self._handle_already_logged_in(user=user, username=username, password=password,)

        return response_data

    def _login_request(self, username, password):
        payload = {
            'username': username,
            'password': password,
        }
        response = self.api.post(self.url + '/accounts/login', json=payload)

        hashed_password = hashlib.sha256(password.encode()).hexdigest()

        _, response_data = get_response_data(response)

        InstragramApiHistory.objects.create(
            action='login',
            payload={'username': username, 'password': hashed_password},
            response=response_data,
            response_status_code=response.status_code,
        )

        return response

    def login_succeded(self, login_response_data: dict, username: str) -> bool:
        account = login_response_data.get('account', {})
        return account.get('username', '') == username and account.get('logged') is True

    def logout(self, username: str) -> requests.Response:
        payload = {
            'username': username,
        }
        response = self.api.post(self.url + '/accounts/logout', json=payload)

        _, response_data = get_response_data(response)

        InstragramApiHistory.objects.create(
            action='logout',
            payload={'username': username},
            response=response_data,
            response_status_code=response.status_code,
        )

        return response

    def _validate_login_response(self, response_data: dict) -> None:
        message = response_data.get('message', '')

        if 'Incorrect username or password.' in message \
                or 'This account is already logged in, but the provided password is incorrect.' in message\
                or 'Invalid username or password.' in message:
            raise ValidationError({'detail': 'Usuário ou senha incorretos.'}, code=status.HTTP_400_BAD_REQUEST)

        if 'Session cookie not found.' in message:
            raise ValidationError({'detail': 'Erro ao realizar login.'}, code=status.HTTP_400_BAD_REQUEST)

        if '2FA is not supported yet' in message:
            raise ValidationError({'detail': 'Autenticação dois fatores no Instagram não suportada. Desative e tente novamente.'}, code=status.HTTP_400_BAD_REQUEST)

        if response_data.get('requiresVerification') is True or 'Challenge required' in message:
            raise ValidationError({'detail': 'Você deve autorizar a tentativa de login no instagram e tentar novamente'}, code=status.HTTP_400_BAD_REQUEST)

    def _handle_already_logged_in(self, user: User, username: str, password: str) -> None:
        has_active_login = self._account_logged_in_users_product(user=user, username=username)

        if has_active_login:
            return

        logged_in_cakto = ProductDelivery.objects.filter(
            contentDelivery__type__in=['instagram_pp', 'instagram_cf'],
            fields__username=username,
            status='active',
        ).exists()

        if not logged_in_cakto:
            logged = self._logout_and_retry_login(username=username, password=password)
            if logged:
                return

        raise ValidationError({'detail': 'Instagram já conectado à outra conta.'}, code=status.HTTP_400_BAD_REQUEST)

    def create_delivery(self, deliveryAccess: DeliveryAccess, requester: str, delivery_type: str) -> requests.Response:
        username = deliveryAccess.productDelivery.fields.get('username')

        payload = {
            "username": username,
            "requester": requester,
            "type": delivery_type,
        }

        response = self.api.post(self.url + '/requests', json=payload)

        _, response_data = get_response_data(response)
        InstragramApiHistory.objects.create(
            deliveryAccess=deliveryAccess,
            action=f'create_delivery:{delivery_type}',
            payload=payload,
            response=response_data,
            response_status_code=response.status_code,
        )

        return response

    def add_to_closed_friends(self, deliveryAccess: DeliveryAccess, requester: str) -> requests.Response:
        self.create_delivery(deliveryAccess, requester, 'FOLLOW')
        return self.create_delivery(deliveryAccess, requester, 'ADD_TO_CLOSED_FRIENDS')

    def remove_from_closed_friends(self, deliveryAccess: DeliveryAccess, requester: str) -> requests.Response:
        return self.create_delivery(deliveryAccess, requester, 'REMOVE_FROM_CLOSED_FRIENDS')

    def add_to_private_account(self, deliveryAccess: DeliveryAccess, requester: str) -> requests.Response:
        return self.create_delivery(deliveryAccess, requester, 'FOLLOW')

    def remove_from_private_account(self, deliveryAccess: DeliveryAccess, requester: str) -> requests.Response:
        return self.create_delivery(deliveryAccess, requester, 'UNFOLLOW')

    def _parse_api_response(self, response: requests.Response):
        try:
            return response.json()
        except JSONDecodeError:
            raise Exception(f'Error in Instagram API \nerror_code: {response.status_code}, content:{response.content.decode("utf-8")}')

    def _logout_and_retry_login(self, username: str, password: str) -> bool:
        logout_response = self.logout(username)

        if not status.is_success(logout_response.status_code):
            raise Exception(
                f'Error in Instagram API on {self._logout_and_retry_login.__name__}'
                f'\nerror_code: {logout_response.status_code}'
                f', content:{logout_response.content.decode("utf-8")}'
            )

        login_response = self._login_request(username, password)

        login_data = self._parse_api_response(login_response)

        self._validate_login_response(login_data)

        if self.login_succeded(login_data, username):
            self.is_logged_in = True
            return True

        return False

    def _account_logged_in_users_product(self, user: User, username: str) -> bool:
        is_logged = ProductDelivery.objects.filter(
            product__user=user,
            contentDelivery__type__in=['instagram_pp', 'instagram_cf'],
            fields__username=username,
            status='active',
        ).exists()
        self.is_logged_in = is_logged
        return is_logged


@job
def remove_instagram_access_from_order(order):
    deliveryAccesses = order.productDeliveryAccesses.filter(productDelivery__contentDelivery__type__in=['instagram_pp', 'instagram_cf'])
    if not deliveryAccesses.exists():
        return

    instagram = Instagram()
    for deliveryAccess in deliveryAccesses:
        if deliveryAccess.status != 'waiting_config':
            if deliveryAccess.productDelivery.contentDelivery.type == 'instagram_pp':
                instagram.remove_from_private_account(deliveryAccess, deliveryAccess.fields.get('username'))
            elif deliveryAccess.productDelivery.contentDelivery.type == 'instagram_cf':
                instagram.remove_from_closed_friends(deliveryAccess, deliveryAccess.fields.get('username'))
        deliveryAccess.status = 'canceled'
        deliveryAccess.save()

@job
def remove_instagram_expired_job(deliveryAccess: DeliveryAccess):
    instagram = Instagram()
    if deliveryAccess.productDelivery.contentDelivery.type == 'instagram_pp':
        res = instagram.remove_from_private_account(deliveryAccess=deliveryAccess, requester=deliveryAccess.fields.get('username'))
    else:
        res = instagram.remove_from_closed_friends(deliveryAccess=deliveryAccess, requester=deliveryAccess.fields.get('username'))

    if status.is_success(res.status_code):
        deliveryAccess.status = 'expired'
        deliveryAccess.save()
    else:
        raise Exception(f'Error in Instagram API \nerror_code: {res.status_code}, content:{res.content.decode("utf-8")}')

@django_apscheduler.util.close_old_connections
def remove_instagram_expired_accesses():
    print('Removing expired instagram accesses')

    deliveryAccesses = DeliveryAccess.objects.filter(
        expiresAt__lte=timezone.now(),
        status='active',
        productDelivery__contentDelivery__type__in=['instagram_pp', 'instagram_cf'],
    )

    count = 0
    for deliveryAccess in deliveryAccesses:
        job_id = f'remove_instagram_access_job_{deliveryAccess.id}'
        if not is_job_scheduled(job_id):
            count += 1
            remove_instagram_expired_job.delay(deliveryAccess)

    print(f'{count} jobs scheduled for removing expired instagram accesses')
