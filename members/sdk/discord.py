import hashlib
import os
from urllib.parse import urlencode, urljoin

import django_apscheduler
import requests
from django.utils import timezone
from django_rq import job
from rest_framework.exceptions import ValidationError

from cakto.utils import is_job_scheduled
from product.models import DeliveryAccess

API_ENDPOINT = 'https://discord.com/api/v10'
DISCORD_OAUTH_URL = 'https://discord.com/api/oauth2/authorize'
TOKEN_URL = 'https://discord.com/api/oauth2/token'
REDIRECT_URI = urljoin(os.getenv('FRONT_END_BASE_URL', 'https://app.cakto.com.br'), '/members/discord/finish-config/')

CLIENT_ID = os.getenv('DISCORD_APP_ID', '')
CLIENT_SECRET = os.getenv('DISCORD_SECRET', '')
DISCORD_BOT_TOKEN = os.getenv('DISCORD_BOT_TOKEN', '')

class Discord:
    def generate_state(self, request) -> str:
        """Generate a random state for the OAuth request and save it in the session for safety reasons."""
        # https://discord.com/developers/docs/topics/oauth2#state-and-security
        state = hashlib.sha256(os.urandom(1024)).hexdigest()
        request.session['discord_oauth_state'] = state
        request.session.save()
        return state

    def get_discord_oauth_producer(self, request, *args, **kwargs) -> str:
        params = {
            'client_id': CLIENT_ID,
            'redirect_uri': REDIRECT_URI,
            'response_type': 'code',
            'permissions': 8,
            'scope': 'bot',
            'state': self.generate_state(request)
        }
        return f'{DISCORD_OAUTH_URL}?{urlencode(params)}'

    def get_discord_oauth_customer(self, request, *args, **kwargs) -> str:
        params = {
            'client_id': CLIENT_ID,
            'redirect_uri': REDIRECT_URI,
            'response_type': 'code',
            'scope': 'identify guilds.join',
            'state': self.generate_state(request)
        }
        return f'{DISCORD_OAUTH_URL}?{urlencode(params)}'

    def exchange_code(self, code) -> dict:
        data = {
            'grant_type': 'authorization_code',
            'code': code,
            'redirect_uri': REDIRECT_URI
        }
        headers = {'Content-Type': 'application/x-www-form-urlencoded'}

        response = requests.post(f'{API_ENDPOINT}/oauth2/token', data=data, headers=headers, auth=(CLIENT_ID, CLIENT_SECRET))

        if response.status_code == 400 and response.json().get('error_description') == 'Invalid "code" in request.':
            raise ValidationError({'detail': 'Invalid authCode'})

        response.raise_for_status()
        return response.json()

    def get_user_info(self, access_token) -> dict:
        headers = {'Authorization': f'Bearer {access_token}'}
        response = requests.get(f'{API_ENDPOINT}/users/@me', headers=headers)
        response.raise_for_status()
        return response.json()

    def add_user_to_guild(self, access_token, user_id, guild_id, roles: list[str] = []) -> int:
        url = f'{API_ENDPOINT}/guilds/{guild_id}/members/{user_id}'
        data = {
            'access_token': access_token,
            'roles': roles
        }
        headers = {'Authorization': f'Bot {DISCORD_BOT_TOKEN}'}

        response = requests.put(url, json=data, headers=headers)
        response.raise_for_status()
        return response.status_code  # returns 201 if it succeeded, 204 if the user is already in the guild

    def create_role(self, guild_id, role_name, permissions=0, hoist=False, mentionable=True) -> dict:
        url = f'{API_ENDPOINT}/guilds/{guild_id}/roles'
        headers = {
            'Authorization': f'Bot {DISCORD_BOT_TOKEN}',
            'Content-Type': 'application/json'
        }
        data = {
            'name': role_name,
            'permissions': '0',
            'hoist': hoist,
            'mentionable': mentionable
        }
        response = requests.post(url, json=data, headers=headers)
        response.raise_for_status()
        return response.json()

    def add_guild_role_to_user(self, guild_id, user_id, role_id) -> int:
        DISCORD_ROLE_URL = f'{API_ENDPOINT}/guilds/{guild_id}/members/{user_id}/roles/{role_id}'
        url = DISCORD_ROLE_URL.format(guild_id=guild_id, user_id=user_id, role_id=role_id)

        headers = {
            'Authorization': f'Bot {DISCORD_BOT_TOKEN}',
            'Content-Type': 'application/json'
        }

        response = requests.put(url, headers=headers)
        response.raise_for_status()
        return response.status_code  # returns 204 if it succeeded

    def remove_guild_role_to_user(self, guild_id, user_id, role_id) -> int:
        DISCORD_ROLE_URL = f'{API_ENDPOINT}/guilds/{guild_id}/members/{user_id}/roles/{role_id}'
        url = DISCORD_ROLE_URL.format(guild_id=guild_id, user_id=user_id, role_id=role_id)

        headers = {
            'Authorization': f'Bot {DISCORD_BOT_TOKEN}',
            'Content-Type': 'application/json'
        }

        response = requests.delete(url, headers=headers)
        response.raise_for_status()
        return response.status_code  # returns 204 if it succeeded

    def handle_token_exchange(self, code: str, deliveryAccess: DeliveryAccess) -> None:
        data = self.exchange_code(code)
        deliveryAccess.fields['access_token'] = data['access_token']
        deliveryAccess.fields['refresh_token'] = data['refresh_token']
        deliveryAccess.fields['expires_in'] = (timezone.now() + timezone.timedelta(seconds=int(data['expires_in']) - 10)).isoformat()
        deliveryAccess.save()

    def get_access_token(self, deliveryAccess: DeliveryAccess) -> str:
        expires_in = deliveryAccess.fields.get('expires_in')
        if timezone.now() > timezone.datetime.fromisoformat(expires_in):
            self.refresh_token(deliveryAccess)
        return deliveryAccess.fields['access_token']

    def refresh_token(self, deliveryAccess: DeliveryAccess) -> None:
        data = {
            'grant_type': 'refresh_token',
            'refresh_token': deliveryAccess.fields.get('refresh_token')
        }
        headers = {'Content-Type': 'application/x-www-form-urlencoded'}

        response = requests.post(f'{API_ENDPOINT}/oauth2/token', data=data, headers=headers, auth=(CLIENT_ID, CLIENT_SECRET))
        response.raise_for_status()
        data = response.json()
        deliveryAccess.fields['access_token'] = data['access_token']
        deliveryAccess.fields['refresh_token'] = data['refresh_token']
        deliveryAccess.fields['expires_in'] = timezone.now() + timezone.timedelta(seconds=int(data['expires_in']) - 10)
        deliveryAccess.save()

    def handle_discord_access(self, deliveryAccess: DeliveryAccess) -> None:
        accessToken = self.get_access_token(deliveryAccess)
        userId = deliveryAccess.fields.get('userId')
        guildId = deliveryAccess.productDelivery.fields.get('guildId')
        roleId = deliveryAccess.productDelivery.fields.get('roleId')
        add_res_status = self.add_user_to_guild(access_token=accessToken, user_id=userId, guild_id=guildId, roles=[roleId])

        if add_res_status == 204:
            # User is already in the guild, just add the role
            self.add_guild_role_to_user(guild_id=guildId, user_id=userId, role_id=roleId)

@job
def remove_discord_access_from_order(order) -> None:
    deliveryAccesses = order.productDeliveryAccesses.filter(productDelivery__contentDelivery__type='discord')

    if not deliveryAccesses.exists():
        return

    discord = Discord()

    for deliveryAccess in deliveryAccesses:
        if deliveryAccess.status != 'waiting_config':
            guildId = deliveryAccess.productDelivery.fields['guildId']
            roleId = deliveryAccess.productDelivery.fields['roleId']
            userId = deliveryAccess.fields['userId']
            discord.remove_guild_role_to_user(guild_id=guildId, user_id=userId, role_id=roleId)
        deliveryAccess.status = 'canceled'

    DeliveryAccess.objects.bulk_update(deliveryAccesses, ['status'])

@job
def remove_discord_access_job(deliveryAccess: DeliveryAccess, status: str = 'expired') -> None:
    deliveryAccess.refresh_from_db()
    discord = Discord()

    guildId = deliveryAccess.productDelivery.fields['guildId']
    roleId = deliveryAccess.productDelivery.fields['roleId']
    userId = deliveryAccess.fields['userId']

    discord.remove_guild_role_to_user(guild_id=guildId, user_id=userId, role_id=roleId)

    deliveryAccess.status = status
    deliveryAccess.save()

@django_apscheduler.util.close_old_connections
def remove_discord_expired_accesses() -> None:
    print('Removing expired discord accesses')

    deliveryAccesses = DeliveryAccess.objects.filter(
        expiresAt__lte=timezone.now(),
        status='active',
        productDelivery__contentDelivery__type='discord'
    )

    count = 0
    for deliveryAccess in deliveryAccesses:
        job_id = f'remove_discord_access_job_{deliveryAccess.id}'
        if not is_job_scheduled(job_id):
            count += 1
            remove_discord_access_job.delay(deliveryAccess, job_id=job_id)

    print(f'{count} jobs scheduled for removing expired discord accesses')
