import datetime
import string
from random import choices
from urllib.parse import urljoin

import requests
from django.conf import settings
from django.core.cache import cache
from django.core.files.uploadedfile import InMemoryUploadedFile
from django.utils import timezone
from django_rq import job
from rest_framework import status
from rest_framework.exceptions import ValidationError
from rest_framework.response import Response

from product.models import Product
from user.models import User

TOKEN_CACHE_KEY_PREFIX = 'members_v2_token_'

class CustomMembersV2Session(requests.Session):
    def __init__(self, timeout=None):
        super().__init__()
        self.timeout = timeout

    def request(self, *args, authUser: User | None = None, **kwargs):
        """
        Override request method to:
            - Handle invalid token response for user and try to get a new token.
            - Return error for the user if the response is a client error.
            - Raise exception if the response is not a success for other reasons.
        """

        kwargs.setdefault('timeout', self.timeout)

        response = super().request(*args, **kwargs)

        if response.status_code == 401 and authUser:
            response = self._process_invalid_token_response(response, *args, authUser=authUser, **kwargs)

        # Return error for the user
        elif status.is_client_error(response.status_code):
            self._raise_validation_error(response)

        elif not status.is_success(response.status_code):
            self._raise_exception_error(response)

        return response

    def _raise_exception_error(self, response):
        url = response.request.url
        url = url.replace(settings.MEMBERS_V2_BASE_URL, '') if url is not None else None
        raise Exception(
            f'Error making request to Members V2 on {url}, '
            f'status_code {response.status_code}, '
            f'response: {response.content.decode()}'
        )

    def _raise_validation_error(self, response):
        if hasattr(response, 'json'):
            data = response.json()
        else:
            data = response.content.decode()
        raise ValidationError(data, code=response.status_code)

    def _process_invalid_token_response(self, response: requests.Response, *args, authUser: User, **kwargs) -> requests.Response:
        """
        Function to handle user authorization token invalid.
        Because the token is cached, if it is invalid, the function will delete it from the cache and
        try to get a new token and make the request again.
        """
        try:
            data = response.json()

            if data.get('message') != 'Token inválido ou erro ao verificar.':
                return response

            cache_key = f'{TOKEN_CACHE_KEY_PREFIX}{authUser.membersV2Id}'
            cache.delete(cache_key)

            membersV2_sdk = MembersV2BaseSDK()
            user_token = membersV2_sdk._login_user(authUser)

            kwargs.get('headers', {})['Authorization'] = user_token

            kwargs.pop('authUser', None)

            cache.set(
                cache_key,
                user_token,
                timeout=settings.MEMBERS_V2_TOKEN_CACHE_TIMEOUT * 60 * 60,
            )

            response = self.request(*args, **kwargs)
        except Exception:
            pass

        return response

class MembersV2BaseSDK:
    api_url = settings.MEMBERS_V2_BASE_URL
    token = settings.MEMBERS_V2_API_TOKEN
    api = CustomMembersV2Session(timeout=5)

    def _build_url(self, endpoint: str) -> str:
        return urljoin(self.api_url, endpoint)

    def _get_admin_headers(self):
        return {'Authorization': self.token}

    def _get_user_headers(self, user: User):
        self._process_user_registration(user)
        user_token = self._get_cached_user_token(user)
        return {'Authorization': user_token}

    def _login_user(self, user: User) -> str:
        """
        Make a request to login a user on Members V2 and return the token.

        Raises:
            Exception: If there is an error logging in the user on Members V2.
        """
        url = self._build_url(f'/loginUser/{user.membersV2Id}')
        admin_headers = self._get_admin_headers()

        response = self.api.post(url, headers=admin_headers)

        return response.json()['token']

    def _get_cached_user_token(self, user: User) -> str:
        """Get the user token from cache or make a request to login the user on Members V2."""

        timeout = settings.MEMBERS_V2_TOKEN_CACHE_TIMEOUT * 60 * 60
        token = cache.get_or_set(
            f'{TOKEN_CACHE_KEY_PREFIX}{user.membersV2Id}',
            lambda: self._login_user(user),
            timeout=timeout
        )

        return token  # type: ignore

    def _process_user_registration(self, user: User) -> None:
        if user.membersV2Id:
            return

        create_response = self._create_user(user)

        user.membersV2Id = create_response.json()['id']
        user.save(update_fields=['membersV2Id'])

    def _create_user(self, user: User) -> requests.Response:
        url = self._build_url('/user')

        characters = string.ascii_letters.replace('I', '').replace('L', '').replace('O', '') + string.digits.replace('0', '')
        password = ''.join(choices(characters, k=8))

        data = {
            'email': user.email,
            'name': ' '.join([user.first_name, user.last_name]),
            'password': password,
        }

        admin_headers = self._get_admin_headers()
        return self.api.post(url, json=data, headers=admin_headers)

    def _get_product_instance_by_id(self, product_id: str, user: User) -> Product:
        product = Product.objects.filter(id=product_id, user=user).only('membersV2Id', 'name', 'user').first()
        if not product:
            raise ValidationError({'detail': 'Produto não encontrado'})
        return product

    def _get_product_membersV2Id(self, product: Product | None = None, search_params: tuple[str, User] | None = None) -> str:
        if not product and not search_params:
            raise ValueError('product or search_params(product_id, user) must be provided.')

        def check_product(product: Product):
            if not product.membersV2Id:
                raise ValidationError({'detail': 'Área de membros não encontrada.'})

        if product and isinstance(product, Product):
            check_product(product)
            return product.membersV2Id  # type:ignore

        if search_params and isinstance(search_params, tuple):
            product_id, user = search_params
            if not isinstance(user, User) or not isinstance(product_id, str):
                raise ValueError('search_params must be a tuple with a product_id string and a User instance.')
            product = self._get_product_instance_by_id(product_id, user)
            check_product(product)
            return product.membersV2Id  # type:ignore

        raise ValidationError({'detail': 'Área de membros não encontrada.'})

    def _add_course_to_user(self, user: User, product_membersV2Id: str, expires_at: datetime.datetime | None = None) -> None:
        self._process_user_registration(user)
        url = self._build_url(f'/admin/addCursoToUser/{user.membersV2Id}')

        payload = {'cursoId': product_membersV2Id}
        if expires_at:
            if expires_at < timezone.now():
                raise ValidationError({
                    'detail': 'Data de expiração inválida. Deve ser maior que a data atual ou null para acesso por tempo indeterminado.'
                })
            payload['finalTime'] = expires_at.isoformat()

        self.api.post(url, headers=self._get_admin_headers(), json=payload)

    def _remove_course_from_user(self, user: User, product_membersV2Id: str) -> None:
        return self._add_course_to_user(
            user=user,
            product_membersV2Id=product_membersV2Id,
            expires_at=timezone.now()
        )

    def update_user_info(self, user: User) -> None:
        if not user.membersV2Id:
            return

        url = self._build_url(f'/user/{user.membersV2Id}')
        payload = {
            'nome': ' '.join([user.first_name, user.last_name]),
            'email': user.email,
        }

        self.api.put(url, headers=self._get_user_headers(user), json=payload, authUser=user)  # type: ignore

class MembersV2CourseSDK(MembersV2BaseSDK):
    def list_courses(self, user: User) -> Response:
        self._process_user_registration(user)
        url = self._build_url(f'/user/getAllCursosByUser/{user.membersV2Id}')
        res = self.api.get(url, headers=self._get_user_headers(user), authUser=user)  # type: ignore

        return Response(res.json(), status=status.HTTP_200_OK)

    def retrieve_course(self, user: User, product_id: str) -> Response:
        membersV2Id = self._get_product_membersV2Id(search_params=(product_id, user))

        url = self._build_url(f'/user/getAllModulosByUser/{membersV2Id}')
        res = self.api.get(url, headers=self._get_user_headers(user), authUser=user)  # type: ignore

        data = res.json()
        if data:
            data = data[0]
            data.pop('id', None)

        return Response(data, status=status.HTTP_200_OK)

    def create_course(self, user: User, product_id: str) -> Response:
        product = self._get_product_instance_by_id(product_id, user=user)

        if product.membersV2Id:
            return Response({'detail': 'Área de membros já existente.'}, status=status.HTTP_200_OK)

        product_membersV2Id = self._create_member_course(product)
        self._add_course_to_user(user, product_membersV2Id)

        return Response({'detail': 'Área de membros criada com sucesso'}, status=status.HTTP_201_CREATED)

    def _create_member_course(self, product: Product) -> str:
        url = self._build_url('/admin/curso/create')
        data = {'nome': product.name}
        res = self.api.post(url, json=data, headers=self._get_admin_headers())

        membersV2Id = res.json()['id']
        product.membersV2Id = membersV2Id
        product.save(update_fields=['membersV2Id'])

        return membersV2Id

    def update_course(self, user: User, product_id: str, data: dict) -> Response:
        membersV2Id = self._get_product_membersV2Id(search_params=(product_id, user))

        url = self._build_url(f'/admin/curso/update/{membersV2Id}')
        res = self.api.put(url, json=data, headers=self._get_admin_headers())

        return Response(res.json(), status=status.HTTP_200_OK)

    def upload_course_logo(self, user: User, product_id: str, logo: InMemoryUploadedFile) -> Response:
        membersV2Id = self._get_product_membersV2Id(search_params=(product_id, user))

        url = self._build_url(f'/admin/uploadLogoCurso/{membersV2Id}')
        files = {'file': (logo.name, logo.read(), logo.content_type or 'image/png')}
        self.api.post(url, files=files, headers=self._get_admin_headers())

        return Response({'detail': 'Logo enviada com sucesso'}, status=status.HTTP_200_OK)

    def delete_course_logo(self, user: User, product_id: str) -> Response:
        membersV2Id = self._get_product_membersV2Id(search_params=(product_id, user))

        url = self._build_url(f'/admin/deleteLogoCurso/{membersV2Id}')
        self.api.delete(url, headers=self._get_admin_headers())

        return Response({'detail': 'Logo deletada com sucesso'}, status=status.HTTP_204_NO_CONTENT)

    def delete_course(self, user: User, product_id: str) -> Response:
        product = self._get_product_instance_by_id(product_id, user=user)
        membersV2Id = self._get_product_membersV2Id(product)

        url = self._build_url(f'/admin/curso/delete/{membersV2Id}')
        self.api.delete(url, headers=self._get_admin_headers())

        product.membersV2Id = None
        product.save(update_fields=['membersV2Id'])

        return Response({'detail': 'Área de membros deletada com sucesso'}, status=status.HTTP_204_NO_CONTENT)

class MembersV2BannersSDK(MembersV2BaseSDK):
    def list_course_banners(self, user: User, product_id: str) -> Response:
        membersV2Id = self._get_product_membersV2Id(search_params=(product_id, user))

        url = self._build_url(f'/admin/getAllBannerCurso/{membersV2Id}')
        res = self.api.get(url, headers=self._get_admin_headers())

        data = res.json()
        for item in data:
            item.pop('cursoId', None)

        return Response(data, status=status.HTTP_200_OK)

    def create_course_banner(self, user: User, product_id: str, data: dict) -> Response:
        membersV2Id = self._get_product_membersV2Id(search_params=(product_id, user))

        data.update({'cursoId': membersV2Id})
        url = self._build_url('/admin/bannerCurso')

        res = self.api.post(url, json=data, headers=self._get_admin_headers())
        data = res.json()
        data.pop('cursoId', None)

        return Response(data, status=status.HTTP_201_CREATED)

    def update_course_banner(self, user: User, product_id: str, banner_id: str, data: dict) -> Response:
        membersV2Id = self._get_product_membersV2Id(search_params=(product_id, user))

        url = self._build_url(f'/admin/cursoBanner/{banner_id}/curso/{membersV2Id}')
        res = self.api.put(url, json=data, headers=self._get_admin_headers())

        data = res.json()
        data.pop('cursoId', None)

        return Response(data, status=status.HTTP_200_OK)

    def delete_course_banner(self, user: User, product_id: str, banner_id: str) -> Response:
        membersV2Id = self._get_product_membersV2Id(search_params=(product_id, user))

        url = self._build_url(f'/admin/bannerCurso/delete/{banner_id}/curso/{membersV2Id}')
        self.api.delete(url, headers=self._get_admin_headers())

        return Response({'detail': 'Banner deletado com sucesso'}, status=status.HTTP_204_NO_CONTENT)

    def upload_course_banner_file(self, user: User, product_id: str, banner_id: str, banner: InMemoryUploadedFile) -> Response:
        membersV2Id = self._get_product_membersV2Id(search_params=(product_id, user))

        url = self._build_url(f'/admin/uploadImageBannerCurso/{banner_id}/curso/{membersV2Id}')
        files = {'file': (banner.name, banner.read(), banner.content_type or 'image/png')}
        self.api.post(url, files=files, headers=self._get_admin_headers())

        return Response({'detail': 'Imagem do banner enviado com sucesso'}, status=status.HTTP_200_OK)

    def delete_course_banner_file(self, user: User, product_id: str, banner_id: str) -> Response:
        membersV2Id = self._get_product_membersV2Id(search_params=(product_id, user))

        url = self._build_url(f'/admin/bannerCursoRemoveImage/delete/{banner_id}/curso/{membersV2Id}')
        self.api.delete(url, headers=self._get_admin_headers())

        return Response({'detail': 'Imagem do banner deletado com sucesso'}, status=status.HTTP_204_NO_CONTENT)

class MembersV2ModulesSDK(MembersV2BaseSDK):
    def create_module(self, user: User, product_id: str, data: dict) -> Response:
        membersV2Id = self._get_product_membersV2Id(search_params=(product_id, user))

        url = self._build_url('/admin/modulo/create')
        data.update({'cursoId': membersV2Id})

        res = self.api.post(url, json=data, headers=self._get_admin_headers())

        data = res.json()
        data.pop('cursoId', None)

        return Response(data, status=status.HTTP_201_CREATED)

    def delete_module(self, user: User, product_id: str, module_id: str) -> Response:
        membersV2Id = self._get_product_membersV2Id(search_params=(product_id, user))

        url = self._build_url(f'/admin/modulo/delete/{module_id}/curso/{membersV2Id}')
        self.api.delete(url, headers=self._get_admin_headers())

        return Response({'detail': 'Módulo deletado com sucesso'}, status=status.HTTP_204_NO_CONTENT)

    def update_module(self, user: User, product_id: str, module_id: str, data: dict) -> Response:
        membersV2Id = self._get_product_membersV2Id(search_params=(product_id, user))

        url = self._build_url(f'/admin/modulo/update/{module_id}/curso/{membersV2Id}')
        res = self.api.put(url, json=data, headers=self._get_admin_headers())

        return Response(res.json(), status=status.HTTP_200_OK)

    def upload_module_cover(self, user: User, product_id: str, module_id: str, cover: InMemoryUploadedFile) -> Response:
        membersV2Id = self._get_product_membersV2Id(search_params=(product_id, user))

        url = self._build_url(f'/admin/uploadCapaModulo/{module_id}/curso/{membersV2Id}')
        files = {'file': (cover.name, cover.read(), cover.content_type or 'image/png')}
        self.api.post(url, files=files, headers=self._get_admin_headers())

        return Response({'detail': 'Capa enviada com sucesso'}, status=status.HTTP_200_OK)

    def delete_module_cover(self, user: User, product_id: str, module_id: str) -> Response:
        membersV2Id = self._get_product_membersV2Id(search_params=(product_id, user))

        url = self._build_url(f'/admin/deleteCapaModulo/{module_id}/curso/{membersV2Id}')
        self.api.delete(url, headers=self._get_admin_headers())

        return Response({'detail': 'Capa deletada com sucesso'}, status=status.HTTP_204_NO_CONTENT)

class MembersV2LessonsSDK(MembersV2BaseSDK):
    def list_module_lessons(self, user: User, module_id: str) -> Response:
        self._process_user_registration(user)

        url = self._build_url(f'/user/aulas/{module_id}/{user.membersV2Id}')
        res = self.api.get(url, headers=self._get_user_headers(user=user), authUser=user)  # type: ignore

        return Response(res.json(), status=status.HTTP_200_OK)

    def create_module_lesson(self, user: User, product_id: str, module_id: str, data: dict) -> Response:
        membersV2Id = self._get_product_membersV2Id(search_params=(product_id, user))

        data.update({
            'cursoId': membersV2Id,
            'moduloId': module_id,
        })

        url = self._build_url('/admin/aula/create')
        res = self.api.post(url, json=data, headers=self._get_admin_headers())

        return Response(res.json(), status=status.HTTP_201_CREATED)

    def update_module_lesson(self, user: User, product_id: str, lesson_id: str, data: dict) -> Response:
        membersV2Id = self._get_product_membersV2Id(search_params=(product_id, user))

        url = self._build_url(f'/admin/aula/update/{lesson_id}/curso/{membersV2Id}')
        res = self.api.put(url, json=data, headers=self._get_admin_headers())

        return Response(res.json(), status=status.HTTP_201_CREATED)

    def delete_module_lesson(self, user: User, product_id: str, lesson_id: str) -> Response:
        membersV2Id = self._get_product_membersV2Id(search_params=(product_id, user))

        url = self._build_url(f'/admin/aula/delete/{lesson_id}/curso/{membersV2Id}')
        self.api.delete(url, headers=self._get_admin_headers())

        return Response({'detail': 'Aula deletada com sucesso'}, status=status.HTTP_204_NO_CONTENT)

    def list_lesson_files(self, user: User, lesson_id: str) -> Response:
        url = self._build_url(f'/user/getAllArquivosAulaforUser/{lesson_id}')
        res = self.api.get(url, headers=self._get_user_headers(user), authUser=user)  # type: ignore

        return Response(res.json(), status=status.HTTP_200_OK)

    def create_lesson_file(self, user: User, product_id: str, lesson_id: str, data: dict) -> Response:
        membersV2Id = self._get_product_membersV2Id(search_params=(product_id, user))

        url = self._build_url(f'/admin/createArquivo/{lesson_id}/curso/{membersV2Id}')
        res = self.api.post(url, json=data, headers=self._get_admin_headers())

        return Response(res.json(), status=status.HTTP_201_CREATED)

    def update_lesson_file_data(self, user: User, product_id: str, file_id: str, data: dict) -> Response:
        membersV2Id = self._get_product_membersV2Id(search_params=(product_id, user))

        url = self._build_url(f'/admin/updateDadosArquivoAula/{file_id}/curso/{membersV2Id}')
        res = self.api.put(url, json=data, headers=self._get_admin_headers())

        return Response(res.json(), status=status.HTTP_200_OK)

    def upload_lesson_file_item(self, user: User, product_id: str, file_id: str, file_item: InMemoryUploadedFile) -> Response:
        membersV2Id = self._get_product_membersV2Id(search_params=(product_id, user))

        url = self._build_url(f'/admin/uploadArquivoAula/{file_id}/curso/{membersV2Id}')
        files = {'file': (file_item.name, file_item.read(), file_item.content_type or 'application/octet-stream')}
        res = self.api.post(url, files=files, headers=self._get_admin_headers())

        return Response(res.json(), status=status.HTTP_200_OK)

    def delete_lesson_file(self, user: User, product_id: str, file_id: str) -> Response:
        membersV2Id = self._get_product_membersV2Id(search_params=(product_id, user))

        url = self._build_url(f'/admin/deleteArquivoAula/delete/{file_id}/curso/{membersV2Id}')
        self.api.delete(url, headers=self._get_admin_headers())

        return Response({'detail': 'Arquivo deletado com sucesso'}, status=status.HTTP_204_NO_CONTENT)

def update_membersV2_user_info(user: User):
    """Send a request to update the user information on Members V2 if it exists there."""
    if not settings.MEMBERS_V2_BASE_URL or not settings.MEMBERS_V2_API_TOKEN:
        return

    members_v2_sdk = MembersV2BaseSDK()
    members_v2_sdk.update_user_info(user)

@job
def add_membersV2_course_to_user(user: User, product: Product, expires_at: str | datetime.datetime | None = None):
    """
    Add a course to a user on Members V2.

    Args:
        user (User): User instance.
        product (Product): Product instance.
        expires_at (str | None): Expiration date of the course access. Iso format string or None for unlimited access.

    Returns:
        None

    Raises:
        Exception: If MEMBERS_V2_BASE_URL or MEMBERS_V2_API_TOKEN are not set.
        Exception: If there is an error adding the course to the user on Members V2.
        ValidationError: If product does not have a Members V2 ID.
    """
    if not settings.MEMBERS_V2_BASE_URL or not settings.MEMBERS_V2_API_TOKEN:
        raise Exception('Env MEMBERS_V2_BASE_URL or MEMBERS_V2_API_TOKEN are not set.')

    members_v2_sdk = MembersV2CourseSDK()

    expires_at = parse_and_validate_expiration(expires_at)

    product_membersV2Id = members_v2_sdk._get_product_membersV2Id(product)

    members_v2_sdk._add_course_to_user(user, product_membersV2Id, expires_at=expires_at)

def parse_and_validate_expiration(expires_at: str | datetime.datetime | None) -> datetime.datetime | None:
    if isinstance(expires_at, str):
        try:
            expires_at = timezone.datetime.fromisoformat(expires_at)
        except ValueError:
            raise ValidationError({'detail': 'Data de expiração inválida. Formato deve ser ISO 8601, ex: 2021-12-31T23:59:59-03:00'})

    if expires_at and expires_at.tzinfo is None:  # type: ignore
        expires_at = timezone.make_aware(expires_at)

    if expires_at and expires_at < timezone.now():
        raise ValidationError({'detail': 'Data de expiração inválida. Deve ser maior que a data atual.'})

    return expires_at  # type: ignore

@job
def remove_membersV2_user_access(user: User, product: Product):
    """
    Remove user course access on Members V2.

    Args:
        user (User): User instance.
        product (Product): Product instance.

    Returns:
        None

    Raises:
        Exception: If MEMBERS_V2_BASE_URL or MEMBERS_V2_API_TOKEN are not set.
        Exception: If there is an error removing the course from the user on Members V2.
    """
    if not user.membersV2Id:
        return 'User does not have a Members V2 ID.'
    if not product.membersV2Id:
        return 'Product does not have a Members V2 ID.'
    if product.user == user:
        return 'User is the owner of the product.'

    if not settings.MEMBERS_V2_BASE_URL or not settings.MEMBERS_V2_API_TOKEN:
        raise Exception('Env MEMBERS_V2_BASE_URL and/or MEMBERS_V2_API_TOKEN are not set.')

    members_v2_sdk = MembersV2BaseSDK()

    product_membersV2Id = members_v2_sdk._get_product_membersV2Id(product)
    members_v2_sdk._remove_course_from_user(user, product_membersV2Id)

def get_user_membersV2_token(user: User, get_cached_token: bool = True) -> str:
    """
    Get the user token from cache or make a request to login the user on Members V2 if get_cached_token is False.

    Args:
        user (User): User instance.
        get_cached_token (bool): If True, get the token from cache. If False, make a request to login the user.

    Returns:
        str: User token.
    """
    members_v2_sdk = MembersV2BaseSDK()
    if get_cached_token:
        return members_v2_sdk._get_cached_user_token(user)
    else:
        return members_v2_sdk._login_user(user)
