# Generated by Django 4.2.5 on 2023-12-07 20:37

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Video',
            fields=[
                ('id', models.CharField(max_length=255, primary_key=True, serialize=False, unique=True)),
                ('title', models.CharField(max_length=255, verbose_name='Título')),
                ('status', models.CharField(choices=[('queued', 'Na fila para codificação.'), ('processing', 'Processando'), ('encoding', 'Codificando.'), ('finished', 'Finalizado.'), ('videoPlayable', 'Video reprodutível. A codificação terminou em pelo menos uma resolução.'), ('encodingFailed', 'Falha na codificação.'), ('uploadStarted', 'Upload iniciado.'), ('uploadFinished', 'Upload finalizado.'), ('uploadFailed', 'Falha no upload.'), ('captionsGenerated', 'Legendas automáticas geradas.'), ('titleOrDescriptionGenerated', 'Geração automática do título ou descrição finalizada.')], default='created', max_length=255, verbose_name='Status')),
                ('uploadedAt', models.DateTimeField(blank=True, null=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
