# runapscheduler.py

from apscheduler.schedulers.blocking import BlockingScheduler
from apscheduler.triggers.cron import CronTrigger
from django.conf import settings
from django.core.management.base import BaseCommand
from django_apscheduler import util
from django_apscheduler.jobstores import DjangoJobStore
from django_apscheduler.models import DjangoJobExecution

from members.sdk.discord import remove_discord_expired_accesses
from members.sdk.instagram import remove_instagram_expired_accesses
from members.sdk.telegram import remove_telegram_expired_accesses


# The `close_old_connections` decorator ensures that database connections, that have become
# unusable or are obsolete, are closed before and after your job has run. You should use it
# to wrap any jobs that you schedule that access the Django database in any way.
@util.close_old_connections
def delete_old_job_executions(max_age=604_800):
    """
    This job deletes APScheduler job execution entries older than `max_age` from the database.
    It helps to prevent the database from filling up with old historical records that are no
    longer useful.

    :param max_age: The maximum length of time to retain historical job execution records.
                    Defaults to 7 days.
    """
    DjangoJobExecution.objects.delete_old_job_executions(max_age)


class Command(BaseCommand):
    help = "Runs APScheduler."

    def handle(self, *args, **options):
        scheduler = BlockingScheduler(timezone=settings.TIME_ZONE)
        scheduler.add_jobstore(DjangoJobStore(), "default")

        scheduler.add_job(
            remove_instagram_expired_accesses,
            trigger=CronTrigger(minute="*/1"),  # Every minute
            id="remove_instagram_expired_access",  # The `id` assigned to each job MUST be unique
            max_instances=1,
            replace_existing=True,
        )
        print("Added job 'remove_instagram_expired_access'.")

        scheduler.add_job(
            remove_telegram_expired_accesses,
            trigger=CronTrigger(minute="*/1"),  # Every minute
            id="remove_telegram_expired_accesses",  # The `id` assigned to each job MUST be unique
            max_instances=1,
            replace_existing=True,
        )
        print("Added job 'remove_telegram_expired_accesses'.")

        scheduler.add_job(
            remove_discord_expired_accesses,
            trigger=CronTrigger(minute="*/1"),  # Every minute
            id="remove_discord_expired_accesses",  # The `id` assigned to each job MUST be unique
            max_instances=1,
            replace_existing=True,
        )
        print("Added job 'remove_discord_expired_accesses'.")

        scheduler.add_job(
            delete_old_job_executions,
            trigger=CronTrigger(day_of_week="mon", hour="10", minute="00"),
            id="delete_old_job_executions",  # The `id` assigned to each job MUST be unique
            max_instances=1,
            replace_existing=True,
        )
        print("Added weekly job: 'delete_old_job_executions'.")

        try:
            print("Starting scheduler...")
            scheduler.start()
        except KeyboardInterrupt:
            print("Stopping scheduler...")
            scheduler.shutdown()
            print("Scheduler shut down successfully!")
