import os
from pprint import pprint
from urllib.parse import urljoin

import requests
from django.core.management.base import BaseCommand
from rest_framework import status

from members.sdk.telegram import Telegram


class Command(BaseCommand):
    help = 'Config Telegram Webhook.'

    def handle(self, *args, **options):
        # https://core.telegram.org/bots/api#setwebhook

        telegram = Telegram()
        url = f'https://api.telegram.org/bot{telegram.bot_token}/setWebhook'
        secret_token = os.getenv('TELEGRAM_SECRET', '')
        backend_base_url = os.getenv('BACKEND_BASE_URL', 'https://api.cakto.com.br/')
        payload = {
            'url': urljoin(backend_base_url, '/api/members/telegram/webhook/'),
            'allowed_updates': ['message', 'callback_query', 'channel_post'],
            'secret_token': secret_token,  # 1-256 characters. Only characters A-Z, a-z, 0-9, _ and - are allowed.
        }

        res = requests.post(url, json=payload)
        if status.is_success(res.status_code):
            print('Webhook configurado com sucesso!\n')
        else:
            pprint(res.content.decode())

        status_res = requests.post(f'https://api.telegram.org/bot{telegram.bot_token}/getWebhookInfo')
        if status.is_success(status_res.status_code):
            print('Current Webhook Info:')
            pprint(status_res.json())
        else:
            print(status_res.content.decode())
