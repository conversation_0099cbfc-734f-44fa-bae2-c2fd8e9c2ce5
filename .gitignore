# Django #
*.log
*.pot
*.pyc
__pycache__
db.sqlite3
media
images

# Backup files #
*.bak

# Python #
*.py[cod]
*$py.class

# Distribution / packaging
.Python build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# pyenv
.python-version

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# mkdocs documentation
/site

# mypy
.mypy_cache/

# Visual Studio Code #
.vscode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
.history

.DS_Store
mypy.ini
todo
duvidas.txt
.flake8
.env
documents
.dockerignore
banners/
.github/**/*instructions.md
.github/**/*.chatmode.md
aws/**
