#!/bin/bash

# if [ -z "${BACKEND_APP}" ]; then
#     BACKEND_APP=api
# fi

## Start app helpers
start_backend_apps_dev() {
    case "${BACKEND_APP}" in
        api)
            echo "Starting Backend API"
            poetry run python manage.py runserver 0.0.0.0:8000
            ;;
        checkout-api)
            echo "Starting Checkout API"
            poetry run python -m uvicorn main:app --host 0.0.0.0 --port 8080 --reload
            ;;
        *)
            start_workers
            ;;
    esac
}

start_backend_apps() {
    case "${BACKEND_APP}" in
        api)

            echo "Running migrations..."
            poetry run python manage.py migrate --no-input

            echo "Collecting static files..."
            poetry run python manage.py collectstatic --noinput

            echo "Starting Backend API"
            if [ -z "${GUNICORN_CMD_ARGS}" ]; then
                echo "!!! WARNING !!! GUNICORN_CMD_ARGS environment variable is not set. Using default values."
            else
                echo "DEBUG: GUNICORN_CMD_ARGS = ${GUNICORN_CMD_ARGS}"
            fi
            poetry run python -m gunicorn cakto.wsgi:application
            ;;
        checkout-api)
            echo "Starting Checkout API"

            if [ -z "${UVICORN_CMD_ARGS}" ]; then
                echo "!!! WARNING !!! UVICORN_CMD_ARGS environment variable is not set. Using default values."
            else
                echo "DEBUG: UVICORN_CMD_ARGS = ${UVICORN_CMD_ARGS}"
            fi

            poetry run python -m uvicorn main:app ${UVICORN_CMD_ARGS}
            ;;
        *)
            start_workers
            ;;
    esac
}

start_workers() {
    case "${BACKEND_APP}" in
        metrics)
            echo "Starting metrics worker"
            poetry run python manage.py rqworker metrics
            ;;
        pixel_events)
            echo "Starting pixel_events worker"
            poetry run python manage.py rqworker pixel_events cakto_pixel --with-scheduler
            ;;
        rqworker)
            echo "Starting rqworker worker"
            poetry run python manage.py rqworker default --with-scheduler
            ;;
        runapscheduler)
            echo "Starting runapscheduler"
            poetry run python manage.py runapscheduler
            ;;
        *)
            echo "Invalid option for BACKEND_APP env variable. Please use one of: api, checkout-api, metrics, pixel_events, rqworker or runapscheduler"
            exit 1
            ;;
    esac
}

### Install apps helpers

command_exists() {
  command -v "$1" >/dev/null 2>&1
}

install_dev_apps(){

    if ! command_exists uv; then
    # UV
    curl -LsSf https://astral.sh/uv/0.7.2/install.sh | UV_INSTALL_DIR=/usr/bin sh
    fi

    if ! command_exists aws; then
        # AWS CLI
        AWSCLI_VERSION=2.27.48
        curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64-${AWSCLI_VERSION}.zip" -o "awscliv2.zip"
        unzip -o awscliv2.zip
        ./aws/install
        rm awscliv2.zip
        rm -rf ./aws
    fi

    # DEV Tools
    uv pip install awscli-local pytest pytest-cov ruff  --upgrade --system

}
