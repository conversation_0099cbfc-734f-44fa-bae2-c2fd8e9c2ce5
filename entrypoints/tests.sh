#!/bin/bash


# Check if <PERSON><PERSON><PERSON><PERSON>_SETTINGS_MODULE is set
if [ -z "${DJANGO_SETTINGS_MODULE}" ]; then
    echo "DJANGO_SETTINGS_MODULE environment variable is not set"
    exit 1
fi

echo "DEBUG: DJANGO_SETTINGS_MODULE = ${DJANGO_SETTINGS_MODULE}"

SCRIPT_DIR="$(dirname "$(readlink -f "$0")")"
source "${SCRIPT_DIR}/shared_functions.sh"

install_dev_apps

awslocal s3api create-bucket --bucket "${AWS_STORAGE_BUCKET_NAME}"

poetry run python manage.py makemigrations --check --dry-run

poetry run python -m pytest --show-capture=stderr -x --reuse-db