from django.db.models import Count, OuterRef, Subquery, Sum
from django.db.models.functions import TruncDay
from django.http import HttpResponse
from drf_excel.renderers import XLSX<PERSON>enderer
from rest_framework import generics, status
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_csv.renderers import CS<PERSON>enderer

from checkout.models import CheckoutAbandonment
from checkout.serializers import CheckoutAbandonmentExportSerializer, CheckoutAbandonmentPublicSerializer
from gateway.models import Order, Split
from gateway.sdk.splitpay import SplitPay
from product.models import Product
from product.serializers import AffiliateExportSerializer, PerCoproductionSerializer, PerProductExportSerializer
from reports.filters import CreatedAtDateFilter
from reports.utils import CustomizeXLSXRenderer, CutomizeCSVRenderer, get_response_with_updated_keys


class PerProductChartDataMixin:
    def get_queryset(self):
        commission_type = self.request.query_params.get('type', 'producer')  # type:ignore

        if commission_type == 'producer':
            products = Product.objects.filter(user=self.request.user)  # type:ignore
        elif commission_type == 'affiliate':
            products = Product.objects.filter(affiliates__user=self.request.user)  # type:ignore
        elif commission_type == 'coproducer':
            products = Product.objects.filter(coproductions__user=self.request.user)  # type:ignore

        splits_values = (
            Split.objects
            .filter(order=OuterRef('id'), type=commission_type, user=self.request.user)  # type:ignore
            .values('order')
            .annotate(total=Sum('totalAmount'))
            .values('total')
        )

        orders = (
            Order.objects
            .filter(product__in=products, splits__user=self.request.user, splits__type=commission_type,)  # type:ignore
            .annotate(commissionAmount=Subquery(splits_values))
        )
        orders = CreatedAtDateFilter().filter_queryset(self.request, orders.filter(status='paid'), self)  # type:ignore

        return orders

    def get_chart_data(self, *args, **kwargs):
        orders = self.get_queryset()
        orders_count = orders.count()

        chartData = []
        for data in orders.values('product__name').annotate(amount=Sum('commissionAmount'), order_count=Count('id')):
            chartData.append({
                'name': data['product__name'],
                'amount': round(data['amount'], 2),
                'salesPercentage': round((data['order_count'] / orders_count) * 100, 2) if orders_count > 0 else 0,
                'salesCount': data['order_count']
            })

        return sorted(chartData, key=lambda x: x['amount'], reverse=True)

class PerProductAPIView(PerProductChartDataMixin, APIView):
    scope = 'reports'

    def get(self, request, format=None):
        chartData = self.get_chart_data()
        if isinstance(chartData, Response):
            return chartData

        return Response({'results': chartData}, status=status.HTTP_200_OK)

class PerProductExportXLSX(PerProductChartDataMixin, generics.ListAPIView, CustomizeXLSXRenderer):
    renderer_classes = [XLSXRenderer]  # Use XLSXRenderer for this view
    scope = 'reports'
    serializer_class = PerProductExportSerializer
    xlsx_use_labels = True

    def list(self, request, *args, **kwargs):
        chartData = self.get_chart_data()
        if isinstance(chartData, Response):
            return chartData
        serializer = self.get_serializer(chartData, many=True)

        # Create a response with XLSX content type
        response = Response(serializer.data, content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        response['Content-Disposition'] = 'attachment; filename="report.xlsx"'
        return response

class PerProductExportCSV(PerProductChartDataMixin, CutomizeCSVRenderer, generics.ListAPIView):
    renderer_classes = [CSVRenderer]  # Use CSVRenderer for this view
    scope = 'reports'
    serializer_class = PerProductExportSerializer

    labels = {
        'Nome do Produto': 'productName',
        'Total em vendas': 'amount',
        'Porcentagem das vendas': 'salesPercentage',
        'Quantidade de vendas': 'salesCount',
    }

    def list(self, request, *args, **kwargs):
        chartData = self.get_chart_data()
        if isinstance(chartData, Response):
            return chartData
        serializer = self.get_serializer(chartData, many=True)

        # Create a response with CSV content type
        response = Response(get_response_with_updated_keys(serializer, self.labels), content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="report.csv"'
        return response

class PerAffiliateChartDataMixin:
    def get_queryset(self):
        split_values = (
            Split.objects
            .filter(type='affiliate', order=OuterRef('id'))
            .values('order')
            .annotate(total=Sum('totalAmount'))
            .values('total')
        )

        orders = (
            Order.objects
            .filter(product__user=self.request.user, status='paid', affiliate__isnull=False)  # type:ignore
            .annotate(affiliate_amount=Subquery(split_values))
        )
        orders = CreatedAtDateFilter().filter_queryset(self.request, orders, self)  # type:ignore

        affiliate_email = self.request.query_params.get('affiliateEmail', None)  # type:ignore
        if affiliate_email:
            orders = orders.filter(affiliate__user__email=affiliate_email)

        return orders

    def get_chart_data(self, *args, **kwargs):
        orders = self.get_queryset()
        orders_count = orders.count()
        chartData = []

        for data in orders.values('affiliate__user__email').annotate(total=Sum('affiliate_amount'), order_count=Count('id')):
            chartData.append({
                'email': data['affiliate__user__email'],
                'amount': round(data['total'], 2),
                'salesPercentage': round((data['order_count'] / orders_count) * 100, 2) if orders_count > 0 else 0,
                'salesCount': data['order_count']
            })

        return sorted(chartData, key=lambda x: x['amount'], reverse=True)

class PerAffiliateAPIView(PerAffiliateChartDataMixin, APIView):
    scope = 'reports'

    def get(self, request):
        chartData = self.get_chart_data()
        if isinstance(chartData, Response):
            return chartData
        return Response({'results': chartData}, status=status.HTTP_200_OK)

class PerAffiliateExportXLSX(PerAffiliateChartDataMixin, generics.ListAPIView, CustomizeXLSXRenderer):
    renderer_classes = [XLSXRenderer]  # Use XLSXRenderer for this view
    scope = 'reports'
    serializer_class = AffiliateExportSerializer
    xlsx_use_labels = True

    def list(self, request, *args, **kwargs):
        chartData = self.get_chart_data()
        if isinstance(chartData, Response):
            return chartData
        serializer = self.get_serializer(chartData, many=True)

        # Create a response with XLSX content type
        response = Response(serializer.data, content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        response['Content-Disposition'] = 'attachment; filename="report.xlsx"'
        return response

class PerAffiliateExportCSV(PerAffiliateChartDataMixin, CutomizeCSVRenderer, generics.ListAPIView):
    renderer_classes = [CSVRenderer]  # Use CSVRenderer for this view
    scope = 'reports'
    serializer_class = AffiliateExportSerializer

    labels = {
        'Email do Afiliado': 'email',
        'Total em Vendas': 'amount',
        'Porcentagem das Vendas': 'salesPercentage',
        'Quantidade de Vendas': 'salesCount',
    }

    def list(self, request, *args, **kwargs):
        chartData = self.get_chart_data()
        if isinstance(chartData, Response):
            return chartData
        serializer = self.get_serializer(chartData, many=True)

        # Create a response with CSV content type
        response = Response(get_response_with_updated_keys(serializer, self.labels), content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="report.csv"'
        return response

class PerCoproductionChartDataMixin:
    def get_queryset(self):
        chart_type = self.request.query_params.get('type', 'producer')  # type:ignore
        commission_type = 'coproducer' if chart_type == 'received' else 'producer'
        user = self.request.user  # type:ignore

        splits_values = (
            Split.objects
            .filter(order=OuterRef('id'), type=commission_type, user=self.request.user)  # type:ignore
            .values('order')
            .annotate(total=Sum('totalAmount'))
            .values('total')
        )

        orders = (
            Order.objects
            .filter(splits__type=commission_type, splits__user=user, status='paid')
            .annotate(commissionAmount=Subquery(splits_values), day=TruncDay('createdAt'))
        )
        orders = CreatedAtDateFilter().filter_queryset(self.request, orders, self)  # type:ignore
        return orders

    def get_chartData(self, *args, **kwargs):
        orders = self.get_queryset()
        chartData = []

        for data in orders.values('day').annotate(value=Sum('commissionAmount')):
            chartData.append({
                'date': data['day'],
                'value': round(data['value'], 2)  # type:ignore
            })
        return chartData

class PerCoproductionAPIView(PerCoproductionChartDataMixin, APIView):
    scope = 'reports'

    def get(self, request):
        chartData = self.get_chartData(request)

        return Response({'results': chartData}, status=status.HTTP_200_OK)

class PerCoproductionExportXLSX(PerCoproductionChartDataMixin, generics.ListAPIView, CustomizeXLSXRenderer):
    renderer_classes = [XLSXRenderer]  # Use XLSXRenderer for this view
    scope = 'reports'
    serializer_class = PerCoproductionSerializer
    xlsx_use_labels = True

    def list(self, request, *args, **kwargs):
        serializer = self.get_serializer(self.get_chartData(), many=True, context={'request': self.request})

        # Create a response with XLSX content type
        response = Response(serializer.data, content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        response['Content-Disposition'] = 'attachment; filename="report.xlsx"'
        return response

class PerCoproductionExportCSV(PerCoproductionChartDataMixin, CutomizeCSVRenderer, generics.ListAPIView):
    renderer_classes = [CSVRenderer]  # Use CSVRenderer for this view
    scope = 'reports'
    serializer_class = PerCoproductionSerializer

    labels = {
        'Data': 'date',
        'Total': 'value',
    }

    def list(self, request, *args, **kwargs):
        serializer = self.get_serializer(self.get_chartData(), many=True, context={'request': self.request})

        # Create a response with CSV content type
        response = Response(get_response_with_updated_keys(serializer, self.labels), content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="report.csv"'
        return response

class CheckoutAbandonmentQuerySetMixin:
    def get_queryset(self, *args, **kwargs):
        products = self.request.query_params.get('products')  # type:ignore

        queryset = CheckoutAbandonment.objects.filter(
            offer__product__user=self.request.user  # type:ignore
        ).select_related('offer')

        if products:
            queryset = queryset.filter(offer__product__in=products.split(','))

        queryset = CreatedAtDateFilter().filter_queryset(self.request, queryset, self)  # type:ignore
        return queryset

class CheckoutAbandonmentReportAPIView(CheckoutAbandonmentQuerySetMixin, generics.ListAPIView):
    scope = 'reports'
    serializer_class = CheckoutAbandonmentPublicSerializer

class CheckoutAbandonmentExportXLSX(CheckoutAbandonmentQuerySetMixin, generics.ListAPIView, CustomizeXLSXRenderer):
    renderer_classes = [XLSXRenderer]  # Use XLSXRenderer for this view
    scope = 'reports'
    serializer_class = CheckoutAbandonmentExportSerializer
    xlsx_use_labels = True

    def list(self, request, *args, **kwargs):
        serializer = self.get_serializer(self.get_queryset(), many=True)

        # Create a response with XLSX content type
        response = Response(serializer.data, content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        response['Content-Disposition'] = 'attachment; filename="report.xlsx"'
        return response

class CheckoutAbandonmentExportCSV(CheckoutAbandonmentQuerySetMixin, CutomizeCSVRenderer, generics.ListAPIView):
    renderer_classes = [CSVRenderer]  # Use CSVRenderer for this view
    scope = 'reports'
    serializer_class = CheckoutAbandonmentExportSerializer

    labels = {
        'Oferta': 'offerName',
        'ID da Oferta': 'offerId',
        'Preço da Oferta': 'offerPrice',
        'Nome do Cliente': 'customerName',
        'Email do Cliente': 'customerEmail',
        'Celular do Cliente': 'customerCellphone',
        'URL do Checkout': 'checkoutUrl',
        'Data': 'createdAt',
    }

    def list(self, request, *args, **kwargs):
        serializer = self.get_serializer(self.get_queryset(), many=True)

        # Create a response with CSV content type
        response = Response(get_response_with_updated_keys(serializer, self.labels), content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="report.csv"'
        return response

class PendingBalanceReportAPIView(APIView):
    scope = 'reports'

    def get(self, request):
        company = request.user._company
        split = SplitPay()

        response = split.getPendingBalance(account_id=company.externalId, query_params=request.query_params)

        if status.is_success(response.status_code) or status.is_client_error(response.status_code):
            return Response(response.json(), status=response.status_code)

        raise Exception(
            'Error fetching pending balance'
            f' | extenalId {company.externalId}'
            f' | error: {response.text}'
        )

class PendingBalanceExportXLSX(APIView):
    scope = 'reports'
    xlsx_use_labels = True

    def get(self, request, *args, **kwargs):
        company = request.user._company
        split = SplitPay()

        response = split.getPendingBalanceXLSX(account_id=company.externalId, query_params=request.query_params)

        if status.is_success(response.status_code):
            # Create a response with XLSX content type
            report_response = HttpResponse(response.content, content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
            report_response['Content-Disposition'] = 'attachment; filename="report.xlsx"'
            return report_response

        elif status.is_client_error(response.status_code):
            return Response(response.json(), status=response.status_code)

        raise Exception(
            'Error fetching pending balance xslsx'
            f' | extenalId {company.externalId}'
            f' | error: {response.text}'
        )

class PendingBalanceExportCSV(APIView):
    scope = 'reports'

    def get(self, request, *args, **kwargs):
        company = request.user._company
        split = SplitPay()

        response = split.getPendingBalanceCSV(account_id=company.externalId, query_params=request.query_params)

        if status.is_success(response.status_code):
            # Create a response with CSV content type
            report_response = HttpResponse(response.content, content_type='text/csv')
            report_response['Content-Disposition'] = 'attachment; filename="report.csv"'
            return report_response

        elif status.is_client_error(response.status_code):
            return Response(response.json(), status=response.status_code)

        raise Exception(
            'Error fetching pending balance csv'
            f' | extenalId {company.externalId}'
            f' | error: {response.text}'
        )

class PendingBalanceProductReportAPIView(APIView):
    scope = 'reports'

    def get(self, request):
        company = request.user._company
        split = SplitPay()

        response = split.getPendingBalanceProduct(account_id=company.externalId, query_params=request.query_params)

        if status.is_success(response.status_code) or status.is_client_error(response.status_code):
            return Response(response.json(), status=response.status_code)

        raise Exception(
            'Error fetching pending balance products'
            f' | extenalId {company.externalId}'
            f' | error: {response.text}'
        )
