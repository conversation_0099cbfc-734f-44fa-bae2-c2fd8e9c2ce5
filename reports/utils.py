from collections import OrderedDict

class CustomizeXLSXRenderer:
    """Just customize the style of the excel file for XLSXRenderer from drf_excel."""
    column_header = {
        'height': 17,
        'style': {
            'alignment': {
                'horizontal': 'center',
                'vertical': 'center',
            },
            'font': {
                'bold': True,
            }
        },
    }
    body = {
        'height': 17,
    }
    xlsx_use_labels = True

class CutomizeCSVRenderer:
    def get_renderer_context(self):
        assert hasattr(self, 'labels'), (
            'You must set the `.labels` attribute before rendering.'
        )
        context = super().get_renderer_context()  # type:ignore
        context['header'] = [key for key in self.labels.keys()]
        return context

def get_nested_data(data, key):
    if '.' in key:
        key, sub_key = key.split('.', 1)
        return get_nested_data(data.get(key, {}), sub_key)
    return data.get(key, '')

def get_response_with_updated_keys(serializer, labels):
    new_response = []
    for data in serializer.data:  # type:ignore
        new_data = OrderedDict.fromkeys(labels.keys())
        for key in new_data.keys():
            if '.' in labels[key]:
                new_data[key] = get_nested_data(data, labels[key])
            else:
                new_data[key] = data.get(labels[key], '')
        new_response.append(new_data)
    return new_response
