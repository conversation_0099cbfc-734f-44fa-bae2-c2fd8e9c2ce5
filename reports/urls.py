from django.urls import path

from . import views

urlpatterns = [
    # Product
    path('reports/product/', views.PerProductAPIView.as_view()),
    path('reports/product/export/xlsx/', views.PerProductExportXLSX.as_view()),
    path('reports/product/export/csv/', views.PerProductExportCSV.as_view()),

    # Affiliate
    path('reports/affiliate/', views.PerAffiliateAPIView.as_view()),
    path('reports/affiliate/export/xlsx/', views.PerAffiliateExportXLSX.as_view()),
    path('reports/affiliate/export/csv/', views.PerAffiliateExportCSV.as_view()),

    # Coproduction
    path('reports/coproduction/', views.PerCoproductionAPIView.as_view()),
    path('reports/coproduction/export/xlsx/', views.PerCoproductionExportXLSX.as_view()),
    path('reports/coproduction/export/csv/', views.PerCoproductionExportCSV.as_view()),

    # Checkout Abandonment
    path('reports/checkout_abandonment/', views.CheckoutAbandonmentReportAPIView.as_view()),
    path('reports/checkout_abandonment/export/xlsx/', views.CheckoutAbandonmentExportXLSX.as_view()),
    path('reports/checkout_abandonment/export/csv/', views.CheckoutAbandonmentExportCSV.as_view()),

    # Pending Balance
    path('reports/pending_balance/', views.PendingBalanceReportAPIView.as_view()),
    path('reports/pending_balance/export/xlsx/', views.PendingBalanceExportXLSX.as_view()),
    path('reports/pending_balance/export/csv/', views.PendingBalanceExportCSV.as_view()),
    path('reports/pending_balance/product/', views.PendingBalanceProductReportAPIView.as_view()),
]
