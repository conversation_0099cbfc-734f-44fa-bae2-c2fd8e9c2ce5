from rest_framework import filters
from django.utils import timezone

class CreatedAtDateFilter(filters.BaseFilterBackend):
    """
    A custom filter backend that filters a queryset by the `createdAt` field based on a start and end date.

    The start and end dates are passed as query parameters in the format '%d-%m-%Y'.
    """

    def filter_queryset(self, request, queryset, view):
        """
        Filters the queryset based on the 'startDate' and 'endDate' query parameters.
        The start and end dates are passed as query parameters in the format '%d-%m-%Y'

        Args:
            request : rest_framework.request.Request
                The request that this view is responding to.
            queryset : django.db.models.query.QuerySet
                The original queryset that needs to be filtered.
            view : rest_framework.views.APIView
                The view that this filter backend is being used in.

        Returns:
                The filtered queryset with the 'createdAt' field within the range of 'startDate' and 'endDate'.
        """
        start_date = request.query_params.get('startDate')
        end_date = request.query_params.get('endDate')

        if start_date and end_date:
            start_date, end_date = parse_dates_and_adjust_timezone(start_date, end_date)

            if end_date >= start_date:
                queryset = queryset.filter(createdAt__range=(start_date, end_date))

        return queryset

def parse_dates_and_adjust_timezone(start_date, end_date):
    start_date = timezone.datetime.strptime(start_date, '%d-%m-%Y').date()
    end_date = timezone.datetime.strptime(end_date, '%d-%m-%Y').date()

    # Combine with time and make timezone-aware
    start_date = timezone.make_aware(timezone.datetime.combine(start_date, timezone.datetime.min.time()))
    end_date = timezone.make_aware(timezone.datetime.combine(end_date, timezone.datetime.max.time()))

    # Convert to local time
    start_date = timezone.localtime(start_date)
    end_date = timezone.localtime(end_date)

    return start_date, end_date
