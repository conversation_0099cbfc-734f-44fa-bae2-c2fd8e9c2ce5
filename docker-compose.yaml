services:

# Main API
  cakto-backend:
    container_name: cakto-backend-local
    build:
      context: .
      dockerfile: Dockerfile
    entrypoint: /app/entrypoints/development.sh
    volumes:
      - .:/app
    environment:
      - AWS_ENDPOINT_URL=http://localstack:4566
      - BACKEND_APP=api
      - INSTALL_DEV_DEPS=true
      - START_DEV_APP=true
    ports:
      - "8000:8000"
    env_file:
      - .env.devcontainer
    depends_on:
      - redis
      - db
      - localstack
    healthcheck:
        test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
        interval: 30s
        timeout: 10s
        retries: 10

# Checkout API
  cakto-checkout:
    container_name: cakto-checkout-local
    build:
      context: .
      dockerfile: Dockerfile
    entrypoint: /app/entrypoints/development.sh
    volumes:
      - .:/app
    environment:
      - AWS_ENDPOINT_URL=http://localstack:4566
      - BACKEND_APP=checkout-api
      - INSTALL_DEV_DEPS=true
      - START_DEV_APP=true
    ports:
      - "8080:8080"
    env_file:
      - .env.devcontainer
    depends_on:
      cakto-backend:
        condition: service_healthy
        restart: true
    healthcheck:
        test: ["CMD", "curl", "-f", "http://localhost:8080/docs"]
        interval: 30s
        timeout: 10s
        retries: 10

# WORKERS

  cakto-rqworker-cakto-metrics:
    container_name: cakto-rqworker-metrics-local
    build:
      context: .
      dockerfile: Dockerfile
    volumes:
      - .:/app
    environment:
      - AWS_ENDPOINT_URL=http://localstack:4566
      - BACKEND_APP=metrics
      - START_DEV_APP=true
    env_file:
      - .env.devcontainer
    depends_on:
      cakto-backend:
        condition: service_healthy
        restart: true

  cakto-rqworker-pixel_events:
    container_name: cakto-rqworker-pixel_events-local
    build:
      context: .
      dockerfile: Dockerfile
    volumes:
      - .:/app
    environment:
      - AWS_ENDPOINT_URL=http://localstack:4566
      - BACKEND_APP=pixel_events
      - START_DEV_APP=true
    env_file:
      - .env.devcontainer
    depends_on:
      cakto-backend:
        condition: service_healthy
        restart: true

  cakto-rqworker:
    container_name: cakto-rqworker-local
    build:
      context: .
      dockerfile: Dockerfile
    volumes:
      - .:/app
    environment:
      - AWS_ENDPOINT_URL=http://localstack:4566
      - BACKEND_APP=rqworker
      - START_DEV_APP=true
    env_file:
      - .env.devcontainer
    depends_on:
      cakto-backend:
        condition: service_healthy
        restart: true

# SCHEDULERS

  cakto-runapscheduler:
    container_name: cakto-runapscheduler-local
    build:
      context: .
      dockerfile: Dockerfile
    volumes:
      - .:/app
    environment:
      - AWS_ENDPOINT_URL=http://localstack:4566
      - BACKEND_APP=runapscheduler
      - START_DEV_APP=true
    env_file:
      - .env.devcontainer
    depends_on:
      cakto-backend:
        condition: service_healthy
        restart: true


# REDIS

  redis:
    container_name: redis-local
    image: redis:7.4-alpine3.20
    ports:
      - "6379:6379"

# DATABASE

  db:
    container_name: postgres-local
    image: postgres:14.13-alpine3.20
    restart: always
    shm_size: 128mb
    environment:
      POSTGRES_USER: caktouser
      POSTGRES_PASSWORD: caktopassword
      POSTGRES_DB: caktodb
    ports:
      - "5432:5432"

  # S3
  localstack:
    container_name: "localstack-local"
    image: localstack/localstack:3.7.2
    ports:
      - "127.0.0.1:4566:4566"
      - "127.0.0.1:4510-4559:4510-4559"
    volumes:
      - "/var/run/docker.sock:/var/run/docker.sock"
    environment:
      - SERVICES=s3
      - DEBUG=1
      - AWS_ACCESS_KEY_ID=aws_s3_access_key_id
      - AWS_SECRET_ACCESS_KEY=aws_secret_access_key
      - DOCKER_HOST=unix:///var/run/docker.sock
