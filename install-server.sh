#!/bin/bash

set -euo pipefail

# Pede AWS Access Key ID
read -p "Informe a AWS Access Key ID: " AWS_ACCESS_KEY

# Pede AWS Secret Access Key (sem ecoar no terminal)
read -s -p "Informe a AWS Secret Access Key: " AWS_SECRET_KEY
echo ""

echo "🔌 Verificando conexão com cluster..."
kubectl cluster-info

# NGINX Ingress
echo "🌐 Instalando ingress-nginx..."
helm repo add ingress-nginx https://kubernetes.github.io/ingress-nginx
helm repo update
helm upgrade --install nginx-ingress ingress-nginx/ingress-nginx -n default \
  --set controller.publishService.enabled=true \
  --set controller.config.limit-req-status-code="429" \
  --set controller.config.use-forwarded-headers="true" \
  --set controller.config.proxy-real-ip-cidr="0.0.0.0/0"

# cert-manager
echo "🔐 Instalando cert-manager..."
kubectl create ns cert-manager || true
kubectl apply -f ./kubernetes/cluster-config/cert-manager/cert-manager.yaml
kubectl apply -f ./kubernetes/cluster-config/cert-manager/letsencrypt-issuer.yaml

# metrics-server
echo "📊 Instalando metrics-server..."
kubectl apply -f ./kubernetes/cluster-config/metrics-server/deploy-metrics-server.yaml

# kube-state-metrics
echo "📈 Instalando kube-state-metrics..."
mkdir -p /tmp/kube-state-metrics
git clone --depth 1 https://github.com/kubernetes/kube-state-metrics.git /tmp/kube-state-metrics
kubectl apply -k /tmp/kube-state-metrics/examples/standard/

# image-pruner
echo "🧹 Instalando image-pruner..."
kubectl apply -k ./kubernetes/cluster-config/image-pruner

# KEDA
echo "⚙️ Instalando KEDA..."
helm repo add kedacore https://kedacore.github.io/charts && helm repo update
helm install keda kedacore/keda --version 2.17.0 --namespace keda --create-namespace

# External Secrets Operator (ESO)
echo "🔐 Instalando ESO..."
helm repo add external-secrets https://charts.external-secrets.io && helm repo update
helm install external-secrets \
  external-secrets/external-secrets \
  -n external-secrets \
  --create-namespace \
  --version 0.16.2 \
  --set installCRDs=true

echo "🔐 Criando Secret com access-key e secret-access-key..."
kubectl create secret generic awssm-secret -n external-secrets \
  --from-literal="access-key=$AWS_ACCESS_KEY" \
  --from-literal="secret-access-key=$AWS_SECRET_KEY" \
  --dry-run=client -o yaml | kubectl apply -f -

echo "🔐 Aplicando ClusterSecretStore (AWS)..."
kubectl apply -f ./kubernetes/cluster-config/external-secrets/aws-cluster-store.yaml

# DataDog
echo "📡 Instalando DataDog operator..."
helm repo add datadog https://helm.datadoghq.com && helm repo update
helm install datadog-operator datadog/datadog-operator \
  -n datadog \
  --create-namespace \
  --version 2.11.1

echo "✅ Cluster de produção configurado com sucesso."

