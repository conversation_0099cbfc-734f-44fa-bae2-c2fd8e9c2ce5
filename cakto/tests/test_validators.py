from django.test import TestCase

from django.core.exceptions import ValidationError

from cakto.validators import CaptialLetterValidator, LowerCaseValidator, MinimunNumberValidator, SymbolValidator


class TestCaptialLetterValidator(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.validator = CaptialLetterValidator()
        cls.validator_custom = CaptialLetterValidator(number_of_capitals=2)

    def test_validate_with_capital_letter(self):
        # Should pass with at least one capital letter
        self.validator.validate("Abcdef")
        self.validator.validate("abcDef")
        self.validator.validate("ABCDEF")

    def test_validate_without_capital_letter(self):
        # Should fail without capital letters
        with self.assertRaises(ValidationError) as context:
            self.validator.validate("abcdef")

        self.assertEqual(
            "A senha deve conter pelo menos 1 letra(s) maiúscula(s).",
            context.exception.messages[0]
        )

    def test_validate_with_custom_capital_requirement(self):
        # Should pass with at least two capitals
        self.validator_custom.validate("ABcdef")

        # Should fail with only one capital
        with self.assertRaises(ValidationError):
            self.validator_custom.validate("Abcdef")

    def test_get_help_text(self):
        self.assertEqual("Sua senha deve conter 1 letra(s) maiúscula(s).", self.validator.get_help_text())
        self.assertEqual("Sua senha deve conter 2 letra(s) maiúscula(s).", self.validator_custom.get_help_text())


class TestLowerCaseValidator(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.validator = LowerCaseValidator()
        cls.validator_custom = LowerCaseValidator(number_of_lower_case_letters=3)

    def test_validate_with_lowercase_letter(self):
        # Should pass with at least one lowercase letter
        self.validator.validate("Abcdef")
        self.validator.validate("ABCDEf")
        self.validator.validate("abcdef")

    def test_validate_without_lowercase_letter(self):
        # Should fail without lowercase letters
        with self.assertRaises(ValidationError) as context:
            self.validator.validate("ABCDEF")

        self.assertEqual(
            "A senha deve conter pelo menos 1 letra(s) minúscula(s).",
            context.exception.messages[0]
        )

    def test_validate_with_custom_lowercase_requirement(self):
        # Should pass with at least three lowercase letters
        self.validator_custom.validate("ABCdef")

        # Should fail with fewer than three lowercase letters
        with self.assertRaises(ValidationError):
            self.validator_custom.validate("ABCde123")

    def test_get_help_text(self):
        self.assertEqual("Sua senha deve conter 1 letra(s) minúscula(s).", self.validator.get_help_text())
        self.assertEqual("Sua senha deve conter 3 letra(s) minúscula(s).", self.validator_custom.get_help_text())


class TestMinimunNumberValidator(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.validator = MinimunNumberValidator()
        cls.validator_custom = MinimunNumberValidator(minimal_number_quantity=2)

    def test_validate_with_number(self):
        # Should pass with at least one number
        self.validator.validate("abc1def")
        self.validator.validate("123456")
        self.validator.validate("abc123")

    def test_validate_without_number(self):
        # Should fail without numbers
        with self.assertRaises(ValidationError) as context:
            self.validator.validate("abcdef")

        self.assertEqual(
            "A senha deve conter pelo menos 1 número(s).",
            context.exception.messages[0]
        )

    def test_validate_with_custom_number_requirement(self):
        # Should pass with at least two numbers
        self.validator_custom.validate("abc12def")

        # Should fail with fewer than two numbers
        with self.assertRaises(ValidationError):
            self.validator_custom.validate("abc1def")

    def test_get_help_text(self):
        self.assertEqual("Sua senha deve conter 1 número(s).", self.validator.get_help_text())
        self.assertEqual("Sua senha deve conter 2 número(s).", self.validator_custom.get_help_text())


class TestSymbolValidator(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.validator = SymbolValidator()
        cls.validator_custom = SymbolValidator(number_of_symbols=2, symbols="!@#")

    def test_validate_with_symbol(self):
        # Should pass with at least one symbol from the default list
        self.validator.validate("abc!def")
        self.validator.validate("abc@def")
        self.validator.validate("abc#$%^&*def")
        self.validator.validate("abc.")

    def test_validate_without_symbol(self):
        # Should fail without symbols
        with self.assertRaises(ValidationError) as context:
            self.validator.validate("abcdef123")

        self.assertEqual(
            "A senha deve conter pelo menos 1 caracter(es) especial(is).",
            context.exception.messages[0]
        )

    def test_validate_with_custom_symbol_requirement(self):
        # Should pass with at least two symbols from custom list
        self.validator_custom.validate("abc!@def")

        # Should fail with fewer than two symbols from custom list
        with self.assertRaises(ValidationError):
            self.validator_custom.validate("abc!def")

        # Should fail when using symbols not in the custom list
        with self.assertRaises(ValidationError):
            self.validator_custom.validate("abc$%def")

    def test_get_help_text(self):
        self.assertEqual("Sua senha deve conter 1 caracter(es) especial(is).", self.validator.get_help_text())
        self.assertEqual("Sua senha deve conter 2 caracter(es) especial(is).", self.validator_custom.get_help_text())
