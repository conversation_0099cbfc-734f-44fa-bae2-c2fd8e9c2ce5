import unittest

from cakto.enums import LabeledEnum


class EnumMock(LabeledEnum):
    FIRST = ('first', 'First description')
    SECOND = ('second', 'Second description')

class TestLabeledEnumMethods(unittest.TestCase):
    def test_properties(self):
        self.assertEqual(EnumMock.FIRST.description, 'First description')
        self.assertEqual(EnumMock.FIRST.id, 'first')

        self.assertEqual(EnumMock.SECOND.description, 'Second description')
        self.assertEqual(EnumMock.SECOND.id, 'second')

    def test_as_list(self):
        expected = [
            {'type': 'first', 'name': 'First description'},
            {'type': 'second', 'name': 'Second description'}
        ]
        self.assertEqual(EnumMock.as_list(), expected)

    def test_choices(self):
        expected = [
            ('first', 'First description'),
            ('second', 'Second description')
        ]
        self.assertEqual(EnumMock.choices(), expected)

    def test_get_member_found(self):
        self.assertEqual(EnumMock.get_member('first'), EnumMock.FIRST)
        self.assertEqual(EnumMock.get_member('second'), EnumMock.SECOND)

    def test_get_member_not_found(self):
        self.assertIsNone(EnumMock.get_member('nonexistent'))
