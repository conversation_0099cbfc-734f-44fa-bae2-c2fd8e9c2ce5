from datetime import datetime
from typing import Any, Literal, Union

import django_rq
import requests
from rq.exceptions import NoSuchJobError
from rq.job import Job


def is_job_scheduled(job_id):
    try:
        job = Job.fetch(job_id, connection=django_rq.get_connection())
        return job.is_queued or job.is_started
    except NoSuchJobError:
        return False

def request_without_wait_response(
    method: Literal['get', 'post', 'put', 'patch', 'delete'],
    url: str,
    headers: dict[Any, Any],
    payload: dict[Any, Any],
    connection_timeout: Union[int, float] | None = None,
    **kwargs
) -> None:
    """
    This function sends an HTTP request through requests library using the specified method, URL, headers and payload.
    It waits for the specified connection timeout to establish a connection but does not wait for the response.

    It will ignore the requests.exceptions.ReadTimeout exception witch is caught and ignored.
    So if you set a connection timeout of 1 second and the connection is not established in that time, the function will raise
    a requests.exceptions.ConnectionError exception.

    Args:
        method (Literal['get', 'post', 'put', 'patch', 'delete']): The HTTP method to use for the request.
        url (str): The URL to which the request is sent.
        headers (dict[Any, Any]): The headers to include in the request.
        payload (dict[Any, Any]): The JSON payload to include in the request.
        connection_timeout (Union[int, float]) | None: The maximum time to wait for a connection to be established. If None, will wait indefinitely.
        **kwargs: Additional keyword arguments to pass to the request function.

    Returns:
        None

    Raises:
        requests.exceptions.ConnectionError: If the connection is not established in the specified connection timeout.
    """
    request_executor = getattr(requests, method)

    try:
        # wait connection_timout seconds to connect and nothing to receive response
        request_executor(url, headers=headers, json=payload, timeout=(connection_timeout, 0.001), **kwargs)
    except requests.exceptions.ReadTimeout:
        pass

def normalize_datetime_input(date: Union[str, datetime]) -> datetime:
    """Given a date in string or datetime format, return a datetime object."""
    if isinstance(date, str):
        return datetime.fromisoformat(date)
    elif isinstance(date, datetime):
        return date
    else:
        raise TypeError(f'Expected type "str" or "datetime", got {type(date)}')
