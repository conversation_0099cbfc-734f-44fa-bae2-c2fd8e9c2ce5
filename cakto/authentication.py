from rest_framework_simplejwt.authentication import J<PERSON><PERSON>uthentication
from rest_framework_simplejwt.exceptions import InvalidToken
from django.core.cache import cache
from rest_framework_simplejwt.tokens import UntypedToken
from django.conf import settings
import jwt

class CustomJWTAuthentication(JWTAuthentication):
    def get_validated_token(self, raw_token):
        # Decode the token to get user information
        try:
            validated_token = super().get_validated_token(raw_token)
            # decoded_token = jwt.decode(raw_token, settings.SECRET_KEY, algorithms=["HS256"])
            # user_id = decoded_token.get('user_id')
            # if user_id == 124:
            #     raise InvalidToken('Token has expired.')

            return validated_token
        except jwt.ExpiredSignatureError:
            raise InvalidToken('Token has expired.')
        except jwt.DecodeError:
            raise InvalidToken('Error decoding token.')
