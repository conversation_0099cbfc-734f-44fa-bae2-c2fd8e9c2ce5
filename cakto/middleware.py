import re
from django.contrib.sessions.middleware import SessionMiddleware

class DisableSessionMiddleware(SessionMiddleware):
    def process_request(self, request):
        # Disable session handling by setting request.session to None
        if re.match(r'^\/api\/product\/checkout\/[a-zA-Z0-9]+\/(\?.*)?$', request.get_full_path()):
            request.session = {}  # type:ignore
            return None
        else:
            return super().process_request(request)
