{% extends "oauth2_provider/base.html" %}

{% load i18n %}
{% block content %}
    <div class="w-full">
        {% if not error %}
            <form id="authorizationForm" method="post" class="w-full">
                <h3 class="text-xl font-bold text-emerald-600 mb-4">{% trans "Autorizar" %} {{ application.name }}?</h3>
                {% csrf_token %}

                {% for field in form %}
                    {% if field.is_hidden %}
                        {{ field }}
                    {% endif %}
                {% endfor %}

                <div class="bg-slate-50 p-4 rounded-md mb-6 border border-slate-200">
                    <p class="text-sm text-slate-700 font-medium mb-2">{% trans "O aplicativo requer as seguintes permissões" %}</p>
                    <ul class="list-disc pl-5 space-y-1">
                        {% for scope in scopes_descriptions %}
                            <li class="text-sm text-slate-600">{{ scope }}</li>
                        {% endfor %}
                    </ul>
                </div>

                {% if form.errors %}
                    <div class="bg-red-50 border border-red-200 text-red-700 p-3 rounded-md mb-4">
                        {{ form.errors }}
                    </div>
                {% endif %}
                
                {% if form.non_field_errors %}
                    <div class="bg-red-50 border border-red-200 text-red-700 p-3 rounded-md mb-4">
                        {{ form.non_field_errors }}
                    </div>
                {% endif %}

                <div class="flex items-center justify-between gap-4 mt-6">
                    <button type="submit" class="px-4 py-2 bg-slate-200 text-slate-800 rounded-md hover:bg-slate-300 transition-all duration-200 text-sm font-medium">
                        {% trans "Cancelar" %}
                    </button>
                    <button type="submit" name="allow" value="Authorize" class="px-4 py-2 bg-emerald-600 text-white rounded-md hover:bg-emerald-700 transition-all duration-200 text-sm font-medium">
                        {% trans "Autorizar" %}
                    </button>
                </div>
            </form>
        {% else %}
            <div class="bg-red-50 border border-red-200 p-4 rounded-md">
                <h2 class="text-lg font-bold text-red-700 mb-2">Erro: {{ error.error }}</h2>
                <p class="text-sm text-red-600">{{ error.description }}</p>
            </div>
        {% endif %}
    </div>
{% endblock %}
