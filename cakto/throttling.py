from rest_framework.throttling import <PERSON>onRate<PERSON><PERSON><PERSON><PERSON>, ScopedRateThrottle, SimpleRateThrottle, UserRateThrottle


class CaktoBaseThrottle(SimpleRateThrottle):
    def get_ident(self, request):
        return request.META.get('HTTP_DO_CONNECTING_IP') or super().get_ident(request)

class CaktoAnonRateThrottle(AnonRateThrottle, CaktoBaseThrottle):
    pass

class CaktoUserRateThrottle(UserRateThrottle, CaktoBaseThrottle):
    pass

class CaktoScopedRateThrottle(ScopedRateThrottle, CaktoBaseThrottle):
    pass
