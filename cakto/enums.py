from enum import Enum
from typing import Union


class LabeledEnum(Enum):
    """
    Override Enum class to add description and id properties

    Usage:
    ----
    class MyEnum(LabeledEnum):
        FIRST = ('first', 'First description')
        SECOND = ('second', 'Second description')

    myenum = MyEnum.FIRST

    print(myenum.description)  # Output: "First description"

    print(myenum.id)  # Output: "first"

    Methods:
    --------
    description: str
        Return the description of the enum, which is the second element of the value tuple

    id: str
        Return the id of the enum, which is the first element of the value tuple

    as_list: list[dict]
        Return a list of dictionaries with the type and name of the enum

    choices: list[tuple[str, str]]
        Return a list of tuples with the type and name of the enum

    get_member: Union['CustomEnum', None]
        Return the enum member if the id is found, otherwise return None
    """

    @property
    def description(self):
        return self.value[1]

    @property
    def id(self):
        return self.value[0]

    @classmethod
    def as_list(cls):
        return [{'type': item.id, 'name': item.description} for item in cls]

    @classmethod
    def as_id_list(cls) -> list[str]:
        return [item.id for item in cls]

    @classmethod
    def choices(cls) -> list[tuple[str, str]]:
        return [(item.id, item.description) for item in cls]

    @classmethod
    def get_member(cls, member_id: str) -> Union['LabeledEnum', None]:
        for item in cls:
            if item.id == member_id:
                return item
        return None
