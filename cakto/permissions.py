from rest_framework import permissions

class IsOwner(permissions.BasePermission):
    """
    Custom permission to only allow owners to edit their own objects.
    """

    def has_object_permission(self, request, view, obj):
        return obj.user == request.user


class CanBlockProduct(permissions.BasePermission):
    """
    Custom permission to only allow specific users to block product.

    Allows access to:
    - Superusers
    - Users with 'product.block_product' permission
    """

    def has_permission(self, request, view):
        # Check if the user is authenticated
        if not request.user or not request.user.is_authenticated:
            return False

        # Allow superusers
        if request.user.is_superuser:
            return True

        # Check if user has the specific permission
        return request.user.has_perm('product.block_product')
