from django.contrib import admin
from django_otp.admin import OTPAdminAuthenticationForm, OTPAdminSite
from rest_framework.authtoken.admin import TokenAdmin as BaseTokenAdmin
from rest_framework.authtoken.models import Token, TokenProxy

from user.models import MFAMethod, User

# Unregister the default TokenAdmin and register custom one
admin.site.unregister(TokenProxy)

class CustomTokenAdmin(BaseTokenAdmin):
    model = Token
    readonly_fields = [f.name for f in model._meta.concrete_fields]

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        return False


# Register the custom admin
admin.site.register(TokenProxy, CustomTokenAdmin)

class MFAAdminAuthenticationForm(OTPAdminAuthenticationForm):
    def clean_otp(self, user):
        if isinstance(user, User):
            mfa_method = MFAMethod.objects.filter(user=user).exists()
            if not mfa_method:
                MFAMethod.get_email_method(user)

        return super().clean_otp(user)

class MFAAdminSite(OTPAdminSite):
    login_form = MFAAdminAuthenticationForm
