from django.core.exceptions import ValidationError


class CaptialLetterValidator:
    def __init__(self, number_of_capitals=1):
        self.number_of_capitals = number_of_capitals

    def validate(self, password: str, user=None):
        capitals = [char for char in password if char.isupper()]
        if len(capitals) < self.number_of_capitals:
            raise ValidationError(
                ("A senha deve conter pelo menos %(min_length)d letra(s) maiúscula(s)."),
                code='password_too_short',
                params={'min_length': self.number_of_capitals},
            )

    def get_help_text(self):
        return (f"Sua senha deve conter {self.number_of_capitals} letra(s) mai<PERSON>cula(s).")

class LowerCaseValidator:
    def __init__(self, number_of_lower_case_letters=1):
        self.number_of_lower = number_of_lower_case_letters

    def validate(self, password: str, user=None):
        lowers = [char for char in password if char.islower()]
        if len(lowers) < self.number_of_lower:
            raise ValidationError(
                ("A senha deve conter pelo menos %(min_length)d letra(s) minúscula(s)."),
                code='password_too_short',
                params={'min_length': self.number_of_lower},
            )

    def get_help_text(self):
        return (f"Sua senha deve conter {self.number_of_lower} letra(s) minúscula(s).")

class MinimunNumberValidator:
    def __init__(self, minimal_number_quantity=1):
        self.min_number_quantity = minimal_number_quantity

    def validate(self, password: str, user=None):
        lowers = [char for char in password if char.isdigit()]
        if len(lowers) < self.min_number_quantity:
            raise ValidationError(
                ("A senha deve conter pelo menos %(min_length)d número(s)."),
                code='password_too_short',
                params={'min_length': self.min_number_quantity},
            )

    def get_help_text(self):
        return (f"Sua senha deve conter {self.min_number_quantity} número(s).")

class SymbolValidator:
    def __init__(self, number_of_symbols=1, symbols="[~!@#$%^&*()_+{}\":;'[]\\."):
        self.number_of_symbols = number_of_symbols
        self.symbols = symbols

    def validate(self, password: str, user=None):
        symbols = [char for char in password if char in self.symbols]
        if len(symbols) < self.number_of_symbols:
            raise ValidationError(
                ("A senha deve conter pelo menos %(min_length)d caracter(es) especial(is)."),
                code='password_too_short',
                params={'min_length': self.number_of_symbols},
            )

    def get_help_text(self):
        return (f"Sua senha deve conter {self.number_of_symbols} caracter(es) especial(is).")
