import os
from django.http import HttpResponse
from django_prometheus import exports
from django.views.decorators.csrf import csrf_exempt

PROMETHEUS_TOKEN = os.environ.get('PROMETHEUS_TOKEN', '')

@csrf_exempt
def prometheus_metrics(request):
    authorization = request.headers.get('Authorization', '')
    if PROMETHEUS_TOKEN in authorization:
        return exports.ExportToDjangoView(request)
    return HttpResponse(status=403)
