from rest_flex_fields import FIELDS_PARAM, FlexFieldsModelSerializer


class CustomFlexFieldsSerializer(FlexFieldsModelSerializer):
    """
    Custom drf-flex-fields serializer class to:
        - Accepts permitted_fields and exclude_fields in the __init__ and in the expandable_fields from its Meta class.
        - Automatically expand fields from the fields query param, like fileds=customer.name will expand customer.

    Usage:
        serializer = CustomFlexFieldsSerializer(queryset, permitted_fields=['field1', 'field2'], exclude_fields=['field3', 'field4'])

    Usage from expandable_fields:
        class MyOtherCustomSerializer(serializers.ModelSerializer):
            class Meta:
                model = MyModel
                fields = ['name', 'id', 'cardNumber']

        class MySerializer(CustomFlexFieldsSerializer):
            class Meta:
                fields = ['field1', 'field2', 'field3', 'field4']
                expandable_fields = {
                    'field1': (MyOtherCustomSerializer, {'permitted_fields': ['name', 'id'], 'exclude_fields': ['name', 'cardNumber']}),
                }

    The exclude_fields will take precedence over permitted_fields.
    So, in the example above the field 'name' will be excluded from the expandable field 'field1' even though it is in the permitted_fields list.
    """

    exclude_fields: list[str] = []

    def __init__(self, *args, permitted_fields: list[str] = [], exclude_fields: list[str] = [], **kwargs):
        self.exclude_fields = exclude_fields

        fields = kwargs.pop(FIELDS_PARAM, [])

        if permitted_fields:
            fields = list(set(fields) & set(permitted_fields))

        kwargs['fields'] = fields or permitted_fields
        super().__init__(*args, **kwargs)

    def get_fields(self, *args, **kwargs):
        fields = super().get_fields(*args, **kwargs)
        for to_exclude_field in self.exclude_fields:
            fields.pop(to_exclude_field, None)
        return fields

    def _get_permitted_expands_from_query_param(self, expand_param: str) -> list[str]:
        expand = self._get_query_param_value(expand_param)
        requested_fields = self._get_query_param_value(FIELDS_PARAM)

        expand.extend(self._get_parent_fields(requested_fields))

        if "permitted_expands" in self.context:
            permitted_expands = self.context["permitted_expands"]
            if self._contains_wildcard_value(expand):
                return permitted_expands
            return list(set(expand) & set(permitted_expands))

        return expand

    def _get_parent_fields(self, fields: list[str]) -> list[str]:
        """Helper method to get parent fields from a list of fields"""
        parent_fields = []
        for field in fields:
            if '.' in field:
                parent_fields.append('.'.join(field.split('.')[:-1]))
        return parent_fields
