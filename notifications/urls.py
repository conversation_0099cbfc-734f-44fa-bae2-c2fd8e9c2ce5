from django.urls import path

from .views import (
    NotificationListAPIView,
    NotificationMarkAllReadAPIView,
    UpdateNotificationPreferenceView,
    UserNotificationPreferencesView,
)

urlpatterns = [
    path('notifications/', NotificationListAPIView.as_view(), name='notifications-list'),
    path('notifications/mark-all-read/', NotificationMarkAllReadAPIView.as_view({'post': 'post'}), name='notifications-mark-all-read'),
    path('notifications/<int:pk>/mark-read/', NotificationMarkAllReadAPIView.as_view({'post': 'mark_read'}), name='notifications-read'),
    path('notification-preferences/', UserNotificationPreferencesView.as_view(), name='user-notification-preferences'),
    path('notification-preferences/update/', UpdateNotificationPreferenceView.as_view(), name='update-notification-preference'),
]
