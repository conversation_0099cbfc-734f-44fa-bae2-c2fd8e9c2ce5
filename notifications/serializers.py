from rest_framework import serializers

from .models import Notification, NotificationPreference, NotificationType


class NotificationSerializer(serializers.ModelSerializer):
    class Meta:
        model = Notification
        fields = ['id', 'icon', 'title', 'description', 'amount', 'link', 'read', 'createdAt']

class UpdateNotificationPreferenceSerializer(serializers.ModelSerializer):
    notification_type = serializers.ChoiceField(choices=NotificationType.choices())

    class Meta:
        model = NotificationPreference
        fields = ['notification_type', 'is_enabled']

    def create(self, validated_data):
        """
        Create or update a notification preference.
        """
        user = self.context['request'].user
        preference, _ = NotificationPreference.objects.update_or_create(
            user=user,
            notification_type=validated_data['notification_type'],
            defaults={'is_enabled': validated_data['is_enabled']}
        )
        return preference
