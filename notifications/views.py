from rest_framework import generics, status, viewsets
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from cakto.permissions import IsOwner

from .models import Notification, NotificationPreference, NotificationType
from .serializers import NotificationSerializer, UpdateNotificationPreferenceSerializer


class NotificationListAPIView(generics.ListAPIView):
    serializer_class = NotificationSerializer
    permission_classes = [IsAuthenticated, IsOwner]

    def get_queryset(self):
        read = self.request.query_params.get('read') or 'all'  # type:ignore
        qs = Notification.objects.filter(user=self.request.user)
        if read != 'all':
            if read == 'true':
                qs = qs.filter(read=True)
            else:
                qs = qs.filter(read=False)
        return qs

class NotificationMarkAllReadAPIView(viewsets.GenericViewSet):
    serializer_class = NotificationSerializer
    permission_classes = [IsAuthenticated, IsOwner]

    def get_queryset(self):
        return Notification.objects.filter(user=self.request.user)

    def post(self, request, *args, **kwargs):
        # Mark the retrieved notifications as read
        self.get_queryset().update(read=True)

        return Response({
            'detail': 'Notificações marcadas como lidas com sucesso.',
            'message': 'Notificações marcadas como lidas com sucesso.'
        }, status=status.HTTP_200_OK)

    @action(detail=True, methods=['post'])
    def mark_read(self, request, *args, **kwargs):
        instance = self.get_object()
        instance.read = True
        instance.save()

        return Response({
            'detail': 'Notificação marcada como lida com sucesso.',
            'message': 'Notificação marcada como lida com sucesso.'
        }, status=status.HTTP_200_OK)

class UserNotificationPreferencesView(generics.ListAPIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        user = request.user
        notification_types = NotificationType.to_list()
        preferences = NotificationPreference.objects.filter(user=user)

        preferences_dict = {
            preference.notification_type: preference.is_enabled
            for preference in preferences
        }

        response_data = [
            {
                "type": notification_type["type"],
                "name": notification_type["name"],
                "is_enabled": preferences_dict.get(notification_type["type"], True)
            }
            for notification_type in notification_types
        ]

        return Response(response_data)

class UpdateNotificationPreferenceView(generics.CreateAPIView):
    permission_classes = [IsAuthenticated]

    def put(self, request):
        serializer = UpdateNotificationPreferenceSerializer(
            data=request.data,
            context={'request': request},
            many=True
        )
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response({
            "detail": "Preferencias de notificações atualizadas com sucesso."
        }, status=status.HTTP_200_OK)
