from .models import NotificationPreference


def is_notification_enabled(user, notification_type):
    """
    Check if a specific notification type is enabled for a user.

    :param user: User object
    :param notification_type: NotificationType enum
    :return: <PERSON><PERSON><PERSON> indicating if the notification is enabled
    """
    try:
        preference = NotificationPreference.objects.get(
            user=user,
            notification_type=notification_type.identifier
        )
        return preference.is_enabled
    except NotificationPreference.DoesNotExist:
        return True
