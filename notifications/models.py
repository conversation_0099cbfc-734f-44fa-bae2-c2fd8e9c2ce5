from enum import Enum

from django.db import models


class Notification(models.Model):
    user = models.ForeignKey('user.User', on_delete=models.CASCADE)
    icon = models.CharField(max_length=255, null=True, blank=True)
    title = models.CharField(max_length=255)
    description = models.TextField()
    amount = models.FloatField(null=True, blank=True)
    link = models.CharField(max_length=255, null=True, blank=True)
    read = models.BooleanField(default=False)
    createdAt = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.title

    class Meta:
        verbose_name = 'Notificação'
        verbose_name_plural = 'Notificações'
        ordering = ['-id']

class NotificationType(Enum):
    ORDER_GENERATED = ('order_generated', 'Venda gerada')
    ORDER_APPROVED = ('order_approved', 'Venda aprovada')

    def __init__(self, identifier, description):
        self.identifier = identifier
        self.description = description

    @classmethod
    def choices(cls):
        return [(tag.identifier, tag.description) for tag in cls]

    @classmethod
    def to_list(cls):
        return [{'type': tag.identifier, 'name': tag.description} for tag in cls]


class NotificationPreference(models.Model):
    user = models.ForeignKey('user.User', on_delete=models.CASCADE)
    notification_type = models.CharField(
        max_length=50,
        choices=NotificationType.choices()
    )
    is_enabled = models.BooleanField(default=True)

    class Meta:
        unique_together = ('user', 'notification_type')
