from django.contrib import admin

from dashboard.models import Banner, ShortLink


@admin.register(Banner)
class BannerAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'url', 'order', 'image', 'imageMobile')
    search_fields = ('name', 'url', 'order', 'image', 'imageMobile')
    list_display_links = ('id', 'name')
    ordering = ('order', 'name')

@admin.register(ShortLink)
class ShortLinkAdmin(admin.ModelAdmin):
    list_display = ('short_id', 'url', 'status', 'createdAt')
    search_fields = ('name', 'url', 'short_id',)
    list_display_links = ('short_id',)
    ordering = ('createdAt', )
    list_filter = ('status', 'createdAt', 'updatedAt',)
