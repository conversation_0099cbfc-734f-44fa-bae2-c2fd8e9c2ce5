import re

from django.core.cache import cache
from django.db import models
from django_lifecycle import AFTER_CREATE, AFTER_DELETE, AFTER_SAVE, LifecycleModelMixin, hook
from rest_framework.exceptions import ValidationError
from shortuuid.django_fields import ShortUUID, ShortUUIDField


def get_banner_order():
    banner = Banner.objects.order_by('-order').first()
    return banner.order + 1 if banner else 1

class Banner(LifecycleModelMixin, models.Model):
    name = models.CharField(max_length=255, blank=True, null=True)
    url = models.URLField(max_length=2048)
    order = models.IntegerField(default=get_banner_order)
    image = models.ImageField(upload_to='banners/')
    imageMobile = models.ImageField(upload_to='banners/')
    location = models.CharField(max_length=255, choices=[
        ('dashboard', 'Dashboard'),
        ('apps', 'Apps'),
        ('courses', 'Meus Cursos'),
        ('register', 'Cadastro'),
        ('login', 'Login'),
        ('vitrine', 'Vitrine')
    ], default='dashboard')

    class Meta:
        ordering = ['order']
        verbose_name = 'Banner'
        verbose_name_plural = 'Banners'

    def __str__(self):
        return f'{self.name or "Banner"}({self.order})'

    cache_prefix = 'banners_'

    @hook(AFTER_SAVE)
    @hook(AFTER_CREATE)
    @hook(AFTER_DELETE)
    def invalidate_cache(self):
        locations = [code for code, _ in self._meta.get_field('location').flatchoices]
        cache.delete_many([f'{self.cache_prefix}{location}' for location in locations])

    @classmethod
    def get_banners(cls, location) -> list[dict]:
        from dashboard.serializers import BannerPublicSerializer

        if location not in dict(cls._meta.get_field('location').flatchoices):
            raise ValidationError({'detail': f'Invalid location: {location}'})

        banners = cls.objects.filter(location=location)

        return cache.get_or_set(f'{cls.cache_prefix}{location}', lambda: BannerPublicSerializer(banners, many=True).data, None)  # type:ignore

    @classmethod
    def get_all_banners(cls) -> list[dict]:
        from dashboard.serializers import BannerPublicSerializer

        banners = cls.objects.all()

        return cache.get_or_set(f'{cls.cache_prefix}all', lambda: BannerPublicSerializer(banners, many=True).data, None)  # type:ignore

class ShortLink(models.Model):
    alphabet = '123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'
    cache_prefix = 'short_link_'

    short_id = ShortUUIDField(  # type:ignore
        length=7,
        max_length=40,
        unique=True,
        db_index=True,
        alphabet=alphabet,
        verbose_name='Short ID',
        help_text='Identificador curto para o link.'
    )
    name = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        verbose_name='Nome',
        help_text='Nome descritivo do link. Usado para identificar o link facilmente.'
    )
    url = models.URLField(
        max_length=2048,
        verbose_name='URL',
        help_text='URL de destino do link. Deve ser um link válido.'
    )
    status = models.CharField(max_length=255, choices=(
        ('active', 'Ativo'),
        ('disabled', 'Desabilitado'),
    ),
        default='active',
        db_index=True,
        verbose_name='Status',
        help_text='Status do link. Links desabilitados não redirecionam.'
    )

    # Dates
    createdAt = models.DateTimeField(
        auto_now_add=True,
        db_index=True,
        verbose_name='Criado em',
        help_text='Data e hora de criação do link.'
    )
    updatedAt = models.DateTimeField(
        auto_now=True,
        verbose_name='Atualizado em',
        help_text='Data e hora da última atualização do link.'
    )

    class Meta:
        ordering = ['-createdAt', 'status']
        verbose_name = 'Short Link'
        verbose_name_plural = 'Short Links'

    def __str__(self):
        return f'{self.short_id} - {self.name}' if self.name else self.short_id

    @property
    def cache_key(self) -> str:
        return self.cache_prefix + self.short_id

    def invalidate_cache(self) -> None:
        cache.delete(self.cache_key)

    def save(self, *args, **kwargs):
        response = super().save(*args, **kwargs)
        self.invalidate_cache()
        return response

    def delete(self, *args, **kwargs):
        self.invalidate_cache()
        return super().delete(*args, **kwargs)

    @classmethod
    def validate_short_id(cls, short_id: str | None = None) -> str | None:
        if not short_id:
            return

        allowed_alphabet = cls.allowed_alphabet()

        short_id = re.sub(rf'[^{allowed_alphabet}]', '', short_id or '')

        return short_id

    @classmethod
    def allowed_alphabet(cls):
        short_id_field = cls._meta.get_field('short_id')
        return ''.join(ShortUUID(alphabet=short_id_field.alphabet)._alphabet)
