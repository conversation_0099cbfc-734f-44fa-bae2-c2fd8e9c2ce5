# Generated by Django 4.2.5 on 2025-06-25 02:24

from django.db import migrations, models
import shortuuid.django_fields


class Migration(migrations.Migration):

    dependencies = [
        ('dashboard', '0005_alter_banner_location'),
    ]

    operations = [
        migrations.CreateModel(
            name='ShortLink',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('short_id', shortuuid.django_fields.ShortUUIDField(alphabet='123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz', db_index=True, help_text='Identificador curto para o link.', length=7, max_length=40, prefix='', unique=True, verbose_name='Short ID')),
                ('name', models.CharField(blank=True, help_text='Nome descritivo do link. Usado para identificar o link facilmente.', max_length=255, null=True, verbose_name='Nome')),
                ('url', models.URLField(help_text='URL de destino do link. Deve ser um link válido.', max_length=2048, verbose_name='URL')),
                ('status', models.CharField(choices=[('active', 'Ativo'), ('disabled', 'Desabilitado')], db_index=True, default='active', help_text='Status do link. Links desabilitados não redirecionam.', max_length=255, verbose_name='Status')),
                ('createdAt', models.DateTimeField(auto_now_add=True, db_index=True, help_text='Data e hora de criação do link.', verbose_name='Criado em')),
                ('updatedAt', models.DateTimeField(auto_now=True, help_text='Data e hora da última atualização do link.', verbose_name='Atualizado em')),
            ],
            options={
                'verbose_name': 'Short Link',
                'verbose_name_plural': 'Short Links',
                'ordering': ['-createdAt', 'status'],
            },
        ),
    ]
