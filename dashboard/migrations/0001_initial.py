# Generated by Django 4.2.5 on 2024-10-02 17:30

from typing import Any

import django_lifecycle.mixins
from django.db import migrations, models

import dashboard.models


class Migration(migrations.Migration):

    initial = True

    dependencies: list[Any] = []

    operations = [
        migrations.CreateModel(
            name='Banner',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(blank=True, max_length=255, null=True)),
                ('url', models.URLField(max_length=2048)),
                ('order', models.IntegerField(default=dashboard.models.get_banner_order)),
                ('image', models.URLField(max_length=2048)),
                ('imageMobile', models.URLField(blank=True, max_length=2048, null=True)),
            ],
            options={
                'verbose_name': 'Banner',
                'verbose_name_plural': 'Banners',
                'ordering': ['order'],
            },
            bases=(django_lifecycle.mixins.LifecycleModelMixin, models.Model),
        ),
    ]
