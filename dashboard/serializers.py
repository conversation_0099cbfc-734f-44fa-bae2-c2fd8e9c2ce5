from rest_framework import serializers

from dashboard.models import Banner, ShortLink


class BannerCreateUpdateSerializer(serializers.ModelSerializer):
    image = serializers.ImageField(required=False, allow_null=True)
    imageMobile = serializers.ImageField(required=False, allow_null=True)

    class Meta:
        model = Banner
        fields = ['id', 'name', 'url', 'order', 'image', 'imageMobile', 'location']

    def update(self, instance, validated_data):
        if 'image' in validated_data:
            if not validated_data['image']:
                if instance.image:
                    instance.image.delete(save=False)
                validated_data.pop('image', None)

        if 'imageMobile' in validated_data:
            if not validated_data['imageMobile']:
                if instance.imageMobile:
                    instance.imageMobile.delete(save=False)
                validated_data.pop('imageMobile', None)

        return super().update(instance, validated_data)

class BannerPublicSerializer(serializers.ModelSerializer):
    class Meta:
        model = Banner
        fields = ['id', 'name', 'url', 'order', 'image', 'imageMobile', 'location']

class ShortLinkSerializer(serializers.ModelSerializer):
    class Meta:
        model = ShortLink
        fields = [
            'short_id',
            'name',
            'url',
            'status',
            'createdAt',
            'updatedAt',
        ]
        read_only_fields = [
            'createdAt',
            'updatedAt',
        ]

    def validate_short_id(self, short_id: str | None) -> str | None:
        if not short_id:
            return

        allowed_alphabet = ShortLink.allowed_alphabet()

        if not all(c in allowed_alphabet for c in short_id):
            raise serializers.ValidationError(
                f'"short_id" deve conter apenas caracteres: "{allowed_alphabet}"'
            )

        return short_id
