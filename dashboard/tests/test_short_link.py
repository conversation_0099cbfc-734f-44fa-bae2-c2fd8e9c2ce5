from django.conf import settings
from django.core.cache import cache
from django.urls import reverse
from django.utils import timezone

from cakto.tests.base import BaseTestCase
from dashboard.models import ShortLink


class ShortLinkTestCase(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()

        cls.user.is_staff = True
        cls.user.save()

        cls.admin_headers = cls.build_user_auth_headers(cls.user)

        cls.short_link = ShortLink.objects.create(
            short_id='abc1234',
            name='Test Link',
            url='https://www.google.com',
        )

        cls.links_url = reverse('dashboard:short-links')
        cls.links_detail_url = reverse(
            'dashboard:short-links-detail',
            args=[cls.short_link.short_id]
        )

    def test_create_short_link_with_custom_short_id(self):
        ShortLink.objects.all().delete()  # Clear existing links

        payload = {
            'short_id': 'custom23',
            'name': 'Custom Link',
            'url': 'https://example.com/custom',
        }

        response = self.client.post(
            self.links_url,
            data=payload,
            format='json',
            headers=self.admin_headers,
        )

        self.assertEqual(response.status_code, 201, response.content.decode())

        result = response.json()
        self.assertEqual(result.get('short_id'), payload['short_id'])

        link: ShortLink = ShortLink.objects.first()  # type:ignore
        self.assertEqual(link.short_id, payload['short_id'])
        self.assertEqual(link.name, payload['name'])
        self.assertEqual(link.url, payload['url'])
        self.assertEqual(link.status, 'active')
        self.assertAlmostEqual(
            link.createdAt,
            timezone.now(),
            delta=timezone.timedelta(seconds=1)  # Allow for slight delay in creation
        )

    def test_create_short_link_without_custom_short_id(self):
        # Clear existing links
        ShortLink.objects.all().delete()

        payload = {
            'name': 'Auto short_id Link',
            'url': 'https://example.com/auto_short_id',
        }
        response = self.client.post(
            self.links_url,
            data=payload,
            format='json',
            headers=self.admin_headers,
        )

        self.assertEqual(response.status_code, 201, response.content.decode())

        self.assertEqual(ShortLink.objects.count(), 1)

        result = response.json()
        self.assertIn('short_id', result)
        self.assertEqual(result.get('name'), payload['name'])
        self.assertEqual(result.get('url'), payload['url'])

    def test_create_short_link_with_invalid_short_id(self):
        payload = {
            'short_id': 'invalid short_id!',  # Invalid ID with spaces and special characters
            'name': 'Invalid ID Link',
            'url': 'https://example.com/invalid-short_id',
        }

        response = self.client.post(
            self.links_url,
            data=payload,
            format='json',
            headers=self.admin_headers,
        )

        self.assertEqual(response.status_code, 400, response.content.decode())
        self.assertEqual(
            response.json().get('short_id'),
            ['"short_id" deve conter apenas caracteres:'
             ' "123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz"']
        )

    def test_create_short_link_with_existing_id(self):
        payload = {
            'short_id': self.short_link.short_id,  # Use existing ID
            'name': 'Duplicate ID Link',
            'url': 'https://example.com/duplicate-short_id',
        }

        response = self.client.post(
            self.links_url,
            data=payload,
            format='json',
            headers=self.admin_headers,
        )

        self.assertEqual(response.status_code, 400, response.content.decode())
        self.assertEqual(
            response.json().get('short_id'),
            ['Short Link com este Short ID já existe.']
        )

    def test_create_short_link_with_invalid_url(self):
        payload = {
            'short_id': 'validid1',
            'name': 'Invalid URL Link',
            'url': 'not-a-valid-url',  # Invalid URL format
        }

        response = self.client.post(
            self.links_url,
            data=payload,
            format='json',
            headers=self.admin_headers,
        )

        self.assertEqual(response.status_code, 400, response.content.decode())
        self.assertEqual(
            response.json().get('url'),
            ['Entrar um URL válido.']
        )

    def test_create_short_link_without_url(self):
        payload = {
            'short_id': 'validid2',
            'name': 'No URL Link',
            # No URL provided
        }

        response = self.client.post(
            self.links_url,
            data=payload,
            format='json',
            headers=self.admin_headers,
        )

        self.assertEqual(response.status_code, 400, response.content.decode())
        self.assertEqual(
            response.json().get('url'),
            ['Este campo é obrigatório.']
        )

    def test_create_short_link_without_name(self):
        payload = {
            'short_id': 'validid3',
            'url': 'https://example.com/no-name',  # No name provided
        }

        response = self.client.post(
            self.links_url,
            data=payload,
            format='json',
            headers=self.admin_headers,
        )

        self.assertEqual(response.status_code, 201, response.content.decode())

        result = response.json()
        self.assertEqual(result.get('short_id'), payload['short_id'])
        self.assertEqual(result.get('name'), None)

    def test_common_user_cant_create_link(self):
        # Create a regular user
        user = self.create_user(
            username='testuser',
            password='testpass',
            is_staff=False,
        )
        user_headers = self.build_user_auth_headers(user)

        payload = {
            'short_id': 'userlink1',
            'name': 'User Link',
            'url': 'https://example.com/user-link',
        }

        response = self.client.post(
            self.links_url,
            data=payload,
            format='json',
            headers=user_headers,
        )

        self.assertEqual(response.status_code, 403, response.content.decode())
        self.assertIn('detail', response.json(), response.content.decode())

    def test_invalidate_cache_on_create(self):
        # Setup cache
        cache.clear()
        short_link_id = 'cachelink1'
        cache_key = ShortLink.cache_prefix + short_link_id
        cache.set(cache_key, 'cached_value')

        payload = {
            'short_id': short_link_id,
            'name': 'Cache Link',
            'url': 'https://example.com/cache-link',
        }

        response = self.client.post(
            self.links_url,
            data=payload,
            format='json',
            headers=self.admin_headers,
        )

        self.assertEqual(response.status_code, 201, response.content.decode())

        # Check if cache is invalidated
        self.assertIsNone(
            cache.get(cache_key),
            'Cache should be invalidated after creating a short link'
        )

    def test_invalidate_cache_on_update(self):
        # Setup cache
        cache.clear()
        cache_key = self.short_link.cache_key
        cache.set(cache_key, 'cached_value')

        payload = {
            'name': 'Updated Cache Link',
            'url': 'https://example.com/updated-cache-link',
        }

        response = self.client.put(
            self.links_detail_url,
            data=payload,
            format='json',
            headers=self.admin_headers,
        )

        self.assertEqual(response.status_code, 200, response.content.decode())

        # Check if cache is invalidated
        self.assertIsNone(
            cache.get(cache_key),
            'Cache should be invalidated after updating a short link'
        )

    def test_invalidate_cache_on_delete(self):
        # Setup cache
        cache.clear()
        cache_key = self.short_link.cache_key
        cache.set(cache_key, 'cached_value')

        response = self.client.delete(
            self.links_detail_url,
            headers=self.admin_headers,
        )

        self.assertEqual(response.status_code, 204, response.content.decode())

        # Check if cache is invalidated
        self.assertIsNone(
            cache.get(cache_key),
            'Cache should be invalidated after deleting a short link'
        )

    def test_redirect_to_short_link(self):
        cache.clear()

        # Test redirect to a valid short link
        response = self.client.get(
            reverse('short-link-redirect', args=[self.short_link.short_id]),
            follow=False,
        )

        self.assertRedirects(response, self.short_link.url, fetch_redirect_response=False)

    def test_redirect_to_short_link_with_invalid_id(self):
        cache.clear()

        # Test redirect with an invalid short link ID
        invalid_id = 'invalid123'
        response = self.client.get(
            reverse('short-link-redirect', args=[invalid_id]),
            follow=False,
        )

        front_end_404_page = settings.FRONT_END_BASE_URL + '/404'

        self.assertRedirects(
            response,
            front_end_404_page,
            fetch_redirect_response=False,
        )

    def test_update_short_link_updated_id(self):
        payload = {
            'short_id': 'newid123',
        }

        response = self.client.put(
            self.links_detail_url,
            data=payload,
            format='json',
            headers=self.admin_headers,
        )

        self.assertEqual(response.status_code, 200, response.content.decode())

        result = response.json()
        self.assertEqual(result.get('short_id'), payload['short_id'])

        # Check if the ID was updated in the database
        link: ShortLink = ShortLink.objects.get(short_id=payload['short_id'])
        self.assertEqual(link.short_id, payload['short_id'])

        self.assertEqual(ShortLink.objects.count(), 1, 'There should be only one short link')
