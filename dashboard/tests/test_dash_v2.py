from django.urls import reverse

from cakto.tests.base import BaseTestCase
from gateway.models import PaymentStatus


class DashV2Test(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()

        cls.create_paymentMethods()

        cls.product = cls.create_product(user=cls.user)

        cls.headers = cls.build_user_auth_headers(cls.user)

        for _ in range(10):
            cls.create_order(
                product=cls.product,
                paymentMethod=cls.credit_card,
                status=PaymentStatus.PAID.id,
            )

        for _ in range(2):
            cls.create_order(
                product=cls.product,
                paymentMethod=cls.credit_card,
                status=PaymentStatus.CHARGEDBACK.id,
            )

        cls.create_order(
            product=cls.product,
            paymentMethod=cls.credit_card,
            status=PaymentStatus.REFUNDED.id,
        )

    def test_chargebackAmount(self):
        # the chargebackAmount is a percentage
        # it keeps this name because of compability
        url = reverse('dashboard:dashboard-v2')

        response = self.client.get(url, headers=self.headers)

        self.assertEqual(
            response.status_code,
            200,
            response.content.decode()
        )

        self.assertEqual(response.json()['chargebackAmount'], 15.38)

    def test_chargebackAmount_not_consider_other_paymentMethods(self):
        # the chargebackAmount is a percentage
        # it keeps this name because of compability
        url = reverse('dashboard:dashboard-v2')

        for _ in range(3):
            self.create_order(
                product=self.product,
                paymentMethod=self.pix,
                status=PaymentStatus.CHARGEDBACK.id,
            )

        response = self.client.get(url, headers=self.headers)

        self.assertEqual(
            response.status_code,
            200,
            response.content.decode()
        )

        self.assertEqual(response.json()['chargebackAmount'], 15.38)
