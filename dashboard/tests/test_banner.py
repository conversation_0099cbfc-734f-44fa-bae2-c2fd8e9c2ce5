from unittest import mock

from django.core.cache import cache
from django.test import override_settings
from django.urls import reverse

from cakto.tests.base import BaseTestCase
from dashboard.models import Banner
from dashboard.serializers import BannerPublicSerializer


@override_settings(STORAGES={
    'default': {
        'BACKEND': 'django.core.files.storage.InMemoryStorage',
    },
})
class BannerTests(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.banner = Banner.objects.create(**cls.make_sample_data(order=1))
        cls.user.is_staff = True
        cls.user.save(update_fields=['is_staff'])
        cls.common_user = cls.create_user(is_staff=False, is_superuser=False)
        cls.headers = cls.create_headers(cls.get_user_access_token(cls.user))

    @classmethod
    def make_sample_data(cls, order: int | None = 1):
        return {
            'name': 'Banner Test',
            'order': order,
            'url': 'https://www.somedomain.com',
            'image': cls.create_test_image(),
            'imageMobile': cls.create_test_image(),
            'location': 'dashboard',
        }

    def test_banner_create(self):
        Banner.objects.all().delete()

        url = reverse('dashboard:banners') + f'?location={self.banner.location}'

        data = self.make_sample_data(order=2)

        count_before = Banner.objects.count()
        expected_data = {'id': mock.ANY, **data, 'image': mock.ANY, 'imageMobile': mock.ANY}

        response = self.client.post(url, data=data, headers=self.headers, format='multipart')

        self.assertEqual(response.status_code, 201, response.content.decode())
        self.assertEqual(response.json(), expected_data)
        self.assertEqual(Banner.objects.count(), count_before + 1)

    def test_banner_list(self):
        url = reverse('dashboard:banners') + f'?location={self.banner.location}'
        expected_list = [{
            'id': self.banner.pk,
            'name': self.banner.name,
            'order': self.banner.order,
            'url': self.banner.url,
            'image': mock.ANY,
            'imageMobile': mock.ANY,
            'location': self.banner.location,
        }]

        response = self.client.get(url)

        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertEqual(response.json(), expected_list)

    def test_banner_invalidate_cache_on_save(self):
        cache_prefix = Banner.cache_prefix
        cache_key = f"{cache_prefix}{self.banner.location}"
        cache.set(cache_key, 'old_value')

        self.banner.save()

        self.assertIsNone(cache.get(cache_key))

    def test_banner_invalidate_cache_on_create(self):
        cache_prefix = Banner.cache_prefix
        cache_key = f"{cache_prefix}{self.banner.location}"
        cache.set(cache_key, 'old_value')

        Banner.objects.create(**self.make_sample_data(order=2))

        self.assertIsNone(cache.get(cache_key))

    def test_banner_invalidate_cache_on_delete(self):
        cache_prefix = Banner.cache_prefix
        cache_key = f"{cache_prefix}{self.banner.location}"
        cache.set(cache_key, 'old_value')

        self.banner.delete()

        self.assertIsNone(cache.get(cache_key))

    def test_banner_order_increment(self):
        Banner.objects.all().delete()
        data = dict(self.make_sample_data())
        del data['order']

        banner1 = Banner.objects.create(**data)
        banner2 = Banner.objects.create(**data)
        self.assertEqual(banner1.order, 1)
        self.assertEqual(banner2.order, 2)

    def test_get_banners(self):
        cache.clear()
        expected_data = BannerPublicSerializer(Banner.objects.all(), many=True).data
        banners = Banner.get_banners(location=self.banner.location)

        self.assertEqual(banners, expected_data)

    def test_delete_banner(self):
        url = reverse('dashboard:banner', kwargs={'pk': self.banner.pk})
        count_before = Banner.objects.count()

        response = self.client.delete(url, headers=self.headers)

        self.assertEqual(response.status_code, 204, response.content.decode())
        self.assertEqual(Banner.objects.count(), count_before - 1)

    def test_update_banner(self):
        url = reverse('dashboard:banner', kwargs={'pk': self.banner.pk})
        data = {
            'name': 'Updated Name',
            'url': 'https://www.updateddomain.com',
            'order': 2,
            'location': 'apps',
        }

        response = self.client.put(url, data=data, headers=self.headers)

        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertEqual(response.json()['name'], data['name'])
        self.banner.refresh_from_db()
        self.assertEqual(self.banner.name, data['name'])
        self.assertEqual(self.banner.url, data['url'])
        self.assertEqual(self.banner.order, data['order'])
        self.assertEqual(self.banner.location, data['location'])

    def test_common_user_cant_update_banner(self):
        user = self.common_user
        headers = self.create_headers(self.get_user_access_token(user))
        url = reverse('dashboard:banner', kwargs={'pk': self.banner.pk})

        data = {
            'name': 'Updated Name',
        }

        response = self.client.put(url, data=data, headers=headers)

        self.assertEqual(response.status_code, 403, response.content.decode())
        self.banner.refresh_from_db()
        self.assertNotEqual(self.banner.name, data['name'])

    def test_common_user_cant_delete_banner(self):
        user = self.common_user
        headers = self.create_headers(self.get_user_access_token(user))
        url = reverse('dashboard:banner', kwargs={'pk': self.banner.pk})
        before_count = Banner.objects.count()

        response = self.client.delete(url, headers=headers)

        self.assertEqual(response.status_code, 403, response.content.decode())
        self.assertTrue(Banner.objects.filter(pk=self.banner.pk).exists())
        self.assertTrue(Banner.objects.count(), before_count)

    def test_common_user_cant_create_banner(self):
        user = self.common_user
        headers = self.create_headers(self.get_user_access_token(user))
        url = reverse('dashboard:banners') + f'?location={self.banner.location}'
        data = self.make_sample_data(order=2)

        before_count = Banner.objects.count()

        response = self.client.post(url, data=data, headers=headers, format='multipart')

        self.assertEqual(response.status_code, 403, response.content.decode())
        self.assertTrue(Banner.objects.count(), before_count)

    def test_common_user_cant_get_all_banners(self):
        user = self.common_user
        headers = self.create_headers(self.get_user_access_token(user))
        url = reverse('dashboard:get-banners-admin')

        response = self.client.get(url, headers=headers)

        self.assertEqual(response.status_code, 403, response.content.decode())

    def test_update_banner_with_image_field_null_deletes_image(self):
        url = reverse('dashboard:banner', kwargs={'pk': self.banner.pk})

        data = {
            'name': 'Updated Name',
            'url': 'https://www.updateddomain.com',
            'order': 2,
            'location': 'apps',
            'image': '',
            'imageMobile': self.create_test_image(),
        }

        response = self.client.put(url, data=data, headers=self.headers)

        self.assertEqual(response.status_code, 200, response.content.decode())

        self.banner.refresh_from_db()

        self.assertFalse(self.banner.image)
        self.assertTrue(self.banner.imageMobile)
        self.assertEqual(self.banner.name, data['name'])
        self.assertEqual(self.banner.url, data['url'])
        self.assertEqual(self.banner.order, data['order'])
        self.assertEqual(self.banner.location, data['location'])

    def test_update_banner_with_imageMobile_field_null_deletes_imageMobile(self):
        url = reverse('dashboard:banner', kwargs={'pk': self.banner.pk})

        data = {
            'name': 'Updated Name',
            'url': 'https://www.updateddomain.com',
            'order': 2,
            'location': 'apps',
            'image': self.create_test_image(),
            'imageMobile': '',
        }

        response = self.client.put(url, data=data, headers=self.headers)

        self.assertEqual(response.status_code, 200, response.content.decode())

        self.banner.refresh_from_db()

        self.assertTrue(self.banner.image)
        self.assertFalse(self.banner.imageMobile)
        self.assertEqual(self.banner.name, data['name'])
        self.assertEqual(self.banner.url, data['url'])
        self.assertEqual(self.banner.order, data['order'])
        self.assertEqual(self.banner.location, data['location'])

    def test_create_banner_without_image_fields(self):
        Banner.objects.all().delete()

        url = reverse('dashboard:banners') + f'?location={self.banner.location}'

        data = {
            'name': 'Updated Name',
            'url': 'https://www.updateddomain.com',
            'order': 2,
            'location': 'apps',
        }

        response = self.client.post(url, data=data, headers=self.headers, format='multipart')

        self.assertEqual(response.status_code, 201, response.content.decode())

        banner = Banner.objects.get(name=data['name'])

        self.assertFalse(banner.image)
        self.assertFalse(banner.imageMobile)
        self.assertEqual(banner.name, data['name'])
        self.assertEqual(banner.url, data['url'])
        self.assertEqual(banner.order, data['order'])
        self.assertEqual(banner.location, data['location'])
