from django.urls import path

from . import views

app_name = 'dashboard'

urlpatterns = [
    # Dashboard
    path('dashboard/v2/', views.DashboardAPIViewV2.as_view(), name='dashboard-v2'),
    path('dashboard/', views.DashboardAPIViewV1.as_view(), name='dashboard-v1'),

    # Banners
    path('banners/', views.BannerAPIView.as_view({'get': 'get', 'post': 'create'}), name='banners'),
    path('banners/locations/', views.BannerAPIView.as_view({'get': 'list_locations'}), name='banners-locations'),
    path('banners/<int:pk>/', views.BannerAPIView.as_view({'put': 'partial_update', 'delete': 'destroy'}), name='banner'),

    # Banners Admin
    path('admin/banners/', views.BannerAPIView.as_view({'get': 'get_all_banners'}), name='get-banners-admin'),

    # ShortLink Admin
    path('admin/short-links/', views.ShortLinkAPIView.as_view({'get': 'list', 'post': 'create'}), name='short-links'),
    path('admin/short-links/<str:short_id>/', views.ShortLinkAPIView.as_view({'get': 'retrieve', 'put': 'partial_update', 'delete': 'destroy'}), name='short-links-detail'),
]
