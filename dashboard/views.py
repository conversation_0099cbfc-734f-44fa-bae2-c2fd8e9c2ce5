from collections import defaultdict

from django.conf import settings
from django.core.cache import cache
from django.db.models import <PERSON>, <PERSON>, <PERSON>teger<PERSON>ield, Max, OuterRef, Q, Subquery, Sum, When
from django.shortcuts import redirect
from django.utils import timezone
from rest_framework import filters, generics, status, views, viewsets
from rest_framework.decorators import action
from rest_framework.permissions import AllowAny, IsAdminUser
from rest_framework.response import Response

from dashboard.serializers import ShortLinkSerializer
from checkout.models import CheckoutAbandonment
from dashboard.models import Banner, ShortLink
from dashboard.serializers import BannerCreateUpdateSerializer, BannerPublicSerializer
from dashboard.utils import get_max_day_of_month
from gateway.models import Order, PaymentStatus, Split
from gateway.utils import get_orders_by_paymentMethod_and_status
from product.models import PaymentMethod
from reports.filters import CreatedAtDateFilter, parse_dates_and_adjust_timezone
from user.models import User


class DashboardAPIViewV2(generics.GenericAPIView):
    scope = 'reports'
    filter_backends = [CreatedAtDateFilter]

    def get(self, request, format=None):
        user: User = self.request.user  # type:ignore
        products = self.request.query_params.get('product', None)  # type:ignore

        user_commissions = Split.objects.filter(order=OuterRef('id'), user=user).values('order').annotate(total=Sum('totalAmount')).values('total')

        all_orders = self.filter_queryset(
            Order.objects.filter(commissionedUsers=user)
            .annotate(userCommissions=Subquery(user_commissions))
        )
        if products:
            all_orders = all_orders.filter(product__in=products.split(','))

        chartData = self.processChartData(request, all_orders)

        salesAbandonment = self.get_salesAbandonment(user, products)

        orders_aggregated = get_orders_by_paymentMethod_and_status(all_orders)

        paidTotalAmount = sum([paymentMethod.get('paid', {}).get('commissions', 0) for paymentMethod in orders_aggregated.values()])
        paidCount = int(sum([paymentMethod.get('paid', {}).get('count', 0) for paymentMethod in orders_aggregated.values()]))

        averageOrderAmount = round(paidTotalAmount / paidCount, 2) if paidCount > 0 else 0  # type:ignore

        orderRefundPercentage = self.get_orderRefundPercentage(orders_aggregated)

        orderMEDPercentage = self.get_orderMEDPercentage(orders_aggregated)

        chargebackPercentage = self.get_chargebackPercentage(orders_aggregated)

        paymentMethods = PaymentMethod.get_valid_payment_methods_types(user=user)

        approval_rates = self.get_approval_rates(request, all_orders)

        paymentMethods_data = {
            paymentMethod: {
                'amount': self.get_amount_by_status(orders_aggregated, paymentMethod, 'paid'),
                'approvalRate': approval_rates.get(paymentMethod, {}).get('approval_rate', 0),
            } for paymentMethod in paymentMethods
        }

        data = {
            'totalSalesAmount': paidTotalAmount,
            'totalSales': paidCount,
            'averageOrderAmount': averageOrderAmount,
            'orderRefundPercentage': orderRefundPercentage,
            'orderMEDPercentage': orderMEDPercentage,
            'chargebackAmount': chargebackPercentage,  # It is percentage, it was amount but was changed and kept the variable name
            'paidBillets': orders_aggregated.get('boleto', {}).get('paid', {}).get('count', 0),
            'salesAbandonment': salesAbandonment,
            'paymentMethods': paymentMethods,
            **paymentMethods_data,
            'chartData': chartData,
        }

        return Response(data, status=status.HTTP_200_OK)

    def get_amount_by_status(self, orders_aggreated, paymentMethod_type: str, status: str = 'paid'):
        return orders_aggreated.get(paymentMethod_type, {}).get(status, {}).get('commissions', 0)

    def get_approval_rates(self, request, orders) -> dict[str, dict[str, float]]:
        start_date = request.query_params.get('startDate')
        end_date = request.query_params.get('endDate')
        products = request.query_params.get('product', None)

        date_filter: str = ''
        if start_date and end_date:
            start_datetime, end_datetime = parse_dates_and_adjust_timezone(start_date, end_date)

            date_filter = (
                """AND "gateway_order"."createdAt" BETWEEN '{}' AND '{}'""".format(
                    start_datetime.isoformat(),
                    end_datetime.isoformat(),
                )
            )

        product_filter: str = ''
        if products:
            product_list = products.split(',')
            product_string = ', '.join([f"'{product}'" for product in product_list])
            product_filter = ("""AND "gateway_order"."product_id" IN ({})""".format(product_string))

        # SQL bruto para calcular tentativas totais e pagas por cliente/produto/método de pagamento
        query = '''
            SELECT
                "paymentMethod",
                COUNT("customer_id"),
                COUNT("customer_id") FILTER (WHERE "is_paid" = 1)
            FROM (
                SELECT
                    "gateway_order"."customer_id" AS "customer_id",
                    "gateway_order"."product_id" AS "product_id",
                    MAX(CASE WHEN "gateway_order"."status" = 'paid' THEN 1 ELSE 0 END) AS "is_paid",
                    "gateway_order"."paymentMethod_id" AS "paymentMethod"
                FROM "gateway_order"
                INNER JOIN "gateway_order_commissionedUsers" ON ("gateway_order"."id" = "gateway_order_commissionedUsers"."order_id")
                WHERE ("gateway_order_commissionedUsers"."user_id" = {user_id} {filters})
                GROUP BY "gateway_order"."customer_id", "gateway_order"."product_id", "gateway_order"."paymentMethod_id"
            ) subquery
            GROUP BY "paymentMethod"
        '''.format(user_id=self.request.user.id, filters=date_filter + product_filter)

        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute(query)
            rows = cursor.fetchall()

        # Processar os resultados da consulta SQL
        results = {}
        for row in rows:
            payment_method_type = row[0]
            non_paid_attempts = row[1]
            paid_attempts = row[2]
            approval_rate = (paid_attempts / non_paid_attempts * 100) if non_paid_attempts > 0 else 0
            results[payment_method_type] = {
                'approval_rate': round(approval_rate, 2),
                'paid_attempts': paid_attempts,
                'non_paid_attempts': non_paid_attempts,
            }
        return results

    def get_orderMEDPercentage(self, orders_aggregated):
        pix_orders = orders_aggregated.get('pix', {})

        pix_paid_med_count = sum([pix_orders.get(status, {}).get('count', 0) for status in ['paid', 'MED']])

        med_count = pix_orders.get('MED', {}).get('count', 0)

        order_med_percentage = med_count / pix_paid_med_count if pix_paid_med_count > 0 else 0
        order_med_percentage = round(order_med_percentage * 100, 2)

        return order_med_percentage

    def get_orderRefundPercentage(self, orders_aggregated):
        refundTotalCount = sum([paymentMethod.get('refunded', {}).get('count', 0) for paymentMethod in orders_aggregated.values()])
        allOrdersCount = sum([data.get('count', 0) for paymentMethod in orders_aggregated.values() for data in paymentMethod.values()])
        orderRefundPercentage = refundTotalCount / allOrdersCount if allOrdersCount > 0 else 0
        orderRefundPercentage = round(orderRefundPercentage * 100, 2)
        return orderRefundPercentage

    def get_chargebackPercentage(self, orders_aggregated: dict):
        card_orders = orders_aggregated.get('credit_card', {})

        statuses_to_consider = [PaymentStatus.PAID.id, PaymentStatus.CHARGEDBACK.id, PaymentStatus.REFUNDED.id]
        total_count = sum([card_orders.get(status, {}).get('count', 0) for status in statuses_to_consider])

        chargedback_count = card_orders.get(PaymentStatus.CHARGEDBACK.id, {}).get('count', 0)

        chargebackPercentage = chargedback_count / total_count if total_count > 0 else 0
        chargebackPercentage = round(chargebackPercentage * 100, 2)

        return chargebackPercentage

    def get_salesAbandonment(self, user, product):
        abandonment_qs = self.filter_queryset(CheckoutAbandonment.objects.filter(offer__product__user=user))
        if product:
            abandonment_qs = abandonment_qs.filter(offer__product__in=product.split(','))
        salesAbandonment = abandonment_qs.values('customerName', 'customerEmail', 'customerCellphone', 'offer__id').distinct().count()
        return salesAbandonment

    def processChartData(self, request, orders):
        start_date = request.query_params.get('startDate')
        end_date = request.query_params.get('endDate')
        now = timezone.now()
        if start_date and end_date:
            start_datetime, end_datetime = parse_dates_and_adjust_timezone(start_date, end_date)
            chartData = self.get_data(start_datetime, end_datetime, orders.filter(status='paid'))
        elif orders:
            chartData = self.get_data(orders.last().createdAt, now, orders.filter(status='paid'))
        else:
            chartData = self.get_data(now, now, orders.filter(status='paid'))
        return chartData

    def get_data(self, start_date, end_date, orders):
        chartData = []  # type:ignore
        if (end_date - start_date).days < 1:
            self.get_data_by_hours(orders, chartData)
            # if more than 60 days show by month and append all months/days between the start and end date
        elif (end_date - start_date).days > 60:
            self.get_data_by_months(start_date, end_date, orders, chartData)
        else:
            self.get_data_by_days(start_date, end_date, orders, chartData)
        return chartData

    def get_data_by_hours(self, orders, chartData):
        # Initialize a dictionary to store the total amount for each hour
        hourly_totals: dict = defaultdict(int)

        # Iterate over the orders and group them by hour
        for order in orders:
            hour = timezone.localtime(order.createdAt).hour
            hourly_totals[hour] += order.userCommissions

        # Append the results to chartData
        for hour in range(0, 24):
            chartData.append({
                'date': f'{hour:02d}:00h-{hour:02d}:59h',
                'amount': hourly_totals[hour]
            })

    def get_data_by_days(self, start_date, end_date, orders, chartData):
        # Initialize a dictionary to store the total amount for each day
        daily_totals: dict = defaultdict(int)

        # Iterate over the orders and group them by day
        for order in orders:
            day = timezone.localtime(order.createdAt).date()
            daily_totals[day] += order.userCommissions

        # Append the results to chartData
        current_date = start_date
        while current_date <= end_date:
            chartData.append({
                'date': current_date.strftime('%d-%m-%Y'),
                'amount': daily_totals[current_date.date()]
            })
            current_date += timezone.timedelta(days=1)

    def get_data_by_months(self, start_date, end_date, orders, chartData):
        # Initialize a dictionary to store the total amount for each month
        monthly_totals: dict = defaultdict(int)

        # Iterate over the orders and group them by month
        for order in orders:
            month = timezone.localtime(order.createdAt).strftime('%Y-%m')
            monthly_totals[month] += order.userCommissions

        # Append the results to chartData
        current_date = start_date
        days_shift = 0
        while current_date <= end_date:
            month_str = current_date.strftime('%Y-%m')
            chartData.append({
                'date': current_date.strftime('%m-%Y'),
                'amount': monthly_totals[month_str]
            })
            # Move to the next month
            if current_date.month == 12:
                current_date = current_date.replace(year=current_date.year + 1, month=1)
            else:
                next_month = current_date.month + 1
                target_day = current_date.day + days_shift
                days_shift = 0
                max_day = get_max_day_of_month(current_date.year, next_month)

                if target_day > max_day:
                    days_shift = target_day - max_day
                    target_day = max_day

                current_date = current_date.replace(month=next_month, day=target_day)

class DashboardAPIViewV1(generics.GenericAPIView):
    scope = 'reports'
    filter_backends = [CreatedAtDateFilter]

    def get(self, request, format=None):
        user = self.request.user
        product = self.request.query_params.get('product', None)  # type:ignore

        user_commissions = Split.objects.filter(order=OuterRef('id'), user=user).values('order').annotate(total=Sum('totalAmount')).values('total')

        all_orders = self.filter_queryset(
            Order.objects.filter(commissionedUsers=user)
            .annotate(userCommissions=Subquery(user_commissions))
        )
        if product:
            all_orders = all_orders.filter(product__in=product.split(','))

        chartData = self.processChartData(request, all_orders)

        approvalRateCreditCard = self.get_approvalRate(all_orders, paymentMethod_type='credit_card')

        pixApprovalRate = self.get_approvalRate(all_orders, paymentMethod_type='pix')

        salesAbandonment = self.get_salesAbandonment(user, product)

        orders_aggregated = get_orders_by_paymentMethod_and_status(all_orders)

        paidTotalAmount = sum([paymentMethod.get('paid', {}).get('commissions', 0) for paymentMethod in orders_aggregated.values()])
        paidCount = sum([paymentMethod.get('paid', {}).get('count', 0) for paymentMethod in orders_aggregated.values()])
        chargebackAmount = sum([paymentMethod.get('chargedback', {}).get('commissions', 0) for paymentMethod in orders_aggregated.values()])

        averageOrderAmount = round(paidTotalAmount / paidCount, 2) if paidCount > 0 else 0  # type:ignore

        orderRefundPercentage = self.get_orderRefundPercentage(orders_aggregated)

        response = Response({
            'totalSalesAmount': paidTotalAmount,
            'totalSales': paidCount,
            'averageOrderAmount': averageOrderAmount,
            'orderRefundPercentage': orderRefundPercentage,
            'approvalRateCreditCard': approvalRateCreditCard,
            'creditCardAmount': orders_aggregated.get('credit_card', {}).get('paid', {}).get('commissions', 0),
            'pixAmount': orders_aggregated.get('pix', {}).get('paid', {}).get('commissions', 0),
            'pixApprovalRate': pixApprovalRate,
            'chargebackAmount': chargebackAmount,
            'paidBillets': orders_aggregated.get('boleto', {}).get('paid', {}).get('count', 0),
            'salesAbandonment': salesAbandonment,
            'chartData': chartData,
        }, status=status.HTTP_200_OK)

        return response

    def get_orderRefundPercentage(self, orders_aggregated):
        refundTotalCount = sum([paymentMethod.get('refunded', {}).get('count', 0) for paymentMethod in orders_aggregated.values()])
        allOrdersCount = sum([data.get('count', 0) for paymentMethod in orders_aggregated.values() for data in paymentMethod.values()])
        orderRefundPercentage = refundTotalCount / allOrdersCount if allOrdersCount > 0 else 0
        orderRefundPercentage = round(orderRefundPercentage * 100, 2)
        return orderRefundPercentage

    def get_salesAbandonment(self, user, product):
        abandonment_qs = self.filter_queryset(CheckoutAbandonment.objects.filter(offer__product__user=user))
        if product:
            abandonment_qs = abandonment_qs.filter(offer__product__in=product.split(','))
        salesAbandonment = abandonment_qs.values('customerName', 'customerEmail', 'customerCellphone', 'offer__id').distinct().count()
        return salesAbandonment

    def get_approvalRate(self, orders, paymentMethod_type: str):
        # Annotate each order with a boolean indicating if it should be counted as paid
        annotated_orders = orders.filter(paymentMethod__type=paymentMethod_type).values('customer', 'product').annotate(
            is_paid=Max(
                Case(
                    When(Q(status='paid'), then=1),
                    default=0,
                    output_field=IntegerField()
                )
            )
        )

        # Aggregate the counts
        aggregated_data = annotated_orders.aggregate(
            total_attempts=Count('customer'),
            paid_attempts=Count('customer', filter=Q(is_paid=1))
        )

        total_attempts = aggregated_data['total_attempts']
        paid_attempts = aggregated_data['paid_attempts']

        approvalRate = (paid_attempts / total_attempts * 100) if total_attempts > 0 else 0
        return round(approvalRate, 2)

    def processChartData(self, request, orders):
        start_date = request.query_params.get('startDate')
        end_date = request.query_params.get('endDate')
        today = timezone.now().date()
        if start_date and end_date:
            start_date = timezone.datetime.strptime(start_date, '%d-%m-%Y').date()
            end_date = timezone.datetime.strptime(end_date, '%d-%m-%Y').date()
            chartData = self.get_data(start_date, end_date, orders.filter(status='paid'))
        elif orders:
            chartData = self.get_data(orders.last().createdAt.date(), today, orders.filter(status='paid'))
        else:
            chartData = self.get_data(today, today, orders.filter(status='paid'))
        return chartData

    def get_data(self, start_date, end_date, orders):
        chartData = []  # type:ignore
        if start_date == end_date:
            self.get_data_by_hours(start_date, orders, chartData)
            # if more than 60 days show by month and append all months/days between the start and end date
        elif (end_date - start_date).days > 60:
            self.get_data_by_months(start_date, end_date, orders, chartData)
        else:
            self.get_data_by_days(start_date, end_date, orders, chartData)
        return chartData

    def get_data_by_hours(self, start_date, orders, chartData):
        # Fetch all orders for the given start_date
        daily_orders = orders.filter(createdAt__date=start_date)

        # Initialize a dictionary to store the total amount for each hour
        hourly_totals: dict = defaultdict(int)

        # Iterate over the orders and group them by hour
        for order in daily_orders:
            hour = timezone.localtime(order.createdAt).hour
            hourly_totals[hour] += order.userCommissions

        # Append the results to chartData
        for hour in range(0, 24):
            chartData.append({
                'date': f'{hour:02d}:00h-{hour:02d}:59h',
                'amount': hourly_totals[hour]
            })

    def get_data_by_days(self, start_date, end_date, orders, chartData):
        # Fetch all orders for the given date range in a single query
        daily_orders = orders.filter(createdAt__date__range=(start_date, end_date))

        # Initialize a dictionary to store the total amount for each day
        daily_totals: dict = defaultdict(int)

        # Iterate over the orders and group them by day
        for order in daily_orders:
            day = order.createdAt.date()
            daily_totals[day] += order.userCommissions

        # Append the results to chartData
        current_date = start_date
        while current_date <= end_date:
            chartData.append({
                'date': current_date.strftime('%d-%m-%Y'),
                'amount': daily_totals[current_date]
            })
            current_date += timezone.timedelta(days=1)

    def get_data_by_months(self, start_date, end_date, orders, chartData):
        # Fetch all orders for the given date range in a single query
        monthly_orders = orders.filter(createdAt__date__range=(start_date, end_date))

        # Initialize a dictionary to store the total amount for each month
        monthly_totals: dict = defaultdict(int)

        # Iterate over the orders and group them by month
        for order in monthly_orders:
            month = order.createdAt.strftime('%Y-%m')
            monthly_totals[month] += order.userCommissions

        # Append the results to chartData
        current_date = start_date
        while current_date <= end_date:
            month_str = current_date.strftime('%Y-%m')
            chartData.append({
                'date': current_date.strftime('%m-%Y'),
                'amount': monthly_totals[month_str]
            })
            # Move to the next month
            if current_date.month == 12:
                current_date = current_date.replace(year=current_date.year + 1, month=1)
            else:
                current_date = current_date.replace(month=current_date.month + 1)

class BannerAPIView(viewsets.ModelViewSet):
    model = Banner
    queryset = Banner.objects.all()
    filter_backends = [filters.SearchFilter]
    search_fields = ['name', 'url', 'location']

    def get_permissions(self):
        if self.request.method in ['POST', 'PUT', 'PATCH', 'DELETE']:
            return [IsAdminUser()]
        return [AllowAny()]

    def get_serializer(self, *args, **kwargs):
        if self.request.method in ['POST', 'PUT']:
            return BannerCreateUpdateSerializer(*args, **kwargs)
        return BannerPublicSerializer(*args, **kwargs)

    def get(self, request, *args, **kwargs):
        location = request.query_params.get('location')
        return Response(Banner.get_banners(location=location))

    def get_all_banners(self, *args, **kwargs):
        user_has_perm = IsAdminUser().has_permission(request=self.request, view=self)
        if not user_has_perm:
            return Response(status=status.HTTP_403_FORBIDDEN)
        return super().list(*args, **kwargs)

    @action(detail=False, methods=['get'])
    def list_locations(self, request):
        locations = self.queryset.model._meta.get_field('location').flatchoices
        formatted_locations = [{"id": key, "title": value} for key, value in locations]
        return Response(formatted_locations)

class ShortLinkAPIView(viewsets.ModelViewSet):
    model = ShortLink
    serializer_class = ShortLinkSerializer
    queryset = ShortLink.objects.all()
    permission_classes = [IsAdminUser]
    lookup_field = 'short_id'

class ShortLinkRedirectAPIView(views.APIView):
    permission_classes = [AllowAny]

    def get_link_url(self, short_id: str) -> str | None:
        link = ShortLink.objects.filter(short_id=short_id, status='active').first()
        return link.url if link else None

    def get_cached_url(self, short_id: str) -> str | None:
        front_end_404_page = settings.FRONT_END_BASE_URL + '/404'

        short_id = ShortLink.validate_short_id(short_id)  # type:ignore

        if not short_id:
            return front_end_404_page

        link_url = cache.get_or_set(
            ShortLink.cache_prefix + short_id,
            lambda: self.get_link_url(short_id),
            60 * 60 * 24 * 30  # Cache for 30 days
        )

        if not link_url:
            return front_end_404_page

        return link_url

    def get(self, request, shortId):
        url = self.get_cached_url(shortId)
        response = redirect(url)
        return response
