### POD USED TO DEBUG CRICTL COMMANDS
apiVersion: v1
kind: Pod
metadata:
  name: debug-containerd
spec:
  hostPID: true
  containers:
    - name: debug
      image: bitnami/kubectl:1.32-debian-12
      env:
        - name: CRICTL_VERSION # define a versão da ferramenta crictl
          value: "v1.32.0"
        - name: CONTAINER_RUNTIME_ENDPOINT
          value: "unix:///run/containerd/containerd.sock"
      command:
        - /bin/sh
        - -c
        - |
          apt-get update && apt-get install -y wget
          wget https://github.com/kubernetes-sigs/cri-tools/releases/download/$CRICTL_VERSION/crictl-$CRICTL_VERSION-linux-amd64.tar.gz
          tar zxvf crictl-$CRICTL_VERSION-linux-amd64.tar.gz -C /usr/local/bin
          rm -f crictl-$CRICTL_VERSION-linux-amd64.tar.gz
          sleep 3600
      volumeMounts:
        - name: containerd-socket
          mountPath: /run/containerd
        - name: scripts-volume
          mountPath: /scripts
      securityContext:
        privileged: true
        runAsUser: 0
  volumes:
    - name: containerd-socket
      hostPath:
        path: /run/containerd
        type: Directory
    - name: scripts-volume
      configMap:
        name: image-pruner-config
  restartPolicy: Never