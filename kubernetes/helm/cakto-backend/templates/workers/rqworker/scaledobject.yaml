apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: cakto-backend-rqworker-scaledobject
  labels:
    {{- include "common.labels" . | nindent 4 }}
spec:
  scaleTargetRef:
    name: cakto-backend-rqworker
  minReplicaCount:  {{ .Values.rqworker.scaledObject.minReplicaCount }}
  maxReplicaCount:  {{ .Values.rqworker.scaledObject.maxReplicaCount }}
  pollingInterval: {{ .Values.rqworker.scaledObject.pollingInterval }}
  triggers:
  - type: redis
    metadata:
      usernameFromEnv: REDIS_USER
      passwordFromEnv: REDIS_PASSWORD
      addressFromEnv: REDIS_ADDRESS
      listName: rq:queue:default
      {{- with .Values.rqworker.scaledObject.metadata }}
        {{- toYaml . | nindent 6 }}
      {{- end }}