apiVersion: apps/v1
kind: Deployment
metadata:
  name: cakto-backend-rqworker
  labels:
    app.kubernetes.io/name: cakto-backend-rqworker
    {{- include "common.labels" . | nindent 4 }}
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: cakto-backend-rqworker
  template:
    metadata:
      labels:
        app.kubernetes.io/name: cakto-backend-rqworker
        {{- include "common.labels" . | nindent 8 }}
    spec:
      containers:
      - name: cakto-backend-rqworker
        image:  "{{ .Values.backend.container.image }}:{{ .Values.backend.container.tag }}"
        imagePullPolicy: IfNotPresent
        {{- with .Values.rqworker.container.resources }}
        resources:
          {{- toYaml . | nindent 10 }}
        {{- end }}
        envFrom:
        - secretRef:
            name: app-secrets
        env:
          - name: BACKEND_APP
            value: rqworker