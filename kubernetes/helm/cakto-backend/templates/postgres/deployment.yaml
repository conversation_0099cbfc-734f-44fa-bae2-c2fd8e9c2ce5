{{- if (default false .Values.postgres.create) }}
# postgres
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cakto-backend-postgres
  labels:
    app.kubernetes.io/name: cakto-backend-postgres
    {{- include "common.labels" . | nindent 4 }}
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: cakto-backend-postgres
  template:
    metadata:
      labels:
        app.kubernetes.io/name: cakto-backend-postgres
        {{- include "common.labels" . | nindent 8 }}
    spec:
      containers:
      - name: database
        image: {{ .Values.postgres.container.imageAndTag }}
        imagePullPolicy: IfNotPresent
        env:
        - name: POSTGRES_USER
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: DB_USER
        - name: POSTGRES_DB
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: DB_NAME
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: DB_PASSWORD
        {{- with .Values.postgres.container.resources }}
        resources:
          {{- toYaml . | nindent 10 }}
        {{- end}}
        ports:
        - containerPort: 5432
          name: dbport
{{- end }}