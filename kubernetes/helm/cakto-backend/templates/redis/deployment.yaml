{{- if (default false .Values.redis.create) }}
# redis
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cakto-backend-redis
  labels:
    app.kubernetes.io/name: cakto-backend-redis
    {{- include "common.labels" . | nindent 4 }}
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: cakto-backend-redis
  template:
    metadata:
      labels:
        app.kubernetes.io/name: cakto-backend-redis
        {{- include "common.labels" . | nindent 8 }}
    spec:
      initContainers:
      - name: create-redis-acl-file
        image: busybox:1.36
        command: ["sh", "-c"]
        args:
          - |
            cat <<EOF > /usr/local/etc/redis/users.acl
            user default off
            user ${REDIS_ACL_USERNAME} on >${REDIS_ACL_PASSWORD} ~* &* +@all
            EOF
        env:
        - name: REDIS_ACL_USERNAME
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: REDIS_USER
        - name: REDIS_ACL_PASSWORD
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: REDIS_PASSWORD
        volumeMounts:
          - name: redis-acl-volume
            mountPath: /usr/local/etc/redis
      containers:
      - name: redis
        image: {{ .Values.redis.container.imageAndTag}}
        command: ["redis-server"]
        args: [
          "--aclfile", "/usr/local/etc/redis/users.acl"
        ]
        imagePullPolicy: IfNotPresent
        env:
        - name: REDIS_ACL_USERNAME
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: REDIS_USER
        - name: REDIS_ACL_PASSWORD
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: REDIS_PASSWORD
        volumeMounts:
        - name: redis-data
          mountPath: /data
        - name: redis-acl-volume
          mountPath: /usr/local/etc/redis/users.acl
          subPath: users.acl
        {{- with .Values.redis.container.resources }}
        resources:
          {{- toYaml . | nindent 10 }}
        {{- end }}
        ports:
        - containerPort: 6379
          name: redisport
      volumes:
      - name: redis-data
        persistentVolumeClaim:
          claimName: cakto-backend-redis-pvc
      - name: redis-acl-volume
        emptyDir: {}
{{- end }}