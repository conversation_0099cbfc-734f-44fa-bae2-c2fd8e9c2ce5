{{- if (default false .Values.datadog.enabled) }}
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: datadog-secrets-external-secret
  namespace: datadog
  labels:
    {{- include "common.labels" . | nindent 4 }}
spec:
  refreshInterval: 10s
  secretStoreRef:
    name: aws-cluster-store
    kind: ClusterSecretStore
  target:
    name: datadog-secrets
    creationPolicy: Owner
  dataFrom:
  - extract:
      key: "global/datadog-secrets"
{{- end }}