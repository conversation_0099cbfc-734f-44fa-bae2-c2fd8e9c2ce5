apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: cakto-backend-main-api
  labels:
    {{- include "common.labels" . | nindent 4 }}
  {{- with .Values.main_api.ingress.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  {{- if (default false .Values.backend.ingress.useTls) }}
  tls:
    - hosts:
      - {{.Values.backend.ingress.host}}
      {{- with .Values.backend.ingress.additionalHosts }}
        {{- range . }}
      - {{ . | trim }}
        {{- end }}
      {{- end }}
      secretName: main-api-tls
  {{- end }}
  ingressClassName: nginx
  rules:
    - host: {{ .Values.backend.ingress.host }}
      http:
        paths:
          - pathType: Prefix
            path: "/"
            backend:
              service:
                name: cakto-backend-main-api
                port:
                  number: 80
  {{- with .Values.backend.ingress.additionalHosts }}
    {{- range . }}
    - host: {{ . | trim }}
      http:
        paths:
          - pathType: Prefix
            path: "/"
            backend:
              service:
                name: cakto-backend-main-api
                port:
                  number: 80
    {{- end }}
  {{- end }}
