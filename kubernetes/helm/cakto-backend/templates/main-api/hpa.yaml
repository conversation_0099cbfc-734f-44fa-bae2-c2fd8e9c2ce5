apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: cakto-backend-main-api-hpa
  labels:
    {{- include "common.labels" . | nindent 4 }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: cakto-backend-main-api
  minReplicas: {{ .Values.main_api.hpa.minReplicas }}
  maxReplicas: {{ .Values.main_api.hpa.maxReplicas }}
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: {{ .Values.main_api.hpa.averageUtilization }}