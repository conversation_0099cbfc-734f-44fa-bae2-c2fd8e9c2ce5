apiVersion: apps/v1
kind: Deployment
metadata:
  name: cakto-backend-main-api
  labels:
    {{- include "common.labels" . | nindent 4 }}
    app.kubernetes.io/name: cakto-backend-main-api
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: cakto-backend-main-api
  template:
    metadata:
      labels:
        {{- include "common.labels" . | nindent 8 }}
        app.kubernetes.io/name: cakto-backend-main-api
      {{- with .Values.main_api.container.annotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    spec:
      containers:
      - name: cakto-backend-main-api
        image: "{{ .Values.backend.container.image }}:{{ .Values.backend.container.tag }}"
        imagePullPolicy: IfNotPresent
        {{- with .Values.main_api.container.resources }}
        resources:
          {{- toYaml . | nindent 10 }}
        {{- end }}
        envFrom:
        - secretRef:
            name: app-secrets
        env:
          - name: BACKEND_APP
            value: api
        ports:
        - containerPort: 8000
          name: http
        {{- with .Values.main_api.container.readinessProbe }}
        readinessProbe:
          {{- toYaml . | nindent 10 }}
        {{- end }}
        {{- with .Values.main_api.container.startupProbe }}
        startupProbe:
          {{- toYaml . | nindent 10 }}
        {{- end }}