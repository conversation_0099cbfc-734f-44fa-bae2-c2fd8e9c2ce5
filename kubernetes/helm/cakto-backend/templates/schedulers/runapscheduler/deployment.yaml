apiVersion: apps/v1
kind: Deployment
metadata:
  name: cakto-backend-runapscheduler
  labels:
    app.kubernetes.io/name: cakto-backend-runapscheduler
    {{- include "common.labels" . | nindent 4 }}
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: cakto-backend-runapscheduler
  replicas: {{ .Values.runapscheduler.container.replicas }}
  template:
    metadata:
      labels:
        app.kubernetes.io/name: cakto-backend-runapscheduler
        {{- include "common.labels" . | nindent 8 }}
    spec:
      containers:
      - name: cakto-backend-runapscheduler
        image: "{{ .Values.backend.container.image }}:{{ .Values.backend.container.tag }}"
        imagePullPolicy: IfNotPresent
        {{- with .Values.runapscheduler.container.resources }}
        resources:
          {{- toYaml . | nindent 10 }}
        {{- end }}
        envFrom:
        - secretRef:
            name: app-secrets
        env:
          - name: BACKEND_APP
            value: runapscheduler