apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: cakto-backend-checkout-api
  labels:
    {{- include "common.labels" . | nindent 4 }}
  {{- with .Values.checkout_api.ingress.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  {{- if (default false .Values.backend.ingress.useTls) }}
  tls:
    - hosts:
      - {{.Values.backend.ingress.host}}
      secretName: main-api-tls
  {{- end }}
  ingressClassName: nginx
  rules:
    - host: {{ .Values.backend.ingress.host }}
      http:
        paths:
          - path: /api/product/checkout
            pathType: Prefix
            backend:
              service:
                name: cakto-backend-checkout-api
                port:
                  number: 80
          - path: /api/checkout/installments
            pathType: Prefix
            backend:
              service:
                name: cakto-backend-checkout-api
                port:
                  number: 80