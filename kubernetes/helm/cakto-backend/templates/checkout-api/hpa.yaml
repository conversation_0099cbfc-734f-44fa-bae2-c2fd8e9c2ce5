apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: cakto-backend-checkout-api-hpa
  labels:
    {{- include "common.labels" . | nindent 4 }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: cakto-backend-checkout-api
  minReplicas: {{ .Values.checkout_api.hpa.minReplicas }}
  maxReplicas: {{ .Values.checkout_api.hpa.maxReplicas }}
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: {{ .Values.checkout_api.hpa.averageUtilization }}