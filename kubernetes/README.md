# Kubernetes
Documentação inicial para utilização do `cakto-backend` com kubernetes


## Configurações iniciais do cluster


### Instalação do NGINX Ingress
Após criar o cluster é necessário realizar a [instalação NGINX Ingress](https://kubernetes.github.io/ingress-nginx/deploy/) no cluster.

#### NGINX Ingress na Digital Ocean
Para clusters na Digital Ocean, basta seguir os passos abaixo (obtidos [nesta página](https://www.digitalocean.com/community/tutorials/****************************************************************helm)):

Adicionar o ingress ao repositório do seu Helm:
```bash
helm repo add ingress-nginx https://kubernetes.github.io/ingress-nginx
```

Obter atualizações do seu repositório Helm:
```bash
helm repo update
```

Realizar a instalação
```bash
helm upgrade --install nginx-ingress ingress-nginx/ingress-nginx -n default \
  --set controller.publishService.enabled=true \
  --set controller.config.limit-req-status-code="429" \
  --set controller.config.use-forwarded-headers="true" \
  --set controller.config.proxy-real-ip-cidr="0.0.0.0/0"
```

### Instalação do kube-state-metrics
Para obter métricas mais detalhadas do cluster, é necessária a instalação do [kube-state-metrics](https://github.com/kubernetes/kube-state-metrics) no cluster.

#### kube-state-metrics na Digital Ocean
Para clusters na Digital Ocean, basta seguir os passos abaixo (obtidos [nesta página](https://docs.digitalocean.com/products/kubernetes/how-to/monitor-advanced/)):
1. Acesse um terminal e crie a pasta `/tmp/kube-state-metrics`, com o comando
```bash
mkdir -p /tmp/kube-state-metrics
```
1. Clone o repositório git com os manifestos dos componentes, através do comando:
```bash
git clone https://github.com/kubernetes/kube-state-metrics.git /tmp/kube-state-metrics
```
1. Realize a instalação dos componentes no cluster, através do comando:
```bash
kubectl apply -k /tmp/kube-state-metrics/examples/standard/
```

### Configuração do image-pruner
O `image-pruner` é responsável por realizar a limpeza de imagens que não são mais utilizads nos nodes do cluster.
Para criá-lo e configurá-lo, siga os passos abaixo:
1. Acesse a pasta raiz do repositório a partir de um terminal;
1. Agora, crie o `image-pruner` utilizando o comando `kubectl apply -k ./kubernetes/cluster-config/image-pruner`;
1. Aguarde alguns minutos e execute o comando `kubectl get pods -n kube-system -o wide| grep -i image-pruner` para verificar se os pods foram criados em todos os nodes;


### Configuração do cert-manager e ClusterIssuer
O `cert-manager` é responsável por gerenciar os certificados ssl dos Ingresses do cluster.
Para criá-lo, siga os passos abaixo:
1. Acesse a pasta raiz do repositório a partir de um terminal;
1. Crie o namespace `cert-manager` através do comando `kubectl create ns cert-manager`;
1. Agora, crie os CRDs do `cert-manager` através do comando `kubectl apply -f ./kubernetes/cluster-config/cert-manager/cert-manager.yaml`;
1. Agora crie o `ClusterIssuer` do **letsencrypt** através do comando `kubectl apply -f ./kubernetes/cluster-config/cert-manager/letsencrypt-issuer.yaml`;


### Configuração do metrics-server
O `metrics-server` é responsável por extrair métricas de CPU e memória de todos os pods para que o Kubernetes consiga fazer o gerenciamento correto de replicas de pods.
Para criá-lo, siga os passos abaixo:
1. Acesse a pasta raiz do repositório a partir de um terminal;
1. Agora, crie o `metrics-server` através do comando `kubectl apply -f ./kubernetes/cluster-config/metrics-server/deploy-metrics-server.yaml`;
1. Aguarde alguns segundos e valide se os `metrics-server` foi corretamente instalado através do comando `kubectl top pods`. Um lista dos pods que mais consomem memória e CPU no node deverá ser exibida.


### Instalação do KEDA
O  [KEDA](https://keda.sh/) é o responsável por escalar para cima ou para baixo os worker da cakto-backend baseado na quantidade de itens em suas filas.
Para instalá-lo, execute os comandos abaixo no cluster desejado:
1. Adicionar o repositório do kedacore
    ```bash
    helm repo add kedacore https://kedacore.github.io/charts  && helm repo update
    ```
2. Instalar os CRDs do KEDA no cluster:
    ```bash
    helm install keda kedacore/keda --version 2.17.0 --namespace keda --create-namespace
    ```


### Instalação do ESO - External Secrets Operator
O  [External Secrets Operator](https://external-secrets.io/) é o responsável por sincornizar as secrets do kubernetes com provedores de vault externos.


#### Pre-Requisitos
Para que seja possível a integração entre *ESO* e o *AWS Secrets Manager*, é necessário que os pre-requisitos abaixo sejam satisfeitos:
1. Ter uma conta na [AWS](https://aws.amazon.com/);
1. Criar as secrets listadas abaixo na seção [Secrets](#secrets), no [AWS Secrets Manager](https://aws.amazon.com/pt/secrets-manager/), respeitando o prefixo de cada ambiente de acordo com a tabela abaixo:
    | Ambiente | Prefixo |
    | --- | --- |
    | Local | `cakto-backend-local`|
    | Staging | `cakto-backend-staging`|
    | Produção | `cakto-backend-prd`|
    | Nommi | `cakto-backend-nommi`|

    > Exemplo: a secret `app-secrets` para o ambiente de **produção** deve ser criada como `cakto-backend-prd/app-secrets`
1. Criar a secret `global/datadog-secrets` no [AWS Secrets Manager](https://aws.amazon.com/pt/secrets-manager/), com apenas um registro chave-valor, substituindo o valor de `<api-key-para-acessar-datadog>` pela api-key obtida no [datadog](https://app.datadoghq.com/organization-settings/api-keys):
    `api-key:<api-key-para-acessar-datadog>`
1. Criar uma [policy no aws](https://docs.aws.amazon.com/IAM/latest/UserGuide/access_policies.html#policies_id-based), que permita apenas a leitura das secrets de um determinado ambiente. Use o template abaixo, substituindo os valores de `<region-da-secret>` pela region onde a secret foi criada, `<id-conta>` pelo número da conta aws onde a secret foi criada e de `<prefixo-secret>` pelo prefixo das secrets relacionadas ao ambiente:
    ```json
    {
        "Version": "2012-10-17", // Mantenha esse valor
        "Statement": [
            {
                "Sid": "AllowReadSecrets", // Nome do statement. Deve ser único. Altere se necessário.
                "Effect": "Allow", // Mantenha esse valor
                "Action": [
                    "secretsmanager:GetSecretValue", // Mantenha esse valor
                    "secretsmanager:DescribeSecret" // Mantenha esse valor
                ],
                "Resource": [
                    "arn:aws:secretsmanager:<region-da-secret>:<id-conta>:secret:global/datadog-secrets*", // Altere apenas <region-da-secret> e <id-conta>. Exemplo: arn:aws:secretsmanager:us-east-1:************:secret:global/datadog-secrets*
                    "arn:aws:secretsmanager:<region-da-secret>:<id-conta>:secret:<prefixo-secret>/*" // Altere <region-da-secret> , <id-conta> e <prefixo-secret>. Exemplo: arn:aws:secretsmanager:us-east-1:************:secret:cakto-backend-staging/*
                ]
            }
        ]
    }
    ```
1. Criar [um user](https://docs.aws.amazon.com/IAM/latest/UserGuide/id_users.html) e atribuir a policy criada no passo anterior a ele.
1. Criar um [access token](https://docs.aws.amazon.com/singlesignon/latest/userguide/generate-token.html) para o usuário criado no passo anterior. **ATENÇÃO**: Guarde os valores de `Access key` e `Secret access key` gerados nesse passo, pois serão utilizados na configuração da `Secret` para o `ClusterSecretStore`.

#### Instalação
Para instalar e configurar o ESO no cluster, siga os passos abaixo:
1. Acesse o cluster desejado;
2. Adicione o *repositório do ESO* através do comando:
   ```bash
   helm repo add external-secrets https://charts.external-secrets.io && helm repo update
   ```
3. Instale os componentes do ESO através do comando:
    ```bash
    helm install external-secrets \
        external-secrets/external-secrets \
        -n external-secrets \
        --create-namespace \
        --version 0.16.2 \
        --set installCRDs=true
    ```

4. **Altere os valores** de `<change-me>` para os valores do token de acesso do usuário criado no AWS e crie a `Secret` utilizada para a integração entre o *ESO* e o *AWS Secrets Manager* através do comando:
    ```bash
    kubectl create secret generic awssm-secret -n external-secrets --from-literal="access-key=<change-me>" --from-literal="secret-access-key=<change-me>"
    ```
5. **Altere o valor** do campo `provider.aws.region` (atualmente com `change-me`) para o valor da *region* onde o *AWS Secrets Manager* foi criadonNo arquivo [kubernetes/cluster-config/external-secrets/aws-cluster-store.yaml](/kubernetes/cluster-config/external-secrets/aws-cluster-store.yaml) e execute o comando:
    ```bash
    kubectl apply -f kubernetes/cluster-config/external-secrets/aws-cluster-store.yaml
    ```

### Instalação do DataDog
O monitoramento desta aplicação é realizado via [datadog](https://www.datadoghq.com/). Com isso é necessário instalar os CRDs do datadog para que seja possível coletar logs e métricas da aplicação. <br/>
Para instalar os CRDs do agent do DataDog, siga os passos abaixo:
<br/>
1. Acesse o cluster desejado;
1. Adicione o *repositório do DataDog* através do comando:
   ```bash
   helm repo add datadog https://helm.datadoghq.com && helm repo update
   ```
1. Instale os componentes do ESO através do comando:
    ```bash
    helm install datadog-operator datadog/datadog-operator \
        -n datadog \
        --create-namespace \
        --version 2.11.1
    ```


## Arquivos de configuração das aplicações
Para que seja possível realizar o deploy das aplicações **em staging**, é necessário que as `Secrets` abaixo sejam criadas no cluster de staging.
Esses itens disponibilizam todas as configurações necessárias para o correto funcionamento de todas as aplicações envolvidas na solução.

### Secrets
| Secret | Descrição | Referência de conteúdo |
| --- | --- | --- |
| `app-secrets` | Esta secret armazena todas as configurações necessárias para as **aplicações cakto-backend**. | [kubernetes/local-tests/localstack/secrets/app-secrets.json](local-tests/localstack/secrets/app-secrets.json) |

<br/>

> **ATENÇÃO**: Para que o Kubernetes possa realizar o readinessProbe, é necessário que o host `pod.localhost` esteja presente na variável de ambiente `ALLOWED_HOSTS` da secret `app-secrets`. Caso contrário o pod da API jamais entrará no ar. (Exemplo de configuração: `ALLOWED_HOSTS=pod.localhost,api.cakto.com.br` )

<br/>

## Testes locais
Abaixo estão listadas as seções de referência para a criação do ambiente local com todos os artefatos ecessários para executar e testar à aplicação `cakto-backend`

### Pré-requisitos
Na máquina da pessoa desenvolvedora, é necessário que estejam instaladas as ferramentas abaixo:
- [Docker Engine](https://docs.docker.com/engine/install/)
- [Docker compose](https://docs.docker.com/compose/install/)
- [Kubectl](https://kubernetes.io/docs/tasks/tools/#kubectl)
- [Kind](https://kubernetes.io/docs/tasks/tools/#kind)
- [Helm](https://helm.sh/docs/intro/install/)


### 1. Criando o cluster localmente
Para criar o cluster local para testes, acesse a **pasta raiz** deste repositório e execute o comando:
```bash
kind create cluster --config kubernetes/local-tests/kind/local-cluster-config.yml
```


Utilize o comando abaixo para verificar  se o cluster foi criado corretamente:

```bash
kubectl get nodes
```

Deverão ser exibidos 4 nodes:
- `kind-control-plane`
- `kind-worker`
- `kind-worker2`
- `kind-worker3`

### 2. Instalando requisitos do cluster
> ATENÇÃO: Certifique-se de estar no contexto `kind-kind` antes de executar os comandos desta seção, utilizando o comando `kubectl config get-contexts`.

**Instalando o servidor de métricas**

Para instalar o metrics-server (responsável por gerenciar as métricas dos PODs e, por permitir o autoscaling dos pods - HPA) no cluster local acesse a **pasta raiz** deste reposistório execute o comando:
```bash
kubectl apply -f kubernetes/cluster-config/metrics-server/deploy-metrics-server-local.yaml
```

**Instalando o local-path-provisioner para os dados do Elasticsearch Kubernetes Cluster**

Para instalar o `local-path-provisioner` no cluster local execute o comando:
```bash
kubectl apply -f https://raw.githubusercontent.com/rancher/local-path-provisioner/v0.0.31/deploy/local-path-storage.yaml
```

**Instalando o KEDA**

Para instalar o [KEDA](https://keda.sh/) no cluster local siga os passos da seção [Instalação do KEDA](#instalação-do-keda)


**Instalando o localstack**

Para instalar o [localstack](https://docs.localstack.cloud/overview/), que simulará os acessos do AWS Secrets, no cluster local execute o comando abaixo:
```bash
kubectl apply -k kubernetes/local-tests/localstack
```

**Instalando o ESO - External Secrets Operator**

Para instalar os Custom Resources Definitions do  [External Secrets Operator](https://external-secrets.io/) no cluster local siga os passos abaixo:
1. Adicione o *repositório do ESO* através do comando:
   ```bash
   helm repo add external-secrets https://charts.external-secrets.io && helm repo update
   ```
1. Instale os componentes do ESO através do comando:
    ```bash
    helm install external-secrets \
        external-secrets/external-secrets \
        -n external-secrets \
        --create-namespace \
        --version 0.16.2 \
        --set extraEnv[0].name=AWS_SECRETSMANAGER_ENDPOINT \
        --set extraEnv[0].value=http://localstack.localstack.svc.cluster.local:4566 \
        --set extraEnv[1].name=AWS_STS_ENDPOINT \
        --set extraEnv[1].value=http://localstack.localstack.svc.cluster.local:4566 \
        --set extraEnv[2].name=AWS_SSM_ENDPOINT \
        --set extraEnv[2].value=http://localstack.localstack.svc.cluster.local:4566 \
        --set installCRDs=true
    ```
1. Crie a secret que permitirá o acesso do `ClusterSecretStore` ao aws secrets através do comando:
    ```bash
    kubectl apply -f kubernetes/local-tests/external-secrets/awssm-secret.yaml
    ```
1. Crie o `ClusterSecretStore` através do comando:
    <br/>

    > **Atenção**: A criação do `ClusterSecretStore` pode dar erro nas primeiras vezes que for executada.<br/> Aguarde alguns segundos e execute o comando novamente, até ser criado com sucesso.

    ```bash
    kubectl apply -f kubernetes/cluster-config/external-secrets/aws-cluster-store.yaml
    ```


**Instalação do DataDog**

Para instalar o [datadog](https://www.datadoghq.com/) agent no cluster local siga os passos da seção [Instalação do DataDog](#instalação-do-dataDog)
<br/>


**Instalando NGINX ingress controller para o kind**

Para instalar os NGINX ingress controller no cluster local siga os passos abaixo:
1. Instale o *NGINX ingress controller* através do comando:
   ```bash
   kubectl apply -f https://raw.githubusercontent.com/kubernetes/ingress-nginx/controller-v1.10.1/deploy/static/provider/kind/deploy.yaml
   ```

### 3. Gerando imagens docker do backend
Para que o cluster consiga executar a api e os worker da solução cakto-backend, é necessário que uma imagem de container seja geradas e carregada para o cluster local. Para isso, siga os passos abaixo

#### 3.1 Gerar e carregar a imagem de cakto-backend

Para gerar a imagem da **cakto-backend**, acesse a **pasta raiz** deste reposistório execute o comando:
```bash
docker build . -t cakto-backend:latest
```

> OBSERVAÇÃO: A geração da imagem pode levar alguns minutos

Para carregar a imagem da **cakto-backend**  no cluster local execute o comando:
```bash
kind load docker-image cakto-backend:latest
```

> OBSERVAÇÃO: O carregamento da imagem pode levar alguns minutos


### 4. Instalando as aplicações e dependências da solução cakto-backend
> ATENÇÃO: Certifique-se de estar no contexto `kind-kind` antes de executar os comandos desta seção, utilizando o comando `kubectl config get-contexts`.

#### 4.1 Instalando
Acesse a **pasta raiz** deste reposistório execute o comando:
```bash
helm upgrade --install cakto-backend-local kubernetes/helm/cakto-backend \
    -f kubernetes/helm/cakto-backend/base-values.yaml \
    --namespace cakto-backend-local \
    --create-namespace
```

#### 4.2 Desinstalando
Acesse a **pasta raiz** deste reposistório execute o comando:
```bash
helm uninstall cakto-backend-local --namespace cakto-backend-local
```