{"AI_COMPLIANCE_API_KEY": "", "AI_COMPLIANCE_URL": "", "ALLOWED_HOSTS": "pod.localhost,backend.dev.teste,frontend.dev.teste,localhost,backend.local", "APPLE_PAY_CERT": "", "APPLE_PAY_DISPLAY_NAME": "", "APPLE_PAY_DOMAIN_NAME": "", "APPLE_PAY_KEY": "", "APPLE_PAY_MERCHANT_IDENTIFIER": "", "AWS_ACCESS_KEY_ID": "aws_s3_access_key_id", "AWS_S3_CUSTOM_DOMAIN": "''", "AWS_S3_ENDPOINT_URL": "'http://localstack:4566'", "AWS_S3_REGION_NAME": "eu-west-1", "AWS_SECRET_ACCESS_KEY": "aws_secret_access_key", "AWS_STORAGE_BUCKET_NAME": "caktobackend", "BACKEND_BASE_URL": "localhost:8000/", "CIELO_3DS_CLIENT_ID": "dba3a8db-fa54-40e0-8bab-7bfb9b6f2e2e", "CIELO_3DS_CLIENT_SECRET": "", "CIELO_3DS_ENABLED": "false", "CIELO_3DS_ESTABLISHMENT_CODE": "11111", "CIELO_3DS_MCC": "1212", "CIELO_3DS_MERCHANT_NAME": "Loja Exemplo Ltda", "CIELO_RETURN_URL": "https://api.teste.com/v1/payments/cielo/return", "CHECKOUT_BASE_URL": "http://localhost:3001", "CORS_ORIGIN_WHITELIST": "http://localhost:3000,http://localhost:3001,http://localhost:3002,https://frontend.dev.teste,https://backend.dev.teste", "COURSE_ACCESS_BASE_URL": "https://members.cakto.com.br/", "CRM_WEBHOOK_TOKEN": "", "CRM_WEBHOOK_URL": "", "CSRF_TRUSTED_ORIGINS": "http://localhost:3000,http://localhost:8000,http://localhost:3001,http://localhost:3002,https://frontend.dev.teste,https://backend.dev.teste", "DB_HOST": "cakto-backend-postgres-svc.cakto-backend-local.svc.cluster.local", "DB_NAME": "caktodb", "DB_PASSWORD": "caktopassword", "DB_PORT": "5432", "DB_SSL_MODE": "disable", "DB_USER": "caktouser", "DEBUG": "false", "DISCORD_APP_ID": "", "DISCORD_BOT_TOKEN": "", "DISCORD_SECRET": "", "DJANGO_SETTINGS_MODULE": "cakto.settings_devcontainer", "EMAIL_HOST_PASSWORD": "", "EMAIL_HOST_USER": "", "EMAIL_HOST": "", "EMAIL_PORT": "", "EMAIL_USE_TLS": "", "ENABLE_DJANGO_ADMIN_MFA": "true", "ENABLE_FAKE_REDIS_CONN": "false", "FACEBOOK_ACCESS_TOKEN": "", "FACEBOOK_PIXEL_ID": "", "FRONT_END_BASE_URL": "https://app.cakto.com.br", "GATEWAY_URL": "", "GOOGLE_APPLICATION_CREDENTIALS": "/app/firebase/cakto-staging.json", "GUNICORN_CMD_ARGS": "-w 4 -b 0.0.0.0:8000 --access-logfile \"-\"", "INFOBIP_API_TOKEN": "", "INFOBIP_AUTH_TEMPLATE_NAME": "autenticacao_cakto", "INFOBIP_BASE_URL": "", "INFOBIP_WHATSAPP_NUMBER": "", "INSTAGRAM_AUTH_TOKEN": "TESTING_TOKEN!@#$*(&)", "INSTAGRAM_BASE_URL": "https://testing-instagram.com/", "MAILCHIMP_API_KEY": "", "MAILCHIMP_AUDIENCE_ID": "", "MAILCHIMP_SERVER_PREFIX": "", "MANYCHAT_AUTO_PHONE_ID": "123456", "MANYCHAT_TOKEN": "manychatTest", "MEMBERS_ADMIN_PASSWORD": "", "MEMBERS_URL": "", "MEMBERS_V2_API_TOKEN": "TESTING_TOKEN", "MEMBERS_V2_BASE_URL": "https://testing-members.com", "MEMBERS_V2_MAX_FILE_SIZE": "1024", "MEMBERS_V2_MAX_IMAGE_SIZE": "10", "MEMBERS_V2_TOKEN_CACHE_TIMEOUT": "1380", "NOX_PUBLIC_KEY": "", "PROMETHEUS_TOKEN": "", "RECAPTCHA_PROJECT_ID": "", "RECAPTCHA_SITE_KEY": "", "RECAPTCHA_THRESHOLD": "0.1", "REDIRECT_DOMAIN": "backend.dev.teste", "REDIS_ADDRESS": "cakto-backend-redis-svc.cakto-backend-local.svc.cluster.local:6379", "REDIS_PASSWORD": "redis_pass", "REDIS_URL_QUEUE": "redis://redis_user:<EMAIL>:6379", "REDIS_URL": "redis://redis_user:<EMAIL>:6379", "REDIS_USER": "redis_user", "REGISTER_PRODUCER_WEBHOOK_URL": "", "SECRET_KEY": "django-insecure-i(^4b$@4lj5%wucdq#qls19tp9z-@4ab$ez^+vpgcb2v%8xs36", "SESSION_COOKIE_DOMAIN": "localhost", "UVICORN_CMD_ARGS": "--host 0.0.0.0 --port 8000 --workers 3", "VIDEO_CDN_HOST_NAME": "", "VIDEO_HOST_APIKEY": "", "VIDEO_LIBRARY_ID": "", "WEBHOOK_SECRETS": "webhooktest", "WHATSAPP_VALIDATION_ENABLED": "false"}