import datetime
import pickle
from decimal import Decimal

import orjson
from django.conf import settings


def decimal_serializer(obj):
    if isinstance(obj, Decimal):
        return float(obj)
    return str(obj)


async def get(app, key):
    data = await app.state.cache.get(':'.join([settings.CACHE_PREFIX, '1', key]))
    if data:
        try:
            return pickle.loads(data)
        except pickle.UnpicklingError:
            return orjson.loads(data)
    return None

async def set(app, key, value, timeout: datetime.timedelta | None = None):
    try:
        data = await value()
    except TypeError:
        data = value
    await app.state.cache.set(':'.join([settings.CACHE_PREFIX, '1', key]), pickle.dumps(data), ex=timeout)
    return data

async def get_or_set(app, key, value, timeout: datetime.timedelta | None = None):
    data = await app.state.cache.get(':'.join([settings.CACHE_PREFIX, '1', key]))
    if data:
        try:
            return pickle.loads(data)
        except pickle.UnpicklingError:
            return orjson.loads(data)
    else:
        try:
            data = await value()
        except TypeError:
            data = value
        await app.state.cache.set(':'.join([settings.CACHE_PREFIX, '1', key]), pickle.dumps(data), ex=timeout)
        return data
