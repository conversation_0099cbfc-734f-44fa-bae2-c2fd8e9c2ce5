from decimal import Decimal
from product.utils import calculateInstallments

async def calculateProductInstallments(offer_data, installments_fees) -> dict:
    offer_data['product']['calculatedInstallments'] = calculateInstallments(
        installments_fees,
        price=Decimal(offer_data['price']),
        max_installments=offer_data['product']['installments'],
        offer_type=offer_data.get('type') or offer_data['product']['type'],
        recurrence_period=offer_data.get('recurrence_period'),
    )
    return offer_data
