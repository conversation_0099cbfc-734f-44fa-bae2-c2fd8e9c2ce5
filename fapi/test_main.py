from typing import OrderedDict
from unittest import mock

import pytest
from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

from main import get_serialized_data, serialize_offer, unpack_offer_id_and_checkout_id
from product.models import Checkout, Offer, Product


def test_unpack_offer_id_and_checkout_id_with_checkout_id():
    result = unpack_offer_id_and_checkout_id("12345_67890")
    assert result == ("12345", "67890")


def test_unpack_offer_id_and_checkout_id_without_checkout_id():
    result = unpack_offer_id_and_checkout_id("12345")
    assert result == ("12345", None)


def test_unpack_offer_id_and_checkout_id_with_empty_string():
    result = unpack_offer_id_and_checkout_id("")
    assert result == ("", None)


def test_unpack_offer_id_and_checkout_id_with_multiple_underscores():
    result = unpack_offer_id_and_checkout_id("12345_67890_abc")
    assert result == ("12345", "67890")


def test_unpack_offer_id_and_checkout_id_with_only_underscore():
    result = unpack_offer_id_and_checkout_id("_")
    assert result == ("", "")


@pytest.mark.django_db
def test_get_serialized_data_with_valid_short_id(product: Product, offer: Offer, checkout: Checkout):
    short_id = f"{offer.id}_{checkout.id}"

    offer_data, offer_instance = get_serialized_data(short_id)
    assert isinstance(offer_data, OrderedDict)
    assert isinstance(offer_instance, Offer)

    assert offer_data["id"] == offer.id
    assert offer_data["name"] == offer.name
    assert offer_data["status"] == offer.status
    assert offer_data["checkout"]["config"] == checkout.config


@pytest.mark.django_db
def test_get_serialized_data_with_invalid_short_id():
    with pytest.raises(HTTPException) as excinfo:
        get_serialized_data('invalid_id')
    assert excinfo.value.status_code == 400
    assert excinfo.value.detail == 'Oferta não encontrada.'


@pytest.mark.django_db
def test_get_serialized_data_with_no_checkout_id(
    product: Product,
    offer: Offer,
    checkout: Checkout,
):
    short_id = f"{offer.id}"
    offer_data, offer_instance = get_serialized_data(short_id)

    assert isinstance(offer_data, OrderedDict)
    assert isinstance(offer_instance, Offer)

    assert offer_data["id"] == offer.id
    assert offer_data["name"] == offer.name
    assert offer_data["status"] == offer.status
    assert offer_data["checkout"]["config"] == checkout.config


@pytest.mark.django_db
def test_offer_instance_with_two_checkouts(
    product: Product,
    offer: Offer,
    checkout: Checkout,
    second_offer_checkout: Checkout,
):
    short_id = f"{offer.id}_{checkout.id}"
    offer_data, offer_instance = get_serialized_data(short_id)

    assert isinstance(offer_data, OrderedDict)
    assert isinstance(offer_instance, Offer)

    assert offer_data["id"] == offer.id
    assert offer_data["name"] == offer.name
    assert offer_data["status"] == offer.status
    assert offer_data["checkout"]["config"] == checkout.config

    short_id = f"{offer.id}_{second_offer_checkout.id}"
    offer_data, offer_instance = get_serialized_data(short_id)

    assert isinstance(offer_data, OrderedDict)
    assert isinstance(offer_instance, Offer)

    assert offer_data["id"] == offer.id
    assert offer_data["name"] == offer.name
    assert offer_data["status"] == offer.status
    assert offer_data["checkout"]["config"] == second_offer_checkout.config

@pytest.fixture
def mock_offer_instance():
    offer_mock = mock.Mock(spec=Offer)
    return offer_mock

@pytest.mark.asyncio
@mock.patch('main.cache.get_or_set')
@mock.patch('main.unpack_offer_id_and_checkout_id')
async def test_serialize_offer_without_checkout_id(
    unpack_mock,
    get_or_set_mock,
    mock_offer_instance,
):
    # Setup mocks
    offer_data_mock = mock.Mock()
    get_or_set_mock.return_value = (offer_data_mock, mock_offer_instance)
    unpack_mock.return_value = ("test_offer", None)
    increase_checkout_visits_mock = mock.patch(
        'main.increase_checkout_visits.delay',
    ).start()

    # Call
    offer_data, offer_instance = await serialize_offer("test_offer")

    # Assertions
    get_or_set_mock.assert_called_once()
    unpack_mock.assert_called_once_with("test_offer")
    assert offer_data == offer_data_mock
    assert offer_instance == mock_offer_instance
    increase_checkout_visits_mock.assert_called_once_with(
        offer=mock_offer_instance,
        checkout_id=None,
    )


@pytest.mark.asyncio
@mock.patch('main.cache.get_or_set')
@mock.patch('main.unpack_offer_id_and_checkout_id')
async def test_serialize_offer_with_checkout_id(
    unpack_mock,
    get_or_set_mock,
    mock_offer_instance,
):
    # Setup mocks
    offer_data_mock = mock.Mock()
    get_or_set_mock.return_value = (offer_data_mock, mock_offer_instance)
    unpack_mock.return_value = ("test_offer", "checkout123")
    increase_checkout_visits_mock = mock.patch(
        'main.increase_checkout_visits.delay',
    ).start()

    # Call
    offer_data, offer_instance = await serialize_offer("test_offer_checkout123")

    # Assertions
    get_or_set_mock.assert_called_once()
    unpack_mock.assert_called_once_with("test_offer_checkout123")
    assert offer_data == offer_data_mock
    assert offer_instance == mock_offer_instance
    increase_checkout_visits_mock.assert_called_once_with(
        offer=mock_offer_instance,
        checkout_id="checkout123",
    )
