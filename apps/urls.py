from django.urls import path
from apps import views

urlpatterns = [
    path('app/platforms/', views.PlatformAPI.as_view({'get': 'list'}), name='platforms'),
    path('app/platforms/<int:pk>/app/', views.AppPlatformAPI.as_view({'post': 'create', 'get': 'list'}), name='apps'),
    path('app/<int:pk>/', views.AppPlatformAPI.as_view({'get': 'retrieve', 'put': 'update', 'patch': 'partial_update', 'delete': 'destroy'}), name='app'),
    path('app/memberkit/redirect/', views.MemberkitRedirectAPI.as_view(), name='memberkit-redirect'),
    path('app/active_campaign/redirect/', views.ActiveCampaignRedirectAPI.as_view(), name='active-campaign-redirect'),
    path('app/events/resend/<int:event_history_pk>/', views.AppHistoryEvents.as_view({'post': 'resend'}), name='resend-app-event'),
    path('app/events/<int:app_pk>/history/', views.AppHistoryEvents.as_view({'get': 'list'}), name='history'),
    path('app/events/<int:app_pk>/test/', views.AppTestEvent.as_view({'post': 'create'}), name='event-test'),
]
