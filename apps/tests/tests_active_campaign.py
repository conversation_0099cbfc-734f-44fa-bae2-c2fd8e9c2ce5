import json
from unittest import mock

import responses
from django.urls import reverse

from apps.models import App, Event, Platform
from apps.strategies.active_campaign import (CONTACT_LISTS_URL, CREATE_CONTACT_URL, GET_CONTACT_TAGS_URL,
                                             GET_CONTACT_URL, LIST_ALL_LISTS_URL, LIST_ALL_TAGS_URL, REMOVE_TAG_URL,
                                             TAGS_URL, ActiveCampaign)
from cakto.tests.base import BaseTestCase


class ActiveCampaignTests(BaseTestCase):
    events = [
        ('purchase_approved', 'Compra Aprovada'),
        ('refund', 'Reembolso'),
    ]
    url = 'https://cakto-testing.api-us1.com'
    account_name = 'cakto-testing.activehosted.com'
    key = 'exampletoken00000000cdcdd8192'

    @classmethod
    def setUpTestData(cls):
        res = super().setUpTestData()
        cls.user = cls.create_user()
        cls.product = cls.create_product(user=cls.user)
        cls.order = cls.create_order(product=cls.product)
        cls.platform, _ = Platform.objects.get_or_create(type='active_campaign', name='Active Campaign')
        Event.objects.bulk_create([Event(custom_id=custom_id, name=name) for custom_id, name in cls.events])
        cls.app = App.objects.create(
            name='Test Active Campaign',
            platform=cls.platform,
            user=cls.user,
            url=cls.url,
            fields={
                'token': cls.key,
                'tagId': '1',
                'listId': '1',
                'customAction': 'insert',
            }
        )
        return res

    @property
    def headers(self):
        return self.build_user_auth_headers(self.user)

    def create_app_payload(
        self,
        name=None,
        events=None,
        products=None,
        token=None,
        tagId=None,
        listId=None,
        customAction=None,
        fields=None,
        url=None
    ) -> dict:
        return {
            'name': name or 'Test Active Campaign',
            'url': url or self.url,
            'events': events or [event.custom_id for event in Event.objects.all()],
            'products': products or [self.product.id],
            'fields': fields or {
                'token': token or '1234',
                'tagId': tagId or '1',
                'listId': listId or '1',
                'customAction': customAction or 'insert',
            }
        }

    @responses.activate
    def test_create_active_campaign_app(self):
        App.objects.all().delete()
        app_url = reverse('apps', args=[self.platform.id])
        payload = self.create_app_payload(token=self.key, url=self.url)

        tag_url = LIST_ALL_TAGS_URL.format(api_url=payload['url'])
        list_url = LIST_ALL_LISTS_URL.format(api_url=payload['url'], limit=5)

        responses.add(responses.GET, tag_url, status=200)
        responses.add(responses.GET, list_url, status=200)

        response = self.client.post(app_url, data=payload, format='json', headers=self.headers)

        self.assertEqual(response.status_code, 200, response.content)
        self.assertEqual(App.objects.count(), 1)

        app = App.objects.first()
        self.assertEqual(
            sorted(app.events.values_list('custom_id', flat=True)), # type:ignore
            sorted(payload['events']),
            'events not saved on App'
        )
        self.assertEqual(app.fields.get('token'), self.key, 'token not saved on App fields')  # type:ignore
        self.assertEqual(app.fields.get('tagId'), '1', 'tagId not saved on App fields')  # type:ignore
        self.assertEqual(app.fields.get('listId'), '1', 'listId not saved on App fields')  # type:ignore
        self.assertEqual(app.fields.get('customAction'), 'insert', 'customAction not saved on App fields')  # type:ignore

    def test_initialize_variables_active_campaign(self):
        expected_app = mock.Mock()
        expected_order = mock.Mock()
        expected_kwarg = {'test': 'test'}

        active_campaign = ActiveCampaign()
        active_campaign.initialize_variables(app=expected_app, order=expected_order, kwargs=expected_kwarg)

        self.assertEqual(active_campaign.app, expected_app)
        self.assertEqual(active_campaign.order, expected_order)
        self.assertIn('test', active_campaign.kwargs['kwargs'])

    def test_construct_list_to_contact_payload(self):
        app_mock = mock.Mock(fields={'token': '1234', 'customEventName': 'pix_gerado'})

        expected_payload = {
            "contactList": {
                "list": '1',
                "contact": '1',
                "status": '1',
            }
        }

        active_campaign = ActiveCampaign()
        active_campaign.initialize_variables(app=app_mock, order=self.order)

        payload = active_campaign.construct_list_to_contact_payload('1', '1', '1')

        self.assertEqual(payload, expected_payload)

    @responses.activate
    def test_get_contact(self):
        get_contact_url = GET_CONTACT_URL.format(api_url=self.app.url, search_email=self.order.customer.email)

        return_payload = {
            'contacts': [
                {
                    'name': 'Test Customer',
                    'email': '<EMAIL>',
                    'id': '11111'
                }
            ],
            'meta': {'total': 1}
        }

        responses.add(responses.GET, get_contact_url, json=return_payload, status=200)

        active_campaign = ActiveCampaign()
        active_campaign.initialize_variables(app=self.app, order=self.order)
        active_campaign.test_event = False

        contact_data = active_campaign.get_or_create_customer_contact()
        self.assertEqual(contact_data, return_payload['contacts'][0])  # type:ignore

    @responses.activate
    def test_contact_create(self):
        active_campaign = ActiveCampaign()
        active_campaign.initialize_variables(app=self.app, order=self.order)
        active_campaign.test_event = False

        get_contact_url = GET_CONTACT_URL.format(api_url=self.app.url, search_email=self.order.customer.email)
        return_payload = {'contacts': [], 'meta': {'total': 0}}
        responses.add(responses.GET, get_contact_url, json=return_payload, status=200)

        create_url = CREATE_CONTACT_URL.format(api_url=self.app.url)
        create_response_payload = {
            'contact': {
                'name': 'Test Customer',
                'email': self.order.customer.email,
                'id': '22222',
            }
        }
        create_request = responses.add(responses.POST, create_url, json=create_response_payload, status=200)

        expected_payload = {
            'contact': {
                'firstName': self.order.customer.name.split()[0],
                'lastName': self.order.customer.name.split()[-1],
                'email': self.order.customer.email,
                'phone': self.order.customer.phone,
            }
        }

        contact_data = active_campaign.get_or_create_customer_contact()

        self.assertEqual(contact_data, create_response_payload['contact'])  # type:ignore

        self.assertEqual(json.loads(create_request.calls[0].request.body), expected_payload)  # type:ignore

    @responses.activate
    def test_add_tag_to_contact(self):
        active_campaign = ActiveCampaign()
        active_campaign.initialize_variables(app=self.app, order=self.order)
        active_campaign.test_event = False

        contact_id = '12345'
        tag_id = '54321'

        url = TAGS_URL.format(api_url=self.app.url)
        expected_payload = {
            "contactTag": {
                "contact": contact_id,
                "tag": tag_id
            }
        }

        response_payload = {'id': '555', 'name': 'NewTagTest'}
        tag_resp = responses.post(url, json=response_payload, status=200)

        tag_data = active_campaign.add_tag_to_contact(tag_id=tag_id, contact_id=contact_id)

        self.assertEqual(response_payload, tag_data)
        self.assertEqual(json.loads(tag_resp.calls[0].request.body), expected_payload)  # type:ignore

    @responses.activate
    def test_get_contact_tag_association_id(self):
        contact_id = '12345'
        tag_id = '54321'

        active_campaign = ActiveCampaign()
        active_campaign.initialize_variables(app=self.app, order=self.order)
        active_campaign.test_event = False

        contact_tags_url = GET_CONTACT_TAGS_URL.format(api_url=self.app.url, contact_id=contact_id)  # type:ignore
        resp_payload = {'contactTags': [{'tag': tag_id, 'id': tag_id}]}
        responses.get(contact_tags_url, json=resp_payload, status=200)

        received_id = active_campaign.get_contact_tag_association_id(tag_id=tag_id, contact_id=contact_id)

        self.assertEqual(received_id, tag_id)
        responses.assert_call_count(contact_tags_url, 1)

    @responses.activate
    def test_remove_tag_to_contact(self):
        active_campaign = ActiveCampaign()
        active_campaign.initialize_variables(app=self.app, order=self.order)
        active_campaign.test_event = False

        contact_id = '12345'
        tag_id = '54321'
        tag_association_id = '4444'

        url = REMOVE_TAG_URL.format(api_url=self.app.url, tag_association_id=tag_association_id)
        expected_payload = {'testing': True}
        responses.delete(url, json=expected_payload, status=200)

        mock.patch('apps.strategies.active_campaign.ActiveCampaign.get_contact_tag_association_id', return_value=tag_association_id).start()
        remove_data = active_campaign.remove_tag_to_contact(tag_id=tag_id, contact_id=contact_id)

        responses.assert_call_count(url, 1)
        self.assertEqual(remove_data, expected_payload)

    @responses.activate
    def test_modify_contact_to_list(self):
        active_campaign = ActiveCampaign()
        active_campaign.initialize_variables(app=self.app, order=self.order)
        active_campaign.test_event = False

        contact_id = '12345'
        contact_status = '1'
        list_id = '54321'

        url = CONTACT_LISTS_URL.format(api_url=self.app.url)
        expected_payload = {
            "contactList": {
                "list": list_id,
                "contact": contact_id,
                "status": contact_status,
            }
        }

        response_payload = {'testing': True}
        tag_resp = responses.post(url, json=response_payload, status=200)

        func_res = active_campaign.modify_contact_to_list(list_id=list_id, contact_status=contact_status, contact_id=contact_id)

        self.assertEqual(response_payload, func_res)
        self.assertEqual(json.loads(tag_resp.calls[0].request.body), expected_payload)  # type:ignore

    def test_handle_insert_calls_get_or_create_customer_contact(self):
        app_mock = mock.Mock(url=self.url, fields={'token': self.key})

        active_campaign = ActiveCampaign()
        active_campaign.initialize_variables(app=app_mock, order=self.order)

        mock.patch('apps.strategies.active_campaign.ActiveCampaign.add_tag_to_contact').start()
        mock.patch('apps.strategies.active_campaign.ActiveCampaign.modify_contact_to_list').start()

        with mock.patch('apps.strategies.active_campaign.ActiveCampaign.get_or_create_customer_contact') as get_or_create_customer_contact:
            active_campaign.handle_insert('1', '1')

        get_or_create_customer_contact.assert_called_once()

    def test_handle_insert_calls_add_tag_to_contact_correctly(self):
        app_mock = mock.Mock(url=self.url, fields={'token': self.key})

        active_campaign = ActiveCampaign()
        active_campaign.initialize_variables(app=app_mock, order=self.order)

        tag_id = '1'
        list_id = '1'
        contact_id = {'id': '12345'}

        mock.patch('apps.strategies.active_campaign.ActiveCampaign.get_or_create_customer_contact', return_value=contact_id).start()
        mock.patch('apps.strategies.active_campaign.ActiveCampaign.modify_contact_to_list').start()

        with mock.patch('apps.strategies.active_campaign.ActiveCampaign.add_tag_to_contact') as add_tag_to_contact_mock:
            active_campaign.handle_insert(tag_id=tag_id, list_id=list_id)

        add_tag_to_contact_mock.assert_called_once_with(int(tag_id), contact_id['id'])

    def test_handle_insert_calls_modify_contact_to_list_correctly(self):
        app_mock = mock.Mock(url=self.url, fields={'token': self.key})

        active_campaign = ActiveCampaign()
        active_campaign.initialize_variables(app=app_mock, order=self.order)

        tag_id = '1'
        list_id = '1'
        contact_id = {'id': '12345'}
        contact_status = '1'

        mock.patch('apps.strategies.active_campaign.ActiveCampaign.get_or_create_customer_contact', return_value=contact_id).start()
        mock.patch('apps.strategies.active_campaign.ActiveCampaign.add_tag_to_contact').start()

        with mock.patch('apps.strategies.active_campaign.ActiveCampaign.modify_contact_to_list') as modify_contact_to_list_mock:
            active_campaign.handle_insert(tag_id=tag_id, list_id=list_id)

        modify_contact_to_list_mock.assert_called_once_with(int(list_id), contact_status, contact_id['id'])

    def test_handle_remove_calls_get_or_create_customer_contact(self):
        app_mock = mock.Mock(url=self.url, fields={'token': self.key})

        active_campaign = ActiveCampaign()
        active_campaign.initialize_variables(app=app_mock, order=self.order)

        mock.patch('apps.strategies.active_campaign.ActiveCampaign.remove_tag_to_contact').start()
        mock.patch('apps.strategies.active_campaign.ActiveCampaign.modify_contact_to_list').start()

        with mock.patch('apps.strategies.active_campaign.ActiveCampaign.get_or_create_customer_contact') as get_or_create_customer_contact:
            active_campaign.handle_remove('1', '1')

        get_or_create_customer_contact.assert_called_once()

    def test_handle_remove_calls_remove_tag_to_contact_correctly(self):
        app_mock = mock.Mock(url=self.url, fields={'token': self.key})

        active_campaign = ActiveCampaign()
        active_campaign.initialize_variables(app=app_mock, order=self.order)

        tag_id = '1'
        list_id = '1'
        contact_id = {'id': '12345'}

        mock.patch('apps.strategies.active_campaign.ActiveCampaign.get_or_create_customer_contact', return_value=contact_id).start()
        mock.patch('apps.strategies.active_campaign.ActiveCampaign.modify_contact_to_list').start()

        with mock.patch('apps.strategies.active_campaign.ActiveCampaign.remove_tag_to_contact') as remove_tag_to_contact_mock:
            active_campaign.handle_remove(tag_id=tag_id, list_id=list_id)

        remove_tag_to_contact_mock.assert_called_once_with(int(tag_id), contact_id['id'])

    def test_handle_remove_calls_modify_contact_to_list_correctly(self):
        app_mock = mock.Mock(url=self.url, fields={'token': self.key})

        active_campaign = ActiveCampaign()
        active_campaign.initialize_variables(app=app_mock, order=self.order)

        tag_id = '1'
        list_id = '1'
        contact_id = {'id': '12345'}
        contact_status = '2'

        mock.patch('apps.strategies.active_campaign.ActiveCampaign.get_or_create_customer_contact', return_value=contact_id).start()
        mock.patch('apps.strategies.active_campaign.ActiveCampaign.remove_tag_to_contact').start()

        with mock.patch('apps.strategies.active_campaign.ActiveCampaign.modify_contact_to_list') as modify_contact_to_list_mock:
            active_campaign.handle_remove(tag_id=tag_id, list_id=list_id)

        modify_contact_to_list_mock.assert_called_once_with(int(list_id), contact_status, contact_id['id'])

    def test_cant_create_active_campaign_app_without_token_field(self):
        App.objects.all().delete()
        app_url = reverse('apps', args=[self.platform.id])
        payload = self.create_app_payload()
        del payload['fields']['token']

        response = self.client.post(app_url, data=payload, format='json', headers=self.headers)

        self.assertEqual(response.status_code, 400, response.content.decode())
        self.assertEqual(App.objects.count(), 0)

    def test_cant_create_active_campaign_app_without_listId_and_tagId_field(self):
        App.objects.all().delete()
        app_url = reverse('apps', args=[self.platform.id])
        payload = self.create_app_payload()
        del payload['fields']['listId']
        del payload['fields']['tagId']

        response = self.client.post(app_url, data=payload, format='json', headers=self.headers)

        self.assertEqual(response.status_code, 400, response.content.decode())
        self.assertEqual(App.objects.count(), 0)

    def test_cant_create_active_campaign_app_with_wrong_customAction(self):
        App.objects.all().delete()
        app_url = reverse('apps', args=[self.platform.id])
        payload = self.create_app_payload(customAction='invalidCustomAction')

        response = self.client.post(app_url, data=payload, format='json', headers=self.headers)

        self.assertEqual(response.status_code, 400, response.content.decode())
        self.assertEqual(App.objects.count(), 0)

    def test_cant_create_active_campaign_app_without_url(self):
        App.objects.all().delete()
        app_url = reverse('apps', args=[self.platform.id])
        payload = self.create_app_payload()
        del payload['url']

        response = self.client.post(app_url, data=payload, format='json', headers=self.headers)

        self.assertEqual(response.status_code, 400, response.content.decode())
        self.assertEqual(App.objects.count(), 0)
