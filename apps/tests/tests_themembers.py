import os
import re
import json
import uuid
import responses
from unittest import mock

from django.urls import reverse
from django.conf import settings
from django.test import override_settings

from apps.models import Event, EventHistory, Platform
from apps.services.app_factory import AppFactory
from apps.strategies.themembers import TheMembers
from cakto.tests.base import BaseTestCase
from customer.models import Customer


@override_settings(
    THEMEMBERS_DEV_TOKEN="dummy_dev_token",
)
class TheMembersTests(BaseTestCase):
    @classmethod
    def tearDownClass(cls):
        mock.patch.stopall()
        super().tearDownClass()

    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()

        cls.product = cls.create_product(user=cls.user)
        cls.app = cls.create_app(
            fields={
                "platform_token": "dummy_platform_token",
                "product_id": "dummy_product_id",
            },
            product=cls.product,
            platform=Platform.objects.get(type="themembers"),
            user=cls.user,
            url="https://dummy.themembers.local/api",
        )
        cls.app.events.set(Event.objects.all())

        cls.customer = cls.create_customer()
        cls.order = cls.create_order(product=cls.product, customer=cls.customer)

        Customer.objects.filter(pk=cls.customer.pk).update(themembers_uuid=None)

    def add_responses(self, error: bool = False):
        base_pattern = re.escape(self.app.url.rstrip("/"))

        if error:
            status_code = 500
            json_response = {"error": "internal error"}

            responses.add(responses.POST, re.compile(rf"{base_pattern}/users/create/.*"),
                          json=json_response, status=status_code)
            responses.add(responses.POST, re.compile(rf"{base_pattern}/users/create/user-subscription/.*"),
                          json=json_response, status=status_code)
            responses.add(responses.PUT, re.compile(rf"{base_pattern}/users/update/disable-user-subscription/.*"),
                          json=json_response, status=status_code)
            responses.add(responses.GET, re.compile(rf"{base_pattern}/users/show-reference/.*"),
                          json=json_response, status=status_code)
        else:
            self.test_uuid = str(uuid.uuid4())

            responses.add(
                responses.POST,
                re.compile(rf"{base_pattern}/users/create/.*"),
                json={"data": [{"id": self.test_uuid}]},
                status=200,
            )

            responses.add(
                responses.POST,
                re.compile(rf"{base_pattern}/users/create/user-subscription/.*"),
                json={"data": {"user_subscription": {"user_id": self.test_uuid}}},
                status=200,
            )

            responses.add(
                responses.PUT,
                re.compile(rf"{base_pattern}/users/update/disable-user-subscription/.*"),
                json={"test": "test"},
                status=200,
            )

            responses.add(
                responses.GET,
                re.compile(rf"{base_pattern}/users/show-reference/.*"),
                json={"detail": "not found"},
                status=404,
            )

    @property
    def headers(self):
        return self.create_headers(self.get_user_access_token(self.user))

    @property
    def events(self):
        return [
            "purchase_approved",
            "refund",
            "chargeback",
            "subscription_created",
            "subscription_canceled",
            "subscription_renewed",
        ]

    @responses.activate
    def test_send_purchase_approved_and_save_in_history(self):
        self.add_responses()

        kwargs = {
            "order": self.order,
            "app": self.app,
            "event_custom_id": "purchase_approved",
        }
        strategy: TheMembers = AppFactory.get_strategy(
            self.app.platform.type, init_vars=True, **kwargs
        )

        new_event = EventHistory.objects.create(
            app=self.app, event_id="purchase_approved"
        )

        strategy.purchase_approved(
            payload=None,
            app=self.app,
            meta_data={"meta": "test_data"},
            new_event_history_id=new_event.id,
        )

        event_in_history = EventHistory.objects.get(id=new_event.id)

        self.assertEqual(event_in_history.app, self.app)
        self.assertIn("data", event_in_history.response)
        self.assertIn("user_subscription", event_in_history.response["data"])
        self.assertEqual(
            event_in_history.response["data"]["user_subscription"]["user_id"],
            self.test_uuid,
        )
        self.assertEqual(event_in_history.meta_data, {"meta": "test_data"})

    @responses.activate
    def test_send_subscription_canceled_and_save_in_history(self):
        self.add_responses()

        kwargs = {
            "order": self.order,
            "app": self.app,
            "event_custom_id": "subscription_canceled",
        }
        strategy: TheMembers = AppFactory.get_strategy(
            self.app.platform.type, init_vars=True, **kwargs
        )
        new_event = EventHistory.objects.create(
            app=self.app, event_id="subscription_canceled"
        )

        strategy.subscription_canceled(
            payload=None,
            app=self.app,
            meta_data={"meta": "test_data"},
            new_event_history_id=new_event.id,
        )

        event_in_history = EventHistory.objects.get(id=new_event.id)
        self.assertEqual(event_in_history.app, self.app)
        self.assertEqual(event_in_history.response, {"test": "test"})
        self.assertEqual(event_in_history.meta_data, {"meta": "test_data"})

    @responses.activate
    def test_send_event_with_test_payload(self):
        self.add_responses()

        test_url = (
            reverse("event-test", args=[self.app.id]) + "?event=purchase_approved"
        )
        response = self.client.post(test_url, headers=self.headers)
        self.assertEqual(response.status_code, 200)

    @responses.activate
    def test_send_event_with_test_payload_and_save_in_history(self):
        self.add_responses()

        test_url = (
            reverse("event-test", args=[self.app.id]) + "?event=purchase_approved"
        )
        response = self.client.post(test_url, headers=self.headers)

        send_event_in_history = EventHistory.objects.order_by("-sentAt").first()
        self.assertEqual(response.status_code, 200)
        self.assertEqual(send_event_in_history.app, self.app)  # type: ignore
        self.assertIn("data", send_event_in_history.response)  # type: ignore
        self.assertIn(
            "user_subscription", send_event_in_history.response["data"]  # type: ignore
        )

    @responses.activate
    def test_send_subscription_created_and_save_in_history(self):
        self.add_responses()

        kwargs = {
            "order": self.order,
            "app": self.app,
            "event_custom_id": "subscription_created",
        }
        strategy: TheMembers = AppFactory.get_strategy(
            self.app.platform.type, init_vars=True, **kwargs
        )
        new_event = EventHistory.objects.create(
            app=self.app, event_id="subscription_created"
        )

        strategy.subscription_created(
            payload=None,
            app=self.app,
            meta_data={"meta": "test_data"},
            new_event_history_id=new_event.id,
        )

        event_in_history = EventHistory.objects.get(id=new_event.id)
        self.assertEqual(event_in_history.app, self.app)
        self.assertIn("data", event_in_history.response)
        self.assertIn("user_subscription", event_in_history.response["data"])

    @responses.activate
    def test_send_subscription_renewed_and_save_in_history(self):
        self.add_responses()

        kwargs = {
            "order": self.order,
            "app": self.app,
            "event_custom_id": "subscription_renewed",
        }
        strategy: TheMembers = AppFactory.get_strategy(
            self.app.platform.type, init_vars=True, **kwargs
        )
        new_event = EventHistory.objects.create(
            app=self.app, event_id="subscription_renewed"
        )

        strategy.subscription_renewed(
            payload=None,
            app=self.app,
            meta_data={"meta": "test_data"},
            new_event_history_id=new_event.id,
        )

        event_in_history = EventHistory.objects.get(id=new_event.id)
        self.assertEqual(event_in_history.app, self.app)
        self.assertIn("data", event_in_history.response)
        self.assertIn("user_subscription", event_in_history.response["data"])

    @responses.activate
    def test_send_refund_and_save_in_history(self):
        self.add_responses()

        kwargs = {
            "order": self.order,
            "app": self.app,
            "event_custom_id": "refund",
        }
        strategy: TheMembers = AppFactory.get_strategy(
            self.app.platform.type, init_vars=True, **kwargs
        )
        new_event = EventHistory.objects.create(app=self.app, event_id="refund")

        strategy.refund(
            payload=None,
            app=self.app,
            meta_data={"meta": "test_data"},
            new_event_history_id=new_event.id,
        )

        event_in_history = EventHistory.objects.get(id=new_event.id)
        self.assertEqual(event_in_history.app, self.app)
        self.assertEqual(event_in_history.response, {"test": "test"})

    @responses.activate
    def test_payload_sent_to_external_api(self):
        self.add_responses()

        kwargs = {
            "order": self.order,
            "app": self.app,
            "event_custom_id": "purchase_approved",
        }
        strategy: TheMembers = AppFactory.get_strategy(
            self.app.platform.type, init_vars=True, **kwargs
        )
        new_event = EventHistory.objects.create(
            app=self.app, event_id="purchase_approved"
        )

        strategy.purchase_approved(
            payload=None,
            app=self.app,
            meta_data={"meta": "test_data"},
            new_event_history_id=new_event.id,
        )

        call = responses.calls[0]
        body = json.loads(call.request.body)

        self.assertIn("dummy_platform_token", call.request.url)
        self.assertIn("product_id", body)
        self.assertIn("users", body)

    @responses.activate
    def test_send_purchase_approved_with_external_api_error(self):
        self.add_responses(error=True)

        kwargs = {
            "order": self.order,
            "app": self.app,
            "event_custom_id": "purchase_approved",
        }
        strategy: TheMembers = AppFactory.get_strategy(
            self.app.platform.type, init_vars=True, **kwargs
        )
        new_event = EventHistory.objects.create(
            app=self.app, event_id="purchase_approved"
        )

        strategy.purchase_approved(
            payload=None,
            app=self.app,
            meta_data={"meta": "test_data"},
            new_event_history_id=new_event.id,
        )

        event_in_history = EventHistory.objects.get(id=new_event.id)
        self.assertEqual(event_in_history.event_status, 500)
        self.assertIn("error", str(event_in_history.response))

    @responses.activate
    def test_persist_uuid_on_create(self):
        uuid_str = str(uuid.uuid4())

        base_pattern = re.escape(self.app.url.rstrip("/"))

        responses.add(
            responses.GET,
            re.compile(rf"{base_pattern}/users/show-reference/.*"),
            json={"detail": "not found"},
            status=404,
        )

        responses.add(
            responses.POST,
            re.compile(rf"{base_pattern}/users/create/.*"),
            json={"data": [{"id": uuid_str}]},
            status=200,
        )
        responses.add(
            responses.POST,
            re.compile(rf"{base_pattern}/users/create/user-subscription/.*"),
            json={"success": True},
            status=200,
        )

        new_event = EventHistory.objects.create(
            app=self.app, event_id="purchase_approved"
        )

        kwargs = {
            "order": self.order,
            "app": self.app,
            "event_custom_id": "purchase_approved",
        }

        strategy: TheMembers = AppFactory.get_strategy(
            self.app.platform.type, init_vars=True, **kwargs
        )

        strategy.purchase_approved(
            payload=None,
            app=self.app,
            meta_data={},
            new_event_history_id=new_event.id,
            test_event=False,
        )

        customer_db = Customer.objects.get(pk=self.customer.id)
        self.order.refresh_from_db()
        self.assertEqual(customer_db.themembers_uuid, uuid_str)
        self.assertEqual(self.order.customer.themembers_uuid, uuid_str)

    @responses.activate
    def test_disable_without_uuid_uses_reference_id(self):
        self.assertIsNone(self.customer.themembers_uuid)

        base_pattern = re.escape(self.app.url.rstrip("/"))
        responses.add(
            responses.PUT,
            re.compile(
                rf"{base_pattern}/users/update/disable-user-subscription/[^/]+/[^/]+$"
            ),
            json={"ok": True},
            status=200,
        )

        new_event = EventHistory.objects.create(app=self.app, event_id="refund")

        kwargs = {
            "order": self.order,
            "app": self.app,
            "event_custom_id": "refund",
        }

        strategy: TheMembers = AppFactory.get_strategy(
            self.app.platform.type, init_vars=True, **kwargs
        )

        strategy.refund(
            payload=None,
            app=self.app,
            new_event_history_id=new_event.id,
        )

        req_body = json.loads(responses.calls[-1].request.body)
        self.assertIn("reference_id", req_body)
        self.assertEqual(req_body["reference_id"], str(self.customer.id))
        self.assertNotIn("user_id", req_body)
