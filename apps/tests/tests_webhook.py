from unittest import mock

import responses
from django.urls import reverse

from apps.models import App, Event, EventHistory, Platform
from apps.services.app_factory import AppFactory
from cakto.tests.base import BaseTestCase


class WebhookTests(BaseTestCase):
    url = 'https://mytesturl.com.br'

    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()

        cls.product = cls.create_product(user=cls.user)
        cls.app = cls.create_app(url=cls.url, fields={'secret': '1234'}, product=cls.product, platform=Platform.objects.get(type='webhook'))
        cls.app.events.set(Event.objects.all())
        cls.customer = cls.create_customer()
        cls.order = cls.create_order(product=cls.product, customer=cls.customer)

    def add_responses(self):
        responses.add(
            responses.POST,
            self.url,
            json={'test': 'test'},
            status=200
        )

    @property
    def headers(self):
        return self.create_headers(self.get_user_access_token(self.user))

    def create_payload(self, name=None, url=None, events=None, products=None, secret=None):
        return {
            'name': name or 'Test Webhook',
            'url': url or self.url,
            'events': events or self.events,
            'products': products or [self.product.id],
            'fields': {'secret': secret or '1234'}
        }

    @property
    def events(self):
        return ["boleto_gerado", "pix_gerado", "purchase_approved", "purchase_refused",
                "refund", "chargeback", "subscription_canceled", "subscription_renewed",
                "checkout_abandonment"]

    def test_webhook_platform_exists(self):
        url = reverse('platforms')

        response = self.client.get(url, headers=self.headers)

        self.assertEqual(response.status_code, 200)
        webhook_exists = any(item for item in response.json()['results'] if item['type'] == 'webhook')
        self.assertTrue(webhook_exists, 'Webhook platform not found on platforms list')

    @responses.activate
    def test_create(self):
        self.add_responses()
        platform_id = Platform.objects.get(type='webhook').id
        app_url = reverse('apps', args=[platform_id])
        payload = self.create_payload()

        response = self.client.post(app_url, data=payload, format='json', headers=self.headers)

        self.assertEqual(response.status_code, 200)
        self.assertEqual(App.objects.count(), 2)

    @responses.activate
    def test_all_events_is_added(self):
        self.add_responses()
        platform_id = Platform.objects.get(type='webhook').id
        app_url = reverse('apps', args=[platform_id])
        payload = self.create_payload()

        response = self.client.post(app_url, data=payload, format='json', headers=self.headers)

        # Check if all events custom_id are in the response
        event_response_count = sum(1 if any(event == item['custom_id'] for event in self.events) else 0 for item in response.json()['events'])
        self.assertEqual(len(self.events), event_response_count, 'Not all events were added to the app')

    @responses.activate
    @mock.patch('apps.strategies.webhook.Webhook.get_payload')
    def test_send_event_and_save_in_history_webhook(self, mock_payload):
        mock_payload.return_value = {'test_payload': 'test_payload'}
        self.add_responses()

        kwargs = {'order': self.order, 'app': self.app, 'event_custom_id': 'boleto_gerado'}

        strategy = AppFactory.get_strategy(self.app.platform.type, init_vars=True, **kwargs)  # type:ignore
        new_event = EventHistory.objects.create(app=self.app, event_id='boleto_gerado')

        status_code, response = strategy.boleto_gerado(payload=None, app=self.app, meta_data={'meta': 'test_data'}, new_event_history_id=new_event.id)  # type:ignore

        event_in_history = EventHistory.objects.get(id=new_event.id)

        self.assertEqual(response, {'test': 'test'})
        self.assertEqual(status_code, 200)
        self.assertEqual(event_in_history.app, self.app)
        self.assertEqual(event_in_history.payload, {'test_payload': 'test_payload'})
        self.assertEqual(event_in_history.response, {'test': 'test'})
        self.assertEqual(event_in_history.meta_data, {'meta': 'test_data'})
        self.assertEqual(event_in_history.event_status, 200)

    @responses.activate
    @mock.patch('apps.strategies.webhook.Webhook.get_payload')
    def test_resend_event_and_save_in_history_webhook(self, mock_payload):
        mock_payload.return_value = {'test_payload': 'test_payload'}
        self.add_responses()

        kwargs = {'order': self.order, 'app': self.app, 'event_custom_id': 'boleto_gerado'}

        strategy = AppFactory.get_strategy(self.app.platform.type, init_vars=True, **kwargs)  # type:ignore
        new_event = EventHistory.objects.create(app=self.app, event_id='boleto_gerado')

        strategy.boleto_gerado(payload=None, app=self.app, meta_data={'meta': 'test_data'}, new_event_history_id=new_event.id)

        resend_url = reverse('resend-app-event', args=[new_event.id])

        response_resend = self.client.post(resend_url, headers=self.headers)

        resend_event_in_history = EventHistory.objects.order_by('-sentAt').first()

        self.assertEqual(response_resend.status_code, 200)
        self.assertEqual(resend_event_in_history.app, self.app)  # type:ignore
        self.assertEqual(resend_event_in_history.payload, {'test_payload': 'test_payload'})  # type:ignore
        self.assertEqual(resend_event_in_history.response, {'test': 'test'})  # type:ignore
        self.assertEqual(resend_event_in_history.meta_data, {'meta': 'test_data'})  # type:ignore
        self.assertEqual(resend_event_in_history.event_status, 200)  # type:ignore

    @responses.activate
    def test_send_event_with_test_payload_webhook(self):
        self.add_responses()

        resend_url = reverse('event-test', args=[self.app.id]) + '?event=boleto_gerado'

        response = self.client.post(resend_url, headers=self.headers)

        self.assertEqual(response.status_code, 200)

    @responses.activate
    def test_send_event_with_test_payload_webhook_and_save_in_history(self):
        self.add_responses()

        resend_url = reverse('event-test', args=[self.app.id]) + '?event=boleto_gerado'

        response = self.client.post(resend_url, headers=self.headers)

        send_event_in_history = EventHistory.objects.order_by('-sentAt').first()

        self.assertEqual(response.status_code, 200)
        self.assertEqual(send_event_in_history.app, self.app)  # type:ignore
        self.assertEqual(send_event_in_history.payload.get('secret'), '1234')  # type:ignore
        self.assertEqual(send_event_in_history.payload.get('event'), 'boleto_gerado')  # type:ignore
        self.assertFalse(send_event_in_history.payload.get('data') == {})  # type:ignore
        self.assertEqual(send_event_in_history.response, {'test': 'test'})  # type:ignore
        self.assertEqual(send_event_in_history.event_status, 200)  # type:ignore
