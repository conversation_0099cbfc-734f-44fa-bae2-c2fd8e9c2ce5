from decimal import Decimal
from unittest import mock

import responses
from django.urls import reverse
from django.utils import timezone

from apps.models import App, Event, EventHistory, Platform
from apps.services.app_factory import AppFactory
from apps.strategies.astron_members import AstronMembers
from cakto.tests.base import BaseTestCase


class AstronMembersTests(BaseTestCase):
    events = ["purchase_approved", "refund", "chargeback", "subscription_canceled", "subscription_renewed"]
    url = 'https://webhook.astronmembers.com.br/cakto-webhook/SLlHreyP3sI1fUJ'

    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()

        cls.product = cls.create_product(user=cls.user)
        cls.app = cls.create_app(url=cls.url, fields={'api_key': '1234'}, product=cls.product, platform=Platform.objects.get(type='astron_members'))
        cls.app.events.set(Event.objects.all())
        cls.order = cls.create_order(product=cls.product)
        cls.platform = Platform.objects.get(type='astron_members')

    def add_responses(self):
        responses.add(
            responses.POST,
            'https://webhook.astronmembers.com.br/cakto-webhook/SLlHreyP3sI1fUJ',
            json={'test': 'test'},
            status=200
        )

    @property
    def headers(self):
        return self.create_headers(self.get_user_access_token(self.user))

    def create_payload(self, name=None, events=None, products=None, api_key=None, fields=None, url=None):
        url = url or self.url
        return {
            'name': name or 'Test astron_members',
            'url': url,
            'events': events or self.events,
            'products': products or [self.product.id],
            'fields': fields or {'api_key': api_key or '1234'}
        }

    def test_astron_members_platform_exists(self):
        url = reverse('platforms')

        response = self.client.get(url, headers=self.headers)

        self.assertEqual(response.status_code, 200)
        astron_members_exists = any(item for item in response.json()['results'] if item['type'] == 'astron_members')
        self.assertTrue(astron_members_exists, 'astron_members platform not found on platforms list')

    @responses.activate
    def test_create_astron_members(self):
        integration_url = self.url
        responses.post(integration_url, json={}, status=200)

        platform_id = self.platform.id
        app_url = reverse('apps', args=[platform_id])
        payload = self.create_payload(api_key='1234', url=integration_url)
        response = self.client.post(app_url, data=payload, format='json', headers=self.headers)

        self.assertEqual(response.status_code, 200, response.content)

        self.assertEqual(App.objects.count(), 2)
        self.assertEqual(App.objects.first().fields.get('api_key'), '1234', 'api_key not saved on App fields')  # type:ignore

    def test_initialize_variables(self):
        expected_app = mock.Mock()
        expected_order = mock.Mock()

        astron_members = AstronMembers()
        astron_members.initialize_variables(app=expected_app, order=expected_order)

        self.assertEqual(astron_members.app, expected_app)
        self.assertEqual(astron_members.order, expected_order)

    def test_get_payload(self):
        app_mock = mock.Mock(fields={'api_key': '1234'})
        order_mock = mock.Mock(
            product=self.product,
            refId='1234',
            createdAt=timezone.now(),
            paidAt=timezone.now(),
            status='paid',
            amount=Decimal('9.99'),
            paymentMethodType='credit_card',
        )
        customer_mock = mock.Mock(name='Test Customer', email='<EMAIL>', phone='123456789', docNumber='123456789', docType='CPF')
        offer_mock = mock.Mock(product=self.product, name='Test Offer', price=Decimal('9.99'), id=12)
        order_mock.customer = customer_mock
        order_mock.offer = offer_mock

        expected_payload = {
            'api_key': app_mock.fields.get('api_key'),  # type:ignore
            'product_id': order_mock.product.pk,
            'product_name': order_mock.product.name,
            'trans_id': order_mock.refId,
            'trans_created_time': order_mock.createdAt.timestamp(),
            'trans_payment_time': order_mock.paidAt.timestamp(),
            'trans_status': 'paid',
            'trans_price': float(order_mock.amount),
            'trans_pay_mode': order_mock.paymentMethodType,
            'customer_name': customer_mock.name,
            'customer_email': customer_mock.email,
            'customer_phone': customer_mock.phone,
            'customer_doc': customer_mock.docNumber,
            'customer_doc_type': customer_mock.docType,
            'offer_id': offer_mock.id,
            'offer_name': offer_mock.name,
            'offer_price': float(offer_mock.price),
        }

        astron_members = AstronMembers()
        astron_members.initialize_variables(app=app_mock, order=order_mock)

        payload = astron_members.get_payload()

        self.assertEqual(payload, expected_payload)

    def test_cant_create_astron_members_without_api_key(self):
        platform_id = self.platform.id
        app_url = reverse('apps', args=[platform_id])
        payload = self.create_payload(fields={'api_key': None})

        response = self.client.post(app_url, data=payload, format='json', headers=self.headers)

        self.assertEqual(response.status_code, 400)
        self.assertEqual(App.objects.count(), 1)

    def test_cant_create_astron_members_with_wrong_url_format(self):
        platform_id = self.platform.id
        app_url = reverse('apps', args=[platform_id])
        payload = self.create_payload(url='https://www.wrong_url.com')

        response = self.client.post(app_url, data=payload, format='json', headers=self.headers)

        self.assertEqual(response.status_code, 400)
        self.assertEqual(App.objects.count(), 1)

    def test_cant_create_astron_members_with_more_than_1_product(self):
        platform_id = self.platform.id
        app_url = reverse('apps', args=[platform_id])
        product_2 = self.create_product(user=self.user)
        payload = self.create_payload(products=[self.product.id, product_2.pk])

        response = self.client.post(app_url, data=payload, format='json', headers=self.headers)

        self.assertEqual(response.status_code, 400)
        self.assertEqual(App.objects.count(), 1)

    @responses.activate
    def test_cant_create_astron_members_without_response_200_on_check_integration(self):
        integration_url = self.url
        responses.post(integration_url, json={}, status=400)

        platform_id = self.platform.id
        app_url = reverse('apps', args=[platform_id])
        payload = self.create_payload(url=integration_url)
        response = self.client.post(app_url, data=payload, format='json', headers=self.headers)

        self.assertEqual(response.status_code, 400)
        self.assertEqual(App.objects.count(), 1)

    @responses.activate
    @mock.patch('apps.strategies.astron_members.AstronMembers.get_payload')
    def test_send_event_and_save_in_history_astrom_members(self, mock_payload):
        mock_payload.return_value = {'test_payload': 'test_payload'}
        self.add_responses()

        kwargs = {'order': self.order, 'app': self.app, 'event_custom_id': 'purchase_approved'}

        strategy = AppFactory.get_strategy(self.app.platform.type, init_vars=True, **kwargs)  # type:ignore
        new_event = EventHistory.objects.create(app=self.app, event_id='purchase_approved')

        status_code, response = strategy.purchase_approved(payload=None, app=self.app, meta_data={'meta': 'test_data'}, new_event_history_id=new_event.id)  # type:ignore

        event_in_history = EventHistory.objects.get(id=new_event.id)

        self.assertEqual(response, {'test': 'test'})
        self.assertEqual(status_code, 200)
        self.assertEqual(event_in_history.app, self.app)
        self.assertEqual(event_in_history.payload, {'test_payload': 'test_payload'})
        self.assertEqual(event_in_history.response, {'test': 'test'})
        self.assertEqual(event_in_history.meta_data, {'meta': 'test_data'})
        self.assertEqual(event_in_history.event_status, 200)

    @responses.activate
    @mock.patch('apps.strategies.astron_members.AstronMembers.get_payload')
    def test_resend_event_and_save_in_history_astrom_members(self, mock_payload):
        mock_payload.return_value = {'test_payload': 'test_payload'}
        self.add_responses()

        kwargs = {'order': self.order, 'app': self.app, 'event_custom_id': 'purchase_approved'}

        strategy = AppFactory.get_strategy(self.app.platform.type, init_vars=True, **kwargs)  # type:ignore
        new_event = EventHistory.objects.create(app=self.app, event_id='purchase_approved')

        strategy.purchase_approved(payload=None, app=self.app, meta_data={'meta': 'test_data'}, new_event_history_id=new_event.id)

        resend_url = reverse('resend-app-event', args=[new_event.id])

        response_resend = self.client.post(resend_url, headers=self.headers)

        resend_event_in_history = EventHistory.objects.order_by('-sentAt').first()

        self.assertEqual(response_resend.status_code, 200)
        self.assertEqual(resend_event_in_history.app, self.app)  # type:ignore
        self.assertEqual(resend_event_in_history.payload, {'test_payload': 'test_payload'})  # type:ignore
        self.assertEqual(resend_event_in_history.response, {'test': 'test'})  # type:ignore
        self.assertEqual(resend_event_in_history.meta_data, {'meta': 'test_data'})  # type:ignore
        self.assertEqual(resend_event_in_history.event_status, 200)  # type:ignore

    @responses.activate
    def test_send_event_with_test_payload(self):
        self.add_responses()

        resend_url = reverse('event-test', args=[self.app.id, ]) + '?event=purchase_approved'

        response = self.client.post(resend_url, headers=self.headers)

        self.assertEqual(response.status_code, 200)

    @responses.activate
    def test_send_event_with_test_payload_and_save_in_history(self):
        self.add_responses()

        resend_url = reverse('event-test', args=[self.app.id]) + '?event=purchase_approved'

        response = self.client.post(resend_url, headers=self.headers)

        send_event_in_history = EventHistory.objects.order_by('-sentAt').first()

        expected_payload = AstronMembers.get_test_payload()

        self.assertEqual(response.status_code, 200)
        self.assertEqual(send_event_in_history.app, self.app)  # type:ignore
        self.assertEqual(send_event_in_history.payload, expected_payload)  # type:ignore
        self.assertEqual(send_event_in_history.response, {'test': 'test'})  # type:ignore
        self.assertEqual(send_event_in_history.event_status, 200)  # type:ignore
