from unittest import mock

from apps.models import Event, EventHistory, Platform
from apps.services.event_manager import dispatch_app_events
from apps.strategies.webhook import Webhook
from cakto.tests.base import BaseTestCase


class AppTests(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.product = cls.create_product(user=cls.user)
        cls.app = cls.create_app(product=cls.product, platform=Platform.objects.get(type='webhook'))
        cls.app.events.set(Event.objects.all())
        cls.customer = cls.create_customer()
        cls.order = cls.create_order(product=cls.product, customer=cls.customer)

    @property
    def events(self):
        return [
            "boleto_gerado", "pix_gerado", "purchase_approved", "purchase_refused", "refund", "chargeback",
            "subscription_canceled", "subscription_renewed", "checkout_abandonment", "picpay_gerado",
            "openfinance_nubank_gerado"
        ]

    def test_save_data_in_model_events_history(self):
        app = self.app
        payload = {'test': 'test'}
        response = {'response_test': 'response_test'}
        meta_data = {'meta_test': 'meta_test'}
        event_id = 'boleto_gerado'
        job = '12345'
        event_status = 200

        EventHistory.objects.create(app=app, payload=payload, response=response,
                                    meta_data=meta_data, event_id=event_id, job=job, event_status=event_status)
        event = EventHistory.objects.first()

        expected_data = {
            'app': app,
            'payload': payload,
            'response': response,
            'meta_data': meta_data,
            'event_id': event_id,
            'job': job,
            'event_status': event_status
        }

        for key, value in expected_data.items():
            self.assertEqual(getattr(event, key), value)

    def test_create_history_in_process_event(self):

        with mock.patch('apps.services.event_manager.enqueue') as enqueue_mock:
            mock_job = mock.Mock()
            mock_job.id = 'mocked_id'
            enqueue_mock.return_value = mock_job

            dispatch_app_events(event_custom_id='boleto_gerado', order=self.order, payment=mock.Mock())

        event = EventHistory.objects.first()

        expected_data = {
            'event_id': 'boleto_gerado',
            'app': self.app,
            'job': 'mocked_id'

        }

        for key, value in expected_data.items():
            self.assertEqual(getattr(event, key), value)

    def test_processing_metadata(self):
        meta_data = {
            'order': self.order,
            'event_custom_id': 'boleto_gerado',
            'app': self.app,
            'product': self.product,
            'customer': self.customer
        }

        meta_data_processed = Webhook().processing_meta_data(**meta_data)

        for key, value in meta_data.items():
            self.assertEqual(str(value), meta_data_processed[key])

    @mock.patch('apps.strategies.base.get_current_job')
    def test_save_event_in_history_with_job(self, job_mock):
        payload = {'test': 'data'}
        meta = {'key': 'value'}
        error = None

        request_data_mock = mock.Mock()
        request_data_mock.json.return_value = {'response_key': 'response_value'}
        request_data_mock.request.url = 'http://test.com'
        request_data_mock.status_code = 200

        job_mock.return_value = mock.Mock(id='test_id')
        EventHistory.objects.create(app=self.app, event_id='boleto_gerado', job='test_id')

        webhook = Webhook()
        webhook.save_event_in_history(
            payload=payload,
            new_event_history_id='',
            meta=meta,
            error=error,
            request_data=request_data_mock
        )

        event_in_history = EventHistory.objects.get(job='test_id')

        self.assertEqual(event_in_history.app, self.app)
        self.assertEqual(event_in_history.event_id, 'boleto_gerado')
        self.assertEqual(event_in_history.event_status, request_data_mock.status_code)
        self.assertEqual(event_in_history.response, request_data_mock.json.return_value)
        self.assertEqual(event_in_history.payload, payload)
        self.assertEqual(event_in_history.meta_data, meta)
        self.assertEqual(event_in_history.errors, error)

    def test_save_event_in_history_with_new_event_history_id(self):
        payload = {'test': 'data'}
        meta = {'key': 'value'}
        error = None

        request_data_mock = mock.Mock()
        request_data_mock.json.return_value = {'response_key': 'response_value'}
        request_data_mock.request.url = 'http://test.com'
        request_data_mock.status_code = 200

        event_id = EventHistory.objects.create(app=self.app, event_id='boleto_gerado', job='test_id')

        webhook = Webhook()
        webhook.save_event_in_history(
            payload=payload,
            new_event_history_id=event_id.id,
            meta=meta,
            error=error,
            request_data=request_data_mock
        )

        event_in_history = EventHistory.objects.get(job='test_id')

        self.assertEqual(event_in_history.app, self.app)
        self.assertEqual(event_in_history.event_id, 'boleto_gerado')
        self.assertEqual(event_in_history.event_status, request_data_mock.status_code)
        self.assertEqual(event_in_history.url, request_data_mock.request.url)
        self.assertEqual(event_in_history.response, request_data_mock.json.return_value)
        self.assertEqual(event_in_history.payload, payload)
        self.assertEqual(event_in_history.meta_data, meta)
        self.assertEqual(event_in_history.errors, error)
