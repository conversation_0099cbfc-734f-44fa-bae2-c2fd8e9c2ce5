from unittest import mock

import responses
from django.urls import reverse

from apps.models import Event, EventHistory, Platform
from apps.services.app_factory import AppFactory
from apps.strategies.cademi import <PERSON><PERSON>
from cakto.tests.base import BaseTestCase


class CademiTests(BaseTestCase):
    def setUp(self):
        self.user = self.create_user()
        self.product = self.create_product(user=self.user)
        self.app = self.create_app(fields={'domain': 'test1234', 'api_key': '1234', 'token': '1234'}, product=self.product, platform=Platform.objects.get(type='cademi'))
        self.app.events.set(Event.objects.all())
        self.customer = self.create_customer()
        self.order = self.create_order(product=self.product, customer=self.customer)
        return super().setUp()

    def add_responses(self):
        responses.add(
            responses.POST,
            'https://test1234.cademi.com.br/api/postback/custom',
            json={'test': 'test'},
            status=200
        )

    def get_expected_test_payload(self):
        return Cademi.get_test_payload(self.app)

    @property
    def headers(self):
        return self.create_headers(self.get_user_access_token(self.user))

    @property
    def events(self):
        return [
            "boleto_gerado", "pix_gerado", "purchase_approved", "purchase_refused", "refund", "chargeback",
            "subscription_canceled", "subscription_renewed", "checkout_abandonment",
        ]

    @responses.activate
    @mock.patch('apps.strategies.cademi.Cademi.get_payload')
    def test_send_event_and_save_in_history_cademi(self, mock_payload):
        mock_payload.return_value = {'test_payload': 'test_payload'}
        self.add_responses()

        kwargs = {'order': self.order, 'app': self.app, 'event_custom_id': 'purchase_approved'}

        strategy = AppFactory.get_strategy(self.app.platform.type, init_vars=True, **kwargs)  # type:ignore
        new_event = EventHistory.objects.create(app=self.app, event_id='purchase_approved')

        status_code, response = strategy.purchase_approved(payload=None, app=self.app, meta_data={'meta': 'test_data'}, new_event_history_id=new_event.id)  # type:ignore

        event_in_history = EventHistory.objects.get(id=new_event.id)

        self.assertEqual(response, {'test': 'test'})
        self.assertEqual(status_code, 200)
        self.assertEqual(event_in_history.app, self.app)
        self.assertEqual(event_in_history.payload, {'test_payload': 'test_payload'})
        self.assertEqual(event_in_history.response, {'test': 'test'})
        self.assertEqual(event_in_history.meta_data, {'meta': 'test_data'})
        self.assertEqual(event_in_history.event_status, 200)

    @responses.activate
    @mock.patch('apps.strategies.cademi.Cademi.get_payload')
    def test_resend_event_and_save_in_history_cademi(self, mock_payload):
        mock_payload.return_value = {'test_payload': 'test_payload'}
        self.add_responses()

        kwargs = {'order': self.order, 'app': self.app, 'event_custom_id': 'purchase_approved'}

        strategy = AppFactory.get_strategy(self.app.platform.type, init_vars=True, **kwargs)  # type:ignore
        new_event = EventHistory.objects.create(app=self.app, event_id='purchase_approved')

        strategy.purchase_approved(payload=None, app=self.app, meta_data={'meta': 'test_data'}, new_event_history_id=new_event.id)

        resend_url = reverse('resend-app-event', args=[new_event.id])

        response_resend = self.client.post(resend_url, headers=self.headers)

        resend_event_in_history = EventHistory.objects.order_by('-sentAt').first()

        self.assertEqual(response_resend.status_code, 200)
        self.assertEqual(resend_event_in_history.app, self.app)  # type:ignore
        self.assertEqual(resend_event_in_history.payload, {'test_payload': 'test_payload'})  # type:ignore
        self.assertEqual(resend_event_in_history.response, {'test': 'test'})  # type:ignore
        self.assertEqual(resend_event_in_history.meta_data, {'meta': 'test_data'})  # type:ignore
        self.assertEqual(resend_event_in_history.event_status, 200)  # type:ignore

    @responses.activate
    def test_send_event_with_test_payload(self):
        self.add_responses()

        resend_url = reverse('event-test', args=[self.app.id]) + '?event=purchase_approved'

        response = self.client.post(resend_url, headers=self.headers)

        self.assertEqual(response.status_code, 200)

    @responses.activate
    def test_send_event_with_test_payload_and_save_in_history(self):
        self.add_responses()

        resend_url = reverse('event-test', args=[self.app.id]) + '?event=purchase_approved'

        response = self.client.post(resend_url, headers=self.headers)

        send_event_in_history = EventHistory.objects.order_by('-sentAt').first()

        self.assertEqual(response.status_code, 200)
        self.assertEqual(send_event_in_history.app, self.app)  # type:ignore
        self.assertEqual(send_event_in_history.payload, self.get_expected_test_payload())  # type:ignore
        self.assertEqual(send_event_in_history.response, {'test': 'test'})  # type:ignore
        self.assertEqual(send_event_in_history.event_status, 200)  # type:ignore
