import responses
from django.urls import reverse

from apps.models import Platform
from cakto.tests.base import BaseTestCase


class AppTests(BaseTestCase):
    url_1 = 'https://myurltest1.com.br'
    url_2 = 'https://myurltest2.com.br'

    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()

        cls.product = cls.create_product(user=cls.user)

    def add_responses(self):
        responses.add(
            responses.POST,
            self.url_1,
            json={'test': 'test'},
            status=200
        )
        responses.add(
            responses.POST,
            self.url_2,
            json={'test': 'test'},
            status=200
        )

    @property
    def headers(self):
        return self.build_user_auth_headers(self.user)

    def create_payload(self, name=None, url=None, events=None, products=None, secret=None):
        return {
            'name': name or 'Test Webhook',
            'url': url or self.url_1,
            'events': events or self.events,
            'products': products or [self.product.id],
            'fields': {'secret': secret or '1234'}
        }

    @property
    def events(self):
        return [
            "boleto_gerado", "pix_gerado", "purchase_approved", "purchase_refused",
            "refund", "chargeback", "subscription_canceled", "subscription_renewed",
            "checkout_abandonment"
        ]

    @responses.activate
    def test_all_events_is_added(self):
        self.add_responses()
        platform_id = Platform.objects.get(type='webhook').id
        app_url = reverse('apps', args=[platform_id])
        payload = self.create_payload()

        response = self.client.post(app_url, data=payload, format='json', headers=self.headers)

        # Check if all events custom_id are in the response
        event_response_count = sum(1 if any(event == item['custom_id'] for event in self.events) else 0 for item in response.json()['events'])
        self.assertEqual(len(self.events), event_response_count, 'Not all events were added to the app')

    @responses.activate
    def test_name_is_correct(self):
        self.add_responses()
        platform_id = Platform.objects.get(type='webhook').id
        app_url = reverse('apps', args=[platform_id])

        expected_name = 'My App Test Name'
        payload = self.create_payload(name=expected_name)

        response = self.client.post(app_url, data=payload, format='json', headers=self.headers)

        self.assertEqual(response.json()['name'], expected_name, 'Name is not the same as the one sent')

    @responses.activate
    def test_url_is_correct(self):
        self.add_responses()

        platform_id = Platform.objects.get(type='webhook').id
        app_url = reverse('apps', args=[platform_id])
        payload = self.create_payload(url=self.url_2)

        response = self.client.post(app_url, data=payload, format='json', headers=self.headers)

        self.assertEqual(response.json()['url'], self.url_2, 'URL is not the same as the one sent')

    @responses.activate
    def test_products_is_correct(self):
        self.add_responses()
        platform_id = Platform.objects.get(type='webhook').id
        app_url = reverse('apps', args=[platform_id])

        expected_products = [self.product.id, self.create_product(user=self.user).id]
        payload = self.create_payload(products=expected_products)

        response = self.client.post(app_url, data=payload, format='json', headers=self.headers)

        response_products_ids = [item['id'] for item in response.json()['products']]
        self.assertEqual(sorted(expected_products), sorted(response_products_ids), 'Products are not the same as the one sent')
