import logging
from unittest import mock

import responses
from django.urls import reverse
from rest_framework import status

from apps.models import App, Event, EventHistory, Platform
from apps.services.app_factory import AppFactory
from apps.strategies.smsfunnel import SmsFunnel
from cakto.tests.base import BaseTestCase


class SmsFunnelTests(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.create_paymentMethods()

        cls.platform, _ = Platform.objects.get_or_create(
            type='smsfunnel',
            defaults={'name': 'SMS Funnel', 'type': 'smsfunnel'}
        )

        cls.product = cls.create_product(user=cls.user)

        cls.app = cls.create_app(url='https://www.google.com/', product=cls.product, platform=cls.platform)
        cls.app.events.set(Event.objects.all())

        cls.customer = cls.create_customer()

        cls.order = cls.create_order(product=cls.product, customer=cls.customer)

    def add_responses(self):
        responses.add(
            responses.POST,
            'https://www.google.com/',
            json={'test': 'test'},
            status=200
        )

    def get_expected_test_payload(self):
        return SmsFunnel.get_test_payload()

    def create_app_payload(self, url):
        return {
            'name': 'Test SMS Funnel',
            'url': url,
            'events': [],
            'products': [self.product.id],
        }

    @property
    def headers(self):
        return self.create_headers(self.get_user_access_token(self.user))

    @property
    def events(self):
        return [
            "boleto_gerado", "pix_gerado", "purchase_approved", "purchase_refused", "refund", "chargeback",
            "subscription_canceled", "subscription_renewed", "checkout_abandonment",
        ]

    @responses.activate
    @mock.patch('apps.strategies.smsfunnel.SmsFunnel.get_payload')
    def test_send_event_and_save_in_history_smsfunnel(self, mock_payload):
        mock_payload.return_value = {'test_payload': 'test_payload'}
        self.add_responses()

        kwargs = {'order': self.order, 'app': self.app, 'event_custom_id': 'boleto_gerado'}

        strategy = AppFactory.get_strategy(self.app.platform.type, init_vars=True, **kwargs)  # type:ignore
        new_event = EventHistory.objects.create(app=self.app, event_id='boleto_gerado')

        status_code, response = strategy.boleto_gerado(payload=None, app=self.app, meta_data={'meta': 'test_data'}, new_event_history_id=new_event.id)  # type:ignore

        event_in_history = EventHistory.objects.get(id=new_event.id)

        self.assertEqual(response, {'test': 'test'})
        self.assertEqual(status_code, 200)
        self.assertEqual(event_in_history.app, self.app)
        self.assertEqual(event_in_history.payload, {'test_payload': 'test_payload'})
        self.assertEqual(event_in_history.response, {'test': 'test'})
        self.assertEqual(event_in_history.meta_data, {'meta': 'test_data'})
        self.assertEqual(event_in_history.event_status, 200)

    @responses.activate
    @mock.patch('apps.strategies.smsfunnel.SmsFunnel.get_payload')
    def test_resend_event_and_save_in_history_smsfunnel(self, mock_payload):
        mock_payload.return_value = {'test_payload': 'test_payload'}
        self.add_responses()

        kwargs = {'order': self.order, 'app': self.app, 'event_custom_id': 'boleto_gerado'}

        strategy = AppFactory.get_strategy(self.app.platform.type, init_vars=True, **kwargs)  # type:ignore
        new_event = EventHistory.objects.create(app=self.app, event_id='boleto_gerado')

        strategy.boleto_gerado(payload=None, app=self.app, meta_data={'meta': 'test_data'}, new_event_history_id=new_event.id)

        resend_url = reverse('resend-app-event', args=[new_event.id])

        response_resend = self.client.post(resend_url, headers=self.headers)

        resend_event_in_history = EventHistory.objects.order_by('-sentAt').first()

        self.assertEqual(response_resend.status_code, 200)
        self.assertEqual(resend_event_in_history.app, self.app)  # type:ignore
        self.assertEqual(resend_event_in_history.payload, {'test_payload': 'test_payload'})  # type:ignore
        self.assertEqual(resend_event_in_history.response, {'test': 'test'})  # type:ignore
        self.assertEqual(resend_event_in_history.meta_data, {'meta': 'test_data'})  # type:ignore
        self.assertEqual(resend_event_in_history.event_status, 200)  # type:ignore

    @responses.activate
    def test_send_event_with_test_payload_webhook(self):
        self.add_responses()

        resend_url = reverse('event-test', args=[self.app.id]) + '?event=boleto_gerado'

        response = self.client.post(resend_url, headers=self.headers)

        self.assertEqual(response.status_code, 200)

    @responses.activate
    def test_send_event_with_test_payload_and_save_in_history(self):
        self.add_responses()

        resend_url = reverse('event-test', args=[self.app.id]) + '?event=boleto_gerado'

        response = self.client.post(resend_url, headers=self.headers)

        send_event_in_history = EventHistory.objects.order_by('-sentAt').first()

        self.assertEqual(response.status_code, 200)
        self.assertEqual(send_event_in_history.app, self.app)  # type:ignore
        self.assertEqual(send_event_in_history.payload, self.get_expected_test_payload())  # type:ignore
        self.assertEqual(send_event_in_history.response, {'test': 'test'})  # type:ignore
        self.assertEqual(send_event_in_history.event_status, 200)  # type:ignore    @responses.activate

    def test_url_validation_v1_format(self):
        """Test that the v1 URL format is accepted."""
        url = "https://v1.smsfunnel.com.br/integrations/6a65ab60-9186-49cd-9a34-18cf7a0f44f1"
        # Should not raise an exception
        try:
            SmsFunnel.check_url({"url": url}, None)
            validation_passed = True
        except Exception:
            validation_passed = False
        self.assertTrue(validation_passed, "v1 URL format should be accepted")

    def test_url_validation_v2_format(self):
        """Test that the v2 URL format is accepted."""
        url = "https://v2.smsfunnel.com.br/integrations/6a65ab60-9186-49cd-9a34-18cf7a0f44f1"
        # Should not raise an exception
        try:
            SmsFunnel.check_url({"url": url}, None)
            validation_passed = True
        except Exception:
            validation_passed = False
        self.assertTrue(validation_passed, "v2 URL format should be accepted")

    def test_create_app_with_v1_url_format(self):
        """Test creating an app with a valid v1 URL format."""
        App.objects.all().delete()
        app_url = reverse('apps', args=[self.platform.id])

        # Valid v1 URL format
        valid_url = 'https://v1.smsfunnel.com.br/integrations/6a65ab60-9186-49cd-9a34-18cf7a0f44f1'

        payload = self.create_app_payload(url=valid_url)

        # Mock the check_integration method to avoid actual HTTP calls
        mock.patch(
            'apps.strategies.smsfunnel.SmsFunnel.check_integration'
        ).start()

        response = self.client.post(app_url, data=payload, format='json', headers=self.headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content)
        self.assertEqual(App.objects.count(), 1)

        app = App.objects.first()
        self.assertEqual(app.url, valid_url)  # type:ignore

    def test_create_app_with_v2_url_format(self):
        """Test creating an app with a valid v2 URL format."""
        App.objects.all().delete()
        app_url = reverse('apps', args=[self.platform.id])

        # Valid v2 URL format
        valid_url = 'https://v2.smsfunnel.com.br/integrations/6a65ab60-9186-49cd-9a34-18cf7a0f44f1'

        payload = self.create_app_payload(url=valid_url)

        # Mock the check_integration method to avoid actual HTTP calls
        mock.patch(
            'apps.strategies.smsfunnel.SmsFunnel.check_integration'
        ).start()

        response = self.client.post(app_url, data=payload, format='json', headers=self.headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content)
        self.assertEqual(App.objects.count(), 1)

        app = App.objects.first()
        self.assertEqual(app.url, valid_url)  # type:ignore

    def test_create_app_with_invalid_url_format(self):
        """Test creating an app with an invalid URL format."""
        App.objects.all().delete()
        app_url = reverse('apps', args=[self.platform.id])

        # Suppress api exception logging
        self.logger = logging.getLogger('django.request')
        self.logger.setLevel(logging.CRITICAL)

        # Invalid URL formats to test
        invalid_urls = [
            'https://v3.smsfunnel.com.br/integrations/6a65ab60-9186-49cd-9a34-18cf7a0f44f1',  # v3 not supported
            'https://smsfunnel.com.br/integrations/6a65ab60-9186-49cd-9a34-18cf7a0f44f1',     # missing v1/v2
            'https://v1.smsfunnel.com.br/integrations/invalid-uuid-format',                   # invalid UUID
            'http://v1.smsfunnel.com.br/integrations/6a65ab60-9186-49cd-9a34-18cf7a0f44f1'    # http instead of https
        ]

        mock.patch(
            'apps.strategies.smsfunnel.SmsFunnel.check_integration'
        ).start()

        for invalid_url in invalid_urls:
            with self.subTest(invalid_url=invalid_url):
                payload = self.create_app_payload(url=invalid_url)

                response = self.client.post(app_url, data=payload, format='json', headers=self.headers)

                self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST, response.content)
                self.assertEqual(App.objects.count(), 0)

    def test_url_pattern_validation_directly(self):
        """Test the URL pattern validation directly using the check_url method."""
        # Valid URLs
        valid_urls = [
            'https://v1.smsfunnel.com.br/integrations/6a65ab60-9186-49cd-9a34-18cf7a0f44f1',
            'https://v2.smsfunnel.com.br/integrations/0af730f1-3d5b-4f75-af57-b0176512a088'
        ]

        # Invalid URLs
        invalid_urls = [
            'https://v3.smsfunnel.com.br/integrations/6a65ab60-9186-49cd-9a34-18cf7a0f44f1',
            'https://smsfunnel.com.br/integrations/6a65ab60-9186-49cd-9a34-18cf7a0f44f1',
            'https://v1.smsfunnel.com.br/integrations/invalid-uuid',
            'http://v1.smsfunnel.com.br/integrations/6a65ab60-9186-49cd-9a34-18cf7a0f44f1'
        ]

        # Test valid URLs
        for url in valid_urls:
            with self.subTest(url=url):
                # Should not raise an exception
                try:
                    SmsFunnel.check_url({'url': url}, None)
                    passed = True
                except Exception:
                    passed = False
                self.assertTrue(passed, f"URL should be valid: {url}")

        # Test invalid URLs
        for url in invalid_urls:
            with self.subTest(url=url):
                # Should raise a ValidationError
                with self.assertRaises(Exception):
                    SmsFunnel.check_url({'url': url}, None)
