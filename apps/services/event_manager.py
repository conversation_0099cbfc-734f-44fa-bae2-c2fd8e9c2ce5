import pprint
from typing import Any, Dict, Literal, Type, Unpack, overload

from django_rq import enqueue, job
from rq import Retry

from apps.models import App, Event, EventHistory
from apps.services.app_factory import AppFactory
from apps.services.types import (CheckoutAbandonmentEventKwargs, DispatchPixelInitCheckoutKwargs,
                                 DispatchPixelPaymentEventKwargs, EventTypes, InitiateCheckoutEventKwargs,
                                 PaymentEventKwargs, PaymentEventTypes)
from product.models import Product
from product.pixel_strategies.base import PixelEvents
from product.pixel_strategies.facebook import FacebookPixelEvents
from product.pixel_strategies.tiktok import TikTokPixelEvents

PIXELS_STRATEGIES: dict[str, Type[PixelEvents]] = {
    'facebook_pixels': FacebookPixelEvents,
    'tiktok_pixels': TikTokPixelEvents
}

@overload
def dispatch_event(event: EventTypes, **kwargs: Unpack[InitiateCheckoutEventKwargs]): ...

@overload
def dispatch_event(event: EventTypes, **kwargs: Unpack[PaymentEventKwargs]): ...

def dispatch_event(
    event: EventTypes,
    **kwargs,
):
    dispatch_app_events.delay(event, **kwargs)
    dispatch_pixel_events.delay(event, **kwargs)


def get_product_from_kwargs(kwargs: Dict[str, Any]):
    order = kwargs.get('order')
    offer = kwargs.get('offer')

    if order is not None:
        return order.product
    elif offer is not None:
        return offer.product

    return None

def get_event(event_custom_id: str):
    return Event.objects.filter(custom_id=event_custom_id, active=True).first()

def get_apps(event: Any, product: Any):
    return App.objects.filter(
        events=event,
        platform__active=True,
        user=product.user,
        products=product,
    ).select_related('platform')

def handle_kwargs(event_custom_id: str, app: App, product: Product, **kwargs):
    kwargs['event_custom_id'] = event_custom_id
    kwargs['app_platform'] = app.platform.type
    kwargs['app'] = app
    kwargs['app_id'] = app.id
    kwargs['product'] = product
    kwargs['product_id'] = product.id

    kwargs['offer'] = kwargs.get('offer', '')

    order = kwargs.get('order', '')
    kwargs['order'] = order

    kwargs['customer'] = order.customer if order else None
    kwargs['customer_email'] = order.customer.email if order else None

    return kwargs

def process_event(event_custom_id: str, app: App, product: Product, **kwargs):
    kwargs = handle_kwargs(event_custom_id, app, product, **kwargs)

    strategy = AppFactory.get_strategy(app.platform.type, init_vars=True, **kwargs)  # type:ignore
    event_method = strategy.get_event_method(event_custom_id)

    if event_method is not None:
        job = enqueue(event_method, **kwargs, retry=Retry(max=5, interval=[5, 60, 150, 360, 1800]))
        if app.platform.type != 'active_campaign':
            EventHistory.objects.create(app=app, event_id=event_custom_id, job=job.id)

@overload
def dispatch_app_events(event_custom_id: Literal['initiate_checkout'], **kwargs: Unpack[InitiateCheckoutEventKwargs]): ...

@overload
def dispatch_app_events(event_custom_id: PaymentEventTypes, **kwargs: Unpack[PaymentEventKwargs]): ...

@overload
def dispatch_app_events(event_custom_id: Literal['checkout_abandonment'], **kwargs: Unpack[CheckoutAbandonmentEventKwargs]): ...

@job('default', retry=Retry(max=5, interval=[5, 25, 30, 360]))
def dispatch_app_events(event_custom_id: str, **kwargs):
    event = get_event(event_custom_id)
    if event is None:
        return

    product = get_product_from_kwargs(kwargs)
    if not product:
        return

    apps = get_apps(event, product)
    for app in apps:
        process_event(event_custom_id, app, product, **kwargs)

@overload
def dispatch_pixel_events(event: str, **kwargs: Unpack[DispatchPixelInitCheckoutKwargs]): ...

@overload
def dispatch_pixel_events(event: str, **kwargs: Unpack[DispatchPixelPaymentEventKwargs]): ...

@job('pixel_events')
def dispatch_pixel_events(
    event: str,
    **kwargs
):
    tracking_pixels = get_tracking_pixels(kwargs)

    for field, strategy in PIXELS_STRATEGIES.items():
        # Get all pixels for each type, e.g. facebook_pixels, google_pixels, etc
        pixels = getattr(tracking_pixels, field).all()
        for pixel in pixels:
            process_pixel_event.delay(
                event=event,
                tracking_pixels=tracking_pixels,
                kwargs=kwargs,
                StrategyClass=strategy,
                pixel=pixel
            )

@job('pixel_events', retry=Retry(max=5, interval=60))
def process_pixel_event(event, tracking_pixels, kwargs, StrategyClass, pixel):
    if pixel:
        pixel.refresh_from_db()
    if tracking_pixels:
        tracking_pixels.refresh_from_db()

    kwargs['pixel'] = pixel
    kwargs['tracking_pixels'] = tracking_pixels

    strategy = StrategyClass(**kwargs)

    if not strategy.check_prerequisites():
        job_response = {
            'event_sent': False,
            'reason': 'Prerequisites not met',
            'pixel_id': pixel.pk,
            'tracking_pixel_id': tracking_pixels.pk
        }
        return pprint.pformat(job_response, sort_dicts=False, width=200)

    event_method = getattr(strategy, event, None)

    if event_method:
        response = event_method()
        job_response = {
            'product_name': tracking_pixels.product.name[:30],
            'product': tracking_pixels.product.pk,
            'pixel_id': pixel.pk,
            'tracking_pixel_id': tracking_pixels.pk,
            'method_response': response
        }
        return pprint.pformat(job_response, sort_dicts=False, width=200)

def get_tracking_pixels(kwargs):
    tracking_pixels = kwargs.get('tracking_pixels')

    if getattr(kwargs.get('order'), 'affiliate', None):
        tracking_pixels = kwargs['order'].product.pixels.filter(affiliate=kwargs['order'].affiliate).first()  # type:ignore
    if not tracking_pixels:
        tracking_pixels = (kwargs.get('offer') or kwargs.get('order')).product.pixels.filter(affiliate__isnull=True).first()  # type:ignore
    if not tracking_pixels:
        raise Exception('No tracking pixels found')
    return tracking_pixels
