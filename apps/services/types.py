from typing import Literal, NotRequired, TypedDict

from checkout.models import CheckoutAbandonment
from gateway.models import Order, Payment
from product.models import Offer, TrackingPixels


class PaymentEventKwargs(TypedDict):
    order: Order
    payment: Payment

class CheckoutAbandonmentEventKwargs(TypedDict):
    offer: Offer
    checkout_abandonment: CheckoutAbandonment

class InitiateCheckoutEventKwargs(TypedDict):
    offer: Offer
    client_ip: str
    client_user_agent: str
    pixelEventId: str
    checkoutUrl: str
    refererUrl: str
    fbc: str
    fbp: str
    email: str
    phone: str
    name: str

class DispatchPixelInitCheckoutKwargs(InitiateCheckoutEventKwargs):
    tracking_pixels: NotRequired[TrackingPixels]

class DispatchPixelPaymentEventKwargs(PaymentEventKwargs):
    tracking_pixels: NotRequired[TrackingPixels]


EventTypes = Literal[
    'purchase_approved',
    'purchase_refused',
    'pix_gerado',
    'boleto_gerado',
    'picpay_gerado',
    'openfinance_nubank_gerado',
    'chargeback',
    'refund',
    'subscription_created',
    'subscription_canceled',
    'subscription_renewed',
    'subscription_renewal_refused',
    'initiate_checkout',
    'checkout_abandonment',
]


PaymentEventTypes = Literal[
    'purchase_approved',
    'purchase_refused',
    'pix_gerado',
    'boleto_gerado',
    'picpay_gerado',
    'openfinance_nubank_gerado',
    'chargeback',
    'refund',
    'subscription_created',
    'subscription_canceled',
    'subscription_renewed',
    'subscription_renewal_refused',
]
