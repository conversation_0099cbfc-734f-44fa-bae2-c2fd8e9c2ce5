import re

import requests
from rest_framework import status
from rest_framework.exceptions import ValidationError

from apps.models import App
from apps.strategies.base import AppStrategy
from apps.utils import get_response_data
from checkout.models import CheckoutAbandonment
from gateway.models import Order, Payment
from product.models import Product


class Voxuy(AppStrategy):
    # doc: https://intercom.help/voxuy/pt-BR/articles/6965206-integracao-api-voxuy

    # Payment Types -> https://intercom.help/voxuy/pt-BR/articles/6965206-integracao-api-voxuy#h_4b1ade4e0e
    paymentTypes = {
        'boleto': 1,
        'credit_card': 2,
        'pix': 7,
        'threeDs': 2,
        'googlepay': 2,
        'applepay': 2,
        'picpay': 99,  # there is no picpay on accepted payment types, so we use 99 wich is a custom event on doc
        'openfinance_nubank': 99,  # there is no openfinance_nubank on accepted payment types, so we use 99 which is a custom event on doc
    }

    # Status Types -> https://intercom.help/voxuy/pt-BR/articles/6965206-integracao-api-voxuy#h_8e4ceb4c22
    statusTypes = {
        'waiting_payment': 0,
        'paid': 1,
        'canceled': 2,
        'chargedback': 3,
        'refunded': 4,
        'processing': 7,
        'partially_paid': 8,
        'refused': 10,
        # 'checkoutabandonment': 80
    }

    def initialize_variables(self, *args, **kwargs):
        self.app: App = kwargs['app']
        self.kwargs = kwargs
        self.product: Product = (kwargs.get('order') or kwargs.get('offer')).product  # type:ignore

    def get_payload(self, checkout_abandonment=False):
        if checkout_abandonment:
            return self._get_checkout_abandonment_payload()
        else:
            return self._get_default_payload(self.app)

    def _get_default_payload(self, app):
        # https://intercom.help/voxuy/pt-BR/articles/6965206-integracao-api-voxuy
        order: Order = self.kwargs['order']
        payment: Payment = self.kwargs['payment']
        return {
            'apiToken': app.fields.get('token'),
            'id': payment.pk,
            'planId': app.fields.get('plan_id'),
            'value': int(order.baseAmount * 100),
            'totalValue': int(payment.amount * 100),
            'paymentType': self.paymentTypes.get(order.paymentMethodType, 99),
            'status': self.statusTypes[payment.status],
            'date': payment.createdAt.isoformat(),
            'clientName': order.customer.name,
            'clientEmail': order.customer.email,
            'clientPhoneNumber': '+55' + order.customer.phone,
            'clientDocument': order.customer.docNumber,
            'checkoutUrl': order.checkoutUrl,
            'paymentLine': payment.boleto.get('barcode') or None if payment.boleto else None,
            'boletoUrl': payment.boleto.get('boletoUrl') or None if payment.boleto else None,
            'pixQrCode': payment.pix.get('qrCode') or None if payment.pix else None,
            'pixUrl': payment.pix.get('pixUrl') or None if payment.pix else None,
            'metadata': {
                'app_name': app.name,
                'product_name': order.product.name,
                'product_id': order.product.id,
                'offer_id': order.offer.id,  # type:ignore
                'offer_name': order.offer.name,  # type:ignore
                'isPicPay': True if payment.picpay else False,
                'picpay_qrCode': payment.picpay.get('qrCode') or None if payment.picpay else None,
                'picpay_paymentURL': payment.picpay.get('paymentURL') or None if payment.picpay else None,
                'picpay_expirationDate': payment.picpay.get('expirationDate') or None if payment.picpay else None,
                'isOpenFinanceNubank': True if payment.openFinanceNubank else False,
                'openFinanceNubank_url': payment.openFinanceNubank.get('URL') or None if payment.openFinanceNubank else None,
            }
        }

    def _get_checkout_abandonment_payload(self):
        checkout_abandonment: CheckoutAbandonment = self.kwargs['checkout_abandonment']
        return {
            'apiToken': self.app.fields.get('token'),  # type:ignore
            'planId': self.app.fields.get('plan_id'),  # type:ignore
            'value': int(checkout_abandonment.offer.price * 100),
            'paymentType': 99,
            'status': 80,
            'date': checkout_abandonment.createdAt.isoformat(),
            'clientName': checkout_abandonment.customerName,
            'clientEmail': checkout_abandonment.customerEmail,
            'clientPhoneNumber': '+55' + (checkout_abandonment.customerCellphone or ''),
            'clientDocument': checkout_abandonment.customerCpf or checkout_abandonment.customerCnpj,
            'checkoutUrl': checkout_abandonment.checkoutUrl,
            'metadata': {
                'app_name': self.app.name,
                'offer_name': checkout_abandonment.offer.name,
                'offer_id': checkout_abandonment.offer.id,
                'product_name': checkout_abandonment.offer.product.name,
                'product_id': checkout_abandonment.offer.product.id,
            }
        }

    @staticmethod
    def get_test_payload(app):
        return {
            'apiToken': app.fields.get('token'),
            'id': '87956abe-940e-4e8b-8a27-82c482920f64',
            'planId': app.fields.get('plan_id'),
            'value': 2500,
            'totalValue': 5000,
            'paymentType': 'credit_card',
            'status': 'paid',
            'date': '2024-09-18T23:12:59.347605+00:00',
            'clientName': 'João Silva',
            'clientEmail': '<EMAIL>',
            'clientPhoneNumber': '11987654321',
            'clientDocument': '**********1',
            'checkoutUrl': 'https://pay.cakto.com.br/example',
            'paymentLine': '****************************************1234',
            'boletoUrl': 'https://url-de-pagamento-boleto.com/**********',
            'pixQrCode': 'example-qr-code-data',
            'pixUrl': 'https://url-de-pagamento-pix.com/**********',
            'metadata': {
                'app_name': app.name,
                'offer_name': 'Oferta Teste',
                'offer_id': '87956abe',
                'product_name': 'Produto Teste',
                'product_id': '87956abe-940e-4e8b-8a27-82c482920f64',
                'isPicPay': False,
                'picpay_qrCode': 'picpayQrCode',
                'picpay_paymentURL': "https://url-de-pagamento-picpay.exemplo.com/12345",
                'picpay_expirationDate': '2024-09-18T23:12:59.347605+00:00',
                'isOpenFinanceNubank': False,
                'openFinanceNubank_url': 'https://url-de-pagamento-openfinance-nubank.exemplo.com/**********',
            }
        }

    def handle_send_data(self, payload, *args, **kwargs):
        checkout_abandonment = 'checkout_abandonment' in kwargs
        test_event = kwargs.get('test_event')
        if not payload and not test_event:
            payload = self.get_payload(checkout_abandonment)
            url = self.app.url
        elif not payload and test_event:
            app = kwargs['app']
            payload = self.get_test_payload(app)
            url = app.url
        else:
            url = kwargs['app'].url

        return payload, url

    def send_event(self, payload, *args, **kwargs):
        meta_data = kwargs.get('meta_data', '')
        try:
            if not meta_data:
                meta_data = self.processing_meta_data(**kwargs)

            payload, url = self.handle_send_data(payload, **kwargs)

            response = requests.post(url, json=payload, headers={'Content-Type': 'application/json'})  # type:ignore
            self.save_event_in_history(request_data=response, payload=payload, meta=meta_data, new_event_history_id=kwargs.get('new_event_history_id', ''), error=None)
        except Exception as e:
            error_msg = str(e)
            self.save_event_in_history(payload=payload, meta=meta_data, new_event_history_id=kwargs.get('new_event_history_id', ''), error=error_msg)

            return 400, error_msg

        return get_response_data(response)

    def boleto_gerado(self, *args, payload=None, **kwargs):
        return self.send_event(payload, **kwargs)

    def pix_gerado(self, *args, payload=None, **kwargs):
        return self.send_event(payload, **kwargs)

    def picpay_gerado(self, *args, payload=None, **kwargs):
        return self.send_event(payload, **kwargs)

    def purchase_approved(self, *args, payload=None, **kwargs):
        return self.send_event(payload, **kwargs)

    def purchase_refused(self, *args, payload=None, **kwargs):
        return self.send_event(payload, **kwargs)

    def refund(self, *args, payload=None, **kwargs):
        return self.send_event(payload, **kwargs)

    def chargeback(self, *args, payload=None, **kwargs):
        return self.send_event(payload, **kwargs)

    def subscription_created(self, *args, payload=None, **kwargs):
        return self.send_event(payload, **kwargs)

    def subscription_canceled(self, *args, payload=None, **kwargs):
        return self.send_event(payload, **kwargs)

    def subscription_renewed(self, *args, payload=None, **kwargs):
        return self.send_event(payload, **kwargs)

    def subscription_renewal_refused(self, *args, payload=None, **kwargs):
        return self.send_event(payload, **kwargs)

    def checkout_abandonment(self, *args, payload=None, **kwargs):
        return self.send_event(payload, **kwargs)

    def openfinance_nubank_gerado(self, *args, payload=None, **kwargs):
        return self.send_event(payload, **kwargs)

    # Check Methods
    @staticmethod
    def check_token_api(data: dict, request):
        uuid_regex = r"\b([A-F 0-9]{8})-([A-F 0-9]{4})-([A-F 0-9]{4})-([A-F 0-9]{4})-([A-F 0-9]{12})\b"
        if not re.search(uuid_regex, data.get('fields', {}).get('token', ''), re.IGNORECASE):
            raise ValidationError(
                {'detail': 'Token API deve seguir padrão: "e5539048-4a3e-4c02-ba20-93781eaf115c".'},
                status.HTTP_400_BAD_REQUEST
            )

    @staticmethod
    def check_plan_id(data: dict, request):
        plan_id_regex = r"\b([A-F 0-9]{8})-([A-F 0-9]{4})-([A-F 0-9]{4})-([A-F 0-9]{4})-([A-F 0-9]{6})\b"
        if not re.search(plan_id_regex, data.get('fields', {}).get('plan_id', ''), re.IGNORECASE):
            raise ValidationError(
                {'detail': 'Id do Plano deve seguir padrão: "e5539048-4a3e-4c02-ba20-93781eaf115c".'},
                status.HTTP_400_BAD_REQUEST
            )

    @staticmethod
    def check_url_pattern(data: dict, request):
        uuid_regex = r"\b([A-F 0-9]{8})-([A-F 0-9]{4})-([A-F 0-9]{4})-([A-F 0-9]{4})-([A-F 0-9]{12})\b"
        url_pattern = fr"https://sistema.voxuy.com/api/{uuid_regex}/webhooks/voxuy/transaction"
        if not re.search(url_pattern, data.get('url', ''), re.IGNORECASE):
            raise ValidationError(
                {'detail': 'A url deve seguir padrão: "https://sistema.voxuy.com/api/<código-voxuy>/webhooks/voxuy/transaction".'},
                status.HTTP_400_BAD_REQUEST
            )

    @staticmethod
    def check_request_is_ok(data: dict, request):
        payload = {
            'apiToken': data.get('fields', {}).get('token', ''),
            'planId': data.get('fields', {}).get('plan_id', ''),
            'paymentType': 99,
            'status': 80,
            'clientPhoneNumber': '+5511999999999',
        }
        res = requests.post(data.get('url', ''), json=payload, headers={'Content-Type': 'application/json'})
        if not status.is_success(res.status_code):
            raise ValidationError(
                {'detail': 'Erro na integração, verifique se os dados estão corretos.'},
                status.HTTP_400_BAD_REQUEST
            )
