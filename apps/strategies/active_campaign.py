import json

import requests
from django.utils import timezone
from rest_framework import status
from rest_framework.exceptions import ValidationError
from rq import get_current_job

from apps.models import App, EventHistory
from apps.strategies.base import AppStrategy
from gateway.models import Order

LIST_ALL_TAGS_URL = '{api_url}/api/3/tags'
LIST_ALL_LISTS_URL = '{api_url}/api/3/lists?limit={limit}'

GET_CONTACT_URL = '{api_url}/api/3/contacts?filters[email]={search_email}'
CREATE_CONTACT_URL = '{api_url}/api/3/contacts'

TAGS_URL = '{api_url}/api/3/contactTags'
CONTACT_LISTS_URL = '{api_url}/api/3/contactLists'

GET_CONTACT_TAGS_URL = '{api_url}/api/3/contacts/{contact_id}/contactTags'
REMOVE_TAG_URL = '{api_url}/api/3/contactTags/{tag_association_id}'

class ActiveCampaign(AppStrategy):
    def initialize_variables(self, *args, **kwargs):
        self.app: App = kwargs['app']
        self.order: Order = kwargs['order']
        self.kwargs = kwargs

    def get_headers(self):
        token = self.app.fields.get('token', '')
        if not token:
            raise Exception('Token não informado.')
        return {'Api-Token': f"{token}"}

    def get_contact_payload(self):
        if self.test_event:
            return {
                'contact': {
                    'email': '<EMAIL>',
                    'firstName': 'Teste Cakto',
                    'lastName': '',
                    'phone': '11999999999',
                }
            }

        if self.order:
            name = self.order.customer.name.split()
            email = self.order.customer.email
            phone = self.order.customer.phone
        else:
            checkout_abandonment = self.kwargs.get('checkout_abandonment')
            if not checkout_abandonment:
                raise Exception('Erro ao buscar contato. Pedido ou Abandono de Checkout não informado.')
            name = checkout_abandonment.customerName.split()
            email = checkout_abandonment.customerEmail
            phone = checkout_abandonment.customerCellphone

        return {
            'contact': {
                'email': email,
                'firstName': name[0],
                'lastName': name[-1] if len(name) > 1 else '',
                'phone': phone,
            }
        }

    def construct_tag_to_contact_payload(self, contact_id, tag_id):
        return {
            "contactTag": {
                "contact": contact_id,
                "tag": tag_id
            }
        }

    def construct_list_to_contact_payload(self, list_id, contact_id, contact_status):
        return {
            "contactList": {
                "list": list_id,
                "contact": contact_id,
                "status": contact_status,
            }
        }

    def get_or_create_customer_contact(self) -> dict:
        # https://developers.activecampaign.com/reference/get-contact

        if self.test_event:
            search = '<EMAIL>'
        elif self.order:
            search = self.order.customer.email
        else:
            checkout_abandonment = self.kwargs.get('checkout_abandonment')
            search = checkout_abandonment.customerEmail if checkout_abandonment else ''

        url = GET_CONTACT_URL.format(api_url=self.app.url, search_email=search)

        response = requests.get(url, headers=self.get_headers())
        self.save_active_campaign_history(response, 'Get Customer Contact')
        response.raise_for_status()
        data = response.json()

        total = int(data.get('meta', {}).get('total', 0))

        if total == 1:
            return data['contacts'][0]
        elif total == 0:
            return self.create_contact()
        else:
            raise Exception('Erro ao buscar contato.', data)

    def get_contact_tag_association_id(self, tag_id, contact_id):
        # https://developers.activecampaign.com/reference/remove-a-contacts-tag

        url = GET_CONTACT_TAGS_URL.format(api_url=self.app.url, contact_id=contact_id)

        response = requests.get(url, headers=self.get_headers())
        self.save_active_campaign_history(response, 'Get Contact Tag Association')
        response.raise_for_status()
        data = response.json()

        tag_association: dict = next((tag for tag in data.get('contactTags', []) if str(tag.get('tag', '')) == str(tag_id)), {})
        return tag_association.get('id')

    def create_contact(self):
        # https://developers.activecampaign.com/reference/create-a-new-contact

        url = CREATE_CONTACT_URL.format(api_url=self.app.url)

        response = requests.post(url, json=self.get_contact_payload(), headers=self.get_headers())
        self.save_active_campaign_history(response, 'Create Customer Contact')
        response.raise_for_status()
        data = response.json()
        return data['contact']

    def add_tag_to_contact(self, tag_id, contact_id):
        # https://developers.activecampaign.com/reference/create-contact-tag

        url = TAGS_URL.format(api_url=self.app.url)

        payload = self.construct_tag_to_contact_payload(contact_id, tag_id)
        response = requests.post(url, json=payload, headers=self.get_headers())
        self.save_active_campaign_history(response, 'Add Tag to Contact')
        response.raise_for_status()

        return response.json()

    def remove_tag_to_contact(self, tag_id, contact_id):
        # https://developers.activecampaign.com/reference/remove-a-contacts-tag

        tag_association_id = self.get_contact_tag_association_id(tag_id, contact_id)

        if not tag_association_id:
            return 'Nenhuma tag encontrada para ser removida.'

        url = REMOVE_TAG_URL.format(api_url=self.app.url, tag_association_id=tag_association_id)
        response = requests.delete(url, headers=self.get_headers())
        self.save_active_campaign_history(response, 'Remove Tag to Contact')
        response.raise_for_status()

        return response.json()

    def modify_contact_to_list(self, list_id, contact_status, contact_id):
        # https://developers.activecampaign.com/reference/update-list-status-for-contact

        url = CONTACT_LISTS_URL.format(api_url=self.app.url)

        payload = self.construct_list_to_contact_payload(list_id, contact_id, contact_status)
        response = requests.post(url, json=payload, headers=self.get_headers())
        self.save_active_campaign_history(response, 'Modify Contact to List')
        response.raise_for_status()

        return response.json()

    def handle_insert(self, tag_id, list_id):
        contact_data = self.get_or_create_customer_contact()
        contact_id = contact_data['id']
        tag_data = self.add_tag_to_contact(int(tag_id), contact_id) if tag_id else {}
        list_data = self.modify_contact_to_list(int(list_id), '1', contact_id) if list_id else {}

        return {'insertTag': tag_data, 'insertInList': list_data}

    def handle_remove(self, tag_id, list_id):
        contact_data = self.get_or_create_customer_contact()
        contact_id = contact_data['id']
        tag_data = self.remove_tag_to_contact(int(tag_id), contact_id) if tag_id else {}
        list_data = self.modify_contact_to_list(int(list_id), '2', contact_id) if list_id else {}

        return {'removeTag': tag_data, 'removeFromList': list_data}

    def send_event(self, *args, payload=None, **kwargs):
        self.test_event = kwargs.get('test_event', False)
        if self.test_event:
            self.app: App = kwargs.get('app')  # type:ignore
            self.kwargs = kwargs

        meta_data = kwargs.get('meta_data', '')
        if not meta_data:
            meta_data = self.processing_meta_data(**kwargs)

        action = self.app.fields.get('customAction', '')
        tag_id = self.app.fields.get('tagId', 0)
        list_id = self.app.fields.get('listId', 0)

        if not tag_id and not list_id:
            raise Exception('tagId ou listId não informados no App:', self.app.pk, ' - ', self.app.name)

        if action == 'insert':
            return 200, self.handle_insert(tag_id, list_id)
        elif action == 'remove':
            return 200, self.handle_remove(tag_id, list_id)

    def save_active_campaign_history(self, response: requests.Response, action: str = 'send_event'):
        try:
            data = response.status_code, response.json()
        except json.JSONDecodeError:
            try:
                data = response.status_code, str(response.content.decode('utf-8'))
            except Exception:
                data = None

        job = get_current_job()
        EventHistory.objects.create(
            app=self.app,
            url=response.request.url,
            payload=json.loads(response.request.body) if response.request.body else {},
            response=data,
            meta_data=self.processing_meta_data(request_action=action, is_test_event=self.test_event, **self.kwargs),
            event_id=self.kwargs.get('event_custom_id', ''),
            job=job.id if job else '',
            event_status=response.status_code,
            sentAt=timezone.now(),
        )

    # Event Methods
    def boleto_gerado(self, *args, payload=None, **kwargs):
        return self.send_event(*args, payload, **kwargs)

    def pix_gerado(self, *args, payload=None, **kwargs):
        return self.send_event(*args, payload, **kwargs)

    def picpay_gerado(self, *args, payload=None, **kwargs):
        return self.send_event(*args, payload, **kwargs)

    def purchase_approved(self, *args, payload=None, **kwargs):
        return self.send_event(*args, payload, **kwargs)

    def purchase_refused(self, *args, payload=None, **kwargs):
        return self.send_event(*args, payload, **kwargs)

    def refund(self, *args, payload=None, **kwargs):
        return self.send_event(*args, payload, **kwargs)

    def chargeback(self, *args, payload=None, **kwargs):
        return self.send_event(*args, payload, **kwargs)

    def subscription_created(self, *args, payload=None, **kwargs):
        return self.send_event(*args, payload, **kwargs)

    def subscription_canceled(self, *args, payload=None, **kwargs):
        return self.send_event(*args, payload, **kwargs)

    def subscription_renewed(self, *args, payload=None, **kwargs):
        return self.send_event(*args, payload, **kwargs)

    def subscription_renewal_refused(self, *args, payload=None, **kwargs):
        return self.send_event(*args, payload, **kwargs)

    def checkout_abandonment(self, *args, payload=None, **kwargs):
        return self.send_event(*args, payload, **kwargs)

    def openfinance_nubank_gerado(self, *args, payload=None, **kwargs):
        return self.send_event(*args, payload, **kwargs)

    # Check Methods
    @staticmethod
    def check_token(data: dict, request):
        secret = data.get('fields', {}).get('token')
        if not secret:
            raise ValidationError({'detail': 'O campo token deve ser informado.'}, status.HTTP_400_BAD_REQUEST)

    @staticmethod
    def check_tag_id_or_list_id(data: dict, request):
        tag_id = data.get('fields', {}).get('tagId')
        list_id = data.get('fields', {}).get('listId')

        if not tag_id and not list_id:
            raise ValidationError({'detail': '"tagId" ou "listId" deve ser informado.'}, status.HTTP_400_BAD_REQUEST)

        if tag_id and not str(tag_id).isnumeric():
            raise ValidationError({'detail': 'O campo tagId deve ser numérico.'}, status.HTTP_400_BAD_REQUEST)
        if list_id and not str(list_id).isnumeric():
            raise ValidationError({'detail': 'O campo listId deve ser numérico.'}, status.HTTP_400_BAD_REQUEST)

    @staticmethod
    def check_custom_action(data: dict, request):
        custom_action = data.get('fields', {}).get('customAction')
        if not custom_action:
            raise ValidationError({'detail': 'O campo customAction deve ser informada.'}, status.HTTP_400_BAD_REQUEST)

        if custom_action not in ('insert', 'remove'):
            raise ValidationError({'detail': 'O campo customAction deve ser "insert" ou "remove".'}, status.HTTP_400_BAD_REQUEST)

    @staticmethod
    def check_api_url(data: dict, request):
        url = data.get('url')
        if not url:
            raise ValidationError({'detail': 'A url deve ser informada.'}, status.HTTP_400_BAD_REQUEST)

    @staticmethod
    def check_integration_account_name(data: dict, request):
        tag_url = LIST_ALL_TAGS_URL.format(api_url=data.get('url', ''))
        response = requests.get(tag_url, headers={'Api-Token': f'{data.get("fields", {}).get("token")}'})
        if not status.is_success(response.status_code):
            raise ValidationError({'detail': 'Erro ao realizar integração, verifique se os dados estão corretos.'}, status.HTTP_400_BAD_REQUEST)

    @staticmethod
    def check_integration_url(data: dict, request):
        list_url = LIST_ALL_LISTS_URL.format(api_url=data.get('url'), limit=5)
        response = requests.get(list_url, headers={'Api-Token': f'{data.get("fields", {}).get("token")}'})
        if not status.is_success(response.status_code):
            raise ValidationError({'detail': 'Erro ao realizar integração, verifique se os dados estão corretos.'}, status.HTTP_400_BAD_REQUEST)
