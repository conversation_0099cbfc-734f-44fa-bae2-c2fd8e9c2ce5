import re
from enum import Enum

import requests
from django.utils import timezone
from rest_framework import status
from rest_framework.exceptions import ValidationError

from apps.models import App, Platform
from apps.strategies.base import AppStrategy
from apps.utils import get_response_data
from gateway.models import Order
from product.models import Product


class AccessStatus(Enum):
    INACTIVE = 'inactive'
    PENDING = 'pending'
    ACTIVE = 'active'
    EXPIRED = 'expired'

class MemberKit(AppStrategy):
    def initialize_variables(self, *args, **kwargs):
        self.order: Order = kwargs['order']
        self.app: App = kwargs['app']

    def get_payload(self, customer, access_status: AccessStatus) -> dict:
        access_options = self.app.fields.get('access', {})  # type:ignore
        payload = {
            # Customer data
            'full_name': customer.name,
            'email': customer.email,
            'cpf_cnpj': customer.docNumber,
            'phone_local_code': customer.phone[:2] if customer.phone else '',
            'phone_number': customer.phone[2:] if customer.phone else '',

            # Access data
            'status': access_status.value,
            'unlimited': access_options.get('unlimited', False),
            'classroom_ids': access_options.get('classroom_ids', []),
            'membership_level_id': access_options.get('membership_level_id', None),
            'expires_at': (
                timezone.now() + timezone.timedelta(days=int(access_options.get('access_days')))
            ).isoformat() if access_options.get('access_days') else None,
        }

        return payload

    @staticmethod
    def get_test_payload():
        payload = {
            # Customer data
            'full_name': 'Ana Souza',
            'email': '<EMAIL>',
            'cpf_cnpj': '12345678900',
            'phone_local_code': '11',
            'phone_number': '987654321',

            # Access data
            'status': AccessStatus.ACTIVE.value,
            'unlimited': True,
            'classroom_ids': [101, 102, 103],
            'membership_level_id': '123456',
            'expires_at': '2024-09-18T23:12:59.347605+00:00',
        }

        return payload

    def handle_send_data(self, payload, access_status: AccessStatus, *args, **kwargs):
        base_url = 'https://memberkit.com.br/api/v1'
        test_event = kwargs.get('test_event')
        if not payload and not test_event:
            payload = self.get_payload(customer=self.order.customer, access_status=access_status)
            url = base_url + f'/users/?api_key={self.app.fields.get("secret_key", "")}'
        elif not payload and test_event:
            app = kwargs['app']
            payload = self.get_test_payload()
            url = base_url + f'/users/?api_key={app.fields.get("secret_key", "")}'
        else:
            app = kwargs['app']
            url = base_url + f'/users/?api_key={app.fields.get("secret_key", "")}'

        return payload, url

    def send_event(self, access_status: AccessStatus, payload, *args, **kwargs):
        meta_data = kwargs.get('meta_data', '')
        try:
            if not meta_data:
                meta_data = self.processing_meta_data(**kwargs)

            payload, url = self.handle_send_data(payload, access_status, **kwargs)

            response = requests.post(url=url, json=payload, headers={"Content-Type": "application/json"})
            self.save_event_in_history(request_data=response, payload=payload, meta=meta_data, new_event_history_id=kwargs.get('new_event_history_id', ''), error=None)
        except Exception as e:
            error_msg = str(e)
            self.save_event_in_history(payload=payload, meta=meta_data, new_event_history_id=kwargs.get('new_event_history_id', ''), error=error_msg)

            return 400, error_msg

        return get_response_data(response)

    def purchase_approved(self, *args, payload=None, **kwargs):
        return self.send_event(AccessStatus.ACTIVE, payload, **kwargs)

    def refund(self, *args, payload=None, **kwargs):
        return self.send_event(AccessStatus.INACTIVE, payload, **kwargs)

    def chargeback(self, *args, payload=None, **kwargs):
        return self.send_event(AccessStatus.INACTIVE, payload, **kwargs)

    def subscription_created(self, *args, payload=None, **kwargs):
        return self.send_event(AccessStatus.ACTIVE, payload, **kwargs)

    def subscription_canceled(self, *args, payload=None, **kwargs):
        # If we send this event, the user's plan will be removed instantly.
        # We already send the expiration time wen the access is given.
        ...

    def subscription_renewal_refused(self, *args, payload=None, **kwargs):
        return self.send_event(AccessStatus.ACTIVE, payload, **kwargs)

    def subscription_renewed(self, *args, payload=None, **kwargs):
        return self.send_event(AccessStatus.ACTIVE, payload, **kwargs)

    def purchase_refused(self, *args, payload=None, **kwargs):
        ...

    def pix_gerado(self, *args, payload=None, **kwargs):
        ...

    def picpay_gerado(self, *args, payload=None, **kwargs):
        ...

    def boleto_gerado(self, *args, payload=None, **kwargs):
        ...

    def checkout_abandonment(self, *args, payload=None, **kwargs):
        ...

    def openfinance_nubank_gerado(self, *args, payload=None, **kwargs):
        ...

    # Check Methods
    @staticmethod
    def check_if_products_are_external(data: dict, request):
        products: list[Product] = data.get('products', [])
        for product in products:
            if not product.has_contentDelivery('external'):
                raise ValidationError({'detail': f'O produto {product.name} não está configurado para utilizar área de membros externa.'}, status.HTTP_400_BAD_REQUEST)

    @staticmethod
    def check__token(data: dict, request):
        if not data.get('fields', {}).get('secret_key'):
            raise ValidationError({'detail': 'Chave Secreta é obrigatório.'}, status.HTTP_400_BAD_REQUEST)

        token_pattern = r'^[a-zA-Z0-9]{24}$'
        if not re.match(token_pattern, data.get('fields', {}).get('secret_key')):
            raise ValidationError({'detail': 'Chave Secreta inválida, deve conter 24 caracteres alfanuméricos.'}, status.HTTP_400_BAD_REQUEST)

    @staticmethod
    def check_access_options(data: dict, request):
        access = data.get('fields', {}).get('access', {})
        if not access.get('unlimited') and not access.get('classroom_ids') and not access.get('membership_level_id'):
            raise ValidationError({'detail': 'Tipo da integração deve ser selecionado.'}, status.HTTP_400_BAD_REQUEST)
        if not access.get('access_days'):
            raise ValidationError({'detail': 'Quantidade de dias de acesso é obrigatório.'}, status.HTTP_400_BAD_REQUEST)

    @staticmethod
    def check_access_days(data: dict, request):
        access_days = data.get('fields', {}).get('access', {}).get('access_days', 0)
        if not access_days:
            raise ValidationError({'detail': 'Quantidade de dias é obrigatório.'})
        elif int(access_days) < 1:
            raise ValidationError({'detail': 'Quantidade de dias deve ser maior que 0.'})
        elif int(access_days) > 1_000_000:
            raise ValidationError({'detail': 'Quantidade de dias deve ser menor ou igual a 1.000.000.'})

    @staticmethod
    def check_integration(data: dict, request):
        url = 'https://memberkit.com.br/api/v1/courses?api_key=' + data.get('fields', {}).get('secret_key', '')
        response = requests.get(url, headers={"Content-Type": "application/json"})
        if not status.is_success(response.status_code):
            raise ValidationError({'detail': 'Erro ao se conectar com a MemberKit. Verifique a Chave Secreta informada.'}, status.HTTP_400_BAD_REQUEST)

    # Data Setup Methods
    def setup_events(self, *args, **kwargs):
        events = Platform.objects.get(type='memberkit').events.all()  # type:ignore
        self.serializer.validated_data['events'] = events
