import re

import requests
from rest_framework import status
from rest_framework.exceptions import ValidationError

from apps.models import App, Platform
from apps.strategies.base import AppStrategy
from apps.utils import get_response_data
from gateway.models import Order

ASTRON_STATUS_MAP = {
    'processing': 'waiting',
    'authorized': 'waiting',
    'paid': 'paid',
    'refund_requested': 'paid',
    'refunded': 'refunded',
    'waiting_payment': 'waiting',
    'scheduled': 'waiting',
    'refused': 'refused',
    'chargedback': 'chargeback',
    'canceled': 'canceled',
    'in_protest': 'waiting',
    'partially_paid': 'waiting',
}

class AstronMembers(AppStrategy):
    def initialize_variables(self, *args, **kwargs):
        self.app: App = kwargs['app']
        self.order: Order = kwargs['order']

    def get_payload(self):
        # https://www.postman.com/astronbr/api-astron-members/request/uq78q5v/astronpay-webhook
        self.order.refresh_from_db()
        payload = {
            'product_id': self.order.product.pk,
            'product_name': self.order.product.name,
            'trans_id': self.order.refId,
            'trans_created_time': self.order.createdAt.timestamp(),
            'trans_payment_time': self.order.paidAt.timestamp() if self.order.paidAt else None,
            'trans_status': ASTRON_STATUS_MAP[self.order.status],
            'trans_price': float(self.order.amount) if self.order.amount else 0,
            'trans_pay_mode': self.order.paymentMethodType,
            'customer_name': self.order.customer.name,
            'customer_email': self.order.customer.email,
            'customer_phone': self.order.customer.phone,
            'customer_doc': self.order.customer.docNumber,
            'customer_doc_type': self.order.customer.docType,
            'offer_id': self.order.offer.id,
            'offer_name': self.order.offer.name,
            'offer_price': float(self.order.offer.price),
        }
        api_key = self.app.fields.get('api_key')
        if api_key:
            payload.update({'api_key': api_key})
        return payload

    @staticmethod
    def get_test_payload():
        return {
            'product_id': '87956abe-940e-4e8b-8a27-82c482920f64',
            'product_name': 'Produto Teste',
            'trans_id': 'B8BcHrY',
            'trans_created_time': 1700000000.0,
            'trans_payment_time': 1700000000.0,
            'trans_status': 'paid',
            'trans_price': 99.99,
            'trans_pay_mode': 'credit_card',
            'customer_name': 'Cliente Teste',
            'customer_email': '<EMAIL>',
            'customer_phone': '11999999999',
            'customer_doc': '12345678900',
            'customer_doc_type': 'cpf',
            'offer_id': '9vbgfmg',
            'offer_name': 'Oferta Exemplo',
            'offer_price': 49.99,
        }

    def handle_send_data(self, payload, *args, **kwargs):
        test_event = kwargs.get('test_event')
        if not payload and not test_event:
            payload = self.get_payload()
            url = self.app.url
        elif not payload and test_event:
            app = kwargs['app']
            payload = self.get_test_payload()
            url = app.url
        else:
            app = kwargs['app']
            url = app.url

        return payload, url

    def send_event(self, payload, *args, **kwargs):
        error_msg = None
        meta_data = kwargs.get('meta_data', '')
        try:
            if not meta_data:
                meta_data = self.processing_meta_data(**kwargs)

            payload, url = self.handle_send_data(payload, **kwargs)

            response = requests.post(url, json=payload, headers={'Content-Type': 'application/json'})  # type:ignore
            self.save_event_in_history(request_data=response, payload=payload, meta=meta_data, new_event_history_id=kwargs.get('new_event_history_id', ''), error=None)
        except Exception as e:
            error_msg = str(e)
            self.save_event_in_history(payload=payload, meta=meta_data, new_event_history_id=kwargs.get('new_event_history_id', ''), error=error_msg)

            return 400, error_msg

        return get_response_data(response)

    def purchase_approved(self, *args, payload=None, **kwargs):
        return self.send_event(payload, **kwargs)

    def refund(self, *args, payload=None, **kwargs):
        return self.send_event(payload, **kwargs)

    def chargeback(self, *args, payload=None, **kwargs):
        return self.send_event(payload, **kwargs)

    def subscription_created(self, *args, payload=None, **kwargs):
        return self.send_event(payload, **kwargs)

    def subscription_canceled(self, *args, payload=None, **kwargs):
        # If we send this event, the user's plan will be removed instantly.
        # On Astron Members, the producer can congfigure the expiration time of the plan.
        ...

    def subscription_renewed(self, *args, payload=None, **kwargs):
        return self.send_event(payload, **kwargs)

    def subscription_renewal_refused(self, *args, payload=None, **kwargs):
        return self.send_event(payload, **kwargs)

    def purchase_refused(self, *args, payload=None, **kwargs):
        ...

    def boleto_gerado(self, *args, payload=None, **kwargs):
        ...

    def pix_gerado(self, *args, payload=None, **kwargs):
        ...

    def picpay_gerado(self, *args, payload=None, **kwargs):
        ...

    def checkout_abandonment(self, *args, payload=None, **kwargs):
        ...

    def openfinance_nubank_gerado(self, *args, payload=None, **kwargs):
        ...

    # Check Methods
    @staticmethod
    def check_url(data: dict, request):
        url_pattern = r'^https:\/\/webhook\.astronmembers\.com\.br\/cakto-webhook\/[a-zA-Z0-9]{15}$'
        url = data.get('url', '')
        if not url:
            raise ValidationError({'detail': 'o campo URL deve ser informado.'}, status.HTTP_400_BAD_REQUEST)
        if not re.match(url_pattern, url):
            raise ValidationError({'detail': 'o campo URL deve ser uma url válida.'}, status.HTTP_400_BAD_REQUEST)

    @staticmethod
    def check_products_length_equals_1(data: dict, request):
        if not len(data.get('products', [])) == 1:
            raise ValidationError({'detail': 'Cada integração deve ter apenas 1 produto.'}, status.HTTP_400_BAD_REQUEST)

    @staticmethod
    def check_integration(data: dict, request):
        payload = {'event': 'integration_test'}
        res = requests.post(data.get('url', ''), json=payload, headers={'Content-Type': 'application/json'})
        if not status.is_success(res.status_code):
            raise ValidationError({'detail': 'Erro ao realizar integração, verifique se os dados estão corretos.'}, status.HTTP_400_BAD_REQUEST)

    # Data Setup Methods
    def setup_events(self, *args, **kwargs):
        events = Platform.objects.get(type='memberkit').events.all()
        self.serializer.validated_data['events'] = events
