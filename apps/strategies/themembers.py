import re
import requests
from enum import Enum
from typing import Any, Dict, <PERSON><PERSON>
from types import SimpleNamespace

import django.db
from django.db import transaction
from django.utils import timezone
from rest_framework import status

from apps.models import App
from apps.strategies.base import AppStrategy
from gateway.models import Order
from apps.services.themembers_api import TheMembersAPI


class AccessAction(Enum):
    CREATE  = "create"
    DISABLE = "disable"


class TheMembers(AppStrategy):

    def initialize_variables(self, *args, **kwargs):
        self.app: App = kwargs["app"]
        self.order: Order | None = kwargs.get("order")
        self.product_id: str | None = self.app.fields.get("product_id")
        self.platform_token: str = self.app.fields["platform_token"]
        self.api = TheMembersAPI(
            platform_token=self.platform_token,
            base_url=self.app.url
        )

    def _ensure_context(self, **kwargs) -> Tuple[App, str, TheMembersAPI]:
        if not hasattr(self, "app"):
            app_actual: App = kwargs["app"]
            product_id = app_actual.fields.get("product_id")
            api = TheMembersAPI(
                platform_token=app_actual.fields["platform_token"],
                base_url=app_actual.url
            )
        else:
            app_actual = self.app
            product_id = self.product_id
            api = self.api
        if not product_id:
            raise ValueError("product_id não definido no App.fields")
        return app_actual, product_id, api

    @staticmethod
    def _safe_json(resp: requests.Response | None) -> Any:
        if resp is None:
            return None
        try:
            return resp.json()
        except ValueError:
            return resp.text or None

    @staticmethod
    def get_test_payload(app: App) -> Dict[str, Any]:
        today = timezone.now().date().isoformat()
        return {
            "name": "Test User",
            "email": "<EMAIL>",
            "reference_id": "9999",
            "accession_date": today,
            "product_id": app.fields.get("product_id"),
        }

    def _send_members(
        self,
        action: AccessAction,
        customer: Dict[str, Any],
        product_id: str,
        api: TheMembersAPI,
        *,
        test_event: bool = False,
    ) -> Tuple[int, Any]:
        try:
            if action is AccessAction.CREATE:
                return self._handle_create(api, customer, product_id, test_event)
            else:
                return self._handle_disable(api, customer, product_id)
        except requests.HTTPError as exc:
            return (
                exc.response.status_code if exc.response else 500,
                self._safe_json(exc.response),
            )
        except Exception as exc:
            return 500, {"error": str(exc)}

    def _is_uuid(self, value: str | None) -> bool:
        return bool(value and re.fullmatch(r"[0-9a-fA-F\-]{36}", value))

    def _handle_create(self, api, customer, product_id, test_event) -> Tuple[int, Any]:
        created_user_id = self._create_or_lookup_user(api, customer, product_id)

        if self._is_uuid(created_user_id):
            self._persist_uuid(customer["id"], created_user_id, test_event)
            if hasattr(self, "order") and self.order:
                self.order.customer.themembers_uuid = created_user_id

        exp_date = (timezone.now() + timezone.timedelta(days=365)).strftime("%Y-%m-%d %H:%M:%S")

        user_is_uuid = self._is_uuid(created_user_id)

        payload_kwargs = {
            "product_id": product_id,
            "exp_date": exp_date,
            "user_id": created_user_id if user_is_uuid else None,
            "reference_id": None if user_is_uuid else str(customer["id"]),
        }

        resp = api.create_subscription(**payload_kwargs)

        new_uuid = (
            resp.get("data", {}).get("user_subscription", {}).get("user_id")
        )
        if self._is_uuid(new_uuid):
            self._persist_uuid(customer["id"], new_uuid, test_event)
            if hasattr(self, "order") and self.order:
                self.order.customer.themembers_uuid = new_uuid

        return 200, resp or {}

    def _handle_disable(self, api, customer, product_id) -> Tuple[int, Any]:
        uuid_saved = None
        if getattr(self, "order", None):
            uuid_saved = getattr(self.order.customer, "themembers_uuid", None)

        if not uuid_saved:
            from customer.models import Customer
            uuid_saved = (
                Customer.objects
                .filter(pk=customer["id"])
                .values_list("themembers_uuid", flat=True)
                .first()
            )

        resp = api.disable_subscription(
            product_id=product_id,
            user_id=uuid_saved,
            reference_id=None if uuid_saved else str(customer["id"]),
        )
        return 200, resp or {}

    def _create_or_lookup_user(self, api, customer, product_id) -> str:
        create_resp = api.create_users(
            users=[{
                "name": customer["name"],
                "last_name": "",
                "email": customer["email"],
                "reference_id": str(customer["id"]),
                "accession_date": timezone.now().date().isoformat(),
            }],
            product_id=product_id,
        )

        created_user_id = (
            (create_resp.get("data") or [{}])[0].get("id")
            or create_resp.get("id")
        )

        if not created_user_id:
            try:
                lookup = api.get_user_by_reference_id(reference_id=str(customer["id"]))
                created_user_id = lookup.get("id")
            except requests.HTTPError:
                created_user_id = str(customer["id"])  # fallback final
        return created_user_id

    def _persist_uuid(self, customer_id: int, uuid: str, test_event: bool):
        if test_event:
            return

        from customer.models import Customer
        try:
            with transaction.atomic():
                cust_obj = Customer.objects.select_for_update().get(pk=customer_id)
        except django.db.DatabaseError:
            cust_obj = Customer.objects.get(pk=customer_id)

        cust_obj.themembers_uuid = uuid
        cust_obj.save(update_fields=["themembers_uuid"])

    def _dispatch(
        self,
        action: AccessAction,
        *args,
        payload=None,
        **kwargs,
    ):
        meta = kwargs.get("meta_data") or self.processing_meta_data(**kwargs)
        app_actual, product_id, api = self._ensure_context(**kwargs)

        # Cliente / Payload de teste ou produção
        if kwargs.get("test_event"):
            payload_test = self.get_test_payload(app_actual)
            customer = {
                "name":  payload_test["name"],
                "email": payload_test["email"],
                "id":    payload_test["reference_id"],
            }
            payload_to_save = payload_test
        else:
            order: Order = kwargs.get("order") or self.order
            customer = {
                "name":  order.customer.name,
                "email": order.customer.email,
                "id":    order.customer.id,
            }
            payload_to_save = payload or {}

        # Integração
        status_code, resp_json = self._send_members(
            action,
            customer,
            product_id,
            api,
            test_event=kwargs.get("test_event", False),
        )

        # Log/histórico
        fake_resp = SimpleNamespace(
            status_code=status_code,
            request=SimpleNamespace(url=f"(mock){action.value}"),
            json=lambda: resp_json,
        )

        self.save_event_in_history(
            request_data=fake_resp,
            payload=payload_to_save,
            meta=meta,
            new_event_history_id=kwargs.get("new_event_history_id"),
            error=None if status_code < 400 else resp_json,
        )
        return status_code, resp_json

    def purchase_approved(self, *args, payload=None, **kwargs):
        return self._dispatch(AccessAction.CREATE, *args, payload=payload, **kwargs)

    def subscription_created(self, *args, payload=None, **kwargs):
        return self._dispatch(AccessAction.CREATE, *args, payload=payload, **kwargs)

    def subscription_renewed(self, *args, payload=None, **kwargs):
        return self._dispatch(AccessAction.CREATE, *args, payload=payload, **kwargs)

    def refund(self, *args, payload=None, **kwargs):
        return self._dispatch(AccessAction.DISABLE, *args, payload=payload, **kwargs)

    def chargeback(self, *args, payload=None, **kwargs):
        return self._dispatch(AccessAction.DISABLE, *args, payload=payload, **kwargs)

    def subscription_canceled(self, *args, payload=None, **kwargs):
        return self._dispatch(AccessAction.DISABLE, *args, payload=payload, **kwargs)

    def subscription_renewal_refused(self, *args, payload=None, **kwargs):
        return self._dispatch(AccessAction.DISABLE, *args, payload=payload, **kwargs)

    def purchase_refused(self, *args, **kwargs):               ...
    def pix_gerado(self, *args, **kwargs):                    ...
    def picpay_gerado(self, *args, **kwargs):                 ...
    def boleto_gerado(self, *args, **kwargs):                 ...
    def checkout_abandonment(self, *args, **kwargs):          ...
    def openfinance_nubank_gerado(self, *args, **kwargs):     ...
    def subscription_renewal_refused(self, *args, **kwargs):  ...
