import re

import requests
from django.db.models import Sum
from django.utils import timezone
from rest_framework import status
from rest_framework.exceptions import ValidationError

from apps.models import App
from apps.strategies.base import AppStrategy
from apps.utils import get_response_data
from product.models import Product

utmify_paymentMethods = {
    'threeDs': 'credit_card',
    'googlepay': 'credit_card',
    'applepay': 'credit_card',
    'picpay': 'unknown',
    'openfinance_nubank': 'unknown',
}

class UTMify(AppStrategy):
    def initialize_variables(self, *args, **kwargs):
        self.app: App = kwargs['app']
        self.product: Product = (kwargs.get('order') or kwargs.get('offer')).product  # type:ignore
        self.event_custom_id = kwargs.get('event_custom_id', '')
        self.kwargs = kwargs

    def get_headers(self, app):
        return {
            'Content-Type': 'application/json',
            'x-api-token': app.fields.get('token', '')  # type:ignore
        }

    def get_payload(self):
        order = self.kwargs['order']  # type:ignore
        order.refresh_from_db()

        paymentMethod = utmify_paymentMethods.get(order.paymentMethodType, order.paymentMethodType)

        data = {
            'orderId': order.pk,
            'platform': 'Cakto',  # ex: Kiwify, PerfectPay
            'paymentMethod': paymentMethod,  # (credit_card, boleto, pix, paypal, free_price)
            'status': order.status,  # (waiting_payment, paid, refused, refunded, chargedback)
            'createdAt': order.createdAt.strftime('%Y-%m-%d %H:%M:%S'),  # data de criação do pagamento (utc time) YYYY-MM-DD HH:MM:SS
            'approvedDate': order.paidAt.strftime('%Y-%m-%d %H:%M:%S') if order.paidAt else None,  # data de aprovação do pagamento (utc time) YYYY-MM-DD HH:MM:SS
            'refundedAt': None,  # | null, # data de estorno do pagamento (utc time) YYYY-MM-DD HH:MM:SS
            'customer': {
                'name': order.customer.name,
                'email': order.customer.email,
                'phone': order.customer.phone,
                'document': order.customer.docNumber,
            },
            'product': {
                'id': self.product.pk,
                'name': self.product.name,
                'planId': order.offer.id,
                'planName': order.offer.name,
                'quantity': 1,
                'priceInCents': int(round(order.offer.price, 2) * 100),
            },
            'trackingParameters': {
                'src': '',
                'utm_source': order.utm_source,
                'utm_medium': order.utm_medium,
                'utm_campaign': order.utm_campaign,
                'utm_term': order.utm_term,
                'utm_content': order.utm_content,
                'sck': order.sck,
            },
            'commission': {
                'totalPriceInCents': int(order.offer.price * 100),
                'gatewayFeeInCents': int(order.fees * 100),
                'userCommissionInCents': int(order.splits.filter(user=self.app.user.pk).aggregate(total_commission=Sum('totalAmount')).get('total_commission', 0) * 100),
                'currency': 'BRL'  # undefined | BRL | USD | EUR | GBP | ARS | CAD
            },
        }

        return data

    @staticmethod
    def get_test_payload():
        return {
            'orderId': '87956abe-940e-4e8b-8a27-82c482920f64',
            'platform': 'Cakto',
            'paymentMethod': 'credit_card',
            'status': 'paid',
            'createdAt': '2024-09-18T23:12:59.347605+00:00',
            'approvedDate': '2024-09-18T23:12:59.347605+00:00',
            'refundedAt': None,
            'customer': {
                'name': 'Maria Oliveira',
                'email': '<EMAIL>',
                'phone': '11987654321',
                'document': '12345678901',
            },
            'product': {
                'id': '87956abe-940e-4e8b-8a27-82c482920f64',
                'name': 'Produto Exemplo',
                'planId': 'B8BcHrY',
                'planName': 'Plano Exemplo',
                'quantity': 1,
                'priceInCents': 2999,
            },
            'trackingParameters': {
                'src': '',
                'utm_source': 'newsletter',
                'utm_medium': 'email',
                'utm_campaign': 'summer_sale',
                'utm_term': 'discount',
                'utm_content': 'button_click',
                'sck': 'secret_key_example',
            },
            'commission': {
                'totalPriceInCents': 2999,
                'gatewayFeeInCents': 150,
                'userCommissionInCents': 200,
                'currency': 'BRL',
            },
        }

    def handle_send_data(self, payload, *args, **kwargs):
        test_event = kwargs.get('test_event')
        if not payload and not test_event:
            payload = self.get_payload()
            headers = self.get_headers(self.app)
        elif not payload and test_event:
            payload = self.get_test_payload()
            headers = self.get_headers(kwargs['app'])
        else:
            headers = self.get_headers(kwargs['app'])

        return payload, headers

    def send_event(self, payload, *args, **kwargs):
        url = 'https://api.utmify.com.br/api-credentials/orders'

        try:
            meta_data = kwargs.get('meta_data', '')
            if not meta_data:
                meta_data = self.processing_meta_data(**kwargs)

            payload, headers = self.handle_send_data(payload, **kwargs)

            response = requests.post(url, json=payload, headers=headers)
            self.save_event_in_history(request_data=response, payload=payload, meta=meta_data, new_event_history_id=kwargs.get('new_event_history_id', ''), error=None)
        except Exception as e:
            error_msg = str(e)
            self.save_event_in_history(payload=payload, meta=meta_data, new_event_history_id=kwargs.get('new_event_history_id', ''), error=error_msg)  # type:ignore

            return 400, error_msg

        return get_response_data(response)

    def boleto_gerado(self, *args, payload=None, **kwargs):
        return self.send_event(payload, **kwargs)

    def pix_gerado(self, *args, payload=None, **kwargs):
        return self.send_event(payload, **kwargs)

    def picpay_gerado(self, *args, payload=None, **kwargs):
        return self.send_event(payload, **kwargs)

    def purchase_approved(self, *args, payload=None, **kwargs):
        return self.send_event(payload, **kwargs)

    def purchase_refused(self, *args, payload=None, **kwargs):
        return self.send_event(payload, **kwargs)

    def refund(self, *args, payload=None, **kwargs):
        return self.send_event(payload, **kwargs)

    def chargeback(self, *args, payload=None, **kwargs):
        return self.send_event(payload, **kwargs)

    def subscription_created(self, *args, payload=None, **kwargs):
        return self.send_event(payload, **kwargs)

    def subscription_canceled(self, *args, payload=None, **kwargs):
        return self.send_event(payload, **kwargs)

    def subscription_renewed(self, *args, payload=None, **kwargs):
        return self.send_event(payload, **kwargs)

    def subscription_renewal_refused(self, *args, payload=None, **kwargs):
        ...

    def checkout_abandonment(self, *args, payload=None, **kwargs):
        ...

    def openfinance_nubank_gerado(self, *args, payload=None, **kwargs):
        return self.send_event(payload, **kwargs)

    # Check Methods
    @staticmethod
    def check_token(data: dict, request):
        token = data.get('fields', {}).get('token')
        if not token:
            raise ValidationError({'detail': 'O campo API Token deve ser informado.'}, status.HTTP_400_BAD_REQUEST)

        token_pattern = r'^[a-zA-Z0-9]{36}$'
        if not re.match(token_pattern, token):
            raise ValidationError(
                {'detail': 'Token API inválido, deve conter 36 caracteres (letras e números). Ex: 6hVxzQxRfD2lmRjb6HAYPaBUr35woQgEj9kp'},
                status.HTTP_400_BAD_REQUEST
            )

    @staticmethod
    def check_integration(data: dict, request):
        url = 'https://api.utmify.com.br/api-credentials/orders'
        headers = {
            'Content-Type': 'application/json',
            'x-api-token': data.get('fields', {}).get('token')  # type:ignore
        }

        data = {
            'orderId': 'testintegration',
            'platform': 'Cakto',  # ex: Kiwify, PerfectPay
            'paymentMethod': 'free_price',  # (credit_card, boleto, pix, paypal, free_price)
            'status': 'waiting_payment',  # (waiting_payment, paid, refused, refunded, chargedback)
            'createdAt': timezone.now().strftime('%Y-%m-%d %H:%M:%S'),  # data de criação do pagamento (utc time) YYYY-MM-DD HH:MM:SS
            'approvedDate': None,  # data de aprovação do pagamento (utc time) YYYY-MM-DD HH:MM:SS
            'refundedAt': None,  # | null, # data de estorno do pagamento (utc time) YYYY-MM-DD HH:MM:SS
            'customer': {
                'name': 'test integration',
                'email': '<EMAIL>',
                'phone': '5566999999999',
                'document': '12345678901',
            },
            'product': {
                'id': 'testintegration',
                'name': 'Test Integration',
                'planId': 'testintegration',
                'planName': 'Test Integration',
                'quantity': 1,
                'priceInCents': 0,
            },
            'trackingParameters': {
                'src': '',
                'utm_source': '',
                'utm_medium': '',
                'utm_campaign': '',
                'utm_term': '',
                'utm_content': '',
                'sck': '',
            },
            'commission': {
                'totalPriceInCents': 0,
                'gatewayFeeInCents': 0,
                'userCommissionInCents': 0,
                'currency': 'BRL'  # undefined | BRL | USD | EUR | GBP | ARS | CAD
            },
        }

        res = requests.post(url, json=data, headers=headers)
        if not status.is_success(res.status_code):
            raise ValidationError(
                {'detail': 'Erro ao se conectar com a API UTMify, verifique os dados informados.'},
                status.HTTP_400_BAD_REQUEST
            )
