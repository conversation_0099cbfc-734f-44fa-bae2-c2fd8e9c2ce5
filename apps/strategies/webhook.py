from uuid import uuid4

import requests

from apps.models import App
from apps.strategies.base import AppStrategy
from apps.utils import WEBHOOK_EXAMPLE_DATA, get_response_data
from checkout.serializers import CheckoutAbandonmentPublicSerializer
from gateway.serializers import OrderWebhookSerializer
from product.models import Product


class Webhook(AppStrategy):
    def initialize_variables(self, *args, **kwargs):
        self.app: App = kwargs['app']
        self.product: Product = (kwargs.get('order') or kwargs.get('offer')).product  # type:ignore
        self.event_custom_id = kwargs.get('event_custom_id', '')
        self.kwargs = kwargs

    def get_payload(self, test_event, app, event_custom_id):
        if test_event:
            data = self.get_test_payload()
        elif self.kwargs.get('order'):
            order = self.kwargs['order']
            order.refresh_from_db()
            data = OrderWebhookSerializer(order, context={'user': self.app.user}).data
        else:
            data = CheckoutAbandonmentPublicSerializer(self.kwargs['checkout_abandonment']).data

        return {
            'secret': app.fields.get('secret', ''),  # type:ignore
            'event': event_custom_id,
            'data': data,
        }

    @staticmethod
    def get_test_payload():
        return WEBHOOK_EXAMPLE_DATA

    def handle_send_data(self, payload, *args, **kwargs):
        test_event = kwargs.get('test_event')
        if not payload and not test_event:
            payload = self.get_payload(test_event, self.app, self.event_custom_id)
            url = self.app.url
        elif not payload and test_event:
            app = kwargs['app']
            event_custom_id = kwargs['event_custom_id']
            payload = self.get_payload(test_event, app, event_custom_id)
            url = app.url
        else:
            url = kwargs['app'].url

        return payload, url

    def send_event(self, payload, *args, **kwargs):
        meta_data = kwargs.get('meta_data', '')
        try:
            if not meta_data:
                meta_data = self.processing_meta_data(**kwargs)

            payload, url = self.handle_send_data(payload, **kwargs)

            headers = {
                'Content-Type': 'application/json',
                'User-Agent': 'CaktoBot/1.0',  # changed because some sites that block requests from python-requests User-Agent
            }

            response = requests.post(url, json=payload, headers=headers)  # type:ignore
            self.save_event_in_history(request_data=response, payload=payload, meta=meta_data, new_event_history_id=kwargs.get('new_event_history_id', ''), error=None)
        except Exception as e:
            error_msg = str(e)
            self.save_event_in_history(payload=payload, meta=meta_data, new_event_history_id=kwargs.get('new_event_history_id', ''), error=error_msg)

            return 400, error_msg

        return get_response_data(response)

    def boleto_gerado(self, *args, payload=None, **kwargs):
        return self.send_event(payload, **kwargs)

    def pix_gerado(self, *args, payload=None, **kwargs):
        return self.send_event(payload, **kwargs)

    def picpay_gerado(self, *args, payload=None, **kwargs):
        return self.send_event(payload, **kwargs)

    def purchase_approved(self, *args, payload=None, **kwargs):
        return self.send_event(payload, **kwargs)

    def purchase_refused(self, *args, payload=None, **kwargs):
        return self.send_event(payload, **kwargs)

    def refund(self, *args, payload=None, **kwargs):
        return self.send_event(payload, **kwargs)

    def chargeback(self, *args, payload=None, **kwargs):
        return self.send_event(payload, **kwargs)

    def subscription_created(self, *args, payload=None, **kwargs):
        return self.send_event(payload, **kwargs)

    def subscription_canceled(self, *args, payload=None, **kwargs):
        return self.send_event(payload, **kwargs)

    def subscription_renewed(self, *args, payload=None, **kwargs):
        return self.send_event(payload, **kwargs)

    def subscription_renewal_refused(self, *args, payload=None, **kwargs):
        return self.send_event(payload, **kwargs)

    def checkout_abandonment(self, *args, payload=None, **kwargs):
        return self.send_event(payload, **kwargs)

    def openfinance_nubank_gerado(self, *args, payload=None, **kwargs):
        return self.send_event(payload, **kwargs)

    def setup_secret(self, *args, **kwargs):
        secret = str(uuid4())
        self.serializer.validated_data['fields']['secret'] = secret
