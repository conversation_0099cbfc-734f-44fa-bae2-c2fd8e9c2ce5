import requests
from rest_framework import status
from rest_framework.exceptions import ValidationError

from apps.models import Platform
from apps.strategies.base import AppStrategy
from apps.utils import WEBHOOK_EXAMPLE_DATA, get_response_data
from gateway.serializers import OrderWebhookSerializer


class Spedy(AppStrategy):
    def initialize_variables(self, *args, **kwargs):
        self.app = kwargs['app']
        self.event_custom_id = kwargs.get('event_custom_id', '')
        self.kwargs = kwargs

    def get_payload(self, test_event, app, event_custom_id):
        if test_event:
            data = self.get_test_payload()
        else:
            order = self.kwargs['order']
            order.refresh_from_db()
            data = OrderWebhookSerializer(order, context={'user': self.app.user}).data

        payload = {
            'secret': app.fields.get('secret', ''),  # type:ignore
            'event': event_custom_id,
            'data': data,
        }
        if test_event:
            payload['testEvent'] = True
        return payload

    @staticmethod
    def get_test_payload():
        return WEBHOOK_EXAMPLE_DATA

    def handle_send_data(self, payload, *args, **kwargs):
        test_event = kwargs.get('test_event')
        if not payload and not test_event:
            payload = self.get_payload(test_event, self.app, self.event_custom_id)
            url = self.app.url
        elif not payload and test_event:
            app = kwargs['app']
            event_custom_id = kwargs['event_custom_id']
            payload = self.get_payload(test_event, app, event_custom_id)
            url = app.url
        else:
            url = kwargs['app'].url

        return payload, url

    def send_event(self, payload, *args, **kwargs):
        meta_data = kwargs.get('meta_data', '')
        try:
            if not meta_data:
                meta_data = self.processing_meta_data(**kwargs)

            payload, url = self.handle_send_data(payload, **kwargs)

            response = requests.post(url, json=payload, headers={'Content-Type': 'application/json'})
            self.save_event_in_history(request_data=response, payload=payload, meta=meta_data, new_event_history_id=kwargs.get('new_event_history_id', ''), error=None)
        except Exception as e:
            error_msg = str(e)
            self.save_event_in_history(payload=payload, meta=meta_data, new_event_history_id=kwargs.get('new_event_history_id', ''), error=error_msg)

            return 400, error_msg

        return get_response_data(response)

    def purchase_approved(self, *args, payload=None, **kwargs):
        return self.send_event(payload, **kwargs)

    def refund(self, *args, payload=None, **kwargs):
        return self.send_event(payload, **kwargs)

    def chargeback(self, *args, payload=None, **kwargs):
        return self.send_event(payload, **kwargs)

    def boleto_gerado(self, *args, payload=None, **kwargs):
        ...

    def pix_gerado(self, *args, payload=None, **kwargs):
        ...

    def picpay_gerado(self, *args, payload=None, **kwargs):
        ...

    def purchase_refused(self, *args, payload=None, **kwargs):
        ...

    def subscription_created(self, *args, payload=None, **kwargs):
        ...

    def subscription_canceled(self, *args, payload=None, **kwargs):
        ...

    def subscription_renewed(self, *args, payload=None, **kwargs):
        ...

    def subscription_renewal_refused(self, *args, payload=None, **kwargs):
        ...

    def checkout_abandonment(self, *args, payload=None, **kwargs):
        ...

    def openfinance_nubank_gerado(self, *args, payload=None, **kwargs):
        ...

    @staticmethod
    def check_secret(data: dict, request):
        secret = data.get('fields', {}).get('secret', '')
        if not secret:
            raise ValidationError({'detail': 'O campo "Chave secreta" deve ser informado.'}, status.HTTP_400_BAD_REQUEST)

    @staticmethod
    def check_integration(data: dict, request):
        url = data.get('url', '')
        payload = {
            'secret': data.get('fields', {}).get('secret', ''),
            'event': 'purchase_approved',
            'data': Spedy().get_test_payload(),
            'testEvent': True,
        }
        response = requests.post(url, headers={"Content-Type": "application/json"}, json=payload)
        if not status.is_success(response.status_code):
            raise ValidationError({'detail': 'Erro ao se conectar com a Spedy. Verifique a url informada.'}, status.HTTP_400_BAD_REQUEST)

    # Data Setup Methods
    def setup_events(self, *args, **kwargs):
        events = Platform.objects.get(type='spedy').events.all()  # type:ignore
        self.serializer.validated_data['events'] = events
