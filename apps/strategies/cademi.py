import re
import requests
from rest_framework import status
from rest_framework.exceptions import ValidationError

from apps.models import App, Platform
from apps.strategies.base import AppStrategy
from apps.utils import get_response_data
from gateway.models import Order
from product.models import Product

CADEMI_BASE_URL = 'https://{}.cademi.com.br/api/postback/custom'

class Cademi(AppStrategy):
    def initialize_variables(self, *args, **kwargs):
        self.app: App = kwargs['app']
        self.order: Order = kwargs['order']
        self.product: Product = self.order.product

    def get_payload(self, status='ativo'):
        payload = {
            'token': self.app.fields.get('token') or '',  # type:ignore
            'codigo': self.order.refId,
            'status': status,
            'produto_id': self.product.pk,
            'produto_nome': self.product.name,
            'valor': float(self.order.amount) if self.order.amount else 0,
            'cliente_email': self.order.customer.email,
            'cliente_nome': self.order.customer.name,
            'cliente_doc': self.order.customer.docNumber,
            'cliente_celular': self.order.customer.phone,
        }

        subscription = self.order.subscription
        if subscription:
            if subscription is not None:
                if subscription.status == 'active':
                    subs_status = 'ativo'
                elif subscription.status == 'canceled':
                    subs_status = 'cancelado'
                elif subscription.status == 'expired':
                    subs_status = 'cancelado'
                else:
                    subs_status = 'ativo'
                payload.update({
                    'recorrencia_id': subscription.pk,
                    'recorrencia_status': subs_status,
                })

        return payload

    @staticmethod
    def get_test_payload(app):
        payload = {
            'token': app.fields.get('token') or '',
            'codigo': 'TEST_REF_ID_001',
            'status': 'ativo',
            'produto_id': 1,
            'produto_nome': 'Produto Teste',
            'valor': 1000,
            'cliente_email': '<EMAIL>',
            'cliente_nome': 'Cliente Teste',
            'cliente_doc': '12345678900',
            'cliente_celular': '+5511999999999',
        }

        return payload

    def get_headers(self, app):
        return {'Authorization': 'Bearer ' + (app.fields.get('api_key') or '')}  # type:ignore

    def handle_send_data(self, payload, status, *args, **kwargs):
        test_event = kwargs.get('test_event')
        if not payload and not test_event:
            headers = self.get_headers(self.app)
            payload = self.get_payload(status=status)
            url = CADEMI_BASE_URL.format(self.app.fields.get('domain', ''))
        elif not payload and test_event:
            app = kwargs['app']
            headers = self.get_headers(app)
            payload = self.get_test_payload(app)
            url = CADEMI_BASE_URL.format(app.fields.get('domain', ''))
        else:
            app = kwargs['app']
            headers = self.get_headers(app)
            url = CADEMI_BASE_URL.format(app.fields.get('domain', ''))

        return payload, url, headers

    def send_event(self, payload, *args, status='aprovado', **kwargs):
        error_msg = None
        meta_data = kwargs.get('meta_data', '')
        try:
            if not meta_data:
                meta_data = self.processing_meta_data(**kwargs)

            payload, url, headers = self.handle_send_data(payload, status=status, **kwargs)

            response = requests.post(url, data=payload, headers=headers)

            self.save_event_in_history(request_data=response, payload=payload, meta=meta_data, new_event_history_id=kwargs.get('new_event_history_id', ''), error=None)
        except Exception as e:
            error_msg = str(e)
            self.save_event_in_history(payload=payload, meta=meta_data, new_event_history_id=kwargs.get('new_event_history_id', ''), error=str(e))

            return 400, error_msg

        return get_response_data(response)

    def purchase_approved(self, *args, payload=None, **kwargs):
        return self.send_event(payload, status='aprovado', **kwargs)

    def refund(self, *args, payload=None, **kwargs):
        return self.send_event(payload, status='cancelado', **kwargs)

    def chargeback(self, *args, payload=None, **kwargs):
        return self.send_event(payload, status='cancelado', **kwargs)

    def subscription_created(self, *args, payload=None, **kwargs):
        return self.chargeback(payload, status='aprovado', **kwargs)

    def subscription_canceled(self, *args, payload=None, **kwargs):
        return self.chargeback(payload, status='cancelado', **kwargs)

    def subscription_renewed(self, *args, payload=None, **kwargs):
        return self.purchase_approved(payload, status='aprovado', **kwargs)

    def subscription_renewal_refused(self, *args, payload=None, **kwargs):
        return self.purchase_approved(payload, status='aprovado', **kwargs)

    def purchase_refused(self, *args, payload=None, **kwargs):
        ...

    def pix_gerado(self, *args, payload=None, **kwargs):
        ...

    def picpay_gerado(self, *args, payload=None, **kwargs):
        ...

    def boleto_gerado(self, *args, payload=None, **kwargs):
        ...

    def checkout_abandonment(self, *args, payload=None, **kwargs):
        ...

    def openfinance_nubank_gerado(self, *args, payload=None, **kwargs):
        ...

    # Check methods
    @staticmethod
    def check_if_products_are_external(data: dict, request):
        products: list[Product] = data.get('products', [])
        for product in products:
            if not product.has_contentDelivery('external'):
                raise ValidationError({'detail': f'O produto {product.name} não está configurado para utilizar área de membros externa.'}, status.HTTP_400_BAD_REQUEST)

    @staticmethod
    def check_domain(data: dict, request):
        domain = data.get('fields', {}).get('domain', '')
        pattern = r'^[a-z]+$'
        if not domain:
            raise ValidationError({'detail': 'O domínio Cademí é obrigatório.'}, status.HTTP_400_BAD_REQUEST)
        elif not bool(re.match(pattern, domain)):
            raise ValidationError({'detail': 'O domínio Cademí deve conter apenas letras, por exemplo: "suacademi".'}, status.HTTP_400_BAD_REQUEST)

    @staticmethod
    def check_integration(data: dict, request):
        for product in data.get('products', []):
            headers = {'Authorization': 'Bearer ' + (data.get('fields', {}).get('api_key'))}
            payload = {
                'token': data.get('fields', {}).get('token') or '',
                'codigo': 'CaktoTesteIntegração',
                'status': 'aprovado',
                'produto_id': product.pk,
                'produto_nome': product.name,
                'valor': 1,
                'cliente_email': product.supportEmail or product.user.email,
            }
            domain = data.get('fields', {}).get('domain', '')
            res = requests.post(CADEMI_BASE_URL.format(domain), data=payload, headers=headers)
            if 'text/html' in res.headers['Content-Type']:
                erro = 'Verifique se o domínio Cademí está correto.'
            else:
                erro = (res.json().get('data').get('carga').get('erro') or '').lstrip('Erro ao construir carga:  ')
            if erro:
                raise ValidationError({'detail': f'Erro na Cademí: {erro}'}, status.HTTP_400_BAD_REQUEST)

    # Data Setup methods
    def setup_events(self, *args, **kwargs):
        events = Platform.objects.get(type='cademi').events.all()  # type:ignore
        self.serializer.validated_data['events'] = events
