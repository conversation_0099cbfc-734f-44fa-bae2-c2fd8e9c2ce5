import re

import requests
from rest_framework import status
from rest_framework.exceptions import ValidationError

from apps.models import App
from apps.strategies.base import AppStrategy
from apps.utils import WEBHOOK_EXAMPLE_DATA, get_response_data
from checkout.serializers import CheckoutAbandonmentPublicSerializer
from gateway.serializers import OrderWebhookSerializer
from product.models import Product


class SmsFunnel(AppStrategy):
    def initialize_variables(self, *args, **kwargs):
        self.app: App = kwargs['app']
        self.product: Product = (kwargs.get('order') or kwargs.get('offer')).product  # type:ignore
        self.event_custom_id = kwargs.get('event_custom_id', '')
        self.kwargs = kwargs

    def get_payload(self, app, event_custom_id):
        if self.kwargs.get('order'):
            order = self.kwargs['order']
            order.refresh_from_db()
            data = OrderWebhookSerializer(order, context={'user': app.user}).data
        else:
            data = CheckoutAbandonmentPublicSerializer(self.kwargs['checkout_abandonment']).data

        return {
            'event': event_custom_id,
            'data': data,
        }

    @staticmethod
    def get_test_payload():
        return WEBHOOK_EXAMPLE_DATA

    def handle_send_data(self, payload, *args, **kwargs):
        test_event = kwargs.get('test_event')
        if not payload and not test_event:
            payload = self.get_payload(self.app, self.event_custom_id)
            url = self.app.url
        elif not payload and test_event:
            app = kwargs['app']
            payload = self.get_test_payload()
            url = app.url
        else:
            url = kwargs['app'].url

        return payload, url

    def send_event(self, payload, *args, **kwargs):
        meta_data = kwargs.get('meta_data', '')
        try:
            if not meta_data:
                meta_data = self.processing_meta_data(**kwargs)

            payload, url = self.handle_send_data(payload, **kwargs)

            response = requests.post(url, json=payload, headers={'Content-Type': 'application/json'})  # type:ignore
            self.save_event_in_history(request_data=response, payload=payload, meta=meta_data, new_event_history_id=kwargs.get('new_event_history_id', ''), error=None)
        except Exception as e:
            error_msg = str(e)
            self.save_event_in_history(payload=payload, meta=meta_data, new_event_history_id=kwargs.get('new_event_history_id', ''), error=error_msg)

            return 400, error_msg

        return get_response_data(response)

    def boleto_gerado(self, *args, payload=None, **kwargs):
        return self.send_event(payload, **kwargs)

    def pix_gerado(self, *args, payload=None, **kwargs):
        return self.send_event(payload, **kwargs)

    def picpay_gerado(self, *args, payload=None, **kwargs):
        return self.send_event(payload, **kwargs)

    def purchase_approved(self, *args, payload=None, **kwargs):
        return self.send_event(payload, **kwargs)

    def purchase_refused(self, *args, payload=None, **kwargs):
        return self.send_event(payload, **kwargs)

    def refund(self, *args, payload=None, **kwargs):
        return self.send_event(payload, **kwargs)

    def chargeback(self, *args, payload=None, **kwargs):
        return self.send_event(payload, **kwargs)

    def subscription_created(self, *args, payload=None, **kwargs):
        return self.send_event(payload, **kwargs)

    def subscription_canceled(self, *args, payload=None, **kwargs):
        return self.send_event(payload, **kwargs)

    def subscription_renewed(self, *args, payload=None, **kwargs):
        return self.send_event(payload, **kwargs)

    def subscription_renewal_refused(self, *args, payload=None, **kwargs):
        return self.send_event(payload, **kwargs)

    def checkout_abandonment(self, *args, payload=None, **kwargs):
        return self.send_event(payload, **kwargs)

    def openfinance_nubank_gerado(self, *args, payload=None, **kwargs):
        return self.send_event(payload, **kwargs)

    # Check Methods
    @staticmethod
    def check_url(data: dict, request):
        url_pattern = r'^https:\/\/v[12]\.smsfunnel\.com\.br\/integrations\/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
        url = data.get('url', '')
        if not url:
            raise ValidationError({'detail': 'o campo URL deve ser informado.'}, status.HTTP_400_BAD_REQUEST)
        if not re.match(url_pattern, url):
            error_message = 'o campo URL deve ser uma url válida. ex.:' \
                            'https://v1.smsfunnel.com.br/integrations/6a65ab60-9186-49cd-9a34-18cf7a0f44f1 ou ' \
                            'https://v2.smsfunnel.com.br/integrations/6a65ab60-9186-49cd-9a34-18cf7a0f44f1'
            raise ValidationError({'detail': error_message}, status.HTTP_400_BAD_REQUEST)

    @staticmethod
    def check_integration(data: dict, request):
        payload = {'event': 'integration_test', 'data': {}}
        res = requests.post(data.get('url', ''), json=payload, headers={'Content-Type': 'application/json'})
        if not status.is_success(res.status_code):
            raise ValidationError({'detail': 'Erro ao realizar integração, verifique se os dados estão corretos.'}, status.HTTP_400_BAD_REQUEST)
