from abc import ABC, abstractmethod

from rest_framework import status
from rest_framework.exceptions import ValidationError
from rq import get_current_job

from apps.models import EventHistory
from apps.utils import get_response_data


class AppStrategy(ABC):
    @abstractmethod
    def initialize_variables(self, payload=None, *args, **kwargs):
        ...

    @abstractmethod
    def boleto_gerado(self, *args, payload=None, **kwargs):
        ...

    @abstractmethod
    def pix_gerado(self, *args, payload=None, **kwargs):
        ...

    @abstractmethod
    def picpay_gerado(self, *args, payload=None, **kwargs):
        ...

    @abstractmethod
    def purchase_approved(self, *args, payload=None, **kwargs):
        ...

    @abstractmethod
    def purchase_refused(self, *args, payload=None, **kwargs):
        ...

    @abstractmethod
    def refund(self, *args, payload=None, **kwargs):
        ...

    @abstractmethod
    def chargeback(self, *args, payload=None, **kwargs):
        ...

    @abstractmethod
    def subscription_created(self, *args, payload=None, **kwargs):
        ...

    @abstractmethod
    def subscription_canceled(self, *args, payload=None, **kwargs):
        ...

    @abstractmethod
    def subscription_renewed(self, *args, payload=None, **kwargs):
        ...

    @abstractmethod
    def subscription_renewal_refused(self, *args, payload=None, **kwargs):
        ...

    @abstractmethod
    def checkout_abandonment(self, *args, payload=None, **kwargs):
        ...

    @abstractmethod
    def openfinance_nubank_gerado(self, *args, payload=None, **kwargs):
        ...

    @staticmethod
    def check_if_user_are_products_owner(data: dict, request):
        for product in data.get('products', []):
            if product.user != request.user:
                raise ValidationError({'detail': f'Você não é dono do produto "{product.name}".'}, status.HTTP_400_BAD_REQUEST)

    def get_event_method(self, event_custom_id: str):
        return getattr(self, event_custom_id, None)

    def validate(self, *args, **kwargs):
        for attr_name in dir(self):
            if attr_name.startswith('check_'):
                getattr(self, attr_name)(data=kwargs['data'], request=kwargs['request'])

    def perform_data_setup(self, *args, **kwargs):
        self.serializer = kwargs['serializer']
        for attr_name in dir(self):
            if attr_name.startswith('setup_'):
                getattr(self, attr_name)(*args, **kwargs)

    def processing_meta_data(self, *args, **kwargs):
        meta_data = {key: str(value) for key, value in kwargs.items()}
        return meta_data

    def save_event_in_history(self, *args, **kwargs):
        payload = kwargs['payload']
        event_history_id = kwargs.get('new_event_history_id', '')

        if not event_history_id:
            job = get_current_job()
            if not job:
                raise ValueError("O job do evento atual não foi encontrado.")
            history = EventHistory.objects.filter(job=job.id).first()
            if not history:
                return
        else:
            history = EventHistory.objects.get(id=event_history_id)

        try:
            history.payload = payload
            if 'request_data' in kwargs:
                history.event_status, history.response = get_response_data(kwargs['request_data'])
                history.url = kwargs['request_data'].request.url
            history.meta_data = kwargs.get('meta')
            history.errors = kwargs.get('error')
            history.save()
        except Exception as e:
            history.meta_data = kwargs.get('meta')
            history.errors = str(e)
            history.save()
            raise e
