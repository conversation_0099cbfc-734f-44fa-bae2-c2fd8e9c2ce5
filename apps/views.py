import requests
from django.shortcuts import get_object_or_404
from rest_framework import filters, generics, status, viewsets
from rest_framework.decorators import action
from rest_framework.response import Response

from apps.services.app_factory import AppFactory
from product.models import Product

from .models import App, Event, EventHistory, Platform
from .serializers import AppSerializer, AppSerializerRead, EventHistorySerializer, PlatformSerializer


class PlatformAPI(viewsets.ModelViewSet):
    scope = 'apps'
    serializer_class = PlatformSerializer

    def get_queryset(self):
        return Platform.objects.filter(active=True)

class AppPlatformAPI(viewsets.ModelViewSet):
    scope = 'apps'
    serializer_class = AppSerializer
    filter_backends = [filters.SearchFilter]
    search_fields = ['name', 'url']

    def get_queryset(self):
        return App.objects.filter(user=self.request.user)

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset().filter(platform=kwargs['pk']))
        if request.GET.get('products'):
            queryset = queryset.filter(products__id__in=request.GET.get('products').split(','))
        serializer = AppSerializerRead(queryset, many=True)
        return Response({
            'count': len(serializer.data),
            'results': serializer.data
        })

    def create(self, request, *args, **kwargs):
        platform = get_object_or_404(Platform, pk=kwargs['pk'])
        if request.data.get('events'):
            request.data['events'] = Event.objects.filter(custom_id__in=request.data['events']).values_list('id', flat=True)
        serializer = AppSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        strategy = AppFactory.get_strategy(platform.type, init_vars=False)  # type:ignore
        strategy.validate(platform=platform, data=serializer.validated_data, request=request)

        products = Product.objects.filter(id__in=request.data.get('products'), user=request.user)
        # Check if it is product owner
        if not products.exists():
            return Response({'detail': 'Apenas o produtor pode criar apps.'}, status=status.HTTP_403_FORBIDDEN)

        strategy.perform_data_setup(platform=platform, serializer=serializer)

        serializer.save(user=request.user, platform=platform)

        return Response(AppSerializerRead(serializer.instance).data)

    def update(self, request, *args, **kwargs):
        instance = self.get_object()
        if request.data.get('events'):
            request.data['events'] = Event.objects.filter(custom_id__in=request.data['events']).values_list('id', flat=True)
        serializer = self.get_serializer(instance, data=request.data)
        serializer.is_valid(raise_exception=True)

        strategy = AppFactory.get_strategy(instance.platform.type, init_vars=False)  # type:ignore
        strategy.validate(platform=instance.platform, data=serializer.validated_data, request=request)

        serializer.save()
        return Response(AppSerializerRead(serializer.instance).data)

    def destroy(self, request, *args, **kwargs):
        super().destroy(request, *args, **kwargs)
        return Response({'detail': 'Deletado com successo'}, status=200)

class MemberkitRedirectAPI(generics.GenericAPIView):
    scope = 'apps'

    def get(self, request, *args, **kwargs):
        api_key = request.query_params.get('api_key')
        endpoint = request.query_params.get('endpoint')
        url = f'https://memberkit.com.br/api/v1/{endpoint}?api_key={api_key}'
        res = requests.get(url)
        if status.is_success(res.status_code):
            return Response(res.json())
        return Response({'detail': 'Erro ao buscar dados'}, status=400)

class ActiveCampaignRedirectAPI(generics.GenericAPIView):
    scope = 'apps'

    def get(self, request, *args, **kwargs):
        url = request.query_params.get('url')
        endpoint = request.query_params.get('endpoint')
        token = request.query_params.get('token')

        if not endpoint == 'lists' and not endpoint == 'tags':
            return Response({'detail': '"endpoint" inválido'}, status=400)
        if not token:
            return Response({'detail': '"token" inválido'}, status=400)
        if not url:
            return Response({'detail': '"url" inválido'}, status=400)

        url = f'{url}/api/3/{endpoint}'

        headers = {'Api-Token': token}

        res = requests.get(url, headers=headers)
        if status.is_success(res.status_code):
            return Response(res.json())
        return Response({'detail': 'Erro ao buscar dados'}, status=400)

class AppHistoryEvents(viewsets.GenericViewSet, generics.ListAPIView):
    scope = 'apps'
    serializer_class = EventHistorySerializer

    def get_queryset(self):
        return EventHistory.objects.filter(app=self.kwargs['app_pk'], app__user=self.request.user)

    @action(detail=True, methods=['post'])
    def resend(self, request, *args, **kwargs):
        event_history_id = kwargs.get('event_history_pk')
        if event_history_id:
            event_data = EventHistory.objects.get(id=event_history_id)
            strategy = AppFactory.get_strategy(event_data.app.platform.type, init_vars=False)  # type:ignore
            event_method = strategy.get_event_method(event_data.event_id)
            new_event = EventHistory.objects.create(app=event_data.app, event_id=event_data.event_id)

            if event_method is None:
                return Response({'detail': 'Evento inválido'}, status=400)

            response_status_code, response_data = event_method(payload=event_data.payload, app=event_data.app, meta_data=event_data.meta_data, new_event_history_id=new_event.id)
            if status.is_success(response_status_code):
                return Response({'detail': 'Evento reenviado com sucesso'}, status=200)
            return Response({'detail': 'Erro ao reenviar evento', 'response': response_data}, status=response_status_code)
        return Response({'detail': 'ID do evento inválido'}, status=400)

class AppTestEvent(viewsets.GenericViewSet):
    scope = 'apps'

    def create(self, request, *args, **kwargs):
        app_id = kwargs.get('app_pk')
        event = request.query_params.get('event', '')
        if not event:
            return Response({'detail': 'ID do evento inválido'}, status=400)

        app = get_object_or_404(App, id=app_id, user=request.user)
        strategy = AppFactory.get_strategy(app.platform.type, init_vars=False)  # type:ignore
        event_method = strategy.get_event_method(event)

        if event_method is None:
            return Response({'detail': 'Evento inválido'}, status=400)

        new_event = EventHistory.objects.create(app=app, event_id=event)
        response_status_code, response_data = event_method(app=app, new_event_history_id=new_event.id, test_event=True, event_custom_id=event)

        if status.is_success(response_status_code):
            return Response({'detail': 'Evento de teste enviado com sucesso'}, status=200)
        return Response({'detail': 'Erro ao enviar evento de teste', 'response': response_data}, status=response_status_code)
