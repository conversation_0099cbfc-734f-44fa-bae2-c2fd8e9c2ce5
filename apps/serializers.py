from rest_framework import serializers
from .models import App, Platform, Event, EventHistory
from product.serializers import ProductSerializer

class EventSerializer(serializers.ModelSerializer):
    class Meta:
        model = Event
        fields = ('id', 'name', 'custom_id')

class AppSerializer(serializers.ModelSerializer):
    class Meta:
        model = App
        fields = ('id', 'name', 'url', 'products', 'events', 'fields')

class AppSerializerRead(serializers.ModelSerializer):
    products = ProductSerializer(many=True, read_only=True)
    events = EventSerializer(many=True, read_only=True)

    class Meta:
        model = App
        fields = ('id', 'name', 'url', 'products', 'events', 'fields', 'createdAt', 'updatedAt')


class PlatformSerializer(serializers.ModelSerializer):
    events = EventSerializer(many=True, read_only=True)

    class Meta:
        model = Platform
        fields = ('id', 'name', 'type', 'events', 'createdAt', 'updatedAt')

class EventHistorySerializer(serializers.ModelSerializer):
    app = AppSerializer(read_only=True)
    event_name = serializers.SerializerMethodField()

    class Meta:
        model = EventHistory
        fields = (
            'id',
            'app',
            'url',
            'payload',
            'response',
            'event_id',
            'event_name',
            'event_status',
            'sentAt',
        )

    def get_event_name(self, obj):
        event = Event.get_cached_event(obj.event_id)
        return event.name if event else None
