from django.db import migrations

EVENT_DATA = {'custom_id': 'subscription_renewal_refused', 'name': 'Renovação de assinatura recusada'}
PLATFORMS_TO_ADD = ['active_campaign', 'smsfunnel', 'voxuy', 'webhook']

def add_event(apps, schema_editor):
    Event = apps.get_model('apps', 'Event')
    Platform = apps.get_model('apps', 'Platform')

    event, _ = Event.objects.get_or_create(**EVENT_DATA)

    platforms = Platform.objects.filter(type__in=PLATFORMS_TO_ADD)

    for platform in platforms:
        event.platform.add(platform)

def remove_event(apps, schema_editor):
    Event = apps.get_model('apps', 'Event')
    Event.objects.filter(**EVENT_DATA).delete()


class Migration(migrations.Migration):

    dependencies = [
        ('apps', '0033_add_subscription_created_event'),
    ]

    operations = [
        migrations.RunPython(add_event, reverse_code=remove_event),
    ]
