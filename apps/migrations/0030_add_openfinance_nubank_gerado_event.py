# Generated by Django 4.2.5 on 2025-02-19 18:32

from django.db import migrations

EVENT_ID = 'openfinance_nubank_gerado'
EVENT_NAME = 'Nubank gerado'

def add_openfinance_nubank(apps, schema_editor):
    Event = apps.get_model('apps', 'Event')
    Platform = apps.get_model('apps', 'Platform')

    event_data = {'custom_id': EVENT_ID, 'name': EVENT_NAME}
    event, _ = Event.objects.get_or_create(**event_data)

    platform_ids = [
        'active_campaign', 'smsfunnel', 'utmify', 'voxuy', 'webhook'
    ]

    platforms_to_add = Platform.objects.filter(type__in=platform_ids)

    for platform in platforms_to_add:
        event.platform.add(platform)

def remove_openfinance_nubank(apps, schema_editor):
    Event = apps.get_model('apps', 'Event')
    Event.objects.filter(custom_id=EVENT_ID).delete()


class Migration(migrations.Migration):

    dependencies = [
        ('apps', '0029_add_spedy_refund_chargeback_events'),
    ]

    operations = [
        migrations.RunPython(
            add_openfinance_nubank,
            remove_openfinance_nubank
        ),
    ]
