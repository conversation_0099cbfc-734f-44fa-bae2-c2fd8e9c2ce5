# Generated by Django 4.2.5 on 2024-01-21 15:10

from django.db import migrations


def alter_cademi_name(apps, schema_editor):
    Platform = apps.get_model('apps', 'Platform')

    platform = Platform.objects.filter(name='Cade<PERSON>').first()
    if platform:
        platform.name = 'Cademí'
        platform.save()

class Migration(migrations.Migration):

    dependencies = [
        ('apps', '0008_remove_app_api_key_alter_app_fields_alter_app_token'),
    ]

    operations = [
        migrations.RunPython(alter_cademi_name, reverse_code=migrations.RunPython.noop),
    ]
