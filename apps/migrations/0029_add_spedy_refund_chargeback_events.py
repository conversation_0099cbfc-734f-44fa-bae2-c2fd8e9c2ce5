# Generated by Django 4.2.5 on 2025-01-22 11:53

from django.db import migrations

def add_spedy_events(apps, schema_editor):
    Event = apps.get_model('apps', 'Event')
    Platform = apps.get_model('apps', 'Platform')

    platform, created = Platform.objects.get_or_create(
        type='spedy',
        defaults={
            'name': 'Spedy',
            'type': 'spedy',
        }
    )

    events_data = [
        {"custom_id": "refund", "name": "<PERSON><PERSON><PERSON><PERSON>"},
        {"custom_id": "chargeback", "name": "Chargeback"},
    ]

    for event_data in events_data:
        event = Event.objects.filter(**event_data, platform=platform).first()
        if event is None:
            event, created = Event.objects.get_or_create(**event_data)
            event.platform.add(platform)

    events = platform.events.all()

    for app in platform.app_set.all():
        app.events.set(events)

def remove_spedy_events(apps, schema_editor):
    Event = apps.get_model('apps', 'Event')
    Platform = apps.get_model('apps', 'Platform')

    platform = Platform.objects.get(type='spedy')

    events = Event.objects.filter(custom_id__in=['refund', 'chargeback'])

    for event in events:
        event.platform.remove(platform)

    for app in platform.app_set.all():
        app.events.remove(*events)

class Migration(migrations.Migration):

    dependencies = [
        ('apps', '0028_add_active_campaign'),
    ]

    operations = [
        migrations.RunPython(add_spedy_events, reverse_code=remove_spedy_events),
    ]
