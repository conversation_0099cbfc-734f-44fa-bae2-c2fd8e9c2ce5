# Generated by Django 4.2.5 on 2024-02-19 17:29

from django.db import migrations

def add_type_on_platforms(apps, schema_editor):
    Platform = apps.get_model('apps', 'Platform')

    cademi = Platform.objects.filter(name='Cadem<PERSON>').first()
    cademi.type = 'cademi'
    cademi.save()

    voxuy = Platform.objects.filter(name='Voxuy').first()
    voxuy.type = 'voxuy'
    voxuy.save()

    webhook = Platform.objects.filter(name='Webhook').first()
    webhook.type = 'webhook'
    webhook.save()


class Migration(migrations.Migration):

    dependencies = [
        ('apps', '0011_platform_type'),
    ]

    operations = [
        migrations.RunPython(add_type_on_platforms, reverse_code=migrations.RunPython.noop),
    ]
