# Generated by Django 4.2.5 on 2025-02-19 19:02

from django.db import migrations

EVENT_ID = 'picpay_gerado'
EVENT_NAME = 'PicPay gerado'

def add_event(apps, schema_editor):
    Platform = apps.get_model('apps', 'Platform')
    Event = apps.get_model('apps', 'Event')

    picpay_gerado = Event.objects.filter(custom_id='picpay_gerado').first()

    active_campaign = Platform.objects.filter(type='active_campaign').first()

    if not picpay_gerado or not active_campaign:
        return

    picpay_gerado.platform.add(active_campaign)

def remove_event(apps, schema_editor):
    Event = apps.get_model('apps', 'Event')
    Platform = apps.get_model('apps', 'Platform')

    picpay_gerado = Event.objects.filter(custom_id='picpay_gerado').first()
    active_campaign = Platform.objects.filter(type='active_campaign').first()

    if not picpay_gerado or not active_campaign:
        return

    picpay_gerado.platform.remove(active_campaign)

class Migration(migrations.Migration):

    dependencies = [
        ('apps', '0030_add_openfinance_nubank_gerado_event'),
    ]

    operations = [
        migrations.RunPython(
            add_event,
            remove_event
        )
    ]
