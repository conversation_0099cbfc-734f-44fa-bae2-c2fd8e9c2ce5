# Generated by Django 4.2.5 on 2024-08-20 20:40

from django.db import migrations


def add_spedy(apps, schema_editor):
    Event = apps.get_model('apps', 'Event')
    Platform = apps.get_model('apps', 'Platform')

    platform, created = Platform.objects.get_or_create(
        type='spedy',
        defaults={
            'name': 'Spedy',
            'type': 'spedy',
        }
    )

    events_data = [
        {"custom_id": "purchase_approved", "name": "Compra aprovada"},
    ]

    for event_data in events_data:
        event = Event.objects.filter(**event_data, platform=platform).first()
        if event is None:
            event, created = Event.objects.get_or_create(**event_data)
            event.platform.add(platform)


class Migration(migrations.Migration):

    dependencies = [
        ('apps', '0025_event_history'),
    ]

    operations = [
        migrations.RunPython(add_spedy, reverse_code=migrations.RunPython.noop),
    ]
