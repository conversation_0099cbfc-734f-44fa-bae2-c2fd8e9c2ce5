# Generated by Django 4.2.5 on 2023-12-13 01:28

from django.db import migrations

def add_default_events(apps, schema_editor):
    Event = apps.get_model('apps', 'Event')
    Platform = apps.get_model('apps', 'Platform')

    # create platform webhook
    platform, created = Platform.objects.get_or_create(name='Webhook')

    # Specify the events to be added
    events_data = [
        {"name": "Boleto gerado", "custom_id": "boleto_gerado"},
        {"custom_id": "pix_gerado", "name": "Pix gerado"},
        {"custom_id": "purchase_approved", "name": "Compra aprovada"},
        {"custom_id": "purchase_refused", "name": "Compra recusada"},
        {"custom_id": "refund", "name": "Reembolso"},
        {"custom_id": "chargeback", "name": "Chargeback"},
        {"custom_id": "subscription_canceled", "name": "Assinatura cancelada"},
        {"custom_id": "subscription_renewed", "name": "Assinatura renovada"},
    ]

    # Add events to the database
    for event_data in events_data:
        Event.objects.create(**event_data, platform=platform)


class Migration(migrations.Migration):

    dependencies = [
        ('apps', '0001_initial'),
    ]

    operations = [
        migrations.RunPython(add_default_events),
    ]
