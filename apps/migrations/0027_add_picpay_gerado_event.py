# Generated by Django 4.2.5 on 2024-09-12 18:46

from django.db import migrations


def add_picpay(apps, schema_editor):
    create_picpay_event(apps)
    create_picpay_paymentMethod(apps)

def create_picpay_event(apps):
    Event = apps.get_model('apps', 'Event')
    Platform = apps.get_model('apps', 'Platform')

    picpay_event_data = {'custom_id': 'picpay_gerado', 'name': 'PicPay gerado'}
    picpay_gerado, _ = Event.objects.get_or_create(**picpay_event_data)

    platforms_to_add = Platform.objects.filter(type__in=['smsfunnel', 'utmify', 'voxuy', 'webhook'])

    for platform in platforms_to_add:
        picpay_gerado.platform.add(platform)

def create_picpay_paymentMethod(apps):
    PaymentMethod = apps.get_model('product', 'PaymentMethod')
    PaymentMethod.objects.get_or_create(name='PicPay', type='picpay')

def delete_picpay_event(apps):
    Event = apps.get_model('apps', 'Event')
    picpay_gerado = Event.objects.filter(custom_id='picpay_gerado').first()
    if picpay_gerado:
        picpay_gerado.delete()

def remove_picpay(apps, schema_editor):
    delete_picpay_event(apps)


class Migration(migrations.Migration):

    dependencies = [
        ('apps', '0026_add_spedy'),
        ('product', '0096_add_product_contentDeliveries'),
    ]

    operations = [
        migrations.RunPython(add_picpay, remove_picpay),
    ]
