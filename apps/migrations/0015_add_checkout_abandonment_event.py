# Generated by Django 4.2.5 on 2024-03-06 16:40

from django.db import migrations

def add_checkout_abandonment_event(apps, schema_editor):
    Event = apps.get_model('apps', 'Event')
    Platform = apps.get_model('apps', 'Platform')

    voxuy_platform = Platform.objects.get(type='voxuy')
    webhook_platform = Platform.objects.get(type='webhook')

    event = Event.objects.create(
        name='Abandono de Checkout',
        custom_id='checkout_abandonment',
    )

    event.platform.add(voxuy_platform, webhook_platform)


class Migration(migrations.Migration):

    dependencies = [
        ('apps', '0014_alter_app_url'),
    ]

    operations = [
        migrations.RunPython(add_checkout_abandonment_event, reverse_code=migrations.RunPython.noop),
    ]
