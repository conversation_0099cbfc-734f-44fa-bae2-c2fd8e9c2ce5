# Generated by Django 4.2.5 on 2024-01-20 15:41

from django.db import migrations


def alter_webhook(apps, schema_editor):
    Event = apps.get_model('apps', 'Event')
    Platform = apps.get_model('apps', 'Platform')

    platform, created = Platform.objects.get_or_create(name='Webhook')
    platform.class_name = 'apps.services.WebhookEvents'
    platform.save()

    events_data = [
        {"name": "Boleto gerado", "custom_id": "boleto_gerado"},
        {"custom_id": "pix_gerado", "name": "Pix gerado"},
        {"custom_id": "purchase_approved", "name": "Compra aprovada"},
        {"custom_id": "purchase_refused", "name": "Compra recusada"},
        {"custom_id": "refund", "name": "<PERSON><PERSON><PERSON><PERSON>"},
        {"custom_id": "chargeback", "name": "Chargeback"},
        {"custom_id": "subscription_canceled", "name": "Assinatura cancelada"},
        {"custom_id": "subscription_renewed", "name": "Assinatura renovada"},
    ]

    for event_data in events_data:
        event = Event.objects.filter(**event_data).first()
        if event is None:
            event, created = Event.objects.get_or_create(**event_data)
        event.platform.add(platform)

class Migration(migrations.Migration):

    dependencies = [
        ('apps', '0005_add_cademi_app'),
    ]

    operations = [
        migrations.RunPython(alter_webhook, reverse_code=migrations.RunPython.noop),
    ]
