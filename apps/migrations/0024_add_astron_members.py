# Generated by Django 4.2.5 on 2024-07-10 16:30

from django.db import migrations


def add_astron_members(apps, schema_editor):
    Event = apps.get_model('apps', 'Event')
    Platform = apps.get_model('apps', 'Platform')

    platform, created = Platform.objects.get_or_create(
        type='astron_members',
        defaults={
            'name': 'Astron Members',
            'type': 'astron_members',
        }
    )

    events_data = [
        {"custom_id": "purchase_approved", "name": "Compra aprovada"},
        {"custom_id": "refund", "name": "<PERSON>embol<PERSON>"},
        {"custom_id": "chargeback", "name": "Chargeback"},
        {"custom_id": "subscription_canceled", "name": "Assinatura cancelada"},
        {"custom_id": "subscription_renewed", "name": "Assinatura renovada"},
    ]

    for event_data in events_data:
        event = Event.objects.filter(**event_data, platform=platform).first()
        if event is None:
            event, created = Event.objects.get_or_create(**event_data)
            event.platform.add(platform)

class Migration(migrations.Migration):

    dependencies = [
        ('apps', '0023_remove_cademi_events'),
    ]

    operations = [
        migrations.RunPython(add_astron_members, reverse_code=migrations.RunPython.noop),
    ]
