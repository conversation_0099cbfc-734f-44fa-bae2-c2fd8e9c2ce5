# Generated by Django 4.2.5 on 2024-06-03 18:34

from django.db import migrations

def remove_cademi_events(apps, schema_editor):
    Event = apps.get_model('apps', 'Event')
    Platform = apps.get_model('apps', 'Platform')

    platform = Platform.objects.get(type='cademi')

    events_to_remove = ["purchase_refused", "pix_gerado", "boleto_gerado", "checkout_abandonment"]

    for custom_id in events_to_remove:
        event = Event.objects.filter(custom_id=custom_id).first()
        if event is not None:
            event.platform.remove(platform)  # type:ignore
            event.save()  # type:ignore

class Migration(migrations.Migration):

    dependencies = [
        ('apps', '0022_add_memberkit'),
    ]

    operations = [
        migrations.RunPython(remove_cademi_events, reverse_code=migrations.RunPython.noop),
    ]
