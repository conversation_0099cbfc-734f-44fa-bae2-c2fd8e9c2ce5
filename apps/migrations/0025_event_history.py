# Generated by Django 4.2.5 on 2024-08-02 17:58

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('apps', '0024_add_astron_members'),
    ]

    operations = [
        migrations.CreateModel(
            name='EventHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('payload', models.J<PERSON><PERSON>ield(blank=True, default=dict)),
                ('response', models.J<PERSON><PERSON>ield(blank=True, default=dict)),
                ('meta_data', models.J<PERSON><PERSON>ield(default=dict, blank=True)),
                ('event_id', models.Char<PERSON>ield(max_length=255)),
                ('job', models.CharField(max_length=255, blank=True, null=True)),
                ('event_status', models.IntegerField(blank=True, null=True)),
                ('errors', models.TextField(blank=True, null=True)),
                ('sentAt', models.DateTime<PERSON>ield(auto_now_add=True)),
                ('app', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='history', to='apps.app')),
            ],
            options={
                'verbose_name': 'Historico de eventos',
                'verbose_name_plural': 'Historico de eventos',
                'ordering': ['-sentAt'],
            },
        ),
    ]
