# Generated by Django 4.2.5 on 2025-02-18 12:48

from django.db import migrations

def add_subscription_created_event(apps, schema_editor):
    Event = apps.get_model('apps', 'Event')
    Platform = apps.get_model('apps', 'Platform')

    event_data = {'custom_id': 'subscription_created', 'name': 'Assinatura criada'}

    event, _ = Event.objects.get_or_create(**event_data)

    platforms = Platform.objects.filter(
        type__in=[
            'active_campaign', 'astron_members', 'cademi', 'memberkit',
            'smsfunnel', 'utmify', 'voxuy', 'webhook'
        ]
    )

    for platform in platforms:
        event.platform.add(platform)

def remove_subscription_created_event(apps, schema_editor):
    Event = apps.get_model('apps', 'Event')

    event_data = {'custom_id': 'subscription_created', 'name': 'Assinatura criada'}

    event = Event.objects.filter(**event_data).first()

    if event:
        event.delete()


class Migration(migrations.Migration):

    dependencies = [
        ('apps', '0032_merge_20250219_1925'),
    ]

    operations = [
        migrations.RunPython(add_subscription_created_event, reverse_code=remove_subscription_created_event),
    ]
