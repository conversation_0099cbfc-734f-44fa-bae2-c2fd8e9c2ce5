from django.db import migrations


def add_memberkit_platform(apps, schema_editor):
    Event = apps.get_model('apps', 'Event')
    Platform = apps.get_model('apps', 'Platform')

    platform, created = Platform.objects.get_or_create(
        type='memberkit',
        defaults={
            'name': 'MemberKit',
            'type': 'memberkit', }
    )

    events_data = [
        {"custom_id": "purchase_approved", "name": "Compra aprovada"},
        {"custom_id": "refund", "name": "<PERSON>embol<PERSON>"},
        {"custom_id": "chargeback", "name": "Chargeback"},
        {"custom_id": "subscription_canceled", "name": "Assinatura cancelada"},
        {"custom_id": "subscription_renewed", "name": "Assinatura renovada"},
    ]

    for event_data in events_data:
        event = Event.objects.filter(**event_data, platform=platform).first()
        if event is None:
            event, created = Event.objects.get_or_create(**event_data)
            event.platform.add(platform)

class Migration(migrations.Migration):

    dependencies = [
        ('apps', '0021_add_utmify'),
    ]

    operations = [
        migrations.RunPython(add_memberkit_platform, reverse_code=migrations.RunPython.noop),
    ]
