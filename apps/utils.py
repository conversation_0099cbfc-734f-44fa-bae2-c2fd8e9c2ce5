import json

import requests


def get_response_data(response: requests.Response) -> tuple[int, dict | bytes | str | None]:
    try:
        return response.status_code, response.json()
    except json.JSONDecodeError:
        pass

    try:
        return response.status_code, str(response.content.decode('utf-8'))
    except Exception:
        pass

    return response.status_code, None


WEBHOOK_EXAMPLE_DATA = {
    'id': '87956abe-940e-4e8b-8a27-82c482920f64',
    'refId': '9vbgfmg',
    'customer': {
        'name': '<PERSON>',
        'email': '<EMAIL>',
        'phone': '34999999999',
        'docNumber': '12345678909',
    },
    'affiliate': '<EMAIL>',
    'offer': {
        'id': 'B8BcHrY',
        'name': 'Special Offer',
        'price': 10,
    },
    'offer_type': 'main',
    'product': {
        "name": "Produto Teste",
        "id": "ff3fdf61-e88f-43b5-982a-32d50f112414",
        "short_id": "AckhQ75",
        "supportEmail": "<EMAIL>",
        "type": "unique",
        "invoiceDescription": ""
    },
    'parent_order': '95M26wi',
    'checkoutUrl': 'https://pay.cakto.com.br/EXAMPLE',
    'status': 'waiting_payment',
    'baseAmount': 100.0,
    'discount': 10.0,
    'amount': 90.0,
    'commissions': [
        {
            "user": "<EMAIL>",
            "totalAmount": 1.89,
            "type": "producer",
            "percentage": 80
        }
    ],
    'reason': 'Motivo de recusa do cartão',
    'refund_reason': 'Motivo de reembolso',
    'installments': 1,
    'paymentMethod': 'credit_card',
    'paymentMethodName': 'Cartão de Crédito',
    'paidAt': '2024-09-18T23:12:59.347605+00:00',
    'createdAt': '2024-09-18T23:12:59.347605+00:00',
    'card': {
        'lastDigits': '4323',
        'holderName': 'Card Example',
        'brand': 'visa',
    },
    'boleto': {
        'barcode': '03399853012970000154708032001011596630000000500',
        'boletoUrl': 'https://urlDePagamento.example.com',
        'expirationDate': '2024-03-22'
    },
    'pix': {
        'expirationDate': '2024-03-21',
        'qrCode': 'pixqrcode'
    },
    'picpay': {
        'qrCode': 'picpaycode',
        'paymentURL': 'https://urlDePagamento.example.com',
        'expirationDate': '2024-09-12 15:36:34-03:00'
    },
}
