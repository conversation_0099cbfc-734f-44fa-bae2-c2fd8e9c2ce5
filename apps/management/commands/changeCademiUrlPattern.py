import re

from django.core.management.base import BaseCommand

from apps.models import App


class Command(BaseCommand):
    help = 'Change the Cademí apps urls pattern.'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Changing Cademí apps urls...'))

        cademi_apps = App.objects.filter(platform__name='Cademí')
        for app in cademi_apps:
            if app.url:
                if re.search(r'^https:\/\/[^\/]+\.cademi\.com\.br\/api\/v1\/webhooks\/cademi\/transaction$', app.url, re.IGNORECASE):
                    app.url = app.url.replace('/api/v1/webhooks/cademi/transaction', '/api/postback/custom')
                    app.save()

        self.stdout.write(self.style.SUCCESS('Successfully changed the urls'))
