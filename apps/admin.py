from django.contrib import admin
from .models import Platform, App, Event, EventHistory

class EventPlatformInline(admin.TabularInline):
    model = Event.platform.through
    extra = 1

@admin.register(Event)
class EventAdmin(admin.ModelAdmin):
    exclude = ('platform',)
    list_display = ('name', 'custom_id', 'active', 'createdAt', 'updatedAt')
    search_fields = ('name', 'custom_id')
    list_filter = ('active', 'createdAt', 'updatedAt')
    inlines = [EventPlatformInline]

@admin.register(Platform)
class PlatformAdmin(admin.ModelAdmin):
    list_display = ('name', 'active', 'createdAt', 'updatedAt')
    search_fields = ('name', )
    list_filter = ('active', 'createdAt', 'updatedAt')
    inlines = [EventPlatformInline]

@admin.register(App)
class AppAdmin(admin.ModelAdmin):
    list_display = ['name', 'platform', 'user', 'createdAt']
    search_fields = ['name', 'user__email', 'id', 'products__name', 'products__id', 'fields']
    list_filter = ['platform', 'events', 'createdAt', 'updatedAt']
    readonly_fields = ['user', 'products']

@admin.register(EventHistory)
class EventHistoryAdmin(admin.ModelAdmin):
    model = EventHistory
    list_display = ('app', 'event_id', 'event_status', 'errors', 'job', 'sentAt')
    search_fields = ('app__name', 'app__user__email', 'payload', 'response', 'meta_data', 'job', 'errors')
    list_filter = ('event_id', 'sentAt', 'event_status')
    readonly_fields = [f.name for f in model._meta.concrete_fields]
