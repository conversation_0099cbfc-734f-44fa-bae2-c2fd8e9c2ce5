import uuid
from typing import Iterable, Union

from django.core.cache import cache
from django.db import models
from django_lifecycle import AFTER_CREATE, AFTER_DELETE, AFTER_SAVE, LifecycleModelMixin, hook
from shortuuid.django_fields import ShortUUIDField

EVENT_CACHE_KEY = 'app_events'

class Event(LifecycleModelMixin, models.Model):
    platform = models.ManyToManyField('apps.Platform', blank=True, related_name='events')
    name = models.CharField(max_length=255)
    custom_id = models.CharField(max_length=255, blank=True, null=True)
    active = models.BooleanField(default=True)
    createdAt = models.DateTimeField(auto_now_add=True)
    updatedAt = models.DateTimeField(auto_now=True)

    @hook(AFTER_SAVE)
    @hook(AFTER_CREATE)
    @hook(AFTER_DELETE)
    def invalidate_cache(self):
        cache.delete(EVENT_CACHE_KEY)

    def __str__(self):
        return self.name

    def serialize(self):
        return {
            'custom_id': self.custom_id,
            'name': self.name,
        }

    @classmethod
    def fetch_events(cls):
        return cls.objects.filter(active=True)

    @classmethod
    def get_cached_events(cls) -> Iterable['Event'] | None:
        payment_methods: Iterable['Event'] | None = cache.get_or_set(EVENT_CACHE_KEY, cls.fetch_events, None)
        return payment_methods

    @classmethod
    def get_cached_event(cls, custom_id: str) -> Union['Event', None]:
        events = cls.get_cached_events()
        if not events:
            return

        for event in events:
            if event.custom_id == custom_id:
                return event

class Platform(models.Model):
    name = models.CharField(max_length=255)
    type = models.CharField(max_length=255, blank=True, null=True, unique=True)
    active = models.BooleanField(default=True)
    createdAt = models.DateTimeField(auto_now_add=True)
    updatedAt = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f'{self.name}'

    class Meta:
        verbose_name = 'Plataforma'
        verbose_name_plural = 'Plataformas'
        ordering = ['-createdAt']

class App(models.Model):
    user = models.ForeignKey('user.User', on_delete=models.CASCADE)
    platform = models.ForeignKey(Platform, on_delete=models.CASCADE, default=1)  # type:ignore
    token = ShortUUIDField(length=7, max_length=40, unique=True, default=uuid.uuid4)  # type:ignore
    name = models.CharField(max_length=255)
    url = models.URLField(max_length=2048, blank=True, null=True)
    events = models.ManyToManyField(Event, blank=True)
    products = models.ManyToManyField('product.Product', blank=True)
    fields = models.JSONField(default=dict, blank=True, null=True)
    createdAt = models.DateTimeField(auto_now_add=True)
    updatedAt = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = 'App'
        verbose_name_plural = 'Apps'
        ordering = ['-createdAt']

class EventHistory(models.Model):
    app = models.ForeignKey('apps.App', related_name='history', on_delete=models.CASCADE)
    url = models.URLField(max_length=2048, blank=True, null=True)
    payload = models.JSONField(default=dict, blank=True)
    response = models.JSONField(default=dict, blank=True)
    meta_data = models.JSONField(default=dict, blank=True)
    event_id = models.CharField(max_length=255, db_index=True)
    job = models.CharField(max_length=255, db_index=True, blank=True, null=True)
    event_status = models.IntegerField(blank=True, null=True, db_index=True)
    errors = models.TextField(blank=True, null=True)
    sentAt = models.DateTimeField(auto_now_add=True, db_index=True)

    def __str__(self):
        return f'{self.event_id} - {self.app}'

    class Meta:
        verbose_name = 'Historico de eventos'
        verbose_name_plural = 'Historico de eventos'
        ordering = ['-sentAt']
