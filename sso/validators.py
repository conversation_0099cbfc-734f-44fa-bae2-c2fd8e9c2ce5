from django.utils.translation import gettext_lazy as _
from oauth2_provider.oauth2_validators import OAuth2Validator

class CustomOAuth2Validator(OAuth2Validator):
    oidc_claim_scope = OAuth2Validator.oidc_claim_scope
    oidc_claim_scope.update({
        "first_name":"user",
        "last_name":"user",
        "email":"user",
        "status":"user",
    })
    def get_additional_claims(self):
        return {
            "first_name": lambda request: request.user.first_name,
            "last_name": lambda request: request.user.last_name,
            "email": lambda request: request.user.email,
            "status": lambda request: request.user.company.status if request.user.company else None,
        }
