name: Sync main into staging

on:
  push:
    branches:
      - main

jobs:
  sync:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up Git
        run: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "GitHub Actions"

      - name: Merge main into staging
        if: github.ref == 'refs/heads/main'
        run: |
          git checkout staging
          git pull origin staging
          git merge main
          if git diff --quiet; then
            echo "No conflicts detected."
          else
            echo "Merge conflicts detected!"
            git merge --abort
            exit 1
          fi
          git push origin staging
