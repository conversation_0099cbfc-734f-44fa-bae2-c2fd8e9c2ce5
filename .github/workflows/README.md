# Pipelines

## Django CI
Este pipeline é disparado, automaticamente, em Pull Requests ou commits em algumas branches.
Abaixo a relação das atividades realizadas por esse pipeline em determinados eventos do github:

|Evento|  Branch de destino | Atividades realizadas|
| --- |  --- | --- |
| Pull Request |  `main` ou `staging` | - Execução de testes de unidade |
| Commit |  `staging` | - Execução de testes de unidade <br/> - Cria<PERSON> da imagem docker; |
| Commit | `main` | - Execução de testes de unidade <br/> - Criação da imagem docker; |


### Github secrets
As secrets abaixo são necessárias para o correto funcionamento do pipeline
| Nome | Descrição | Exemplo de conteúdo|
| --- | --- | --- |
| `DIGITALOCEAN_ACCESS_TOKEN` | Access token da digital ocean, para realizar o push da imagem docker gerada | `d0c8a3f2b9d7e4c6a1b3f5e8d0c9b4a7` |
| `KUBE_CONFIG_STAGING` | Conteúdo do arquivo `kubeconfig` para acessar o **cluster de staging** | apiVersion: v1<br/>clusters:<br/>- cluster:<br/>&nbsp;&nbsp;&nbsp;&nbsp;    certificate-authority-data: ... |
| `BACKEND_URL_STAGING` | Url para acesso às APIs `backend` e `checkout` no **ambiente de staging** | `apistg-k8s.cakto.com.br`  |
| `ALTERNATIVE_BACKEND_URL_LIST_STAGING` | *(OPCIONAL)* Lista com as Url alternativas para acesso à API `backend` no **ambiente de staging** | `{cakto-staging.app,cakto-staging.xyz,cakto-staging.net}`  |
| `KUBE_CONFIG_PRD` | Conteúdo do arquivo `kubeconfig` para acessar o **cluster de produção** | apiVersion: v1<br/>clusters:<br/>- cluster:<br/>&nbsp;&nbsp;&nbsp;&nbsp;    certificate-authority-data: ... |
| `BACKEND_URL_PRD` | Url para acesso às APIs `backend` e `checkout` no **ambiente de produção** | `api-k8s.cakto.com.br`  |
| `ALTERNATIVE_BACKEND_URL_LIST_PRD` | *(OPCIONAL)* Lista com as Url alternativas para acesso à API `backend` no **ambiente de produção** | `{cakto.app,cakto.xyz,cakto.net}`  |
| `KUBE_CONFIG_NOMMI` | Conteúdo do arquivo `kubeconfig` para acessar o **cluster de produção da NOMMI** | apiVersion: v1<br/>clusters:<br/>- cluster:<br/>&nbsp;&nbsp;&nbsp;&nbsp;    certificate-authority-data: ... |
| `BACKEND_URL_NOMMI` | Url para acesso às APIs `backend` e `checkout` no **ambiente de produção da NOMMI** | `api-k8s.cakto.com.br`  |
| `ALTERNATIVE_BACKEND_URL_LIST_NOMMI` | *(OPCIONAL)* Lista com as Url alternativas para acesso à API `backend` no **ambiente de produção da NOMMI** | `{nommi.app,nommi.xyz,nommi.net}`  |
