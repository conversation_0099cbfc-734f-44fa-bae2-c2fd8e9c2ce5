from decimal import Decimal
from typing import Mu<PERSON>Mapping, Sequence

from rest_framework_simplejwt.tokens import RefreshToken

from customer.models import Card
from gateway.models import Order, Payment, PaymentMethodType, PaymentStatus, Subscription
from gateway.utils import STATUS_MAPPING
from product.enums import ProductType
from user.enums import UserAccessLocations
from user.models import User
from user.utils import log_user_login_history

from .base import PaymentStrategy


class CardStrategy(PaymentStrategy):
    paymentMethodType = PaymentMethodType.CREDIT_CARD.id

    def beforePayment(self, *args, **kwargs):
        self.orders = kwargs.get('orders', [])
        self.totalAmount = sum(order.amount or 0 for order in self.orders)
        self.card = kwargs.get('card', None)
        self.extra_tx_data = kwargs.get('extra_tx_data', {})

    def executePayment(self) -> Payment:
        order = self.orders[0]

        if order.type == ProductType.SUBSCRIPTION.id:
            payment = self.process_subscription_payment(order)
        else:
            payment = self.process_unique_payment(orders=self.orders)

        self.handle_payment_status_change(
            payment=payment,
            current_status=PaymentStatus.WAITING_PAYMENT.id,
            new_status=payment.status,
            webhook_data=self.payment_data,
        )

        return payment

    def process_subscription_payment(self, order) -> Payment:
        _, payment = self.create_subscription(order)
        return payment

    def process_unique_payment(self, orders):
        tx = self.createTransaction()
        self.payment_data = tx.get('data', {})

        self.set_orders_split_data(paymentData=self.payment_data, orders=orders)

        payment = self.process_payment(paymentData=self.payment_data, orders=orders)

        return payment

    def set_orders_split_data(self, paymentData: MutableMapping, orders: Sequence[Order]) -> None:
        """Sets the fees, commissions, amount, and external info for credit_card Order."""
        assert all(order.paymentMethodType == self.paymentMethodType for order in orders), f'Only {self.paymentMethodType} orders allowed'

        paymentData['status'] = STATUS_MAPPING.get(paymentData['status'], paymentData['status'])  # type:ignore

        fixedFee = Decimal(paymentData.get('fixedFee') or 0)
        variableFee = Decimal(paymentData.get('percentageFee') or 0)
        itemFixedFee = fixedFee / len(orders)  # type:ignore

        for order in orders:
            # get baseAmount from received paymentData or order.baseAmount
            payment_items = paymentData.get('items') or []
            order_baseAmount = Decimal(next(
                (item['unitPrice'] for item in payment_items if item['externalRef'] == order.offer.id),
                (order.baseAmount - (order.discount or 0)) or 0
            ))

            orderTotalPercentage = order_baseAmount / Decimal(paymentData.get('baseAmount'))  # type:ignore
            itemVariableFee = (orderTotalPercentage * variableFee)
            orderLiquidAmount = order_baseAmount - itemFixedFee - itemVariableFee  # type:ignore

            commissionedUsers, commissions = self.get_commissions(order.offer, orderLiquidAmount, order.affiliate)  # type:ignore
            order.commissions = commissions
            order.commissionedUsers.set(commissionedUsers)

            self.create_order_splits(order)

            order.status = paymentData.get('status')
            order.reason = paymentData.get('reason')
            order.amount = Decimal(paymentData.get('amount') or 0) * orderTotalPercentage
            order.externalId = paymentData.get('id')
            order.acquirerType = paymentData.get('acquirerType')
            order.fees = itemFixedFee + itemVariableFee
            order.checkoutUrl = paymentData.get('checkoutUrl')
            order.installments = paymentData.get('installments') or 1
            order.fees = paymentData.get('fee', 0)
            order.due_date = paymentData.get('due_date')

            tx_card: dict | None = paymentData.get('card', {})  # type:ignore
            if tx_card:
                order_card, _ = Card.objects.get_or_create(
                    token=tx_card.get('token'),
                    defaults={
                        'lastDigits': tx_card.get('lastDigits'),
                        'holderName': tx_card.get('holderName'),
                        'brand': tx_card.get('brand'),
                        'customer': order.customer,
                    }
                )
                order.card = order_card

        Order.objects.bulk_update(orders, [
            'amount', 'status', 'installments', 'acquirerType', 'externalId',
            'fees', 'reason', 'card', 'commissions', 'checkoutUrl', 'due_date',
        ])

    def afterPaymentProcess(self, response_data, *args, **kwargs):
        customer = kwargs['customer']
        user = User.objects.filter(email__iexact=customer.email).first()
        if user and not user.has_usable_password():
            refresh = RefreshToken.for_user(user)
            response_data['accessToken'] = str(refresh.access_token)
            log_user_login_history(user=user, location=UserAccessLocations.FIRST_PURCHASE.id)

    def create_subscription(self, parent_order: Order) -> tuple[Subscription, Payment]:
        if parent_order.subscription and parent_order.subscription.externalId:
            return parent_order.subscription

        payload = self.get_subscription_payload(parent_order)

        response = self.split.createSubscription(**payload)

        self.validate_subscription_transaction_response(payload, response)

        split_data = response.json().get('subscription', {})

        subscription = self.to_subscription_model(parent_order, commit=False)

        self.set_split_data_to_subscription(subscription, split_data, commit=False)

        subscription.save()

        parent_order.save(update_fields=['subscription', 'subscription_period'])

        self.payment_data = split_data.get('first_payment', {})

        self.set_orders_split_data(paymentData=self.payment_data, orders=[parent_order])

        payment = self.process_payment(paymentData=self.payment_data, orders=[parent_order], subscriptions=[subscription])

        return subscription, payment
