from gateway.models import Payment, PaymentMethodType
from gateway.strategies.card_strategy import CardStrategy


class GooglePayStrategy(CardStrategy):
    paymentMethodType = PaymentMethodType.GOOGLEPAY.id

    def beforePayment(self, *args, **kwargs):
        super().beforePayment(*args, **kwargs)
        self.googlepay = kwargs.get('googlepay', None)

    def executePayment(self) -> Payment:
        tx = self.createTransaction()
        data = tx.get('data', {})

        self.set_orders_split_data(paymentData=data, orders=self.orders)

        payment = self.process_payment(paymentData=data, orders=self.orders)

        self.handle_payment_status_change(
            payment=payment,
            current_status='waiting_payment',
            new_status=payment.status,
            webhook_data=data
        )

        return payment

    def create_subscription(self, *args, **kwargs):
        raise NotImplementedError(f'{self.__class__.__name__}.create_subscription not implemented')
