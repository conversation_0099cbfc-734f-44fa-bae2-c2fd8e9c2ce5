from django.urls import path

from . import views

urlpatterns = [
    path('orders/', views.OrderListAPIView.as_view(), name='order-list'),
    path('orders/export/xlsx/', views.OrderListExportXLSX.as_view(), name='order-list-xlsx'),
    path('orders/export/csv/', views.OrderListExportCSV.as_view(), name='order-list-csv'),
    path('order/<str:pk>/', views.OrderRetrieveAPI.as_view({'get': 'retrieve'}), name='order-detail'),
    path('order/<str:pk>/refund/', views.OrderRetrieveAPI.as_view({'post': 'refund'}), name='order-refund'),
    path('order/<str:pk>/resend_approved_email/', views.OrderRetrieveAPI.as_view({'post': 'resend_approved_email'}), name='order-resend-approved-email'),
    path('order/<str:pk>/resend_access/', views.OrderRetrieveAPI.as_view({'post': 'resend_access'}), name='order-resend-access'),
    path('checkout/<str:product_short_id>/', views.PaymentAPIView.as_view({'post': 'post'}), name='checkout'),
    path('payment/status/<str:pk>/', views.PaymentStatusPublicAPIView.as_view(), name='payment-status'),
    path('payment/upsell/<str:offer_id>/<str:fingerprint>/', views.PaymentAPIView.as_view({'post': 'process_upsell'}), name='upsell-checkout'),
    path('webhook/splitpay/', views.WebhookAPIView.as_view(), name='webhook-splitpay'),
    path('request_refund/get_token/', views.RefundOtpView.as_view({'post': 'post'}), name='request-refund-check-email'),
    path('request_refund/confirm_email/', views.RefundOtpView.as_view({'post': 'confirm'}), name='request-refund-confirm-email'),
    path('request_refund/orders/', views.RefundAPIView.as_view({'get': 'list'}), name='request-refund-orders'),
    path('request_refund/orders/<str:pk>/', views.RefundAPIView.as_view({'post': 'refund'}), name='order-refund-customer'),
    path('admin/orders/', views.OrderAdminListAPIView.as_view(), name='admin-orders-list'),
    path('admin/order/<str:pk>/', views.OrderAdminAPI.as_view({'get': 'get'}), name='admin-order-refund'),
    path('admin/order/<str:pk>/refund/', views.OrderAdminAPI.as_view({'post': 'refund'}), name='admin-order-refund'),
    path('admin/reprocess_split_payment/', views.ReprocessSplitPaymentAPI.as_view(), name='admin-reprocess-split-payment'),
    path('applepay/validate-merchant/', views.ValidateApplePayView.as_view(), name='validate_merchant'),
    path('payment-method/', views.PaymentMethodListAPIView.as_view(), name='payment-method'),
    path('payment-status/', views.PaymentStatusesAPIView.as_view(), name='payment-status'),
    path('coproduction-type/', views.CoproductionTypeAPIView.as_view(), name='coproduction-type'),
    path('offer-type/', views.OfferTypeAPIView.as_view(), name='offer-type'),

    # Subscriptions
    path('subscriptions/', views.SubscriptionAPIView.as_view({'get': 'list'}), name='subscription-list'),
    path('subscriptions/<str:pk>/', views.SubscriptionAPIView.as_view({'get': 'retrieve', 'delete': 'destroy', 'put': 'update'}), name='subscription'),
    path('subscriptions/<str:pk>/cancel/', views.SubscriptionAPIView.as_view({'post': 'cancel'}), name='subscription-cancel'),
    path('subscriptions/export/xlsx/', views.SubscriptionExportXLSX.as_view(), name='subscription-export-xlsx'),
    path('subscriptions/export/csv/', views.SubscriptionExportCSV.as_view(), name='subscription-export-csv'),

    # ApplePay
    path('applepay/validate-merchant/', views.ValidateApplePayView.as_view(), name='validate_merchant'),
]
