# Generated by Django 4.2.5 on 2025-01-24 18:47

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('gateway', '0070_payment_applepay_payment_googlepay'),
    ]

    operations = [
        migrations.AlterField(
            model_name='order',
            name='status',
            field=models.CharField(choices=[('processing', 'Processando'), ('authorized', 'Autorizado'), ('paid', 'Pago'), ('refund_requested', 'Reembolso solicitado'), ('refunded', 'Reembolsado'), ('waiting_payment', 'Aguardando pagamento'), ('refused', 'Recusado'), ('blocked', 'Recusado'), ('chargedback', 'Chargeback'), ('canceled', 'Cancelado'), ('in_protest', 'Em protesto'), ('partially_paid', 'Parcialmente pago'), ('prechargeback', 'Prechargeback'), ('scheduled', 'Scheduled'), ('retrying', 'Retrying'), ('MED', 'Med')], default='waiting_payment', max_length=255),
        ),
    ]
