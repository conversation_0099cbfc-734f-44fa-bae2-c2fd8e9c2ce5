# Generated by Django 4.2.5 on 2023-09-29 17:18

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('gateway', '0002_settings'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='order',
            options={'ordering': ['-date'], 'verbose_name': 'Pedido', 'verbose_name_plural': 'Pedidos'},
        ),
        migrations.AlterModelOptions(
            name='settings',
            options={'verbose_name': 'Configurações', 'verbose_name_plural': 'Configurações'},
        ),
        migrations.AddField(
            model_name='settings',
            name='antecipation',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Antecipação (%)'),
        ),
        migrations.AddField(
            model_name='settings',
            name='boleto',
            field=models.IntegerField(default=1, verbose_name='<PERSON>leto (D+)'),
        ),
        migrations.AddField(
            model_name='settings',
            name='credit_card',
            field=models.IntegerField(default=15, verbose_name='Cartão de crédito (D+)'),
        ),
        migrations.AddField(
            model_name='settings',
            name='pix',
            field=models.IntegerField(default=0, verbose_name='Pix (D+)'),
        ),
        migrations.AddField(
            model_name='settings',
            name='stuck',
            field=models.IntegerField(default=0, verbose_name='Dias para retenção'),
        ),
        migrations.AddField(
            model_name='settings',
            name='stuckPercentage',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Retenção (%)'),
        ),
    ]
