# Generated by Django 4.2.5 on 2024-09-10 18:35

from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('gateway', '0055_alter_order_paymentmethod_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='split',
            name='amount',
        ),
        migrations.AddField(
            model_name='split',
            name='totalAmount',
            field=models.DecimalField(decimal_places=2, default=Decimal('0'), max_digits=10, verbose_name='Valor total'),
        ),
        migrations.AlterField(
            model_name='split',
            name='amountReserve',
            field=models.DecimalField(decimal_places=2, default=Decimal('0'), max_digits=10, verbose_name='Reserva'),
        ),
        migrations.AlterField(
            model_name='split',
            name='percentage',
            field=models.DecimalField(decimal_places=2, default=Decimal('0'), max_digits=5, verbose_name='Porcentagem'),
        ),
    ]
