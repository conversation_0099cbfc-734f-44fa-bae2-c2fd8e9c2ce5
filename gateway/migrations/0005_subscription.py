# Generated by Django 4.2.5 on 2023-10-15 12:27

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('product', '0013_alter_product_status'),
        ('customer', '0003_remove_customer_products'),
        ('gateway', '0004_order_coproductiontype'),
    ]

    operations = [
        migrations.CreateModel(
            name='Subscription',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('paymentMethod', models.CharField(choices=[('credit_card', 'Cartão de crédito'), ('boleto', 'Boleto'), ('pix', 'Pix')], max_length=255)),
                ('status', models.CharField(choices=[('active', 'Ativa'), ('waiting_payment', 'Aguardando pagamento'), ('trial', 'Período de testes'), ('past_due', 'Atrasada'), ('canceled', 'Cancelada'), ('ended', 'Finalizada')], default='active', max_length=255)),
                ('createdAt', models.DateTimeField(auto_now_add=True)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='customer.customer')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='product.product')),
            ],
        ),
    ]
