# Generated by Django 4.2.5 on 2023-12-26 19:06

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('gateway', '0024_subscription_externalplanid_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='subscription',
            name='createdOnGatewayAt',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='subscription',
            name='externalTransactionId',
            field=models.CharField(blank=True, db_index=True, max_length=30, null=True),
        ),
        migrations.AddField(
            model_name='subscription',
            name='isCreatedOnGateway',
            field=models.BooleanField(db_index=True, default=False, verbose_name='Foi criado na gateway'),
        ),
        migrations.AddField(
            model_name='subscription',
            name='order',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.PROTECT, to='gateway.order'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='subscription',
            name='externalPlanId',
            field=models.CharField(blank=True, db_index=True, max_length=30, null=True),
        ),
        migrations.AlterField(
            model_name='subscription',
            name='nextBillingAt',
            field=models.DateTimeField(blank=True, db_index=True, null=True),
        ),
        migrations.DeleteModel(
            name='PendingSubscription',
        ),
    ]
