# Generated by Django 4.2.5 on 2023-09-28 22:24

from django.db import migrations, models
import django.db.models.deletion
import gateway.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('customer', '0003_remove_customer_products'),
        ('product', '0004_product_affiliate_product_affiliateclick_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Order',
            fields=[
                ('id', models.CharField(default=gateway.models.generate_random_id, max_length=255, primary_key=True, serialize=False, unique=True)),
                ('hoppyId', models.CharField(blank=True, max_length=255, null=True)),
                ('status', models.CharField(choices=[('processing', 'Processando'), ('authorized', 'Autorizado'), ('paid', 'Pago'), ('refunded', 'Reembolsado'), ('waiting_payment', 'Aguardando pagamento'), ('refused', 'Recusado'), ('chargedback', 'Chargeback'), ('canceled', 'Cancelado'), ('in_protest', 'Em protesto'), ('partially_paid', 'Parcialmente pago')], default='waiting_payment', max_length=255)),
                ('baseAmount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('type', models.CharField(choices=[('unique', 'Pagamento único'), ('subscription', 'Assinatura recorrente')], default='unique', max_length=255)),
                ('paymentMethod', models.CharField(choices=[('credit_card', 'Cartão de crédito'), ('boleto', 'Boleto'), ('pix', 'Pix')], max_length=255)),
                ('installments', models.IntegerField(default=1)),
                ('acquirerId', models.CharField(blank=True, max_length=255, null=True)),
                ('acquirerType', models.CharField(max_length=255)),
                ('coproduction', models.JSONField(blank=True, null=True)),
                ('fees', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('date', models.DateTimeField(auto_now_add=True)),
                ('releaseDate', models.DateTimeField(auto_now_add=True)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='customer.customer')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='product.product')),
            ],
        ),
    ]
