# Generated by Django 4.2.5 on 2023-10-25 16:15

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('gateway', '0008_settings_boletopercentage_settings_pixpercentage'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='order',
            options={'ordering': ['-createdAt'], 'verbose_name': 'Pedido', 'verbose_name_plural': 'Pedidos'},
        ),
        migrations.RenameField(
            model_name='order',
            old_name='date',
            new_name='createdAt',
        ),
        migrations.AddField(
            model_name='order',
            name='updatedAt',
            field=models.DateTimeField(default=django.utils.timezone.now),
        ),
    ]
