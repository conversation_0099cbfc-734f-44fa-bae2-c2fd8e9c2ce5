# Generated by Django 4.2.5 on 2025-05-19 20:52

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('gateway', '0078_remove_split_unique_split_split_unique_split'),
    ]

    operations = [
        migrations.AddField(
            model_name='order',
            name='due_date',
            field=models.DateTimeField(blank=True, help_text='Data de agendamento do pagamento', null=True),
        ),
        migrations.AddField(
            model_name='payment',
            name='chargedbackAt',
            field=models.DateTimeField(blank=True, help_text='Data de chargeback do pagamento', null=True),
        ),
        migrations.AddField(
            model_name='payment',
            name='due_date',
            field=models.DateTimeField(blank=True, help_text='Data de agendamento do pagamento', null=True),
        ),
        migrations.AddField(
            model_name='payment',
            name='refundedAt',
            field=models.DateTimeField(blank=True, help_text='Data de reembolso do pagamento', null=True),
        ),
        migrations.AlterField(
            model_name='order',
            name='chargedbackAt',
            field=models.DateTimeField(blank=True, help_text='Data de chargeback do pedido', null=True),
        ),
        migrations.AlterField(
            model_name='order',
            name='createdAt',
            field=models.DateTimeField(auto_now_add=True, db_index=True, help_text='Data de criação do pedido'),
        ),
        migrations.AlterField(
            model_name='order',
            name='paidAt',
            field=models.DateTimeField(blank=True, help_text='Data que o pagamento foi realizado', null=True),
        ),
        migrations.AlterField(
            model_name='order',
            name='refundedAt',
            field=models.DateTimeField(blank=True, help_text='Data que o pedido foi reembolsado', null=True),
        ),
        migrations.AlterField(
            model_name='order',
            name='releaseDate',
            field=models.DateTimeField(blank=True, help_text='Data esperada de liberação das comissões', null=True),
        ),
        migrations.AlterField(
            model_name='order',
            name='updatedAt',
            field=models.DateTimeField(default=django.utils.timezone.now, help_text='Data da última atualização do pedido'),
        ),
        migrations.AlterField(
            model_name='payment',
            name='createdAt',
            field=models.DateTimeField(auto_now_add=True, help_text='Data de criação do pagamento'),
        ),
        migrations.AlterField(
            model_name='payment',
            name='paidAt',
            field=models.DateTimeField(blank=True, help_text='Data que o pagamento foi realizado', null=True),
        ),
        migrations.AlterField(
            model_name='payment',
            name='updatedAt',
            field=models.DateTimeField(default=django.utils.timezone.now, help_text='Data da última atualização do pagamento'),
        ),
    ]
