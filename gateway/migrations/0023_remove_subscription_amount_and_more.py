# Generated by Django 4.2.5 on 2023-12-12 22:53

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('product', '0045_coproduction_receivesalesfromaffiliate_and_more'),
        ('gateway', '0022_settings_caktowithdrawfee'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='subscription',
            name='amount',
        ),
        migrations.RemoveField(
            model_name='subscription',
            name='paymentMethod',
        ),
        migrations.RemoveField(
            model_name='subscription',
            name='status',
        ),
        migrations.CreateModel(
            name='PendingSubscription',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transactionExternalId', models.CharField(max_length=255)),
                ('subscriptionExternalId', models.CharField(blank=True, max_length=255, null=True)),
                ('paymentMethod', models.CharField(choices=[('boleto', 'Boleto'), ('pix', 'PIX')], max_length=60, null=True)),
                ('transactionStatus', models.CharField(choices=[('processing', 'Processando'), ('authorized', 'Autorizado'), ('paid', 'Pago'), ('refunded', 'Reembolsado'), ('waiting_payment', 'Aguardando pagamento'), ('refused', 'Recusado'), ('chargedback', 'Chargeback'), ('canceled', 'Cancelado'), ('in_protest', 'Em protesto'), ('partially_paid', 'Parcialmente pago')], default='waiting_payment', max_length=60)),
                ('isProcessed', models.BooleanField(blank=True, db_index=True, default=False)),
                ('canProcessAt', models.DateTimeField(db_index=True, verbose_name='Data para gerar Subscription')),
                ('processedAt', models.DateTimeField(blank=True, null=True, verbose_name='Data em que a Subscription foi gerada')),
                ('createdAt', models.DateTimeField(auto_now_add=True)),
                ('offer', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='product.offer')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='gateway.order')),
            ],
            options={
                'verbose_name': 'Assinatura pendente',
                'verbose_name_plural': 'Assinaturas pendentes',
                'ordering': ['-createdAt'],
            },
        ),
    ]
