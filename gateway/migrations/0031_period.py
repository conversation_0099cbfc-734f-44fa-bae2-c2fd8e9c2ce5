# Generated by Django 4.2.5 on 2024-01-16 13:04

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('gateway', '0030_remove_payment_externalplanid'),
    ]

    operations = [
        migrations.CreateModel(
            name='Period',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('startAt', models.DateTimeField(blank=True, null=True)),
                ('endAt', models.DateTimeField(blank=True, null=True)),
                ('createdAt', models.DateTimeField(auto_now_add=True)),
                ('subscription', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='periods', to='gateway.subscription')),
            ],
            options={
                'verbose_name': 'Periodo de Assinatura',
                'verbose_name_plural': 'Periodos de Assinaturas',
                'ordering': ['-createdAt'],
            },
        ),
    ]
