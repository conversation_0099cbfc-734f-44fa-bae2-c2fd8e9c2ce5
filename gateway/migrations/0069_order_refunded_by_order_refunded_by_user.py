# Generated by Django 4.2.5 on 2024-12-31 02:38

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('gateway', '0068_order_subscription_period'),
    ]

    operations = [
        migrations.AddField(
            model_name='order',
            name='refunded_by',
            field=models.CharField(blank=True, choices=[('customer', 'Cliente'), ('producer', 'Produtor'), ('admin', 'Admin')], max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='order',
            name='refunded_by_user',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='refunded_orders', to=settings.AUTH_USER_MODEL),
        ),
    ]
