# Generated by Django 4.2.5 on 2023-12-15 14:07

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('gateway', '0023_remove_subscription_amount_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='subscription',
            name='externalPlanId',
            field=models.CharField(db_index=True, max_length=30, null=True),
        ),
        migrations.AddField(
            model_name='subscription',
            name='nextBillingAt',
            field=models.DateTimeField(null=True),
        ),
        migrations.AddField(
            model_name='subscription',
            name='paymentMethod',
            field=models.CharField(choices=[('credit_card', 'Cartão de Crédito'), ('boleto', 'Boleto'), ('pix', 'PIX')], default='creadit_card', max_length=70, verbose_name='Método de Pagamento'),
        ),
        migrations.AddField(
            model_name='subscription',
            name='status',
            field=models.Char<PERSON>ield(choices=[('processing', 'Processando'), ('authorized', 'Autorizado'), ('paid', 'Pago'), ('refunded', 'Reembolsado'), ('waiting_payment', 'Aguardando pagamento'), ('refused', 'Recusado'), ('chargedback', 'Chargeback'), ('canceled', 'Cancelado'), ('in_protest', 'Em protesto'), ('partially_paid', 'Parcialmente pago')], default='waiting_payment', max_length=60),
        ),
    ]
