# Generated by Django 4.2.5 on 2024-07-23 18:46

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('gateway', '0052_order_coupon_order_couponcode_order_discount'),
    ]

    operations = [
        migrations.CreateModel(
            name='Split',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('type', models.CharField(choices=[('affiliate', 'Afiliado'), ('coproducer', 'Coprodutor'), ('producer', 'Produtor')], max_length=40, verbose_name='Tipo de comissão')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Valor recebido')),
                ('amountReserve', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Reserva')),
                ('percentage', models.DecimalField(decimal_places=2, max_digits=5, verbose_name='Porcentagem')),
                ('createdAt', models.DateTimeField(auto_now_add=True, verbose_name='Criado em')),
                ('updatedAt', models.DateTimeField(auto_now=True, verbose_name='Atualizado em')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='splits', to='gateway.order')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='splits', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Comissão',
                'verbose_name_plural': 'Comissões',
                'ordering': ['-createdAt'],
            },
        ),
    ]
