# Generated by Django 4.2.5 on 2023-11-24 02:25

from django.db import migrations, models
import gateway.models


class Migration(migrations.Migration):

    dependencies = [
        ('gateway', '0012_order_affiliate'),
    ]

    operations = [
        migrations.CreateModel(
            name='Payment',
            fields=[
                ('id', models.CharField(default=gateway.models.generate_random_id, max_length=255, primary_key=True, serialize=False, unique=True)),
                ('externalId', models.CharField(blank=True, max_length=255, null=True)),
                ('acquirerType', models.CharField(default='hoppy', max_length=255)),
                ('refunded', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('status', models.CharField(choices=[('active', 'Ativa'), ('waiting_payment', 'Aguardando pagamento'), ('trial', 'Período de testes'), ('past_due', 'Atrasada'), ('canceled', 'Cancelada'), ('ended', 'Finalizada')], default='active', max_length=255)),
                ('paymentMethod', models.CharField(choices=[('credit_card', 'Cartão de crédito'), ('boleto', 'Boleto'), ('pix', 'Pix')], default='pix', max_length=255)),
                ('installments', models.IntegerField(default=1)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('createdAt', models.DateTimeField(auto_now_add=True)),
                ('pix', models.JSONField(blank=True, null=True)),
                ('boleto', models.JSONField(blank=True, null=True)),
                ('card', models.JSONField(blank=True, null=True)),
            ],
            options={
                'verbose_name': 'Pagamento',
                'verbose_name_plural': 'Pagamentos',
                'ordering': ['-createdAt'],
            },
        ),
    ]
