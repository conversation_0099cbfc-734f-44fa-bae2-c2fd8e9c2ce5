# Generated by Django 4.2.5 on 2023-12-06 18:09

from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('gateway', '0020_alter_order_releasedate'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='settings',
            name='boletoPercentage',
        ),
        migrations.RemoveField(
            model_name='settings',
            name='pixPercentage',
        ),
        migrations.AddField(
            model_name='settings',
            name='caktoFee',
            field=models.DecimalField(decimal_places=2, default=Decimal('5'), max_digits=10, verbose_name='Taxa Cakto (%)'),
        ),
        migrations.AddField(
            model_name='settings',
            name='caktoFixedFee',
            field=models.DecimalField(decimal_places=2, default=Decimal('1'), max_digits=5, verbose_name='Taxa Cakto fixa (R$)'),
        ),
    ]
