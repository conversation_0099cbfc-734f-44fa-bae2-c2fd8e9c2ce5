# Generated by Django 4.2.5 on 2024-12-20 16:41

import datetime
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('gateway', '0066_subscription_paid_payments_quantity'),
    ]

    operations = [
        migrations.AddField(
            model_name='subscription',
            name='retention',
            field=models.DurationField(default=datetime.timedelta(0)),
        ),
        migrations.AlterField(
            model_name='subscription',
            name='createdAt',
            field=models.DateTimeField(auto_now_add=True, db_index=True),
        ),
    ]
