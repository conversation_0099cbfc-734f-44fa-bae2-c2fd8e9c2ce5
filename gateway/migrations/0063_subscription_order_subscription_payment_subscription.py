# Generated by Django 4.2.5 on 2024-12-11 21:07

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import gateway.models


class Migration(migrations.Migration):

    dependencies = [
        ('customer', '0011_customerhistory_birthdate'),
        ('product', '0110_merge_20241204_1859'),
        ('gateway', '0062_remove_subscription_customer_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Subscription',
            fields=[
                ('id', models.CharField(default=gateway.models.generate_random_id, max_length=255, primary_key=True, serialize=False, unique=True)),
                ('externalId', models.IntegerField(blank=True, null=True)),
                ('status', models.CharField(choices=[('active', 'Ativa'), ('canceled', 'Cancelada'), ('expired', 'Expirada'), ('trial', 'Em período de teste')], default='trialing', max_length=255)),
                ('recurrence_period', models.IntegerField()),
                ('quantity_recurrences', models.IntegerField()),
                ('trial_days', models.IntegerField()),
                ('max_retries', models.IntegerField(verbose_name='Número máximo de tentativas de cobrança')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('next_payment_date', models.DateTimeField(blank=True, null=True)),
                ('createdAt', models.DateTimeField(auto_now_add=True)),
                ('updatedAt', models.DateTimeField(default=django.utils.timezone.now)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='subscriptions', to='customer.customer')),
                ('offer', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='subscriptions', to='product.offer')),
                ('parent_order', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='subscriptions', to='gateway.order', verbose_name='Pedido de Origem')),
                ('paymentMethod', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='subscriptions', to='product.paymentmethod')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='subscriptions', to='product.product')),
            ],
            options={
                'verbose_name': 'Assinatura',
                'verbose_name_plural': 'Assinaturas',
                'ordering': ['-createdAt'],
            },
        ),
        migrations.AddField(
            model_name='order',
            name='subscription',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='orders', to='gateway.subscription'),
        ),
        migrations.AddField(
            model_name='payment',
            name='subscription',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='payments', to='gateway.subscription'),
        ),
    ]
