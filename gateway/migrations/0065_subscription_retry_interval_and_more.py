# Generated by Django 4.2.5 on 2024-12-16 15:19

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('gateway', '0064_subscription_canceledat'),
    ]

    operations = [
        migrations.AddField(
            model_name='subscription',
            name='retry_interval',
            field=models.IntegerField(default=1, verbose_name='Intervalo entre tentativas (em dias)'),
        ),
        migrations.AlterField(
            model_name='subscription',
            name='quantity_recurrences',
            field=models.IntegerField(verbose_name='Quantidade de recorrências'),
        ),
        migrations.AlterField(
            model_name='subscription',
            name='recurrence_period',
            field=models.IntegerField(verbose_name='Período de recorrência (em dias)'),
        ),
        migrations.AlterField(
            model_name='subscription',
            name='trial_days',
            field=models.IntegerField(verbose_name='Dias de teste'),
        ),
    ]
