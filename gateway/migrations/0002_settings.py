# Generated by Django 4.2.5 on 2023-09-29 09:31

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('gateway', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Settings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('installments1', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='1x (% da taxa)')),
                ('installments2', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='2x (% da taxa)')),
                ('installments3', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='3x (% da taxa)')),
                ('installments4', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='4x (% da taxa)')),
                ('installments5', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='5x (% da taxa)')),
                ('installments6', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='6x (% da taxa)')),
                ('installments7', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='7x (% da taxa)')),
                ('installments8', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='8x (% da taxa)')),
                ('installments9', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='9x (% da taxa)')),
                ('installments10', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='10x (% da taxa)')),
                ('installments11', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='11x (% da taxa)')),
                ('installments12', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='12x (% da taxa)')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
