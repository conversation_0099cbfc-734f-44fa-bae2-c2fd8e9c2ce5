# Generated by Django 4.2.5 on 2025-01-31 19:20

import datetime
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import gateway.models


class Migration(migrations.Migration):

    dependencies = [
        ('product', '0114_alter_offer_type'),
        ('customer', '0011_customerhistory_birthdate'),
        ('gateway', '0074_alter_order_offer_type'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='payment',
            name='subscription',
        ),
        migrations.AddField(
            model_name='payment',
            name='subscriptions',
            field=models.ManyToManyField(related_name='payments', to='gateway.subscription'),
        ),
        migrations.AddField(
            model_name='subscription',
            name='current_period',
            field=models.IntegerField(default=0, help_text='Período atual', verbose_name='Período Atual'),
        ),
        migrations.AlterField(
            model_name='subscription',
            name='amount',
            field=models.DecimalField(decimal_places=2, help_text='Valor da Assinatura', max_digits=10),
        ),
        migrations.AlterField(
            model_name='subscription',
            name='canceledAt',
            field=models.DateTimeField(blank=True, help_text='Data de cancelamento', null=True),
        ),
        migrations.AlterField(
            model_name='subscription',
            name='createdAt',
            field=models.DateTimeField(auto_now_add=True, db_index=True, help_text='Data de criação'),
        ),
        migrations.AlterField(
            model_name='subscription',
            name='customer',
            field=models.ForeignKey(help_text='Cliente da Assinatura', on_delete=django.db.models.deletion.PROTECT, related_name='subscriptions', to='customer.customer'),
        ),
        migrations.AlterField(
            model_name='subscription',
            name='externalId',
            field=models.IntegerField(blank=True, db_index=True, help_text='ID da Assinatura no Gateway de Pagamento', null=True),
        ),
        migrations.AlterField(
            model_name='subscription',
            name='id',
            field=models.CharField(default=gateway.models.generate_random_id, help_text='ID da Assinatura', max_length=255, primary_key=True, serialize=False, unique=True),
        ),
        migrations.AlterField(
            model_name='subscription',
            name='max_retries',
            field=models.IntegerField(default=3, help_text='Número máximo de tentativas de cobrança', verbose_name='Número máximo de tentativas de cobrança'),
        ),
        migrations.AlterField(
            model_name='subscription',
            name='next_payment_date',
            field=models.DateTimeField(blank=True, help_text='Data do próximo pagamento', null=True),
        ),
        migrations.AlterField(
            model_name='subscription',
            name='offer',
            field=models.ForeignKey(help_text='Oferta da Assinatura', on_delete=django.db.models.deletion.PROTECT, related_name='subscriptions', to='product.offer'),
        ),
        migrations.AlterField(
            model_name='subscription',
            name='paid_payments_quantity',
            field=models.IntegerField(default=0, help_text='Quantidade de pagamentos pagos'),
        ),
        migrations.AlterField(
            model_name='subscription',
            name='parent_order',
            field=models.ForeignKey(help_text='Pedido de Origem da Assinatura', on_delete=django.db.models.deletion.PROTECT, related_name='subscriptions', to='gateway.order', verbose_name='Pedido de Origem'),
        ),
        migrations.AlterField(
            model_name='subscription',
            name='paymentMethod',
            field=models.ForeignKey(help_text='Método de pagamento', on_delete=django.db.models.deletion.PROTECT, related_name='subscriptions', to='product.paymentmethod'),
        ),
        migrations.AlterField(
            model_name='subscription',
            name='product',
            field=models.ForeignKey(help_text='Produto da Assinatura', on_delete=django.db.models.deletion.PROTECT, related_name='subscriptions', to='product.product'),
        ),
        migrations.AlterField(
            model_name='subscription',
            name='quantity_recurrences',
            field=models.IntegerField(default=12, help_text='Quantidade de recorrências', verbose_name='Quantidade de recorrências'),
        ),
        migrations.AlterField(
            model_name='subscription',
            name='recurrence_period',
            field=models.IntegerField(default=30, help_text='Período de recorrência (dias)', verbose_name='Período de recorrência (em dias)'),
        ),
        migrations.AlterField(
            model_name='subscription',
            name='retention',
            field=models.DurationField(default=datetime.timedelta(0), help_text='Tempo de retenção'),
        ),
        migrations.AlterField(
            model_name='subscription',
            name='retry_interval',
            field=models.IntegerField(default=1, help_text='Intervalo entre tentativas de cobrança (dias)', verbose_name='Intervalo entre tentativas (em dias)'),
        ),
        migrations.AlterField(
            model_name='subscription',
            name='status',
            field=models.CharField(choices=[('active', 'Ativa'), ('inactive', 'Inativa'), ('canceled', 'Cancelada'), ('expired', 'Expirada'), ('trial', 'Em período de teste')], default='inactive', help_text='Status da Assinatura', max_length=255),
        ),
        migrations.AlterField(
            model_name='subscription',
            name='trial_days',
            field=models.IntegerField(default=0, help_text='Dias de teste', verbose_name='Dias de teste'),
        ),
        migrations.AlterField(
            model_name='subscription',
            name='updatedAt',
            field=models.DateTimeField(default=django.utils.timezone.now, help_text='Data de atualização'),
        ),
    ]
