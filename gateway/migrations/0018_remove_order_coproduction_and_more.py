# Generated by Django 4.2.5 on 2023-11-28 13:28

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('gateway', '0017_alter_payment_status'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='order',
            name='coproduction',
        ),
        migrations.RemoveField(
            model_name='order',
            name='coproductionType',
        ),
        migrations.AddField(
            model_name='order',
            name='comissionedUsers',
            field=models.ManyToManyField(blank=True, db_index=True, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='order',
            name='comissions',
            field=models.JSONField(default=list),
        ),
    ]
