# Generated by Django 4.2.5 on 2025-02-19 18:40

from django.db import migrations, models

PAYMENT_METHOD_ID = 'openfinance_nubank'
PAYMENT_METHOD_NAME = 'Nubank'

def add_openfinance_nubank_PaymentMethod(apps, schema_editor):
    PaymentMethod = apps.get_model('product', 'PaymentMethod')
    PaymentMethod.objects.get_or_create(name=PAYMENT_METHOD_NAME, type=PAYMENT_METHOD_ID)

def remove_openfinance_nubank_PaymentMethod(apps, schema_editor):
    PaymentMethod = apps.get_model('product', 'PaymentMethod')
    PaymentMethod.objects.filter(type=PAYMENT_METHOD_ID).delete()


class Migration(migrations.Migration):

    dependencies = [
        ('gateway', '0064_alter_order_offer_type'),
    ]

    operations = [
        migrations.AddField(
            model_name='payment',
            name='openFinanceNubank',
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.RunPython(add_openfinance_nubank_PaymentMethod, remove_openfinance_nubank_PaymentMethod),
    ]
