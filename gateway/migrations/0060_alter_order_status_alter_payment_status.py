# Generated by Django 4.2.5 on 2024-11-25 15:04

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('gateway', '0059_alter_order_options'),
    ]

    operations = [
        migrations.AlterField(
            model_name='order',
            name='status',
            field=models.CharField(choices=[('processing', 'Processando'), ('authorized', 'Autorizado'), ('paid', 'Pago'), ('refund_requested', 'Reembolso solicitado'), ('refunded', 'Reembolsado'), ('waiting_payment', 'Aguardando pagamento'), ('refused', 'Recusado'), ('chargedback', 'Chargeback'), ('canceled', 'Cancelado'), ('in_protest', 'Em protesto'), ('partially_paid', 'Parcialmente pago'), ('prechargeback', 'Prechargeback'), ('scheduled', 'Scheduled'), ('retrying', 'Retrying'), ('MED', 'Med')], default='waiting_payment', max_length=255),
        ),
        migrations.AlterField(
            model_name='payment',
            name='status',
            field=models.CharField(choices=[('processing', 'Processando'), ('authorized', 'Autorizado'), ('paid', 'Pago'), ('refunded', 'Reembolsado'), ('waiting_payment', 'Aguardando pagamento'), ('refused', 'Recusado'), ('chargedback', 'Chargeback'), ('canceled', 'Cancelado'), ('in_protest', 'Em protesto'), ('partially_paid', 'Parcialmente pago'), ('prechargeback', 'Prechargeback'), ('scheduled', 'Scheduled'), ('retrying', 'Retrying'), ('MED', 'Med')], default='waiting_payment', max_length=255),
        ),
    ]
