# Generated by Django 4.2.5 on 2025-05-27 20:30

from django.db import migrations, models
import django.db.models.deletion
import shortuuid.django_fields


class Migration(migrations.Migration):

    dependencies = [
        ('product', '0133_checkout_sales_count_offer_checkout_visits_and_more'),
        ('gateway', '0080_order_canceledat'),
    ]

    operations = [
        migrations.AddField(
            model_name='order',
            name='checkout',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='orders', to='product.checkout'),
        ),
        migrations.AlterField(
            model_name='order',
            name='externalId',
            field=models.CharField(blank=True, db_index=True, help_text='ID do pedido no Gateway de Pagamento', max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='order',
            name='refId',
            field=shortuuid.django_fields.ShortUUIDField(alphabet=None, db_index=True, help_text='ID do Pedido', length=7, max_length=40, prefix='', unique=True),
        ),
        migrations.AlterField(
            model_name='order',
            name='status',
            field=models.CharField(choices=[('processing', 'Processando'), ('authorized', 'Autorizado'), ('paid', 'Pago'), ('refund_requested', 'Reembolso solicitado'), ('refunded', 'Reembolsado'), ('waiting_payment', 'Aguardando pagamento'), ('refused', 'Recusado'), ('blocked', 'Recusado'), ('chargedback', 'Chargeback'), ('canceled', 'Cancelado'), ('in_protest', 'Em protesto'), ('partially_paid', 'Parcialmente pago'), ('prechargeback', 'Prechargeback'), ('scheduled', 'Scheduled'), ('retrying', 'Retrying'), ('MED', 'Med')], db_index=True, default='waiting_payment', help_text='Status do pedido', max_length=255),
        ),
    ]
