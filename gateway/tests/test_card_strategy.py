from typing import Any
from unittest import mock

from cakto.tests.base import BaseTestCase
from customer.models import Card
from gateway.strategies.card_strategy import CardStrategy
from product.enums import ProductType
from rest_framework_simplejwt.tokens import AccessToken

class TestCardStrategy(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.create_paymentMethods()

        cls.product = cls.create_product(price=10)

        cls.customer = cls.create_customer()

        cls.order = cls.create_order(
            product=cls.product,
            paymentMethod=cls.credit_card,
            baseAmount=10,
            amount=10,
            customer=cls.customer,
        )

    def test_CardStrategy_set_order_and_payment_data(self):
        paymentData: dict[str, Any] = {
            'status': 'paid',
            'id': 'TestPayment',
            'baseAmount': 10,
            'amount': 10,
            'fee': 2,
            'paymentMethod': 'credit_card',
            'installments': 3,
            'acquirerType': 'testAcquirer',
            'reason': 'Test Order reason',
            'card': {
                'token': '123456',
                'lastDigits': '1234',
                'holderName': 'Test Holder',
                'brand': 'Test Brand',
            }
        }

        mock.patch('gateway.strategies.card_strategy.CardStrategy.create_order_splits', mock.Mock()).start()

        with mock.patch('gateway.strategies.card_strategy.CardStrategy.get_commissions') as get_commissions_mock:
            expected_commissions = {'test': 'comissions'}
            get_commissions_mock.return_value = ([self.product.user], expected_commissions)
            CardStrategy().set_orders_split_data(paymentData, [self.order])
            CardStrategy().process_payment(paymentData, [self.order])

        self.order.refresh_from_db()

        self.assertEqual(self.order.commissions, expected_commissions)
        self.assertEqual(self.order.commissionedUsers.first(), self.product.user)
        self.assertEqual(self.order.status, paymentData['status'])
        self.assertEqual(self.order.externalId, paymentData['id'])
        self.assertEqual(self.order.amount, paymentData['amount'])
        self.assertEqual(self.order.fees, paymentData['fee'])
        self.assertEqual(self.order.installments, paymentData['installments'])
        self.assertEqual(self.order.acquirerType, paymentData['acquirerType'])
        self.assertEqual(self.order.reason, paymentData['reason'])

        # Card check
        self.assertIsInstance(self.order.card, Card, 'Card should be created')
        self.assertEqual(self.order.card.token, paymentData['card']['token'])
        self.assertEqual(self.order.card.lastDigits, paymentData['card']['lastDigits'])
        self.assertEqual(self.order.card.holderName, paymentData['card']['holderName'])
        self.assertEqual(self.order.card.brand, paymentData['card']['brand'])
        self.assertEqual(self.order.card.customer, self.customer)

    def test_CardStrategy_executePayment_with_unique_order(self):
        self.order.type = ProductType.UNIQUE.id
        self.order.save(update_fields=['type'])

        process_unique_payment_mock = mock.patch(
            'gateway.strategies.card_strategy.CardStrategy.process_unique_payment'
        ).start()

        handle_payment_status_change_mock = mock.patch(
            'gateway.strategies.card_strategy.CardStrategy.handle_payment_status_change'
        ).start()

        card_strategy = CardStrategy()
        card_strategy.payment_data = {}
        card_strategy.orders = [self.order]

        # Call
        card_strategy.executePayment()

        process_unique_payment_mock.assert_called_once_with(orders=[self.order])

        handle_payment_status_change_mock.assert_called_once_with(
            payment=process_unique_payment_mock.return_value,
            current_status='waiting_payment',
            new_status=process_unique_payment_mock.return_value.status,
            webhook_data={}
        )

    def test_CardStrategy_executePayment_with_subscription_order(self):
        self.order.type = ProductType.SUBSCRIPTION.id
        self.order.save(update_fields=['type'])

        process_subscription_payment_mock = mock.patch(
            'gateway.strategies.card_strategy.CardStrategy.process_subscription_payment'
        ).start()

        handle_payment_status_mock = mock.patch(
            'gateway.strategies.card_strategy.CardStrategy.handle_payment_status_change'
        ).start()

        card_strategy = CardStrategy()
        card_strategy.payment_data = {}
        card_strategy.orders = [self.order]

        # Call
        card_strategy.executePayment()

        process_subscription_payment_mock.assert_called_once_with(self.order)

        handle_payment_status_mock.assert_called_once_with(
            payment=process_subscription_payment_mock.return_value,
            current_status='waiting_payment',
            new_status=process_subscription_payment_mock.return_value.status,
            webhook_data={}
        )

    def test_CardStrategy_process_subscription_payment(self):
        subscription_mock = mock.Mock()
        payment_mock = mock.Mock()

        create_subscription_mock = mock.patch(
            'gateway.strategies.card_strategy.CardStrategy.create_subscription'
        ).start()
        create_subscription_mock.return_value = (subscription_mock, payment_mock)

        result = CardStrategy().process_subscription_payment(self.order)

        create_subscription_mock.assert_called_once_with(self.order)

        self.assertEqual(result, payment_mock)

    def test_CardStrategy_process_unique_payment(self):
        tx_mock = {'data': {'testing': 'unique_payment'}}
        create_transaction_mock = mock.patch(
            'gateway.strategies.card_strategy.CardStrategy.createTransaction',
            return_value=tx_mock
        ).start()

        set_orders_split_data_mock = mock.patch(
            'gateway.strategies.card_strategy.CardStrategy.set_orders_split_data'
        ).start()

        payment_mock = mock.Mock()
        process_payment_mock = mock.patch(
            'gateway.strategies.card_strategy.CardStrategy.process_payment',
            return_value=payment_mock
        ).start()

        card_strategy = CardStrategy()

        result = card_strategy.process_unique_payment(orders=[self.order])

        create_transaction_mock.assert_called_once()

        set_orders_split_data_mock.assert_called_once_with(
            paymentData=tx_mock['data'],
            orders=[self.order]
        )

        process_payment_mock.assert_called_once_with(
            paymentData=tx_mock['data'],
            orders=[self.order]
        )

        self.assertEqual(result, payment_mock)

    def test_CardStrategy_set_orders_split_data(self):
        paymentData: dict[str, Any] = {
            'status': 'paid',
            'id': 'TestPayment',
            'baseAmount': 10,
            'amount': 10,
            'liquidAmount': 8,
            'fixedFee': 1,
            'fee': 2,
            'percentageFee': 1,
            'paymentMethod': 'credit_card',
            'installments': 1,
            'acquirerType': 'testAcquirer',
        }

        create_order_splits_mock = mock.patch('gateway.strategies.card_strategy.CardStrategy.create_order_splits').start()

        get_commissions_mock = mock.patch('gateway.strategies.card_strategy.CardStrategy.get_commissions').start()
        expected_commissions = {
            'userId': self.product.user.id,
            'type': 'producer',
            'commissionValue': 8,
            'commissionPercentage': 100,
        }
        get_commissions_mock.return_value = ([self.product.user], expected_commissions)

        # Call
        CardStrategy().set_orders_split_data(paymentData, [self.order])

        create_order_splits_mock.assert_called_once_with(self.order)

        self.order.refresh_from_db()

        self.assertEqual(self.order.commissions, expected_commissions)
        self.assertEqual(self.order.commissionedUsers.first(), self.product.user)
        self.assertEqual(self.order.status, paymentData['status'])
        self.assertEqual(self.order.externalId, paymentData['id'])
        self.assertEqual(self.order.amount, paymentData['amount'])
        self.assertEqual(self.order.fees, paymentData['fixedFee'] + paymentData['percentageFee'])
        self.assertEqual(self.order.installments, paymentData['installments'])
        self.assertEqual(self.order.acquirerType, paymentData['acquirerType'])

    def test_CardStrategy_create_subscription(self):
        subscription_mock = mock.Mock()
        subscription_mock.externalId = None

        parent_order_mock = mock.Mock(subscription=subscription_mock)

        subscription_payload = {'test': 'subscription_payload'}
        get_subscription_payload_mock = mock.patch(
            'gateway.strategies.base.PaymentStrategy.get_subscription_payload',
            return_value=subscription_payload
        ).start()

        split_subscription_data = {
            'subscription': {
                'id': 'TestSubscription',
                'first_payment': {'id': 'TestPayment'},
            },
        }
        createSubscription_response = self.get_response_mock(content=split_subscription_data)
        createSubscription_mock = mock.patch(
            'gateway.strategies.base.SplitPay.createSubscription',
            return_value=createSubscription_response
        ).start()

        validate_subscription_transaction_response_mock = mock.patch(
            'gateway.strategies.card_strategy.CardStrategy.validate_subscription_transaction_response'
        ).start()

        to_subscription_model_mock = mock.patch(
            'gateway.strategies.base.PaymentStrategy.to_subscription_model',
            return_value=subscription_mock
        ).start()

        set_split_data_to_subscription_mock = mock.patch(
            'gateway.strategies.base.PaymentStrategy.set_split_data_to_subscription'
        ).start()

        set_orders_split_data_mock = mock.patch(
            'gateway.strategies.card_strategy.CardStrategy.set_orders_split_data'
        ).start()

        payment_mock = mock.Mock()
        process_payment_mock = mock.patch(
            'gateway.strategies.base.PaymentStrategy.process_payment',
            return_value=payment_mock
        ).start()

        # Call
        result = CardStrategy().create_subscription(parent_order_mock)

        # Assertions
        get_subscription_payload_mock.assert_called_once_with(parent_order_mock)

        createSubscription_mock.assert_called_once_with(**subscription_payload)

        validate_subscription_transaction_response_mock.assert_called_once_with(
            subscription_payload,
            createSubscription_response
        )

        to_subscription_model_mock.assert_called_once_with(
            parent_order_mock,
            commit=False,
        )

        set_split_data_to_subscription_mock.assert_called_once_with(
            subscription_mock,
            split_subscription_data.get('subscription', {}),
            commit=False,
        )

        subscription_mock.save.assert_called_once()

        first_payment_data = split_subscription_data.get('subscription').get('first_payment')  # type:ignore
        set_orders_split_data_mock.assert_called_once_with(
            paymentData=first_payment_data,
            orders=[parent_order_mock]
        )

        process_payment_mock.assert_called_once_with(
            paymentData=first_payment_data,
            orders=[parent_order_mock],
            subscriptions=[subscription_mock]
        )

        self.assertEqual(result, (subscription_mock, payment_mock))

    def test_CardStrategy_afterPaymentProcess_with_user_that_has_no_password(self):
        customer_user = self.create_user(email=self.customer.email)
        customer_user.set_unusable_password()
        customer_user.save()

        response_data_mock = {}

        CardStrategy().afterPaymentProcess(response_data_mock, customer=self.customer)

        self.assertIn('accessToken', response_data_mock)

        token = AccessToken(response_data_mock['accessToken'])
        self.assertEqual(token.payload.get('user_id'), customer_user.pk)

    def test_CardStrategy_afterPaymentProcess_without_user_that_has_password(self):
        self.create_user(email=self.customer.email, password='TestPassword')

        response_data_mock = {}

        CardStrategy().afterPaymentProcess(response_data_mock, customer=self.customer)

        self.assertNotIn('otpAccessToken', response_data_mock)
        self.assertNotIn('accessToken', response_data_mock)
