from typing import Any
from unittest import mock

from django.utils import timezone

from customer.models import Address
from cakto.tests.base import BaseTestCase
from gateway.models import Order, Payment, PaymentStatus, Split, Subscription
from gateway.strategies.base import PaymentStrategy
from product.enums import ProductType


class TestPaymentStrategy(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.create_paymentMethods()

        cls.product = cls.create_product(price=10)

        cls.order: Order = cls.create_order(
            product=cls.product,
            amount=1_000,
            baseAmount=900,
            type=ProductType.SUBSCRIPTION.id,
            paymentMethod=cls.pix,
        )

        cls.customer = cls.create_customer()

        cls.subscription = Subscription.objects.create(
            parent_order=cls.order,
            amount=cls.order.amount,
            paymentMethod=cls.pix,
            customer=cls.customer,
            offer=cls.product.offers.first(),
            product=cls.product,
        )

        cls.order_2 = cls.create_order(product=cls.product, customer=cls.customer)

        cls.payment = cls.create_payment(orders=[cls.order, cls.order_2], externalId='paymentTestId')

    def test_process_payment_sets_correct_data(self):
        paymentData: dict[str, Any] = {
            'id': 'paymentTestId',
            'status': PaymentStatus.PAID.id,
            'amount': 10,
            'installments': 1,
            'acquirerType': 'testAcquirer',
            'paymentMethod': 'pix',
            'pix': {
                'qrCode': 'testValue',
            },
            'boleto': {
                'url': 'testValue',
            },
            'card': {
                'cardNumber': '1234',
            }
        }

        payment = PaymentStrategy.process_payment(paymentData=paymentData, orders=[self.order])

        self.assertEqual(payment.orders.first(), self.order)
        self.assertEqual(payment.status, paymentData['status'])
        self.assertEqual(payment.amount, paymentData['amount'])
        self.assertEqual(payment.installments, paymentData['installments'])
        self.assertEqual(payment.acquirerType, paymentData['acquirerType'])
        self.assertEqual(payment.paymentMethod.type, paymentData['paymentMethod'])
        self.assertEqual(payment.pix, paymentData['pix'])
        self.assertEqual(payment.boleto, paymentData['boleto'])
        self.assertEqual(payment.card, paymentData['card'])

    def test_process_payment_sets_correct_data_when_payment_already_exists(self):
        Payment.objects.all().delete()

        existent_payment = self.create_payment(orders=[self.order], externalId='testId', status=PaymentStatus.WAITING_PAYMENT.id)
        paymentData: dict[str, Any] = {
            'id': 'testId',
            'status': PaymentStatus.PAID.id,
            'amount': 10,
            'installments': 1,
            'acquirerType': 'testAcquirer',
            'paymentMethod': 'pix',
            'pix': {
                'qrCode': 'testValue',
            },
            'boleto': {
                'url': 'testValue',
            }
        }
        payment = PaymentStrategy.process_payment(paymentData=paymentData, orders=[self.order])

        self.assertEqual(payment, existent_payment)
        self.assertEqual(Payment.objects.count(), 1)
        self.assertEqual(payment.status, paymentData['status'])
        self.assertEqual(payment.externalId, paymentData['id'])

    def test_get_subscription_payload(self):
        self.order.type = ProductType.SUBSCRIPTION.id
        self.order.amount = 1_000
        self.order.baseAmount = 2_000
        self.order.paymentMethod = self.pix
        self.order.customer = self.create_customer(
            name='Test Customer',
            email='<EMAIL>',
            phone='123456789',
            birthDate='1990-01-01',
            docType='CPF',
            docNumber='12345678901'
        )
        self.order.installments = 2
        self.order.checkoutUrl = 'https://payload.test.com'
        self.order.refererUrl = 'https://referer.payload.com'
        self.order.save()

        offer = self.order.offer
        offer.recurence_period = 144
        offer.quantity_recurrences = 99
        offer.trial_days = 8
        offer.retry_interval = 4
        offer.save()

        expected_payload = {
            'amount': 2000.0,
            'paymentMethod': 'pix',
            'customer': {
                'name': 'Test Customer',
                'email': '<EMAIL>',
                'phone': '123456789',
                'birthDate': '1990-01-01',
                'docType': 'CPF',
                'docNumber': '12345678901',
                'ip': None
            },
            'address': {},
            'installments': 2,
            'exId': self.order.refId,
            'feeByItem': True,
            'items': [
                {
                    'title': 'Test Product',
                    'unitPrice': 1000.0,
                    'quantity': 1,
                    'tangible': False,
                    'externalRef': offer.id,
                }
            ],
            'seller_id': str(self.order.product.user.company.externalId),
            'splits': [],
            'expiresInDays': 1,
            'checkoutUrl': 'https://payload.test.com',
            'saveCard': False,
            'refererUrl': 'https://referer.payload.com',
            'recurrence_period': 30,
            'quantity_recurrences': 99,
            'trial_days': 8,
            'max_retries': 3,
            'retry_interval': 4,
        }

        response = PaymentStrategy().get_subscription_payload(self.order)

        with self.subTest('Payload'):
            for key, value in expected_payload.items():
                self.assertEqual(response[key], value, f'Key: {key} should be {value}')

    def test_to_subscription_model(self):
        Subscription.objects.all().delete()

        subscription = PaymentStrategy().to_subscription_model(self.order, subscription=None)

        self.assertEqual(Subscription.objects.count(), 1)
        self.assertEqual(subscription.parent_order, self.order)
        self.assertEqual(subscription.amount, self.order.baseAmount)
        self.assertEqual(subscription.paymentMethod, self.order.paymentMethod)
        self.assertEqual(subscription.customer, self.order.customer)
        self.assertEqual(subscription.product, self.order.product)
        self.assertEqual(subscription.offer, self.order.offer)
        self.assertEqual(self.order.subscription, subscription)
        self.assertEqual(self.order.subscription_period, 1)

    def test_to_subscription_model_with_subscription(self):
        count_before = Subscription.objects.count()

        subscription = PaymentStrategy().to_subscription_model(self.order, subscription=self.subscription)

        self.assertEqual(Subscription.objects.count() - count_before, 0)
        self.assertEqual(subscription.parent_order, self.order)
        self.assertEqual(subscription.amount, self.order.baseAmount)
        self.assertEqual(subscription.paymentMethod, self.order.paymentMethod)
        self.assertEqual(subscription.customer, self.order.customer)
        self.assertEqual(subscription.product, self.order.product)
        self.assertEqual(subscription.offer, self.order.offer)
        self.assertEqual(self.order.subscription, subscription)
        self.assertEqual(self.order.subscription_period, 1)

    def test_set_split_data_to_subscription(self):
        split_data = {
            'id': 149,
            'status': 'active',
            'recurrence_period': 1,
            'quantity_recurrences': 3,
            'trial_days': 0,
            'max_retries': 3,
            'retry_interval': 1,
            'next_payment_date': '2025-01-29T10:58:57.516334-03:00',
            'first_payment': {
                'amount': 200
            }
        }

        PaymentStrategy().set_split_data_to_subscription(self.subscription, split_data)

        self.subscription.save()
        self.subscription.refresh_from_db()

        self.assertEqual(self.subscription.externalId, split_data['id'])
        self.assertEqual(self.subscription.status, split_data['status'])
        self.assertEqual(self.subscription.recurrence_period, split_data['recurrence_period'])
        self.assertEqual(self.subscription.quantity_recurrences, split_data['quantity_recurrences'])
        self.assertEqual(self.subscription.trial_days, split_data['trial_days'])
        self.assertEqual(self.subscription.max_retries, split_data['max_retries'])
        self.assertEqual(self.subscription.retry_interval, split_data['retry_interval'])
        self.assertEqual(self.subscription.next_payment_date, timezone.datetime.strptime(split_data['next_payment_date'], '%Y-%m-%dT%H:%M:%S.%f%z'))
        self.assertEqual(self.subscription.amount, split_data['first_payment']['amount'])

    def test_process_new_subscription_payment_calls_create_new_subscription_order_correctly(self):
        webhook_data = {
            'id': 'paymentTestExternalId',
            'status': PaymentStatus.PAID.id,
            'amount': 10,
        }

        new_order = self.create_order()

        create_new_subscription_order_mock = mock.patch(
            'gateway.strategies.base.PaymentStrategy.create_new_subscription_order'
        ).start()
        create_new_subscription_order_mock.return_value = new_order

        mock.patch(
            'gateway.strategies.base.PaymentStrategy.process_payment'
        ).start()

        mock.patch(
            'gateway.strategies.base.PaymentStrategy.create_order_splits'
        ).start()

        PaymentStrategy().process_new_subscription_payment(
            subscription=self.subscription,
            webhook_data=webhook_data
        )

        create_new_subscription_order_mock.assert_called_once_with(
            self.order,
            self.subscription,
            webhook_data,
        )

    def test_process_new_subscription_payment_calls_process_payment_correctly(self):
        webhook_data = {
            'id': 'paymentTestExternalId',
            'status': PaymentStatus.PAID.id,
            'amount': 10,
        }

        new_order = self.create_order()

        create_new_subscription_order_mock = mock.patch(
            'gateway.strategies.base.PaymentStrategy.create_new_subscription_order'
        ).start()
        create_new_subscription_order_mock.return_value = new_order

        process_payment_mock = mock.patch(
            'gateway.strategies.base.PaymentStrategy.process_payment'
        ).start()

        mock.patch(
            'gateway.strategies.base.PaymentStrategy.create_order_splits'
        ).start()

        PaymentStrategy().process_new_subscription_payment(
            subscription=self.subscription,
            webhook_data=webhook_data
        )

        process_payment_mock.assert_called_once_with(
            paymentData=webhook_data,
            orders=[new_order],
            subscriptions=[self.subscription]
        )

    def test_process_new_subscription_payment_calls_create_order_splits(self):
        webhook_data = {
            'id': 'paymentTestExternalId',
            'status': PaymentStatus.PAID.id,
            'amount': 10,
        }

        new_order = self.create_order()

        create_new_subscription_order_mock = mock.patch(
            'gateway.strategies.base.PaymentStrategy.create_new_subscription_order'
        ).start()
        create_new_subscription_order_mock.return_value = new_order

        mock.patch(
            'gateway.strategies.base.PaymentStrategy.process_payment'
        ).start()

        update_recurrence_metrics_mock = mock.patch(
            'gateway.strategies.base.Subscription.update_recurrence_metrics'
        ).start()

        PaymentStrategy().process_new_subscription_payment(
            subscription=self.subscription,
            webhook_data=webhook_data
        )

        update_recurrence_metrics_mock.assert_called_once()

    def test_process_new_subscription_payment_sets_new_payment_order_correctly(self):
        webhook_data = {
            'id': 'paymentTestExternalId',
            'status': PaymentStatus.PAID.id,
            'amount': 10,
        }

        new_order = self.create_order()

        create_new_subscription_order_mock = mock.patch(
            'gateway.strategies.base.PaymentStrategy.create_new_subscription_order'
        ).start()
        create_new_subscription_order_mock.return_value = new_order

        new_payment_mock = self.create_payment()

        process_payment_mock = mock.patch(
            'gateway.strategies.base.PaymentStrategy.process_payment'
        ).start()
        process_payment_mock.return_value = new_payment_mock

        mock.patch(
            'gateway.strategies.base.PaymentStrategy.create_order_splits'
        ).start()

        PaymentStrategy().process_new_subscription_payment(
            subscription=self.subscription,
            webhook_data=webhook_data
        )

        self.assertEqual(new_payment_mock.orders.first(), new_order)

    def test_process_new_subscription_payment_sets_next_payment_date(self):
        expected_next_payment_date = '2025-01-29T10:58:57.516334-00:00'
        webhook_data = {
            'id': 'paymentTestExternalId',
            'status': PaymentStatus.PAID.id,
            'amount': 10,
            'subscription': {
                'next_payment_date': expected_next_payment_date,
            }
        }

        new_order = self.create_order()

        create_new_subscription_order_mock = mock.patch(
            'gateway.strategies.base.PaymentStrategy.create_new_subscription_order'
        ).start()
        create_new_subscription_order_mock.return_value = new_order

        new_payment_mock = self.create_payment()

        process_payment_mock = mock.patch(
            'gateway.strategies.base.PaymentStrategy.process_payment'
        ).start()
        process_payment_mock.return_value = new_payment_mock

        mock.patch(
            'gateway.strategies.base.PaymentStrategy.create_order_splits'
        ).start()

        PaymentStrategy().process_new_subscription_payment(
            subscription=self.subscription,
            webhook_data=webhook_data
        )

        self.subscription.refresh_from_db()

        self.assertEqual(
            self.subscription.next_payment_date.isoformat(),  # type:ignore
            timezone.datetime.strptime(expected_next_payment_date, '%Y-%m-%dT%H:%M:%S.%f%z').isoformat()
        )

    def test_create_new_subscription_order_creates_order_correctly(self):
        parent_order_mock = mock.MagicMock()

        order_objects_create_mock = mock.patch(
            'gateway.strategies.base.Order.objects.create'
        ).start()

        webhook_data_mock = mock.Mock()

        mock.patch('gateway.strategies.base.PaymentStrategy.set_orders_split_data').start()

        PaymentStrategy().create_new_subscription_order(
            parent_order=parent_order_mock,
            subscription=self.subscription,
            webhook_data=webhook_data_mock,
        )

        order_objects_create_mock.assert_called_once_with(
            subscription=self.subscription,
            customer=parent_order_mock.customer,
            amount=webhook_data_mock.get('amount'),
            baseAmount=parent_order_mock.baseAmount,
            paymentMethod=parent_order_mock.paymentMethod,
            product=parent_order_mock.product,
            offer=parent_order_mock.offer,
            parent_order=parent_order_mock,
            type='subscription',
            card=parent_order_mock.card,
            affiliate=parent_order_mock.affiliate,
            subscription_period=self.subscription.current_period + 1,
            coupon=parent_order_mock.coupon,
            couponCode=parent_order_mock.couponCode,
            checkout=parent_order_mock.checkout,
        )

    def test_create_new_subscription_order_calls_set_orders_split_data(self):
        parent_order_mock = mock.MagicMock()

        order_mock = mock.MagicMock()

        order_objects_create_mock = mock.patch(
            'gateway.strategies.base.Order.objects.create'
        ).start()
        order_objects_create_mock.return_value = order_mock

        set_orders_split_data_mock = mock.patch(
            'gateway.strategies.base.PaymentStrategy.set_orders_split_data'
        ).start()

        webhook_data_mock = mock.Mock()

        PaymentStrategy().create_new_subscription_order(
            parent_order=parent_order_mock,
            subscription=self.subscription,
            webhook_data=webhook_data_mock,
        )

        set_orders_split_data_mock.assert_called_once_with(
            paymentData=webhook_data_mock,
            orders=[order_mock],
        )

    def test_handle_payment_status_returns_does_nothing_when_status_not_changed(self):
        handle_approval_mock = mock.patch('gateway.strategies.base.handle_approval').start()
        handle_refund_mock = mock.patch('gateway.strategies.base.handle_refund').start()
        handle_chargeback_mock = mock.patch('gateway.strategies.base.handle_chargeback').start()
        handle_subcription_renewal_payment_mock = mock.patch('gateway.strategies.base.handle_subcription_renewal_payment').start()

        PaymentStrategy().handle_payment_status_change(
            payment=self.payment,
            current_status=PaymentStatus.PAID.id,
            new_status=PaymentStatus.PAID.id,
            webhook_data={}
        )

        handle_approval_mock.assert_not_called()
        handle_refund_mock.assert_not_called()
        handle_chargeback_mock.assert_not_called()
        handle_subcription_renewal_payment_mock.assert_not_called()

    def get_webhook_data(
        self,
        status: str = PaymentStatus.PAID.id,
        order_date_field: str = 'paidAt',
        order_date: str | None = None,
        releaseDate: str | None = None,
    ) -> dict[str, Any]:
        data = {
            'status': status,
            'pending': {'releaseDate': releaseDate},
        }

        if order_date_field and order_date:
            data.update({order_date_field: order_date})

        return data

    def test_handle_payment_status_change_from_waiting_payment_to_paid_without_paidAt(self):
        self.order.status = PaymentStatus.WAITING_PAYMENT.id
        self.order.paidAt = None
        self.order.save(update_fields=['status', 'paidAt'])

        data = self.get_webhook_data(status=PaymentStatus.PAID.id)

        handle_approval_mock = mock.patch(
            'gateway.strategies.base.handle_approval'
        ).start()

        PaymentStrategy().handle_payment_status_change(
            payment=self.payment,
            current_status=self.order.status,
            new_status=PaymentStatus.PAID.id,
            webhook_data=data
        )

        self.order.refresh_from_db()
        self.assertEqual(self.order.status, PaymentStatus.PAID.id)
        self.assertAlmostEqual(
            self.order.paidAt,  # type:ignore
            timezone.now(),
            delta=timezone.timedelta(seconds=1),
            msg='paidAt should be updated to current time'
        )
        handle_approval_mock.assert_called()

    def test_handle_payment_status_change_from_waiting_payment_to_paid(self):
        self.order.status = PaymentStatus.WAITING_PAYMENT.id
        self.order.paidAt = None
        self.order.save(update_fields=['status', 'paidAt'])

        data = self.get_webhook_data(
            status=PaymentStatus.PAID.id,
            order_date_field='paidAt',
            order_date='2022-01-01T00:00:00-03:00',
            releaseDate='2024-01-01T00:00:00-03:00',
        )

        handle_approval_mock = mock.patch(
            'gateway.strategies.base.handle_approval'
        ).start()

        send_purchase_event_to_fb_mock = mock.patch(
            'gateway.strategies.base.send_purchase_event_to_fb.delay'
        ).start()

        PaymentStrategy().handle_payment_status_change(
            payment=self.payment,
            current_status=self.order.status,
            new_status=PaymentStatus.PAID.id,
            webhook_data=data
        )

        self.order.refresh_from_db()

        self.assertEqual(self.order.status, PaymentStatus.PAID.id)
        self.assertEqual(
            self.order.paidAt,
            timezone.datetime.strptime(data['paidAt'], '%Y-%m-%dT%H:%M:%S%z')
        )
        self.assertEqual(
            self.order.releaseDate,
            timezone.datetime.strptime(data['pending']['releaseDate'], '%Y-%m-%dT%H:%M:%S%z')
        )

        calls = [call for call in handle_approval_mock.mock_calls if call != mock.call.__bool__()]

        self.assertEqual(len(calls), len(self.payment.orders.all()), calls)

        with self.subTest('Test handle_approval calls'):
            for i, call in enumerate(calls):
                kwargs = call.kwargs
                self.assertEqual(kwargs['order'], self.payment.orders.all()[i])
                self.assertEqual(kwargs['payment'], self.payment)
                self.assertEqual(kwargs['webhook_data'], data)

        send_purchase_event_to_fb_mock.assert_called_once_with(
            orders=list(self.payment.orders.all()),
        )

    def test_handle_payment_status_change_from_paid_to_refunded(self):
        self.order.status = PaymentStatus.PAID.id
        self.order.save(update_fields=['status'])

        data = self.get_webhook_data(
            status=PaymentStatus.REFUNDED.id,
            order_date_field='refundedAt',
            order_date='2022-01-01T00:00:00-03:00',
        )

        handle_refund_mock = mock.patch(
            'gateway.strategies.base.handle_refund'
        ).start()

        PaymentStrategy().handle_payment_status_change(
            payment=self.payment,
            current_status=self.order.status,
            new_status=PaymentStatus.REFUNDED.id,
            webhook_data=data
        )

        self.order.refresh_from_db()

        self.assertEqual(self.order.status, PaymentStatus.REFUNDED.id)
        self.assertEqual(
            self.order.refundedAt,
            timezone.datetime.strptime(data['refundedAt'], '%Y-%m-%dT%H:%M:%S%z')
        )

        calls = [call for call in handle_refund_mock.mock_calls if call != mock.call.__bool__()]

        self.assertEqual(len(calls), len(self.payment.orders.all()), calls)

        with self.subTest('Test handle_refund calls'):
            for i, call in enumerate(calls):
                kwargs = call.kwargs
                self.assertEqual(kwargs['order'], self.payment.orders.all()[i])
                self.assertEqual(kwargs['payment'], self.payment)
                self.assertEqual(kwargs['webhook_data'], data)

    def test_handle_payment_status_change_from_paid_to_chargedback(self):
        self.order.status = PaymentStatus.PAID.id
        self.order.save(update_fields=['status'])

        data = self.get_webhook_data(
            status=PaymentStatus.CHARGEDBACK.id,
            order_date_field='chargedbackAt',
            order_date='2022-01-01T00:00:00-03:00',
        )

        handle_chargeback_mock = mock.patch(
            'gateway.strategies.base.handle_chargeback'
        ).start()

        PaymentStrategy().handle_payment_status_change(
            payment=self.payment,
            current_status=self.order.status,
            new_status=PaymentStatus.CHARGEDBACK.id,
            webhook_data=data
        )

        self.order.refresh_from_db()

        self.assertEqual(self.order.status, PaymentStatus.CHARGEDBACK.id)
        self.assertEqual(
            self.order.chargedbackAt,
            timezone.datetime.strptime(data['chargedbackAt'], '%Y-%m-%dT%H:%M:%S%z')
        )

        calls = [call for call in handle_chargeback_mock.mock_calls if call != mock.call.__bool__()]

        self.assertEqual(len(calls), len(self.payment.orders.all()), calls)

        with self.subTest('Test handle_chargeback calls'):
            for i, call in enumerate(calls):
                kwargs = call.kwargs
                self.assertEqual(kwargs['order'], self.payment.orders.all()[i])
                self.assertEqual(kwargs['payment'], self.payment)
                self.assertEqual(kwargs['webhook_data'], data)

    def test_handle_payment_status_change_from_paid_to_med(self):
        self.order.status = PaymentStatus.PAID.id
        self.order.save(update_fields=['status'])

        data = self.get_webhook_data(
            status=PaymentStatus.MED.id,
            order_date_field='chargedbackAt',
            order_date='2022-01-01T00:00:00-03:00',
        )

        handle_med_mock = mock.patch(
            'gateway.strategies.base.handle_med'
        ).start()

        PaymentStrategy().handle_payment_status_change(
            payment=self.payment,
            current_status=self.order.status,
            new_status=PaymentStatus.MED.id,
            webhook_data=data
        )

        self.order.refresh_from_db()

        self.assertEqual(self.order.status, PaymentStatus.MED.id)
        self.assertEqual(
            self.order.chargedbackAt,
            timezone.datetime.strptime(data['chargedbackAt'], '%Y-%m-%dT%H:%M:%S%z')
        )

        calls = [call for call in handle_med_mock.mock_calls if call != mock.call.__bool__()]

        self.assertEqual(len(calls), len(self.payment.orders.all()), calls)

        with self.subTest('Test handle_med calls'):
            for i, call in enumerate(calls):
                kwargs = call.kwargs
                self.assertEqual(kwargs['order'], self.payment.orders.all()[i])
                self.assertEqual(kwargs['payment'], self.payment)
                self.assertEqual(kwargs['webhook_data'], data)

    def test_handle_payment_status_change_from_paid_to_canceled(self):
        self.order.status = PaymentStatus.PAID.id
        self.order.save(update_fields=['status'])

        data = self.get_webhook_data(
            status=PaymentStatus.CANCELED.id,
            order_date_field='chargedbackAt',
            order_date='2022-01-01T00:00:00-03:00',
        )

        PaymentStrategy().handle_payment_status_change(
            payment=self.payment,
            current_status=self.order.status,
            new_status=PaymentStatus.CANCELED.id,
            webhook_data=data
        )

        self.order.refresh_from_db()

        self.assertEqual(self.order.status, PaymentStatus.CANCELED.id)
        self.assertAlmostEqual(
            self.order.canceledAt,  # type:ignore
            timezone.now(),
            delta=timezone.timedelta(seconds=1),
        )

    def test_handle_payment_status_change_from_scheduled_to_waiting_payment(self):
        self.order.status = PaymentStatus.SCHEDULED.id
        self.order.save(update_fields=['status'])
        self.payment.orders.set([self.order])

        data = self.get_webhook_data(status=PaymentStatus.WAITING_PAYMENT.id)

        handle_subcription_renewal_payment_mock = mock.patch(
            'gateway.strategies.base.handle_subcription_renewal_payment.delay'
        ).start()

        PaymentStrategy().handle_payment_status_change(
            payment=self.payment,
            current_status=PaymentStatus.SCHEDULED.id,
            new_status=PaymentStatus.WAITING_PAYMENT.id,
            webhook_data=data
        )

        self.order.refresh_from_db()

        self.assertEqual(self.order.status, PaymentStatus.WAITING_PAYMENT.id)

        handle_subcription_renewal_payment_mock.assert_called_once_with(
            payment=self.payment,
            order=self.order,
            webhook_data=data
        )

    def test_handle_payment_status_change_set_date_field_as_datetime(self):
        self.order.status = PaymentStatus.PAID.id
        self.order.save(update_fields=['status'])

        data = self.get_webhook_data(
            status=PaymentStatus.CHARGEDBACK.id,
            order_date_field='chargedbackAt',
            order_date='2022-01-01T00:00:00-03:00',
        )

        handle_chargeback_mock = mock.patch(
            'gateway.strategies.base.handle_chargeback'
        ).start()

        PaymentStrategy().handle_payment_status_change(
            payment=self.payment,
            current_status=self.order.status,
            new_status=PaymentStatus.CHARGEDBACK.id,
            webhook_data=data
        )

        order = handle_chargeback_mock.call_args.kwargs['order']

        self.assertIsInstance(
            order.chargedbackAt,  # type:ignore
            timezone.datetime,
            'chargedbackAt should be a datetime object'
        )

    def test_handle_payment_status_change_set_date_field_as_datetime_on_paidAt(self):
        self.order.status = PaymentStatus.WAITING_PAYMENT.id
        self.order.paidAt = None
        self.order.save(update_fields=['status', 'paidAt'])

        data = self.get_webhook_data(
            status=PaymentStatus.PAID.id,
            order_date_field='paidAt',
            order_date='2022-01-01T00:00:00-03:00',
            releaseDate='2024-01-01T00:00:00-03:00',
        )

        handle_approval_mock = mock.patch(
            'gateway.strategies.base.handle_approval'
        ).start()

        mock.patch(
            'gateway.strategies.base.send_purchase_event_to_fb.delay'
        ).start()

        PaymentStrategy().handle_payment_status_change(
            payment=self.payment,
            current_status=self.order.status,
            new_status=PaymentStatus.PAID.id,
            webhook_data=data
        )

        order = handle_approval_mock.call_args.kwargs['order']

        self.assertIsInstance(
            order.paidAt,  # type:ignore
            timezone.datetime,
            'paidAt should be a datetime object'
        )

    def test_create_order_splits(self):
        Split.objects.all().delete()

        another_user = self.create_user()

        self.order.commissions = [
            {
                'userId': self.order.product.user.id,
                'type': 'producer',
                'commissionValue': 4,
                'commissionPercentage': 40,
            },
            {
                'userId': another_user.id,
                'type': 'affiliate',
                'commissionValue': 6,
                'commissionPercentage': 60,
            }
        ]
        self.order.save(update_fields=['commissions'])

        # Call
        PaymentStrategy.create_order_splits(self.order)

        self.assertEqual(Split.objects.count(), 2, 'Should create 2 splits')

        producer_split = Split.objects.get(user=self.order.product.user)
        affiliate_split = Split.objects.get(user=another_user)

        self.assertEqual(producer_split.order, self.order)
        self.assertEqual(producer_split.totalAmount, 4)
        self.assertEqual(producer_split.percentage, 40)
        self.assertEqual(producer_split.type, 'producer')

        self.assertEqual(affiliate_split.order, self.order)
        self.assertEqual(affiliate_split.totalAmount, 6)
        self.assertEqual(affiliate_split.percentage, 60)
        self.assertEqual(affiliate_split.type, 'affiliate')

    def test_create_order_splits_dont_duplicate_split(self):
        Split.objects.all().delete()

        self.order.commissions = [
            {
                'userId': self.order.product.user.id,
                'type': 'producer',
                'commissionValue': 4,
                'commissionPercentage': 40,
            },
            {
                'userId': self.order.product.user.id,
                'type': 'producer',
                'commissionValue': 4,
                'commissionPercentage': 40,
            }
        ]
        self.order.save(update_fields=['commissions'])

        # Call
        PaymentStrategy.create_order_splits(self.order)

        self.assertEqual(Split.objects.count(), 1, 'Should create only 1 splits')

    def test_build_address_payload(self):
        address = Address.objects.create(
            customer=self.customer,
            country='BR',
            state='SP',
            city='São Paulo',
            zipcode='01000000',
            street='Avenida Paulista',
            neighborhood='Centro',
            complement='Apto 123',
            number='456',
            is_default=True
        )

        self.order.address = address
        self.order.save(update_fields=['address'])

        expected_payload = {
            'country': 'BR',
            'state': 'SP',
            'city': 'São Paulo',
            'zipcode': '01000000',
            'street': 'Avenida Paulista',
            'neighborhood': 'Centro',
            'complement': 'Apto 123',
            'number': '456'
        }

        result = PaymentStrategy.build_address_payload(self.order)

        self.assertEqual(
            result,
            expected_payload,
            'Address payload should match expected payload',
        )
