from django.urls import reverse
from django.utils import timezone
from rest_framework_simplejwt.tokens import AccessToken

from cakto.tests.base import BaseTestCase
from user.enums import UserAccessLocations
from user.models import UserLoginHistory


class TestPaymentViews(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()

        cls.create_paymentMethods()

        cls.customer = cls.create_customer()

        cls.customer_user = cls.create_user(email=cls.customer.email)

        cls.order = cls.create_order(customer=cls.customer)

        cls.payment = cls.create_payment(orders=[cls.order], status='paid')

    def test_get_payment_status_view_with_user_that_has_no_password(self):
        url = reverse('payment-status', args=[self.payment.pk])

        self.customer_user.set_unusable_password()
        self.customer_user.save()

        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)

        data = response.json()

        self.assertIn('accessToken', data)

        token = AccessToken(data['accessToken'])
        self.assertEqual(token.payload.get('user_id'), self.customer_user.pk)

    def test_get_payment_status_view_with_user_that_does_have_password(self):
        url = reverse('payment-status', args=[self.payment.pk])

        self.customer_user.set_password('TestPassword')
        self.customer_user.save()

        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)

        data = response.json()

        self.assertNotIn('accessToken', data)

    def test_get_payment_status_view_amount_of_queries(self):
        url = reverse('payment-status', args=[self.payment.pk])

        with self.assertNumQueries(2):
            self.client.get(url)

    def test_get_payment_status_view_creates_user_login_history(self):
        url = reverse('payment-status', args=[self.payment.pk])

        self.customer_user.set_unusable_password()
        self.customer_user.save()

        # Call
        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)

        self.assertEqual(UserLoginHistory.objects.count(), 1)

        history: UserLoginHistory = UserLoginHistory.objects.first()  # type:ignore

        self.assertEqual(history.user, self.customer_user)
        self.assertIsNone(history.ip)
        self.assertIsNone(history.user_agent)
        self.assertEqual(
            history.access_token_source,
            UserAccessLocations.FIRST_PURCHASE.id
        )
        self.assertAlmostEqual(
            history.createdAt,
            timezone.now(),
            delta=timezone.timedelta(seconds=2)
        )

    def test_get_payment_status_view_without_token_does_not_creates_user_login_history(self):
        url = reverse('payment-status', args=[self.payment.pk])

        self.customer_user.recovery_password_token = None
        self.customer_user.recovery_password_token_expires = timezone.now() - timezone.timedelta(minutes=5)
        self.customer_user.save()

        # Call
        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)

        self.assertNotIn('otpAccessToken', response.json())

        self.assertEqual(UserLoginHistory.objects.count(), 0)

    def test_refund_otp_confirm_view_creates_user_login_history(self):
        url = reverse('request-refund-confirm-email')

        token = '123456'

        # Set up the user with a recovery password token
        self.customer_user.recovery_password_token = token
        self.customer_user.recovery_password_token_expires = timezone.now() + timezone.timedelta(minutes=5)
        self.customer_user.save()

        # Setup user_agent and IP
        user_agent = 'TestUserAgent'
        ip = '**********'
        payload = {
            'token': token,
            'email': self.customer_user.email,
        }

        # Call
        response = self.client.post(
            url,
            data=payload,
            REMOTE_ADDR=ip,
            HTTP_USER_AGENT=user_agent,
        )

        self.assertEqual(response.status_code, 200, response.content.decode())

        self.assertEqual(UserLoginHistory.objects.count(), 1)

        history: UserLoginHistory = UserLoginHistory.objects.first()  # type:ignore

        self.assertEqual(history.user, self.customer_user)
        self.assertEqual(history.ip, ip)
        self.assertEqual(history.user_agent, user_agent)
        self.assertEqual(
            history.access_token_source,
            UserAccessLocations.REFUND_VIEW.id
        )
        self.assertAlmostEqual(
            history.createdAt,
            timezone.now(),
            delta=timezone.timedelta(seconds=2)
        )
