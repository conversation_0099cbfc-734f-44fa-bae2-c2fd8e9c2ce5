from cakto.tests.base import BaseTestCase
from gateway.strategies.applepay_strategy import ApplePayStrategy
from rest_framework_simplejwt.tokens import AccessToken

class TestApplePayStrategy(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()

        cls.create_paymentMethods()

        cls.product = cls.create_product(price=10)

        cls.customer = cls.create_customer()

        cls.order = cls.create_order(
            product=cls.product,
            paymentMethod=cls.applepay,
            amount=10,
            customer=cls.customer,
        )

    def test_ApplePayStrategy_afterPaymentProcess_with_user_that_has_no_password(self):
        customer_user = self.create_user(email=self.customer.email)
        customer_user.set_unusable_password()
        customer_user.save()

        response_data_mock = {}

        ApplePayStrategy().afterPaymentProcess(response_data_mock, customer=self.customer)

        self.assertIn('accessToken', response_data_mock)

        token = AccessToken(response_data_mock['accessToken'])
        self.assertEqual(token.payload.get('user_id'), customer_user.pk)

    def test_ApplePayStrategy_afterPaymentProcess_with_user_that_has_password(self):
        self.create_user(email=self.customer.email, password='TestPassword')

        response_data_mock = {}

        ApplePayStrategy().afterPaymentProcess(response_data_mock, customer=self.customer)

        self.assertNotIn('accessToken', response_data_mock)
