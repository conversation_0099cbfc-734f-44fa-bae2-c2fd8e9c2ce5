import json
from decimal import Decimal
from unittest import mock

import responses
from django.conf import settings
from django.urls import reverse
from django.utils import timezone

from cakto.tests.base import BaseTestCase
from customer.models import Address
from gateway.models import Order
from gateway.sdk.splitpay import SplitPay
from product.enums import PaymentMethodStatus
from product.models import Checkout, Coupon, Offer
from user.enums import UserAccessLocations
from user.models import ExperimentalFeature, User, UserLoginHistory
from user.tests.tests_user import mock_createAccount


class TestCheckout(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.create_paymentMethods()

        cls.product = cls.create_product(price=10)

    @responses.activate
    def test_checkout_pix(self):
        responses.post(
            url=settings.GATEWAY_URL + 'api/checkout/',
            json=json.load(open('gateway/tests/mocks/split_pix_payment_response.json')),
            status=200,
        )

        url = reverse('checkout', args=[self.product.short_id])
        payload = dict(json.load(open('gateway/tests/mocks/pix_payment_payload.json')))
        payload['items'][0]['id'] = self.product.offers.first().pk
        expected_response = {'status': 'waiting_payment',
                             'paymentMethod': 'pix',
                             'installments': 1,
                             'amount': '10.00',
                             'pix': {'qrCode': mock.ANY, 'expirationDate': mock.ANY}}

        mock.patch('gateway.serializers.CheckoutProcessPaymentSerializer._validate_installments', mock.Mock()).start()
        with mock.patch('gateway.strategies.pix_strategy.PixStrategy.create_order_splits', mock.Mock()):
            response = self.client.post(url, data=payload, format='json')

        self.assertEqual(response.status_code, 200, response.json())
        for key, value in expected_response.items():
            with self.subTest():
                self.assertEqual(value, response.json()['payments'][0][key], response.json())

    @responses.activate
    def test_checkout_openfinance_nubank(self):
        response_mock = json.load(open('gateway/tests/mocks/split_openfinance_nubank_payment_response.json'))
        responses.post(
            url=settings.GATEWAY_URL + 'api/checkout/',
            json=response_mock,
            status=200,
        )

        url = reverse('checkout', args=[self.product.short_id])
        payload = json.load(open('gateway/tests/mocks/openfinance_nubank_payload.json'))
        payload['items'][0]['id'] = self.product.offers.first().pk
        expected_response = {
            'status': 'waiting_payment',
            'paymentMethod': 'openfinance_nubank',
            'installments': 1,
            'amount': '10.00',
            'openFinanceNubank': {'URL': response_mock['data']['openFinanceNubank']['URL']},
        }

        mock.patch('gateway.serializers.CheckoutProcessPaymentSerializer._validate_installments', mock.Mock()).start()
        with mock.patch('gateway.strategies.pix_strategy.PixStrategy.create_order_splits', mock.Mock()):
            response = self.client.post(url, payload, format='json')

        self.assertEqual(response.status_code, 200, response.json())
        for key, value in expected_response.items():
            with self.subTest():
                self.assertEqual(value, response.json()['payments'][0][key], response.json())

    @responses.activate
    def test_checkout_picpay(self):
        responses.post(
            url=settings.GATEWAY_URL + 'api/checkout/',
            json=json.load(open('gateway/tests/mocks/split_picpay_payment_response.json')),
            status=200,
        )

        url = reverse('checkout', args=[self.product.short_id])
        payload = dict(json.load(open('gateway/tests/mocks/picpay_payment_payload.json')))
        payload['items'][0]['id'] = self.product.offers.first().pk
        expected_response = {
            'status': 'waiting_payment',
            'paymentMethod': 'picpay',
                             'installments': 1,
                             'amount': '10.00',
                             'picpay': {
                                 'qrCode': mock.ANY,
                                 'expirationDate': mock.ANY,
                                 'paymentURL': mock.ANY,
                             }
        }

        mock.patch('gateway.serializers.CheckoutProcessPaymentSerializer._validate_installments', mock.Mock()).start()
        with mock.patch('gateway.strategies.pix_strategy.PixStrategy.create_order_splits', mock.Mock()):
            response = self.client.post(url, data=payload, format='json')

        self.assertEqual(response.status_code, 200, response.json())
        for key, value in expected_response.items():
            with self.subTest():
                self.assertEqual(value, response.json()['payments'][0][key], response.json())

    @responses.activate
    def test_checkout_card(self):
        responses.post(
            url=settings.GATEWAY_URL + 'api/checkout/',
            json=json.load(open('gateway/tests/mocks/split_card_payment_response.json')),
            status=200,
        )

        url = reverse('checkout', args=[self.product.short_id])
        payload = dict(json.load(open('gateway/tests/mocks/card_payment_payload.json')))
        payload['items'][0]['id'] = self.product.offers.first().pk
        expected_response = {'status': 'paid',
                             'paymentMethod': 'credit_card',
                             'installments': 1,
                             'amount': '10.00', }

        mock.patch('gateway.serializers.CheckoutProcessPaymentSerializer._validate_installments', mock.Mock()).start()
        with mock.patch.object(SplitPay, 'createAccount', new_callable=mock_createAccount):
            with mock.patch('gateway.strategies.card_strategy.CardStrategy.create_order_splits', mock.Mock()):
                response = self.client.post(url, data=payload, format='json')

        self.assertEqual(response.status_code, 200, response.json())
        for key, value in expected_response.items():
            with self.subTest():
                self.assertEqual(value, response.json()['payments'][0][key])

    @responses.activate
    def test_checkout_with_experimental_paymentMethod(self):
        self.openfinance_nubank.status = PaymentMethodStatus.TESTER_USER_ACCESS.id
        self.openfinance_nubank.save()

        self.tester_feature = ExperimentalFeature.objects.create(
            id=self.openfinance_nubank.type,
            name=self.openfinance_nubank.name,
        )

        self.product.user.experimental_features.add(self.tester_feature)

        response_mock = json.load(open('gateway/tests/mocks/split_openfinance_nubank_payment_response.json'))
        responses.post(
            url=settings.GATEWAY_URL + 'api/checkout/',
            json=response_mock,
            status=200,
        )

        url = reverse('checkout', args=[self.product.short_id])
        payload = json.load(open('gateway/tests/mocks/openfinance_nubank_payload.json'))
        payload['items'][0]['id'] = self.product.offers.first().pk
        expected_response = {
            'status': 'waiting_payment',
            'paymentMethod': 'openfinance_nubank',
            'installments': 1,
            'amount': '10.00',
            'openFinanceNubank': {'URL': response_mock['data']['openFinanceNubank']['URL']},
        }

        with mock.patch('gateway.strategies.pix_strategy.PixStrategy.create_order_splits', mock.Mock()):
            response = self.client.post(url, payload, format='json')

        self.assertEqual(response.status_code, 200, response.json())
        for key, value in expected_response.items():
            with self.subTest():
                self.assertEqual(value, response.json()['payments'][0][key], response.json())

    # TODO: IMPLEMENTAR TESTE PARA GOOGLEPAY E APPLEPAY

    @responses.activate
    def test_checkout_parent_order(self):
        responses.post(
            url=settings.GATEWAY_URL + 'api/checkout/',
            json=json.load(open('gateway/tests/mocks/split_card_payment_response.json')),
            status=200,
        )

        url = reverse('checkout', args=[self.product.short_id])
        payload = dict(json.load(open('gateway/tests/mocks/card_payment_payload.json')))
        payload['items'][0]['id'] = self.product.offers.first().pk

        product_2 = self.create_product(price=10, user=self.product.user)
        payload['items'].append({'id': product_2.offers.first().pk, 'offerType': 'orderbump'})

        mock.patch('gateway.serializers.CheckoutProcessPaymentSerializer._validate_installments', mock.Mock()).start()
        with mock.patch.object(SplitPay, 'createAccount', new_callable=mock_createAccount):
            with mock.patch('gateway.strategies.pix_strategy.PixStrategy.create_order_splits', mock.Mock()):
                self.client.post(url, data=payload, format='json')

        main_order = Order.objects.get(offer_type='main')
        orderbump_order = Order.objects.get(offer_type='orderbump')

        self.assertEqual(Order.objects.count(), 2)
        self.assertEqual(orderbump_order.parent_order, main_order)
        self.assertIsNone(main_order.parent_order)

    def test_coupoun_is_applied_on_pix_checkout(self):
        coupon = Coupon.objects.create(code='Test', discount=10, startTime=timezone.now())
        coupon.products.add(self.product)

        main_offer = self.product.offers.first()
        main_offer.price = 10
        main_offer.save()

        createTransactionResponse = json.load(open('gateway/tests/mocks/split_pix_payment_response.json'))
        createTransactionResponse['data']['items'] = [
            {
                'externalRef': str(main_offer.pk),
                'unitPrice': float(main_offer.price * 0.9),
            }
        ]
        createTransactionResponse['data']['baseAmount'] = float(main_offer.price) * 0.9
        createTransactionResponse['data']['amount'] = float(main_offer.price) * 0.9

        url = reverse('checkout', args=[self.product.short_id])
        payload = dict(json.load(open('gateway/tests/mocks/pix_payment_payload.json')))
        payload['items'][0]['id'] = main_offer.pk

        payload['coupon'] = coupon.code

        expected_amount = Decimal('9.00')

        with mock.patch(
            'gateway.strategies.base.SplitPay.createTransaction',
            mock.Mock(return_value=self.get_response_mock(content=createTransactionResponse))
        ) as createTransactionMock:
            response = self.client.post(url, data=payload, format='json')

        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertEqual(expected_amount, Decimal(createTransactionMock.call_args[1]['amount']))

        order = Order.objects.first()
        self.assertEqual(order.discount, Decimal('1.00'))  # type:ignore
        self.assertEqual(order.coupon, coupon)  # type:ignore
        self.assertEqual(order.couponCode, coupon.code)  # type:ignore
        self.assertEqual(order.amount, Decimal('9.00'))  # type:ignore

    def test_coupoun_is_applied_on_pix_checkout_with_order_bump_with_applyOnBumps_True(self):
        self.product.offers.update(price=100)

        main_offer = self.product.offers.first()
        order_bump_offer = Offer.objects.create(product=self.product, price=5)

        createTransactionResponse = json.load(open('gateway/tests/mocks/split_pix_payment_response.json'))
        createTransactionResponse['data']['items'] = [
            {
                'externalRef': str(main_offer.pk),
                'unitPrice': float(main_offer.price),
            },
            {
                'externalRef': str(order_bump_offer.pk),
                'unitPrice': float(order_bump_offer.price),
            }
        ]

        url = reverse('checkout', args=[self.product.short_id])
        payload = dict(json.load(open('gateway/tests/mocks/pix_payment_payload.json')))
        payload['items'][0]['id'] = main_offer.pk

        # Adds coupon to payload
        coupon = Coupon.objects.create(code='Test', discount=10, startTime=timezone.now(), applyOnBumps=True)
        coupon.products.add(self.product)

        createTransactionResponse['data']['baseAmount'] = float(main_offer.price + order_bump_offer.price) * 0.9

        payload['coupon'] = coupon.code

        # Adds order bump to payload
        payload['items'].append({
            'id': order_bump_offer.pk,
            'offerType': 'orderbump'
        })

        expected_amount = Decimal('94.50')

        with mock.patch(
            'gateway.strategies.base.SplitPay.createTransaction',
            mock.Mock(return_value=self.get_response_mock(content=createTransactionResponse))
        ) as createTransactionMock:
            response = self.client.post(url, data=payload, format='json')

        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertEqual(expected_amount, Decimal(createTransactionMock.call_args[1]['amount']))

    def test_coupoun_isnt_applied_on_order_bump_when_applyOnBumps_is_false(self):
        Order.objects.all().delete()

        createTransactionResponse = json.load(open('gateway/tests/mocks/split_pix_payment_response.json'))
        url = reverse('checkout', args=[self.product.short_id])
        payload = dict(json.load(open('gateway/tests/mocks/pix_payment_payload.json')))
        payload['items'][0]['id'] = self.product.offers.first().pk

        # Adds coupon to payload
        coupon = Coupon.objects.create(code='Test', discount=10, startTime=timezone.now(), applyOnBumps=False)
        coupon.products.add(self.product)
        self.product.offers.update(price=100)
        payload['coupon'] = coupon.code

        # Adds order bump to payload
        order_bump_offer = Offer.objects.create(product=self.product, price=5)
        payload['items'].append({
            'id': order_bump_offer.pk,
            'offerType': 'orderbump'
        })

        expected_amount = Decimal('95.00')

        with mock.patch(
            'gateway.strategies.base.SplitPay.createTransaction',
            mock.Mock(return_value=self.get_response_mock(content=createTransactionResponse))
        ) as createTransactionMock:
            response = self.client.post(url, data=payload, format='json')

        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertEqual(expected_amount, Decimal(createTransactionMock.call_args[1]['amount']))

        order: Order = Order.objects.first()  # type:ignore
        self.assertEqual(order.discount, 0)
        self.assertEqual(order.coupon, None)
        self.assertEqual(order.couponCode, None)

    def test_coupoun_is_applied_on_card_checkout(self):
        createTransactionResponse = json.load(open('gateway/tests/mocks/split_card_payment_response.json'))
        url = reverse('checkout', args=[self.product.short_id])
        payload = dict(json.load(open('gateway/tests/mocks/card_payment_payload.json')))
        payload['items'][0]['id'] = self.product.offers.first().pk

        coupon = Coupon.objects.create(code='Test', discount=10, startTime=timezone.now())
        coupon.products.add(self.product)
        payload['coupon'] = coupon.code
        self.product.offers.update(price=100)

        expected_amount = Decimal('90.00')

        with mock.patch(
            'gateway.strategies.base.SplitPay.createTransaction',
            mock.Mock(return_value=self.get_response_mock(content=createTransactionResponse))
        ) as createTransactionMock:
            response = self.client.post(url, data=payload, format='json')

        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertEqual(expected_amount, Decimal(createTransactionMock.call_args[1]['amount']))

    def test_coupon_saved_in_order(self):
        createTransactionResponse = json.load(open('gateway/tests/mocks/split_card_payment_response.json'))
        url = reverse('checkout', args=[self.product.short_id])
        payload = dict(json.load(open('gateway/tests/mocks/card_payment_payload.json')))
        payload['items'][0]['id'] = self.product.offers.first().pk

        coupon = Coupon.objects.create(code='Test', discount=10, startTime=timezone.now())
        coupon.products.add(self.product)
        payload['coupon'] = coupon.code
        self.product.offers.update(price=100)

        with mock.patch(
            'gateway.strategies.base.SplitPay.createTransaction',
            mock.Mock(return_value=self.get_response_mock(content=createTransactionResponse))
        ):
            self.client.post(url, data=payload, format='json')

        order = Order.objects.first()
        self.assertEqual(order.coupon, coupon)  # type:ignore
        self.assertEqual(order.couponCode, coupon.code)  # type:ignore
        self.assertEqual(order.discount, Decimal('10.00'))  # type:ignore

    @responses.activate
    def test_checkout_accepts_wrong_formating_item_id(self):
        responses.post(
            url=settings.GATEWAY_URL + 'api/checkout/',
            json=json.load(open('gateway/tests/mocks/split_pix_payment_response.json')),
            status=200,
        )

        url = reverse('checkout', args=[self.product.short_id])
        payload = dict(json.load(open('gateway/tests/mocks/pix_payment_payload.json')))
        payload['items'][0]['id'] = self.product.offers.first().pk + "'_anythingElse"

        mock.patch('gateway.serializers.CheckoutProcessPaymentSerializer._validate_installments', mock.Mock()).start()
        with mock.patch('gateway.strategies.pix_strategy.PixStrategy.create_order_splits', mock.Mock()):
            response = self.client.post(url, data=payload, format='json')

        self.assertEqual(response.status_code, 200, response.json())

    @responses.activate
    def test_mercadopago_deviceId_is_sended_to_split(self):
        split_tx_responses = responses.post(
            url=settings.GATEWAY_URL + 'api/checkout/',
            json=json.load(open('gateway/tests/mocks/split_pix_payment_response.json')),
            status=200,
        )

        url = reverse('checkout', args=[self.product.short_id])
        payload = dict(json.load(open('gateway/tests/mocks/pix_payment_payload.json')))
        payload['items'][0]['id'] = self.product.offers.first().pk

        # Adds mercadopago deviceId to payload that will be processed by our api
        payload['deviceId'] = 'mp_deviceId_7766'

        mock.patch('gateway.serializers.CheckoutProcessPaymentSerializer._validate_installments', mock.Mock()).start()
        with mock.patch('gateway.strategies.pix_strategy.PixStrategy.create_order_splits', mock.Mock()):
            response = self.client.post(url, data=payload, format='json')

        self.assertEqual(response.status_code, 200, response.json())

        payload_sended_to_split = json.loads(split_tx_responses.calls[0].request.body)  # type:ignore

        self.assertIn('mercadopago', payload_sended_to_split, 'mercadopago not in payload sended to split')
        self.assertEqual(payload['deviceId'], payload_sended_to_split['mercadopago']['deviceId'])

    @responses.activate
    def test_antifraud_profiling_attempt_reference_is_sended_to_split(self):
        split_tx_responses = responses.post(
            url=settings.GATEWAY_URL + 'api/checkout/',
            json=json.load(open('gateway/tests/mocks/split_pix_payment_response.json')),
            status=200,
        )

        url = reverse('checkout', args=[self.product.short_id])
        payload = dict(json.load(open('gateway/tests/mocks/pix_payment_payload.json')))
        payload['items'][0]['id'] = self.product.offers.first().pk

        # Adds antifraud profiling attempt reference to payload that will be processed by our api
        payload['antifraud_profiling_attempt_reference'] = 'prof_att_ref_8877'

        mock.patch('gateway.serializers.CheckoutProcessPaymentSerializer._validate_installments', mock.Mock()).start()
        with mock.patch('gateway.strategies.pix_strategy.PixStrategy.create_order_splits', mock.Mock()):
            response = self.client.post(url, data=payload, format='json')

        self.assertEqual(response.status_code, 200, response.json())

        payload_sended_to_split = json.loads(split_tx_responses.calls[0].request.body)  # type:ignore

        # Check if the antifraud profiling attempt reference is in the payload sent to Split
        self.assertIn('antifraud_profiling_attempt_reference', payload_sended_to_split, 'antifraud_profiling_attempt_reference not in payload sended to split')
        self.assertEqual(payload['antifraud_profiling_attempt_reference'], payload_sended_to_split['antifraud_profiling_attempt_reference'])

    @responses.activate
    def test_payment_process_sets_order_checkout(self):
        responses.post(
            url=settings.GATEWAY_URL + 'api/checkout/',
            json=json.load(open('gateway/tests/mocks/split_card_payment_response.json')),
            status=200,
        )

        offer = self.product.offers.first()

        checkout = Checkout.objects.create(
            product=self.product,
            name='Test Checkout',
        )
        checkout.offers.set([offer])

        url = reverse('checkout', args=[self.product.short_id])
        payload = dict(json.load(open('gateway/tests/mocks/card_payment_payload.json')))

        # Update payload to use checkout id
        payload['items'][0]['id'] = self.product.offers.first().pk + '_' + str(checkout.id)

        product_2 = self.create_product(price=10, user=self.product.user)
        payload['items'].append({'id': product_2.offers.first().pk, 'offerType': 'orderbump'})

        # Call
        mock.patch('gateway.serializers.CheckoutProcessPaymentSerializer._validate_installments', mock.Mock()).start()
        with mock.patch.object(SplitPay, 'createAccount', new_callable=mock_createAccount):
            with mock.patch('gateway.strategies.pix_strategy.PixStrategy.create_order_splits', mock.Mock()):
                result = self.client.post(url, data=payload, format='json')

        self.assertEqual(result.status_code, 200, result.content.decode())

        self.assertEqual(Order.objects.count(), 2)
        main_order = Order.objects.get(offer_type='main')
        orderbump_order = Order.objects.get(offer_type='orderbump')

        self.assertEqual(main_order.checkout, checkout)
        self.assertEqual(orderbump_order.checkout, checkout)

    @responses.activate
    def test_threeDSecure_is_sended_to_split(self):
        split_tx_responses = responses.post(
            url=settings.GATEWAY_URL + 'api/checkout/',
            json=json.load(open('gateway/tests/mocks/split_pix_payment_response.json')),
            status=200,
        )

        url = reverse('checkout', args=[self.product.short_id])
        payload = dict(json.load(open('gateway/tests/mocks/pix_payment_payload.json')))
        payload['items'][0]['id'] = self.product.offers.first().pk

        # Adds threeDSecure to payload that will be processed by our api
        payload['threeDSecure'] = {
            'Cavv': 'TestCavv',
            'Eci': 'TestEci',
            'Xid': 'TestXid',
            'ReferenceId': 'TestReferenceId',
            'Version': 'TestVersion',
            'DataOnly': False,
        }

        mock.patch('gateway.serializers.CheckoutProcessPaymentSerializer._validate_installments', mock.Mock()).start()
        with mock.patch('gateway.strategies.pix_strategy.PixStrategy.create_order_splits', mock.Mock()):
            response = self.client.post(url, data=payload, format='json')

        self.assertEqual(response.status_code, 200, response.json())

        payload_sended_to_split = json.loads(split_tx_responses.calls[0].request.body)  # type:ignore

        self.assertIn('threeDSecure', payload_sended_to_split, 'threeDSecure should be in payload sended to split')
        self.assertEqual(payload['threeDSecure'], payload_sended_to_split['threeDSecure'])

    @responses.activate
    def test_checkout_card_paid_creates_user_login_history(self):
        UserLoginHistory.objects.all().delete()  # Clear any existing login history

        responses.post(
            url=settings.GATEWAY_URL + 'api/checkout/',
            json=json.load(open('gateway/tests/mocks/split_card_payment_response.json')),
            status=200,
        )

        url = reverse('checkout', args=[self.product.short_id])
        payload = dict(json.load(open('gateway/tests/mocks/card_payment_payload.json')))
        payload['items'][0]['id'] = self.product.offers.first().pk

        mock.patch('gateway.serializers.CheckoutProcessPaymentSerializer._validate_installments', mock.Mock()).start()

        with mock.patch.object(SplitPay, 'createAccount', new_callable=mock_createAccount):
            with mock.patch('gateway.strategies.card_strategy.CardStrategy.create_order_splits', mock.Mock()):
                response = self.client.post(url, data=payload, format='json')

        self.assertEqual(response.status_code, 200, response.json())

        self.assertIn('accessToken', response.json())

        self.assertEqual(UserLoginHistory.objects.count(), 1)

        history: UserLoginHistory = UserLoginHistory.objects.first()  # type:ignore

        customer_user: User = User.objects.get(email=payload['customer']['email'])

        self.assertEqual(history.user, customer_user)
        self.assertEqual(history.access_token_source, UserAccessLocations.FIRST_PURCHASE.id)
        self.assertIsNone(history.ip)
        self.assertIsNone(history.user_agent)
        self.assertAlmostEqual(
            history.createdAt,
            timezone.now(),
            delta=timezone.timedelta(seconds=2)
        )

    @responses.activate
    def test_checkout_card_paid_does_not_create_user_login_history_when_customer_already_has_password_setted(self):
        UserLoginHistory.objects.all().delete()  # Clear any existing login history

        responses.post(
            url=settings.GATEWAY_URL + 'api/checkout/',
            json=json.load(open('gateway/tests/mocks/split_card_payment_response.json')),
            status=200,
        )

        url = reverse('checkout', args=[self.product.short_id])
        payload = dict(json.load(open('gateway/tests/mocks/card_payment_payload.json')))
        payload['items'][0]['id'] = self.product.offers.first().pk

        mock.patch('gateway.serializers.CheckoutProcessPaymentSerializer._validate_installments').start()
        mock.patch('gateway.strategies.card_strategy.CardStrategy.create_order_splits').start()

        # Create a user with customer email and set a usable password
        with mock.patch.object(SplitPay, 'createAccount', new_callable=mock_createAccount):
            customer_user = User.objects.create(
                email=payload['customer']['email'],
                first_name='Test',
                last_name='User',
            )
            customer_user.set_password('testpassword123')
            customer_user.save(update_fields=['password'])

        # Call
        response = self.client.post(url, data=payload, format='json')

        self.assertEqual(response.status_code, 200, response.json())

        self.assertNotIn('accessToken', response.json())

        self.assertEqual(UserLoginHistory.objects.count(), 0)

    def test_coupon_validation_in_checkout_processing(self):
        """Testa que cupons com valor líquido insuficiente são rejeitados no processamento do pedido"""
        self.product.price = Decimal('5.00')
        self.product.save()

        main_offer = self.product.offers.first()
        main_offer.price = Decimal('5.00')
        main_offer.save()

        coupon = Coupon.objects.create(code='Test', discount=80, startTime=timezone.now())
        coupon.products.add(self.product)

        createTransactionResponse = json.load(open('gateway/tests/mocks/split_pix_payment_response.json'))
        createTransactionResponse['data']['baseAmount'] = 5.00
        createTransactionResponse['data']['amount'] = 5.00
        createTransactionResponse['data']['items'] = [
            {
                'externalRef': str(main_offer.pk),
                'unitPrice': 5.00,
            }
        ]

        url = reverse('checkout', args=[self.product.short_id])
        payload = dict(json.load(open('gateway/tests/mocks/pix_payment_payload.json')))
        payload['items'][0]['id'] = main_offer.pk
        payload['coupon'] = coupon.code

        expected_amount = Decimal('5.00')

        with mock.patch(
            'gateway.strategies.base.SplitPay.createTransaction',
            mock.Mock(return_value=self.get_response_mock(content=createTransactionResponse))
        ) as createTransactionMock:
            response = self.client.post(url, data=payload, format='json')

        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertEqual(expected_amount, Decimal(createTransactionMock.call_args[1]['amount']))

        order = Order.objects.first()
        self.assertIsNone(order.coupon)
        self.assertIsNone(order.couponCode)
        self.assertEqual(order.discount, Decimal('0.00'))

    @responses.activate
    def test_checkout_with_address(self):
        responses.post(
            url=settings.GATEWAY_URL + 'api/checkout/',
            json=json.load(open('gateway/tests/mocks/split_card_payment_response.json')),
            status=200,
        )

        url = reverse('checkout', args=[self.product.short_id])

        payload = dict(json.load(open('gateway/tests/mocks/card_payment_payload.json')))
        payload['items'][0]['id'] = self.product.offers.first().pk

        # Add address to payload
        address_payload = {
            'country': 'BR',
            'state': 'SP',
            'city': 'São Paulo',
            'zipcode': '01310-200',
            'complement': 'Apto 123',
            'street': 'Avenida Paulista',
            'neighborhood': 'Centro',
            'number': '1578',
            'is_default': True,
        }
        payload['address'] = address_payload

        mock.patch('gateway.serializers.CheckoutProcessPaymentSerializer._validate_installments', mock.Mock()).start()
        with mock.patch.object(SplitPay, 'createAccount', new_callable=mock_createAccount):
            with mock.patch('gateway.strategies.card_strategy.CardStrategy.create_order_splits', mock.Mock()):
                response = self.client.post(url, data=payload, format='json')

        self.assertEqual(response.status_code, 200, response.json())

        self.assertEqual(Address.objects.count(), 1)
        address: Address = Address.objects.first()  # type:ignore
        self.assertEqual(address.country, address_payload['country'])
        self.assertEqual(address.state, address_payload['state'])
        self.assertEqual(address.city, address_payload['city'])
        self.assertEqual(address.zipcode, address_payload['zipcode'].replace('-', ''))  # Normalized zipcode
        self.assertEqual(address.complement, address_payload['complement'])
        self.assertEqual(address.street, address_payload['street'])
        self.assertEqual(address.neighborhood, address_payload['neighborhood'])
        self.assertEqual(address.number, address_payload['number'])
        self.assertEqual(address.is_default, address_payload['is_default'])
