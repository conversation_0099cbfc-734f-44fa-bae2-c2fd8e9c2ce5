import json
import os
from decimal import Decimal
from typing import Any
from unittest import mock

from django.urls import reverse

from cakto.tests.base import BaseTestCase
from gateway.enums import SubscriptionStatus
from gateway.models import Order, PaymentStatus, Subscription
from gateway.strategies.pix_strategy import PixStrategy
from product.enums import ProductType


class TestPixStrategy(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.create_paymentMethods()

        cls.product = cls.create_product(price=10)

        # Cria oferta com type=subscription explicitamente
        subscription_offer = cls.product.offers.first()
        subscription_offer.type = ProductType.SUBSCRIPTION.id
        subscription_offer.save()

        cls.order: Order = cls.create_order(
            product=cls.product,
            offer=subscription_offer,
            baseAmount=10,
            amount=10,
            paymentMethod=cls.pix,
        )

        cls.payment = cls.create_payment(product=cls.product, orders=[cls.order])

        cls.customer = cls.create_customer()

        cls.subscription = Subscription.objects.create(
            status=SubscriptionStatus.INACTIVE.id,
            parent_order=cls.order,
            amount=cls.order.amount,
            paymentMethod=cls.pix,
            customer=cls.customer,
            offer=cls.product.offers.first(),
            product=cls.product,
            current_period=1,
        )

    def test_PixStrategy_executePayment(self):
        tx_mock = {'data': {'testing': 'pix_payment'}}
        create_transaction_mock = mock.patch(
            'gateway.strategies.pix_strategy.PixStrategy.createTransaction',
            return_value=tx_mock
        ).start()

        set_orders_split_data_mock = mock.patch(
            'gateway.strategies.pix_strategy.PixStrategy.set_orders_split_data'
        ).start()

        payment_mock = mock.Mock()
        process_payment_mock = mock.patch(
            'gateway.strategies.pix_strategy.PixStrategy.process_payment',
            return_value=payment_mock
        ).start()

        handle_payment_email_mock = mock.patch(
            'gateway.strategies.pix_strategy.PixStrategy.handle_payment_email'
        ).start()

        handle_new_payment_event_mock = mock.patch(
            'gateway.strategies.pix_strategy.PixStrategy.handle_new_payment_event'
        ).start()

        handle_initial_subscriptions_mock = mock.patch(
            'gateway.strategies.pix_strategy.PixStrategy.handle_initial_subscriptions'
        ).start()

        pix_strategy = PixStrategy()
        pix_strategy.orders = [self.order]

        # Call
        result = pix_strategy.executePayment()

        # Assertions
        create_transaction_mock.assert_called_once()

        set_orders_split_data_mock.assert_called_once_with(
            paymentData=tx_mock['data'],
            orders=[self.order]
        )

        process_payment_mock.assert_called_once_with(
            paymentData=tx_mock['data'],
            orders=[self.order]
        )

        handle_payment_email_mock.assert_called_once_with(payment=payment_mock)

        handle_new_payment_event_mock.assert_called_once_with(
            payment=payment_mock,
            orders=[self.order]
        )

        handle_initial_subscriptions_mock.assert_called_once_with(
            orders=[self.order],
            payment=payment_mock
        )

        self.assertEqual(result, payment_mock)

    def test_PixStrategy_handle_payment_email(self):
        payment_mock = mock.MagicMock()

        send_payment_mail_mock = mock.patch(
            'gateway.strategies.pix_strategy.send_payment_mail.delay'
        ).start()

        # Call
        PixStrategy.handle_payment_email(payment=payment_mock)

        # Assertions
        send_payment_mail_mock.assert_called_once_with(payment_mock)

    def test_PixStrategy_handle_new_payment_event_with_pix_order(self):
        payment_mock = mock.MagicMock(paymentMethodType='pix')
        order_mock = mock.MagicMock()

        dispatch_event_mock = mock.patch(
            'gateway.strategies.pix_strategy.dispatch_event'
        ).start()

        # Call
        PixStrategy.handle_new_payment_event(payment=payment_mock, orders=[order_mock])

        # Assertions
        dispatch_event_mock.assert_called_once_with(
            'pix_gerado',
            order=order_mock,
            payment=payment_mock
        )

    def test_PixStrategy_handle_new_payment_event_with_boleto_order(self):
        payment_mock = mock.MagicMock(paymentMethodType='boleto')
        order_mock = mock.MagicMock()

        dispatch_event_mock = mock.patch(
            'gateway.strategies.pix_strategy.dispatch_event'
        ).start()

        # Call
        PixStrategy.handle_new_payment_event(payment=payment_mock, orders=[order_mock])

        # Assertions
        dispatch_event_mock.assert_called_once_with(
            'boleto_gerado',
            order=order_mock,
            payment=payment_mock
        )

    def test_PixStrategy_handle_new_payment_event_with_picpay_order(self):
        payment_mock = mock.MagicMock(paymentMethodType='picpay')
        order_mock = mock.MagicMock()

        dispatch_event_mock = mock.patch(
            'gateway.strategies.pix_strategy.dispatch_event'
        ).start()

        # Call
        PixStrategy.handle_new_payment_event(payment=payment_mock, orders=[order_mock])

        # Assertions
        dispatch_event_mock.assert_called_once_with(
            'picpay_gerado',
            order=order_mock,
            payment=payment_mock
        )

    def test_PixStrategy_set_orders_split_data(self):
        paymentData: dict[str, Any] = {
            'status': 'paid',
            'id': 'TestPayment',
            'baseAmount': 10,
            'amount': 10,
            'fixedFee': 1,
            'percentageFee': 1,
            'paymentMethod': 'pix',
            'installments': 1,
            'acquirerType': 'testAcquirer',
        }

        create_order_splits_mock = mock.patch('gateway.strategies.pix_strategy.PixStrategy.create_order_splits').start()

        get_commissions_mock = mock.patch('gateway.strategies.pix_strategy.PixStrategy.get_commissions').start()
        expected_commissions = {
            'userId': self.product.user.id,
            'type': 'producer',
            'commissionValue': 10,
            'commissionPercentage': 100,
        }
        get_commissions_mock.return_value = ([self.product.user], expected_commissions)

        # Call
        PixStrategy().set_orders_split_data(paymentData, [self.order])

        create_order_splits_mock.assert_called_once_with(self.order)

        self.order.refresh_from_db()

        self.assertEqual(self.order.commissions, expected_commissions)
        self.assertEqual(self.order.commissionedUsers.first(), self.product.user)
        self.assertEqual(self.order.status, paymentData['status'])
        self.assertEqual(self.order.externalId, paymentData['id'])
        self.assertEqual(self.order.amount, paymentData['amount'])
        self.assertEqual(self.order.fees, paymentData['fixedFee'] + paymentData['percentageFee'])
        self.assertEqual(self.order.installments, paymentData['installments'])
        self.assertEqual(self.order.acquirerType, paymentData['acquirerType'])

    def test_PixStrategy_set_orders_split_data_with_two_orders(self):
        order_2: Order = self.create_order(
            product=self.product,
            offer=self.product.offers.first(),
            externalId='',
            baseAmount=2,
            amount=2,
            paymentMethod='pix',
        )

        paymentData: dict[str, Any] = {
            'status': 'paid',
            'id': 'TestPayment',
            'baseAmount': 10,
            'amount': 10,
            'fixedFee': 1,
            'percentageFee': 1,
            'paymentMethod': 'pix',
            'installments': 1,
            'acquirerType': 'testAcquirer',
        }

        mock.patch('gateway.strategies.pix_strategy.PixStrategy.create_order_splits').start()
        with mock.patch('gateway.strategies.pix_strategy.PixStrategy.get_commissions') as get_commissions_mock:
            expected_commissions = {'test': 'comissions'}
            get_commissions_mock.return_value = ([self.product.user], expected_commissions)
            PixStrategy().set_orders_split_data(paymentData, [self.order, order_2])

        self.order.refresh_from_db()

        self.assertEqual(self.order.commissions, expected_commissions)
        self.assertEqual(order_2.commissions, expected_commissions)

        self.assertEqual(self.order.commissionedUsers.first(), self.product.user)
        self.assertEqual(order_2.commissionedUsers.first(), self.product.user)

        self.assertEqual(self.order.status, paymentData['status'])
        self.assertEqual(order_2.status, paymentData['status'])

        self.assertEqual(self.order.externalId, paymentData['id'])
        self.assertEqual(order_2.externalId, paymentData['id'])

        self.assertEqual(self.order.amount, 10)
        self.assertEqual(order_2.amount, 2)

        self.assertEqual(self.order.fees, Decimal("1.5"))
        self.assertEqual(order_2.fees, Decimal("0.7"))

        self.assertEqual(self.order.installments, paymentData['installments'])
        self.assertEqual(order_2.installments, paymentData['installments'])

        self.assertEqual(self.order.acquirerType, paymentData['acquirerType'])
        self.assertEqual(order_2.acquirerType, paymentData['acquirerType'])

    def test_PixStrategy_handle_initial_subscriptions(self):
        orders = [self.order]

        with mock.patch('gateway.strategies.pix_strategy.PixStrategy.create_initial_subscription') as create_initial_subscription_mock:
            create_initial_subscription_mock.return_value = self.subscription
            PixStrategy().handle_initial_subscriptions(orders=orders, payment=self.payment)

        create_initial_subscription_mock.assert_called_once_with(orders[0])
        self.assertEqual(self.payment.subscriptions.first(), self.subscription)

    def test_PixStrategy_create_initial_subscription(self):
        Subscription.objects.all().delete()

        self.order.customer = self.create_customer(email='<EMAIL>')
        self.order.save(update_fields=['customer'])

        PixStrategy().create_initial_subscription(self.order)

        self.assertEqual(Subscription.objects.count(), 1)
        subscription: Subscription = Subscription.objects.first()  # type:ignore

        self.assertEqual(self.order.subscription, subscription)

        offer = self.order.offer

        self.assertEqual(subscription.status, 'inactive')
        self.assertEqual(subscription.offer, offer)
        self.assertEqual(subscription.product, offer.product)
        self.assertEqual(subscription.recurrence_period, offer.recurrence_period)
        self.assertEqual(subscription.quantity_recurrences, offer.quantity_recurrences)
        self.assertEqual(subscription.trial_days, offer.trial_days)
        self.assertEqual(subscription.max_retries, offer.max_retries)
        self.assertEqual(subscription.amount, offer.price)
        self.assertEqual(subscription.retry_interval, offer.retry_interval)
        self.assertEqual(subscription.current_period, 1)

    def test_PixStrategy_create_subscription(self):
        self.order.subscription = self.subscription
        self.subscription.externalId = None
        self.subscription.recurrence_period = 1
        self.subscription.save(update_fields=['externalId', 'recurrence_period'])
        self.order.save(update_fields=['subscription'])

        get_subscription_payload_mock = mock.patch('gateway.strategies.pix_strategy.PixStrategy.get_subscription_payload').start()
        get_subscription_payload_mock.return_value = {'test': 'payload_create_subscription'}

        createSubscription_mock = mock.patch('gateway.strategies.base.SplitPay.createSubscription').start()
        response_mock = mock.MagicMock()
        response_mock.json.return_value = {'id': 'test_create_subscription', 'subscription': 152436}
        createSubscription_mock.return_value = response_mock

        validate_subscription_transaction_response_mock = mock.patch('gateway.strategies.pix_strategy.PixStrategy.validate_subscription_transaction_response').start()

        subscription_mock = mock.MagicMock()
        to_subscription_model_mock = mock.patch('gateway.strategies.pix_strategy.PixStrategy.to_subscription_model').start()
        to_subscription_model_mock.return_value = subscription_mock

        set_split_data_to_subscription_mock = mock.patch('gateway.strategies.pix_strategy.PixStrategy.set_split_data_to_subscription').start()

        # Call
        result = PixStrategy().create_subscription(self.order)

        # Assertions
        get_subscription_payload_mock.assert_called_once_with(self.order)

        createSubscription_mock.assert_called_once_with(
            **{'test': 'payload_create_subscription', 'trial_days': 1}
        )

        validate_subscription_transaction_response_mock.assert_called_once_with(
            {'test': 'payload_create_subscription', 'trial_days': 1},
            response_mock
        )

        to_subscription_model_mock.assert_called_once_with(
            self.order,
            subscription=self.subscription,
            commit=False
        )

        set_split_data_to_subscription_mock.assert_called_once_with(
            subscription_mock,
            response_mock.json.return_value['subscription'],
            commit=False
        )

        subscription_mock.save.assert_called_once()

        self.assertEqual(result, subscription_mock)

    def test_PixStrategy_pix_subscription_first_payment_approval(self):
        url = reverse('webhook-splitpay')

        payment_approval_webhook_payload = json.load(open('gateway/tests/mocks/split_webhook_pix_approved.json'))
        payment_approval_webhook_payload['secret'] = os.getenv('WEBHOOK_SECRETS', '').split(',')[0]

        self.order.subscription = self.subscription
        self.order.externalId = payment_approval_webhook_payload['data']['id']
        self.order.status = PaymentStatus.WAITING_PAYMENT.id
        self.order.save(update_fields=['subscription', 'externalId', 'status'])

        self.payment.externalId = payment_approval_webhook_payload['data']['id']
        self.payment.save(update_fields=['externalId'])

        handle_new_subscription_mock = mock.patch(
            'financial.utils.handle_new_subscription.delay'
        ).start()

        # Call
        self.client.post(url, data=payment_approval_webhook_payload, format='json')

        # Assertions
        handle_new_subscription_mock.assert_called_once_with(
            order=self.order,
            payment=self.payment,
            webhook_data=payment_approval_webhook_payload['data']
        )
