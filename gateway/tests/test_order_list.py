from django.urls import reverse
from django.utils import timezone

from cakto.tests.base import BaseTestCase
from gateway.models import OfferType, Split
from product.enums import ProductType
from product.models import Affiliate, Offer


class OrderListTest(BaseTestCase):
    url = reverse('order-list')

    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.create_paymentMethods()

        cls.token = cls.get_user_access_token(cls.user)
        cls.headers = cls.create_headers(cls.token)
        cls.customer = cls.create_customer(email=cls.user.email)
        cls.product = cls.create_product(user=cls.user)

        cls.orders = [cls.create_order(cls.customer, product=cls.product, amount=10) for _ in range(2)]

        cls.orders.append(cls.create_order(
            cls.customer,
            product=cls.product,
            amount=10,
            couponCode='COUPON123',
            utm_source='google',
            utm_medium='cpc',
            utm_campaign='summer_sale',
            utm_content='landing_page',
            utm_term='hr+ebooks',
            sck='sckTest',
        ))

        cls.o1 = cls.orders[0]
        cls.o2 = cls.orders[1]
        cls.o3 = cls.orders[2]

        cls.o4 = cls.create_order(product=cls.create_product(user=cls.user))  # from another product
        cls.orders.append(cls.o4)

        [order.commissionedUsers.set([cls.user]) for order in cls.orders]

    def test_order_list(self):
        response = self.client.get(self.url, headers=self.headers)
        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertEqual(len(response.json()['results']), 4)

    def test_order_list_with_filter_utm_source(self):
        utm_source = 'google'
        response = self.client.get(f'{self.url}?utm_source={utm_source}', headers=self.headers)
        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertEqual(len(response.json()['results']), 1)
        for order in response.json()['results']:
            self.assertIn(utm_source, order['utm_source'])

    def test_order_list_with_filter_utm_medium(self):
        utm_medium = 'cpc'
        response = self.client.get(f'{self.url}?utm_medium={utm_medium}', headers=self.headers)
        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertEqual(len(response.json()['results']), 1)
        for order in response.json()['results']:
            self.assertIn(utm_medium, order['utm_medium'])

    def test_order_list_with_filter_utm_campaign(self):
        utm_campaign = 'summer_sale'
        response = self.client.get(f'{self.url}?utm_campaign={utm_campaign}', headers=self.headers)
        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertEqual(len(response.json()['results']), 1)
        for order in response.json()['results']:
            self.assertIn(utm_campaign, order['utm_campaign'])

    def test_order_list_with_filter_utm_content(self):
        utm_content = 'landing_page'
        response = self.client.get(f'{self.url}?utm_content={utm_content}', headers=self.headers)
        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertEqual(len(response.json()['results']), 1)
        for order in response.json()['results']:
            self.assertIn(utm_content, order['utm_content'])

    def test_order_list_with_filter_utm_term(self):
        utm_term = 'hr+ebooks'
        response = self.client.get(f'{self.url}?utm_term={utm_term.replace("+", "%2B")}', headers=self.headers)
        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertEqual(len(response.json()['results']), 1)
        for order in response.json()['results']:
            self.assertIn(utm_term, order['utm_term'])

    def test_order_list_with_filter_sck(self):
        sck = 'sckTest'
        response = self.client.get(f'{self.url}?sck={sck}', headers=self.headers)
        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertEqual(len(response.json()['results']), 1)
        for order in response.json()['results']:
            self.assertIn(sck, order['sck'])

    def test_order_list_with_filter_offer(self):
        offer = self.product.offers.first()
        offer2 = Offer.objects.create(
            product=self.product,
            name='Offer 2',
            price=200.00,
        )
        offer3 = Offer.objects.create(
            product=self.product,
            name='Offer 3',
            price=200.00,
        )

        # Remove offer de todas as orders
        for o in self.orders:
            o.offer = None
            o.save()

        # Define explicitamente as ofertas
        first_order = self.orders[0]
        first_order.offer = offer
        first_order.save()

        second_order = self.orders[1]
        second_order.offer = offer2
        second_order.save()

        # Define uma oferta extra que não estará no filtro
        self.orders[2].offer = offer3
        self.orders[2].save()

        response = self.client.get(f'{self.url}?offer={offer.id},{offer2.id}', headers=self.headers)

        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertEqual(len(response.json()['results']), 2)
        self.assertEqual(response.json()['results'][1]['id'], first_order.id)
        self.assertEqual(response.json()['results'][0]['id'], second_order.id)

    def test_order_list_with_date_filter(self):
        o4 = self.create_order(product=self.product)

        # Create a date range
        start_date = timezone.make_aware(timezone.datetime.fromisoformat('2023-01-01'))
        end_date = timezone.make_aware(timezone.datetime.fromisoformat('2023-01-03'))

        # Set orders' createdAt
        self.o1.createdAt = start_date
        self.o1.save()
        self.o2.createdAt = start_date + timezone.timedelta(days=1)
        self.o2.save()

        # Set orders' createdAt outside the range
        self.o3.createdAt = end_date + timezone.timedelta(days=1)
        self.o3.save()
        o4.createdAt = start_date - timezone.timedelta(days=1)
        o4.save()

        # Make a request with the date range
        url = f'{self.url}?startDate={start_date.strftime("%d-%m-%Y")}&endDate={end_date.strftime("%d-%m-%Y")}'
        response = self.client.get(url, headers=self.headers)

        # Check the response status code
        self.assertEqual(response.status_code, 200, response.content.decode())

        result_order_ids = [order['id'] for order in response.json()['results']]

        self.assertIn(self.o1.id, result_order_ids, 'o1 should be in result')
        self.assertIn(self.o2.id, result_order_ids, 'o2 should be in result')
        self.assertNotIn(self.o3.id, result_order_ids, 'o3 should not be result')
        self.assertNotIn(o4.id, result_order_ids, 'o4 should not be result')
        self.assertEqual(len(response.json()['results']), 2, response.content.decode())

    def test_order_list_with_status_filter_paid_returns_refunded_amount_and_percentage(self):
        self.o1.status = 'paid'
        self.o2.status = 'paid'
        self.o3.status = 'refunded'
        [order.save() for order in self.orders]

        url = f'{self.url}?status=paid'
        response = self.client.get(url, headers=self.headers)
        self.assertEqual(response.status_code, 200, response.content.decode())

        results = response.json()['results']
        order_ids = [order['id'] for order in results]

        self.assertEqual(len(results), 2, response.content.decode())
        self.assertIn(self.o1.id, order_ids, 'o1 should be in result')
        self.assertIn(self.o2.id, order_ids, 'o2 should be in result')
        self.assertNotIn(self.o3.id, order_ids, 'o3 should not be result')
        self.assertEqual(response.json()['ordersRefundAmount'], 10)
        self.assertEqual(response.json()['refundPercentage'], 33.33)

    def test_order_list_with_products_filter(self):
        url = f'{self.url}?products={self.product.id}'

        response = self.client.get(url, headers=self.headers)

        self.assertEqual(response.status_code, 200, response.content.decode())

        product_ids = [order['product']['id'] for order in response.json()['results']]

        self.assertIn(self.product.id, product_ids, 'product should be in result')
        self.assertNotIn(self.o4.product.id, product_ids, 'product should not be in result')

        self.assertEqual(len(response.json()['results']), 3, response.content.decode())

    def test_order_list_with_affiliate_id_filter(self):
        affiliate = Affiliate.objects.create(user=self.create_user(), product=self.product, commission=10)
        another_affiliate = Affiliate.objects.create(user=self.create_user(), product=self.product, commission=10)

        self.o1.affiliate = affiliate
        self.o1.save()
        self.o2.affiliate = another_affiliate
        self.o2.save()

        url = f'{self.url}?affiliate={affiliate.id},{another_affiliate.id}'

        response = self.client.get(url, headers=self.headers)

        self.assertEqual(response.status_code, 200, response.content.decode())

        self.assertIn(self.o1.id, [order['id'] for order in response.json()['results']], 'o1 should be in result')
        self.assertIn(self.o2.id, [order['id'] for order in response.json()['results']], 'o2 should be in result')
        self.assertEqual(len(response.json()['results']), 2, response.content.decode())

    def test_order_list_with_affiliate_short_id_filter(self):
        affiliate = Affiliate.objects.create(user=self.create_user(), product=self.product, commission=10)
        another_affiliate = Affiliate.objects.create(user=self.create_user(), product=self.product, commission=10)

        self.o1.affiliate = affiliate
        self.o1.save()
        self.o2.affiliate = another_affiliate
        self.o2.save()

        url = f'{self.url}?affiliate={affiliate.short_id},{another_affiliate.short_id}'

        response = self.client.get(url, headers=self.headers)

        self.assertEqual(response.status_code, 200, response.content.decode())

        self.assertIn(self.o1.id, [order['id'] for order in response.json()['results']], 'o1 should be in result')
        self.assertIn(self.o2.id, [order['id'] for order in response.json()['results']], 'o2 should be in result')
        self.assertEqual(len(response.json()['results']), 2, response.content.decode())

    def test_order_list_with_affiliate_short_email_filter(self):
        affiliate = Affiliate.objects.create(user=self.create_user(), product=self.product, commission=10)
        another_affiliate = Affiliate.objects.create(user=self.create_user(), product=self.product, commission=10)

        self.o1.affiliate = affiliate
        self.o1.save()
        self.o2.affiliate = another_affiliate
        self.o2.save()

        url = f'{self.url}?affiliate={affiliate.user.email},{another_affiliate.user.email}'

        response = self.client.get(url, headers=self.headers)

        self.assertEqual(response.status_code, 200, response.content.decode())

        self.assertIn(self.o1.id, [order['id'] for order in response.json()['results']], 'o1 should be in result')
        self.assertIn(self.o2.id, [order['id'] for order in response.json()['results']], 'o2 should be in result')
        self.assertEqual(len(response.json()['results']), 2, response.content.decode())

    def test_order_list_with_paymentMethods_filter(self):
        self.o1.paymentMethod = self.pix
        self.o2.paymentMethod = self.boleto
        self.o3.paymentMethod = self.credit_card
        self.o4.paymentMethod = self.boleto
        [order.save() for order in self.orders]

        url = f'{self.url}?paymentMethods={self.pix.type},{self.credit_card.type}'
        response = self.client.get(url, headers=self.headers)

        self.assertEqual(response.status_code, 200, response.content.decode())

        self.assertIn(self.o1.id, [order['id'] for order in response.json()['results']], 'o1 should be in result')
        self.assertIn(self.o3.id, [order['id'] for order in response.json()['results']], 'o3 should be in result')
        self.assertNotIn(self.o2.id, [order['id'] for order in response.json()['results']], 'o2 should not be in result')
        self.assertNotIn(self.o4.id, [order['id'] for order in response.json()['results']], 'o4 should not be in result')
        self.assertEqual(len(response.json()['results']), 2, response.content.decode())

    def test_order_list_with_paymentMethod_filter(self):
        self.o1.paymentMethod = self.pix
        self.o2.paymentMethod = self.boleto
        self.o3.paymentMethod = self.credit_card
        self.o4.paymentMethod = self.boleto
        [order.save() for order in self.orders]

        url = f'{self.url}?paymentMethod={self.pix.type},{self.credit_card.type}'
        response = self.client.get(url, headers=self.headers)

        self.assertEqual(response.status_code, 200, response.content.decode())

        self.assertIn(self.o1.id, [order['id'] for order in response.json()['results']], 'o1 should be in result')
        self.assertIn(self.o3.id, [order['id'] for order in response.json()['results']], 'o3 should be in result')
        self.assertNotIn(self.o2.id, [order['id'] for order in response.json()['results']], 'o2 should not be in result')
        self.assertNotIn(self.o4.id, [order['id'] for order in response.json()['results']], 'o4 should not be in result')
        self.assertEqual(len(response.json()['results']), 2, response.content.decode())

    def test_order_list_with_type_filter(self):
        self.o1.type = ProductType.UNIQUE.id
        self.o2.type = ProductType.SUBSCRIPTION.id
        self.o3.type = ProductType.UNIQUE.id
        self.o4.type = ProductType.SUBSCRIPTION.id
        [order.save(update_fields=['type']) for order in self.orders]

        url = f'{self.url}?type={ProductType.UNIQUE.id}'

        response = self.client.get(url, headers=self.headers)

        self.assertEqual(response.status_code, 200, response.content.decode())

        self.assertIn(self.o1.id, [order['id'] for order in response.json()['results']], 'o1 should be in result')
        self.assertIn(self.o3.id, [order['id'] for order in response.json()['results']], 'o3 should be in result')
        self.assertNotIn(self.o2.id, [order['id'] for order in response.json()['results']], 'o2 should not be in result')
        self.assertNotIn(self.o4.id, [order['id'] for order in response.json()['results']], 'o4 should not be in result')
        self.assertEqual(len(response.json()['results']), 2, response.content.decode())

    def test_order_list_with_offer_type_filter(self):
        self.o1.offer_type = OfferType.MAIN.id
        self.o2.offer_type = OfferType.ORDERBUMP.id
        self.o3.offer_type = OfferType.UPSELL.id
        self.o4.offer_type = OfferType.MAIN.id
        [order.save() for order in self.orders]

        url = f'{self.url}?offer_type={OfferType.MAIN.id},{OfferType.ORDERBUMP.id}'
        response = self.client.get(url, headers=self.headers)

        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertIn(self.o1.id, [order['id'] for order in response.json()['results']], 'o1 should be in result')
        self.assertIn(self.o2.id, [order['id'] for order in response.json()['results']], 'o2 should be in result')
        self.assertIn(self.o4.id, [order['id'] for order in response.json()['results']], 'o4 should be in result')
        self.assertNotIn(self.o3.id, [order['id'] for order in response.json()['results']], 'o3 should not be in result')
        self.assertEqual(len(response.json()['results']), 3, response.content.decode())

    def test_order_list_with_offerType_filter(self):
        self.o1.offer_type = OfferType.MAIN.id
        self.o2.offer_type = OfferType.ORDERBUMP.id
        self.o3.offer_type = OfferType.UPSELL.id
        self.o4.offer_type = OfferType.MAIN.id
        [order.save() for order in self.orders]

        url = f'{self.url}?offerType={OfferType.MAIN.id},{OfferType.ORDERBUMP.id}'
        response = self.client.get(url, headers=self.headers)

        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertIn(self.o1.id, [order['id'] for order in response.json()['results']], 'o1 should be in result')
        self.assertIn(self.o2.id, [order['id'] for order in response.json()['results']], 'o2 should be in result')
        self.assertIn(self.o4.id, [order['id'] for order in response.json()['results']], 'o4 should be in result')
        self.assertNotIn(self.o3.id, [order['id'] for order in response.json()['results']], 'o3 should not be in result')
        self.assertEqual(len(response.json()['results']), 3, response.content.decode())

    def test_order_list_with_coupon_code_filter(self):
        code = 'GET20DISCOUNT'
        self.o1.couponCode = code
        self.o1.save()

        url = f'{self.url}?couponCode={code}'
        response = self.client.get(url, headers=self.headers)

        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertIn(self.o1.id, [order['id'] for order in response.json()['results']], 'o1 should be in result')
        self.assertNotIn(self.o2.id, [order['id'] for order in response.json()['results']], 'o2 should not be in result')
        self.assertNotIn(self.o3.id, [order['id'] for order in response.json()['results']], 'o3 should not be in result')
        self.assertNotIn(self.o4.id, [order['id'] for order in response.json()['results']], 'o4 should not be in result')
        self.assertEqual(len(response.json()['results']), 1, response.content.decode())

    def test_order_list_with_subscription_type_filter(self):
        self.o1.subscription_period = 1
        self.o2.subscription_period = 2
        self.o3.subscription_period = 10
        self.o4.subscription_period = 5
        [order.save() for order in self.orders]

        url = f'{self.url}?subscription_type=new'
        response = self.client.get(url, headers=self.headers)

        self.assertEqual(response.status_code, 200, response.content.decode())

        self.assertIn(self.o1.id, [order['id'] for order in response.json()['results']], 'o1 should be in result')
        self.assertNotIn(self.o2.id, [order['id'] for order in response.json()['results']], 'o2 should not be in result')
        self.assertNotIn(self.o3.id, [order['id'] for order in response.json()['results']], 'o3 should not be in result')
        self.assertNotIn(self.o4.id, [order['id'] for order in response.json()['results']], 'o4 should not be in result')
        self.assertEqual(len(response.json()['results']), 1, response.content.decode())

        url = f'{self.url}?subscription_type=renewed'
        response = self.client.get(url, headers=self.headers)

        self.assertEqual(response.status_code, 200, response.content.decode())

        self.assertIn(self.o2.id, [order['id'] for order in response.json()['results']], 'o2 should be in result')
        self.assertIn(self.o3.id, [order['id'] for order in response.json()['results']], 'o3 should be in result')
        self.assertIn(self.o4.id, [order['id'] for order in response.json()['results']], 'o4 should be in result')
        self.assertNotIn(self.o1.id, [order['id'] for order in response.json()['results']], 'o1 should not be in result')
        self.assertEqual(len(response.json()['results']), 3, response.content.decode())

    def test_order_list_with_coproductionType_filter_and_affiliate_value(self):
        Split.objects.all().delete()
        Split.objects.create(order=self.o1, user=self.user, type='affiliate')
        Split.objects.create(order=self.o2, user=self.user, type='producer')
        Split.objects.create(order=self.o3, user=self.user, type='coproducer')

        url = f'{self.url}?coproductionType=affiliate'
        response = self.client.get(url, headers=self.headers)

        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertIn(self.o1.id, [order['id'] for order in response.json()['results']], 'o1 should be in result')
        self.assertNotIn(self.o2.id, [order['id'] for order in response.json()['results']], 'o2 should not be in result')
        self.assertNotIn(self.o3.id, [order['id'] for order in response.json()['results']], 'o3 should not be in result')
        self.assertEqual(len(response.json()['results']), 1, response.content.decode())

    def test_order_list_with_coproduction_type_filter_and_coproduction_value(self):
        Split.objects.all().delete()
        Split.objects.create(order=self.o1, user=self.user, type='coproducer')
        Split.objects.create(order=self.o2, user=self.user, type='affiliate')
        Split.objects.create(order=self.o3, user=self.user, type='producer')

        url = f'{self.url}?coproductionType=coproduction'
        response = self.client.get(url, headers=self.headers)

        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertIn(self.o1.id, [order['id'] for order in response.json()['results']], 'o1 should be in result')
        self.assertNotIn(self.o2.id, [order['id'] for order in response.json()['results']], 'o2 should not be in result')
        self.assertNotIn(self.o3.id, [order['id'] for order in response.json()['results']], 'o3 should not be in result')
        self.assertEqual(len(response.json()['results']), 1, response.content.decode())

    def test_order_list_with_coproduction_type_filter_and_production_value(self):
        Split.objects.all().delete()
        Split.objects.create(order=self.o1, user=self.user, type='producer')
        Split.objects.create(order=self.o2, user=self.user, type='affiliate')
        Split.objects.create(order=self.o3, user=self.user, type='coproducer')

        url = f'{self.url}?coproductionType=production'
        response = self.client.get(url, headers=self.headers)

        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertIn(self.o1.id, [order['id'] for order in response.json()['results']], 'o1 should be in result')
        self.assertNotIn(self.o2.id, [order['id'] for order in response.json()['results']], 'o2 should not be in result')
        self.assertNotIn(self.o3.id, [order['id'] for order in response.json()['results']], 'o3 should not be in result')
        self.assertEqual(len(response.json()['results']), 1, response.content.decode())

    def test_order_list_without_status_filter_does_not_return_scheduled_orders(self):
        self.o1.status = 'paid'
        self.o2.status = 'scheduled'
        self.o3.status = 'refunded'
        self.o4.status = 'scheduled'
        [order.save() for order in self.orders]

        url = f'{self.url}'
        response = self.client.get(url, headers=self.headers)

        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertIn(self.o1.id, [order['id'] for order in response.json()['results']], 'o1 should be in result')
        self.assertNotIn(self.o2.id, [order['id'] for order in response.json()['results']], 'o2 should not be in result')
        self.assertIn(self.o3.id, [order['id'] for order in response.json()['results']], 'o3 should be in result')
        self.assertNotIn(self.o4.id, [order['id'] for order in response.json()['results']], 'o4 should not be in result')
        self.assertEqual(len(response.json()['results']), 2, response.content.decode())

    def test_order_list_with_search_filter(self):
        url = f'{self.url}?search={self.o1.refId}'
        response = self.client.get(url, headers=self.headers)

        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertIn(self.o1.id, [order['id'] for order in response.json()['results']], 'o1 should be in result')
        self.assertNotIn(self.o2.id, [order['id'] for order in response.json()['results']], 'o2 should not be in result')
        self.assertNotIn(self.o3.id, [order['id'] for order in response.json()['results']], 'o3 should not be in result')
        self.assertNotIn(self.o4.id, [order['id'] for order in response.json()['results']], 'o4 should not be in result')
        self.assertEqual(len(response.json()['results']), 1, response.content.decode())

    def test_order_list_with_ordering_filter(self):
        url = f'{self.url}?ordering=createdAt'
        response = self.client.get(url, headers=self.headers)

        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertEqual(len(response.json()['results']), 4, response.content.decode())

        # Check if the orders are sorted by createdAt
        created_at_list = [order['createdAt'] for order in response.json()['results']]
        self.assertEqual(created_at_list, sorted(created_at_list), 'Orders should be sorted by createdAt')

    def test_order_list_with_ordering_filter_descending(self):
        url = f'{self.url}?ordering=-createdAt'
        response = self.client.get(url, headers=self.headers)

        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertEqual(len(response.json()['results']), 4, response.content.decode())

        # Check if the orders are sorted by createdAt in descending order
        created_at_list = [order['createdAt'] for order in response.json()['results']]
        self.assertEqual(created_at_list, sorted(created_at_list, reverse=True), 'Orders should be sorted by createdAt in descending order')
