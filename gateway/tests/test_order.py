from unittest import mock

from django.urls import reverse
from rest_framework import status

from cakto.tests.base import BaseTestCase
from product.models import Offer


class OrderTest(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.create_paymentMethods()

        cls.headers = cls.build_user_auth_headers(cls.user)

    def test_order_paymentMethodType(self):
        order = self.create_order(paymentMethod='pix')
        self.assertEqual(order.paymentMethodType, 'pix')

    def test_order_paymentMethodName(self):
        order = self.create_order(paymentMethod='pix')
        self.assertEqual(order.paymentMethodName, 'Pix')

    @mock.patch('gateway.views.SplitPay.refund')
    def test_order_refund_by_producer(self, mock_refund):
        mock_refund.return_value.status_code = status.HTTP_200_OK
        mock_refund.return_value.json.return_value = {'detail': 'Reembolso solicitado com sucesso!'}

        order = self.create_order(status='paid', product=self.create_product(user=self.user))

        url = reverse('order-refund', args=[order.id])

        response = self.client.post(url, headers=self.headers)

        order.refresh_from_db()
        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertEqual(order.status, 'paid')
        self.assertEqual(order.refunded_by, 'producer')
        self.assertEqual(order.refunded_by_user, self.user)

    @mock.patch('gateway.views.SplitPay.refund')
    def test_order_refund_by_customer(self, mock_refund):
        mock_refund.return_value.status_code = status.HTTP_200_OK
        mock_refund.return_value.json.return_value = {'detail': 'Reembolso solicitado com sucesso!'}

        order = self.create_order(
            status='paid',
            product=self.create_product(),
            customer=self.create_customer(email=self.user.email)
        )

        url = reverse('order-refund-customer', args=[order.id])

        response = self.client.post(url, headers=self.headers)

        order.refresh_from_db()
        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertEqual(order.status, 'paid')
        self.assertEqual(order.refunded_by, 'customer')
        self.assertEqual(order.refunded_by_user, self.user)

    def test_order_type_follows_offer_type(self):
        product = self.create_product()
        offer = Offer.objects.create(
            product=product, name='Assinatura Teste',
            type='subscription', price=150.00, intervalType='month'
        )

        order = self.create_order(product=product, offer=offer)
        self.assertEqual(order.type, 'subscription')
