from django.urls import reverse
from unittest.mock import patch
from rest_framework import status
from cakto.tests.base import BaseTestCase
from django.contrib.auth.models import Permission

class OrderAdminAPITestCase(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.create_paymentMethods()

        cls.user.is_staff = True
        cls.user.save(update_fields=['is_staff'])
        permissions = Permission.objects.filter(codename__in=['view_order', 'refund_order'])
        cls.user.user_permissions.add(*permissions)

        cls.headers = cls.build_user_auth_headers(cls.user)

        cls.customer = cls.create_customer(email=cls.user.email)
        cls.order = cls.create_order(customer=cls.customer, status='paid')

    @patch('gateway.views.SplitPay.refund')
    def test_refund_order(self, mock_refund):
        mock_refund.return_value.status_code = status.HTTP_200_OK
        mock_refund.return_value.json.return_value = {'detail': 'Reembolso solicitado com sucesso!'}

        url = reverse('admin-order-refund', kwargs={'pk': self.order.pk})
        response = self.client.post(url, format='json', headers=self.headers)

        self.order.refresh_from_db()

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content)
        self.assertEqual(response.data['detail'], 'Reembolso solicitado com sucesso!')
        self.assertEqual(self.order.refunded_by, 'admin')
        self.assertEqual(self.order.refunded_by_user, self.user)

    def test_refund_already_refunded_order(self):
        self.order.status = 'refunded'
        self.order.save(update_fields=['status'])

        url = reverse('admin-order-refund', kwargs={'pk': self.order.pk})
        response = self.client.post(url, format='json', headers=self.headers)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST, response.content)
        self.assertEqual(response.data['detail'], 'Pedido já reembolsado')
        self.assertIsNone(self.order.refunded_by)
        self.assertIsNone(self.order.refunded_by_user)

    def test_refund_non_paid_order(self):
        self.order.status = 'pending'
        self.order.save(update_fields=['status'])

        url = reverse('admin-order-refund', kwargs={'pk': self.order.pk})
        response = self.client.post(url, format='json', headers=self.headers)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST, response.content)
        self.assertEqual(response.data['detail'], 'Não é possível reembolsar uma ordem que não está paga')
