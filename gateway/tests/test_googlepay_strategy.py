from rest_framework_simplejwt.tokens import AccessToken

from cakto.tests.base import BaseTestCase
from gateway.strategies.googlepay_strategy import GooglePayStrategy


class TestGooglePayStrategy(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()

        cls.create_paymentMethods()

        cls.product = cls.create_product(price=10)

        cls.customer = cls.create_customer()

        cls.order = cls.create_order(
            product=cls.product,
            paymentMethod=cls.googlepay,
            amount=10,
            customer=cls.customer,
        )

    def test_GooglePayStrategy_afterPaymentProcess_with_user_that_has_no_password(self):
        customer_user = self.create_user(email=self.customer.email)
        customer_user.set_unusable_password()
        customer_user.save()

        response_data_mock = {}

        GooglePayStrategy().afterPaymentProcess(response_data_mock, customer=self.customer)

        self.assertIn('accessToken', response_data_mock)

        token = AccessToken(response_data_mock['accessToken'])
        self.assertEqual(token.payload.get('user_id'), customer_user.pk)

    def test_GooglePayStrategy_afterPaymentProcess_without_user_that_has_password(self):
        self.create_user(email=self.customer.email)

        response_data_mock = {}

        GooglePayStrategy().afterPaymentProcess(response_data_mock, customer=self.customer)

        self.assertNotIn('accessToken', response_data_mock)
