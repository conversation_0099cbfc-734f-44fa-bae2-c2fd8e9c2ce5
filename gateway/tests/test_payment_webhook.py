import json
import os
import re
from decimal import Decimal
from unittest import mock

import responses
from django.conf import settings
from django.urls import reverse

from cakto.tests.base import BaseTestCase
from gateway.models import Order, Payment, Split, Subscription
from gateway.strategies.base import PaymentStrategy
from gateway.views import WebhookAPIView


class TestPaymentWebhook(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()

        cls.create_paymentMethods()
        cls.product = cls.create_product(user=cls.user, price=10)

        cls.order = cls.create_order(
            product=cls.product,
            paymentMethod='pix'
        )

        cls.subscription = Subscription.objects.create(
            parent_order=cls.order,
            amount=cls.order.amount,
            paymentMethod=cls.pix,
            customer=cls.create_customer(),
            product=cls.product,
            externalId='456',
            offer=cls.product.offers.first(),
        )

    def test_handle_new_subscription_payment_is_called_when_new_subscription_payment_webhook_is_received(self):
        Payment.objects.all().delete()

        url = reverse('webhook-splitpay')

        payload = {
            'secret': os.getenv('WEBHOOK_SECRETS', '').split(',')[0],
            'type': 'transaction.changed',
            'data': {
                'exId': '123',
                'status': 'paid',
                'subscription': {'id': self.subscription.externalId},
            },
        }

        with mock.patch('gateway.views.WebhookAPIView.handle_new_subscription_payment') as handle_new_subscription_payment_mock:
            response = self.client.post(url, data=payload, format='json')

        self.assertEqual(response.status_code, 200, response.json())
        handle_new_subscription_payment_mock.assert_called_with(payload['data'], mock.ANY)

    def test_handle_new_subscription_payment_calls_process_new_subscription_payment_correctly(self):
        data = {
            'exId': '123',
            'status': 'paid',
            'subscription': {'id': self.subscription.externalId},
        }

        with mock.patch(
            'gateway.services.payment_factory.PixStrategy.process_new_subscription_payment'
        ) as process_new_subscription_payment_mock:
            WebhookAPIView().handle_new_subscription_payment(data=data, subscription=self.subscription)

        process_new_subscription_payment_mock.assert_called_with(
            subscription=self.subscription,
            webhook_data=data
        )

    def test_handle_new_subscription_payment_calls_handle_payment_status_change_correctly(self):
        data = {
            'exId': '123',
            'status': 'paid',
            'subscription': {'id': self.subscription.externalId},
        }

        payment_mock = mock.MagicMock()

        process_new_subscription_payment_mock = mock.patch(
            'gateway.services.payment_factory.PixStrategy.process_new_subscription_payment'
        ).start()
        process_new_subscription_payment_mock.return_value = payment_mock

        handle_payment_status_change_mock = mock.patch(
            'gateway.services.payment_factory.PixStrategy.handle_payment_status_change'
        ).start()

        WebhookAPIView().handle_new_subscription_payment(data=data, subscription=self.subscription)

        handle_payment_status_change_mock.assert_called_with(
            payment=payment_mock,
            current_status='scheduled',
            new_status=data['status'],
            webhook_data=data
        )

    @responses.activate
    def test_admin_reprocess_split_payment_view(self):
        # Mock splitpay API response
        json_response = {
            'testing': 'response',
        }

        payment_url = re.compile(r'.*api\/payment\/externalId.\/')

        get_payment_mock = responses.get(
            payment_url,
            json=json_response,
            status=200,
        )

        # Mock process_payment_from_split_data
        process_payment_mock = mock.patch(
            'gateway.views.PaymentStrategy.process_payment_from_split_data'
        ).start()

        # Prepare the request for the view
        url = reverse('admin-reprocess-split-payment')

        payload = {
            'payment_ids': ['externalId1', 'externalId2'],
        }

        # Call
        result = self.client.post(
            url,
            data=json.dumps(payload),
            content_type='application/json',
            headers=self.build_user_auth_headers(self.create_user(is_staff=True)),
        )

        # Assert
        self.assertEqual(result.status_code, 200, result.content.decode())
        calls = get_payment_mock.calls
        self.assertEqual(len(calls), 2)
        self.assertEqual(calls[0].request.url, f'{settings.GATEWAY_URL}api/payment/externalId1/')
        self.assertEqual(calls[1].request.url, f'{settings.GATEWAY_URL}api/payment/externalId2/')
        self.assertEqual(result.status_code, 200)

        self.assertEqual(process_payment_mock.call_count, 2)
        process_payment_mock.assert_called_with(
            paymentData=json_response,
        )

    def test_handle_not_found_payment_calls_process_payment_from_split_data(self):
        url = reverse('webhook-splitpay')

        paymentData_mock = {
            'testing': 'webhook',
            'status': 'paid',
        }

        payload = {
            'secret': os.getenv('WEBHOOK_SECRETS', '').split(',')[0],
            'type': 'transaction.changed',
            'data': paymentData_mock,
        }

        process_payment_from_split_data_mock = mock.patch(
            'gateway.views.PaymentStrategy.process_payment_from_split_data'
        ).start()

        # Call
        response = self.client.post(url, data=payload, format='json')

        self.assertEqual(response.status_code, 200, response.json())

        process_payment_from_split_data_mock.assert_called_with(
            paymentData=paymentData_mock,
        )

    def test_process_payment_from_split_data(self):
        Split.objects.all().delete()
        Payment.objects.all().delete()
        Subscription.objects.all().delete()
        Order.objects.all().delete()

        customer_user = self.create_user(email='<EMAIL>')
        product_2 = self.create_product(user=self.user, price=50)
        product_3 = self.create_product(user=self.user, price=12.22)

        product_offer = self.product.offers.first()
        product_2_offer = product_2.offers.first()
        product_3_offer = product_3.offers.first()

        self.product.price = Decimal('4.44')
        self.product.save()
        product_offer.price = self.product.price
        product_offer.save()

        refIds = ['9zbbg3a', '6Uch39e', 'CLixpeb']

        paymentData_mock = {
            'id': 'e36f9fe5-c6c9-4bf1-9dbf-caf71ca64430',
            'user': {
                'id': self.user.company.externalId,
                'email': self.user.email,
                'first_name': self.user.first_name,
                'last_name': self.user.last_name,
            },
            'customer': {
                'id': customer_user.company.externalId,
                'name': 'Customer Test',
                'email': customer_user.email,
                'phone': '5511999998888',
                'docNumber': '87686581098',
                'docType': 'cpf'
            },
            'splits': [
                {
                    'amount': 54.53,
                    'amountReserve': 0,
                    'type': 'producer',
                    'rawPercentage': 100,
                    'percentage': 100,
                    'user': {
                            'id': self.user.company.externalId,
                            'email': self.user.email,
                    },
                    'pendingBalance': {
                        'releaseDate': '2025-05-26T19:13:28.436537-03:00'
                    },
                    'pendingBalanceReserve': {
                        'releaseDate': '2025-06-15T19:13:28.436537-03:00'
                    }
                }
            ],
            'card': {
                'lastDigits': '4242',
                'holderName': 'Customer Test',
                'brand': None,
                'token': '29c43e88-2848-4b13-99fa-df5cc4a565a5'
            },
            'fee': 12.129534,
            'pending': {
                'releaseDate': '2025-05-26T19:13:28.436537-03:00'
            },
            'pendingReserve': {
                'releaseDate': '2025-06-15T19:13:28.436537-03:00'
            },
            'liquidAmount': 54.530466,
            'refundedAmount': 0,
            'amount': 66.66,
            'paidAmount': 0,
            'reason': None,
            'exId': ','.join(refIds),
            'subscription': None,
            'acquirerType': '',
            'baseAmount': '66.66',
            'installments': 1,
            'status': 'paid',
            'paymentMethod': 'credit_card',
            'checkoutUrl': f'https://paystg.cakto.com.br/{product_offer.id}',
            'refererUrl': None,
            'fixedFee': '7.47',
            'percentageFee': '4.66',
            'items': [
                {
                    'title': product_offer.name,
                    'unitPrice': 4.44,
                    'quantity': 1,
                    'tangible': False,
                    'externalRef': product_offer.id,
                },
                {
                    'title': product_2_offer.name,
                    'unitPrice': 50,
                    'quantity': 1,
                    'tangible': False,
                    'externalRef': product_2_offer.id,
                },
                {
                    'title': product_3_offer.name,
                    'unitPrice': 12.22,
                    'quantity': 1,
                    'tangible': False,
                    'externalRef': product_3_offer.id,
                }
            ]
        }

        # Call
        payment = PaymentStrategy().process_payment_from_split_data(paymentData=paymentData_mock)

        # Assert Payment
        self.assertEqual(Payment.objects.count(), 1)
        self.assertEqual(payment, Payment.objects.first())
        self.assertEqual(payment.externalId, paymentData_mock['id'])
        self.assertEqual(payment.status, paymentData_mock['status'])
        self.assertEqual(payment.amount, paymentData_mock['amount'])
        self.assertEqual(payment.paymentMethod, self.credit_card)
        self.assertEqual(payment.installments, paymentData_mock['installments'])
        self.assertEqual(payment.card, paymentData_mock['card'])

        self.assertEqual(Order.objects.count(), 3)

        # Assert First product's Order
        o1 = Order.objects.get(refId=refIds[0])
        self.assertEqual(o1.externalId, paymentData_mock['id'])
        self.assertEqual(o1.status, paymentData_mock['status'])
        self.assertEqual(float(o1.amount), paymentData_mock['items'][0]['unitPrice'])  # type:ignore
        self.assertEqual(float(o1.baseAmount), paymentData_mock['items'][0]['unitPrice'])
        self.assertEqual(o1.paymentMethod, self.credit_card)
        self.assertEqual(o1.installments, paymentData_mock['installments'])
        self.assertEqual(o1.checkoutUrl, paymentData_mock['checkoutUrl'])
        self.assertEqual(o1.offer, product_offer)

        # First order commissions
        self.assertEqual(Split.objects.filter(order=o1).count(), 1)
        split = Split.objects.get(order=o1)
        self.assertEqual(split.user, self.user)
        self.assertEqual(float(split.totalAmount), 1.64)  # 1.64 considering fees
        self.assertEqual(split.percentage, 100)
        self.assertEqual(split.type, 'producer')

        # Assert Second product's Order
        o2 = Order.objects.get(refId=refIds[1])
        self.assertEqual(o2.externalId, paymentData_mock['id'])
        self.assertEqual(o2.status, paymentData_mock['status'])
        self.assertEqual(float(o2.amount), paymentData_mock['items'][1]['unitPrice'])  # type:ignore
        self.assertEqual(float(o2.baseAmount), paymentData_mock['items'][1]['unitPrice'])
        self.assertEqual(o2.paymentMethod, self.credit_card)
        self.assertEqual(o2.installments, paymentData_mock['installments'])
        self.assertEqual(o2.checkoutUrl, paymentData_mock['checkoutUrl'])
        self.assertEqual(o2.offer, product_2_offer)

        # Second order commissions
        self.assertEqual(Split.objects.filter(order=o2).count(), 1)
        split = Split.objects.get(order=o2)
        self.assertEqual(split.user, self.user)
        self.assertEqual(float(split.totalAmount), 44.01)  # 44.01 considering fees
        self.assertEqual(split.percentage, 100)
        self.assertEqual(split.type, 'producer')

        # Assert Third product's Order
        o3 = Order.objects.get(refId=refIds[2])
        self.assertEqual(o3.externalId, paymentData_mock['id'])
        self.assertEqual(o3.status, paymentData_mock['status'])
        self.assertEqual(float(o3.amount), paymentData_mock['items'][2]['unitPrice'])  # type:ignore
        self.assertEqual(float(o3.baseAmount), paymentData_mock['items'][2]['unitPrice'])
        self.assertEqual(o3.paymentMethod, self.credit_card)
        self.assertEqual(o3.installments, paymentData_mock['installments'])
        self.assertEqual(o3.checkoutUrl, paymentData_mock['checkoutUrl'])
        self.assertEqual(o3.offer, product_3_offer)

        # Third order commissions
        self.assertEqual(Split.objects.filter(order=o3).count(), 1)
        split = Split.objects.get(order=o3)
        self.assertEqual(split.user, self.user)
        self.assertEqual(float(split.totalAmount), 8.88)  # 8.88 considering fees
        self.assertEqual(split.percentage, 100)
        self.assertEqual(split.type, 'producer')
