from cakto.tests.base import BaseTestCase
from gateway.strategies.pix_strategy import PixStrategy
from product.models import Affiliate, Coproduction


class TestCoproductionSplits(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.product = cls.create_product(price=10)

        cls.coproducer_user_1 = cls.create_user()
        cls.coproduction_1 = Coproduction.objects.create(
            user=cls.coproducer_user_1,
            product=cls.product,
            amount=10,
            _status='accepted',
            receiveSalesFromProducer=True,
            receiveSalesFromAffiliate=True,
        )

        cls.coproducer_user_2 = cls.create_user()
        cls.coproduction_2 = Coproduction.objects.create(
            user=cls.coproducer_user_2,
            product=cls.product,
            amount=20,
            _status='accepted',
            receiveSalesFromProducer=True,
            receiveSalesFromAffiliate=True,
        )

    def test_get_coproduction_splits_returns_correct_splits_with_one_coproducer(self):
        self.coproduction_2.delete()

        splits = PixStrategy().get_coproduction_splits(offer=self.product.offers.first(), affiliate=None)

        expected_splits = [{
            'seller_id': self.coproducer_user_1.company.externalId,
            'percentage': float(self.coproduction_1.amount)
        }]

        self.assertEqual(splits, expected_splits)

    def test_get_coproduction_splits_returns_correct_splits_with_two_coproducer(self):
        splits = PixStrategy().get_coproduction_splits(offer=self.product.offers.first(), affiliate=None)

        expected_splits = [
            {
                'seller_id': self.coproducer_user_1.company.externalId,
                'percentage': float(self.coproduction_1.amount)
            },
            {
                'seller_id': self.coproducer_user_2.company.externalId,
                'percentage': float(self.coproduction_2.amount)
            },
        ]

        splits_sorted = sorted(splits, key=lambda x: x['seller_id'])
        expected_splits_sorted = sorted(expected_splits, key=lambda x: x['seller_id'])

        self.assertEqual(splits_sorted, expected_splits_sorted)

    def test_get_coproduction_splits_returns_correct_splits_with_two_coproducer_but_one_receives(self):
        self.coproduction_2.receiveSalesFromProducer = False
        self.coproduction_2.save(update_fields=['receiveSalesFromProducer'])

        splits = PixStrategy().get_coproduction_splits(offer=self.product.offers.first(), affiliate=None)
        expected_splits = [{
            'seller_id': self.coproducer_user_1.company.externalId,
            'percentage': float(self.coproduction_1.amount)
        }]

        self.assertEqual(splits, expected_splits)

    def test_get_coproduction_splits_returns_splits_empty_with_affiliate(self):
        Coproduction.objects.all().delete()

        affiliate_user = self.create_user()
        affiliate = Affiliate.objects.create(user=affiliate_user, product=self.product, commission=10)

        splits = PixStrategy().get_coproduction_splits(offer=self.product.offers.first(), affiliate=affiliate)

        self.assertEqual(splits, [])

    def test_get_coproduction_splits_coproducer_not_receive_affiliate_sales(self):
        affiliate_user = self.create_user()
        affiliate = Affiliate.objects.create(user=affiliate_user, product=self.product, commission=10)

        self.coproduction_2.delete()
        self.coproduction_1.receiveSalesFromAffiliate = False
        self.coproduction_1.save(update_fields=['receiveSalesFromAffiliate'])

        splits = PixStrategy().get_coproduction_splits(offer=self.product.offers.first(), affiliate=affiliate)

        self.assertEqual(splits, [])

    def test_get_coproduction_splits_coproducer_not_receive_producer_sales(self):
        self.coproduction_2.delete()
        self.coproduction_1.receiveSalesFromProducer = False
        self.coproduction_1.save(update_fields=['receiveSalesFromProducer'])

        splits = PixStrategy().get_coproduction_splits(offer=self.product.offers.first(), affiliate=None)

        self.assertEqual(splits, [])
