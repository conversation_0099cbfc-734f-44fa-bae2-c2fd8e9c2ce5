from cakto.tests.base import BaseTestCase

class PaymentTest(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.create_paymentMethods()

        cls.order = cls.create_order(paymentMethod='pix')

    def test_payment_paymentMethodType(self):
        payment = self.create_payment(orders=[self.order])
        self.assertEqual(payment.paymentMethodType, 'pix')

    def test_payment_paymentMethodName(self):
        payment = self.create_payment(orders=[self.order])
        self.assertEqual(payment.paymentMethodName, 'Pix')
