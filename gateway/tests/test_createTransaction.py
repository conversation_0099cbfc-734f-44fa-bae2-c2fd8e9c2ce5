import json
from decimal import Decimal
from unittest import mock

import responses
from django.conf import settings

from cakto.tests.base import BaseTestCase
from gateway.models import Split
from gateway.strategies.pix_strategy import PixStrategy
from product.models import Affiliate


class TestCreateTransaction(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.create_paymentMethods()

        cls.product = cls.create_product(price=10)

        cls.affiliate_user = cls.create_user()

        cls.affiliate = Affiliate.objects.create(
            user=cls.affiliate_user,
            product=cls.product,
            status='active',
            commission=10
        )

        cls.order = cls.create_order(product=cls.product, create_splits=False)

        cls.order_with_affiliate = cls.create_order(product=cls.product, affiliate=cls.affiliate, create_splits=False)

    @responses.activate
    def test_createTransaction_calls_get_coproduction_splits_correctly(self):
        responses.post(
            url=settings.GATEWAY_URL + 'api/checkout/',
            json=json.load(open('gateway/tests/mocks/split_pix_payment_response.json')),
            status=200,
        )

        with mock.patch('gateway.strategies.pix_strategy.PixStrategy.get_coproduction_splits') as get_coproduction_splits_mock:
            with mock.patch('gateway.strategies.pix_strategy.PixStrategy.create_order_splits', mock.Mock()):
                get_coproduction_splits_mock.return_value = {}
                strategy = PixStrategy()
                strategy.orders = [self.order_with_affiliate]
                strategy.createTransaction()

        args, kwargs = get_coproduction_splits_mock.call_args
        self.assertEqual(args[0], self.order_with_affiliate.offer)
        self.assertEqual(args[1], self.order_with_affiliate.affiliate)

    def test_get_payment_payload_returns_affiliate_data(self):
        payload = PixStrategy().get_payment_payload(order=self.order_with_affiliate, orders=[self.order_with_affiliate], splits=[])

        self.assertEqual(
            payload['affiliate'],
            {
                'id': self.affiliate_user.company.externalId,
                'percentage': float(self.affiliate.commission)
            }
        )

    def test_get_payment_payload_returns_coproduction_splits(self):
        expected_splits = [{'id': 111, 'percentage': 10.0}]
        payload = PixStrategy().get_payment_payload(order=self.order, orders=[self.order], splits=expected_splits)

        self.assertEqual(payload['splits'], expected_splits)

    @responses.activate
    def test_SplitPay_createTransaction_is_called_get_payment_payload_return(self):
        response = responses.post(url=settings.GATEWAY_URL + 'api/checkout/', json={}, status=200)

        expected_payload = {'test_payload': 'test'}

        with mock.patch('gateway.strategies.pix_strategy.PixStrategy.get_payment_payload', mock.Mock(return_value=expected_payload)):
            with mock.patch('gateway.strategies.pix_strategy.PixStrategy.get_coproduction_splits', mock.Mock(return_value=[])):
                strategy = PixStrategy()
                strategy.orders = [self.order]
                strategy.createTransaction()

        self.assertEqual(json.loads(response.calls[0].request.body), expected_payload)  # type: ignore

    def test_create_order_splits_affiliate(self):
        commissions = [{
            "commissionValue": 11,
            "type": "affiliate",
            "commissionPercentage": 5.0,
            "userId": self.affiliate_user.id,
        }]

        self.order.commissions = commissions
        self.order.save(update_fields=['commissions'])

        PixStrategy.create_order_splits(self.order)

        self.assertEqual(self.order.splits.filter(type='affiliate').count(), 1)
        self.assertEqual(Split.objects.filter(user=self.affiliate_user).count(), 1)
        self.assertEqual(self.order.splits.first().totalAmount, Decimal("11"))
        self.assertEqual(self.order.splits.first().percentage, Decimal("5.0"))

    def test_create_order_splits_coproducer(self):
        commissions = [{
            "commissionValue": 11,
            "type": "coproducer",
            "commissionPercentage": 5.0,
            "userId": self.product.user.id,
        }]

        self.order.commissions = commissions
        self.order.save(update_fields=['commissions'])

        PixStrategy.create_order_splits(self.order)

        self.assertEqual(self.order.splits.filter(type='coproducer').count(), 1)
        self.assertEqual(Split.objects.filter(user=self.product.user).count(), 1)
        self.assertEqual(self.order.splits.first().totalAmount, Decimal("11"))
        self.assertEqual(self.order.splits.first().percentage, Decimal("5.0"))

    def test_create_order_splits_producer(self):
        commissions = [{
            "commissionValue": 11,
            "type": "producer",
            "commissionPercentage": 5.0,
            "userId": self.product.user.id,
        }]

        self.order.commissions = commissions
        self.order.save(update_fields=['commissions'])

        PixStrategy.create_order_splits(self.order)

        self.assertEqual(self.order.splits.filter(type='producer').count(), 1)
        self.assertEqual(Split.objects.filter(user=self.product.user).count(), 1)
        self.assertEqual(self.order.splits.first().totalAmount, Decimal("11"))
        self.assertEqual(self.order.splits.first().percentage, Decimal("5.0"))

    def test_create_order_splits_with_two_commissions(self):
        commissions = [{
            "commissionValue": 20,
            "type": "affiliate",
            "commissionPercentage": 50,
            "userId": self.affiliate_user.id,
        },
            {
                "commissionValue": 11,
                "type": "producer",
                "commissionPercentage": 5.0,
                "userId": self.product.user.id,
        }]

        self.order.commissions = commissions
        self.order.save(update_fields=['commissions'])

        PixStrategy.create_order_splits(self.order)

        self.assertEqual(self.order.splits.count(), 2)

        self.assertEqual(self.order.splits.filter(type='producer').count(), 1)
        self.assertEqual(Split.objects.filter(user=self.product.user).count(), 1)
        self.assertEqual(self.order.splits.filter(type='producer').first().totalAmount, Decimal("11"))
        self.assertEqual(self.order.splits.filter(type='producer').first().percentage, Decimal("5"))

        self.assertEqual(self.order.splits.filter(type='affiliate').count(), 1)
        self.assertEqual(Split.objects.filter(user=self.affiliate_user).count(), 1)
        self.assertEqual(self.order.splits.filter(type='affiliate').first().totalAmount, Decimal("20"))
        self.assertEqual(self.order.splits.filter(type='affiliate').first().percentage, Decimal("50"))
