{"secret": "bbd4990f-4560-4747-9721-6348756e60e8", "type": "transaction.changed", "data": {"id": "26e9c6f4-3a53-4d86-a9ed-c5ac224fecc2", "user": {"id": 23, "email": "<EMAIL>", "first_name": "<PERSON><PERSON>", "last_name": "Pro"}, "customer": {"id": 5, "name": "Customer Test", "email": "<EMAIL>", "phone": "5534999988888", "docNumber": "70736616071", "docType": "cpf"}, "address": null, "splits": [{"amount": 2.12, "amountReserve": 0.24, "type": "producer", "rawPercentage": 100.0, "percentage": 100.0, "user": {"id": 23, "email": "<EMAIL>"}, "pendingBalance": {"releaseDate": "2025-02-10T11:41:57.085769-03:00"}, "pendingBalanceReserve": {"releaseDate": "2025-02-02T11:41:57.085769-03:00"}}], "affiliateSplit": null, "producerSplit": {"amount": 2.12445, "amountReserve": 0.23605, "type": "producer", "rawPercentage": 100.0, "percentage": 100.0, "user": {"id": 23, "email": "<EMAIL>"}, "pendingBalance": {"releaseDate": "2025-02-10T11:41:57.085769-03:00"}, "pendingBalanceReserve": {"releaseDate": "2025-02-02T11:41:57.085769-03:00"}}, "card": null, "pix": {"qrCode": "00020101021226990014br.gov.bcb.pix2577qrcodes.cielo.com.br/qr-pix/qrs1/v2/01DGIFYdr7KudTl4TButYH8XB2FWsk0T8UneQrt3b52040000530398654045.005802BR5914CAKTO PAY LTDA6007MARILIA62070503***6304FEEC", "expirationDate": "2025-01-31 16:35:35.590559+00:00"}, "boleto": null, "picpay": null, "nupay": null, "googlepay": null, "applepay": null, "fee": 2.6395, "pending": {"releaseDate": "2025-02-10T11:41:57.085769-03:00"}, "pendingReserve": {"releaseDate": "2025-02-02T11:41:57.085769-03:00"}, "liquidAmount": 2.3605, "refundedAmount": 0.0, "amount": 5.0, "paidAmount": 0.0, "reason": null, "exId": "sandbox_exId_37ecf4bb-a46d-4dd8-88fc-2594f75a5937", "nfeId": null, "nfeURL": null, "nfeStatus": null, "chargeId": "sandbox_chargeId_37ecf4bb-a46d-4dd8-88fc-2594f75a5937", "e2eId": "sandbox_e2eId_37ecf4bb-a46d-4dd8-88fc-2594f75a5937", "acquirerType": "", "baseAmount": "5.00", "installments": 1, "feeByItem": true, "status": "paid", "internalReason": null, "paymentMethod": "pix", "description": null, "checkoutUrl": "https://paystg.cakto.com.br/4rcfRwK", "refererUrl": null, "receipt": null, "nextStep": null, "fine": "0.00", "gatewayFee": "0.00", "interest": "0.00", "fixedFee": "2.49", "percentageFee": "0.15", "costRelease": 0, "cost": "0.00", "fixedCost": "0.00", "percentageCost": "0.00", "rawPercentageCost": "0.00", "profit": "2.64", "items": [{"title": "Test Product", "unitPrice": 5.0, "quantity": 1, "tangible": false, "externalRef": "4rcfRwK"}], "postbackUrl": null, "refusedByAntifraud": false, "antifraud_recomendation_id": "2736472289393508505", "antifraud_profiling_attempt_reference": "eac806d0-30e2-4cc6-9d2c-74c7a2338c5e", "antifraud_transaction_id": null, "trackingCode": null, "createdAt": "2025-01-31T11:41:57.085769-03:00", "updatedAt": "2025-01-31T11:41:57.983636-03:00", "paidAt": null, "chargedbackAt": null, "refundedAt": null, "expiresInDays": 1, "due_date": null, "retry_count": 0, "acquirer": 21, "subscription": null}}