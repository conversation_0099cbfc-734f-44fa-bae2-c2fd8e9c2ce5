{"secret": "dea28f03-d76b-4898-a701-5998136ec4fd", "type": "subscription.subscription_status_changed", "data": {"id": 1, "payments": ["04330102-daca-4f8b-83ba-c515a1e06f6e", "acbb98f6-132e-44eb-9fc7-5168bde5af08", "56711177-03db-46e1-9872-482dd53e1bf3"], "first_payment": {"id": "04330102-daca-4f8b-83ba-c515a1e06f6e", "user": {"id": 1, "email": "<EMAIL>", "first_name": "Nome", "last_name": "Souza"}, "customer": {"id": 2, "name": "Test customer", "email": "<EMAIL>", "phone": "11999999999", "docNumber": "12345678900", "docType": "cpf"}, "address": {"id": 2, "street": "<PERSON><PERSON>", "number": "801", "complement": "801", "neighborhood": null, "city": "São Paulo", "state": "SP", "zipcode": "12345678", "customer": 2}, "splits": [{"amount": -2.06, "amountReserve": -0.23, "type": "producer", "rawPercentage": 100, "percentage": 100, "user": {"id": 1, "email": "<EMAIL>"}, "pendingBalance": null, "pendingBalanceReserve": null}], "affiliateSplit": null, "producerSplit": {"amount": -2.06, "amountReserve": -0.23, "type": "producer", "rawPercentage": 100, "percentage": 100, "user": {"id": 1, "email": "<EMAIL>"}, "pendingBalance": null, "pendingBalanceReserve": null}, "card": null, "pix": null, "boleto": null, "picpay": null, "nupay": null, "googlepay": null, "applepay": null, "openFinanceNubank": null, "fee": 5.49, "pending": null, "pendingReserve": null, "liquidAmount": -2.29, "refundedAmount": 0, "amount": 3.2, "paidAmount": 0, "reason": null, "exId": null, "nfeId": null, "nfeURL": null, "nfeStatus": null, "subscription": {"id": 1, "status": "inactive", "paymentMethod": "pix", "recurrence_period": 2, "quantity_recurrences": 2, "trial_days": 0, "max_retries": 3, "retry_interval": 1, "retry_count": 0, "next_payment_date": "2024-10-14T19:58:37.965662-03:00", "amount": null, "createdAt": "2024-10-14T19:58:37.966261-03:00", "updatedAt": "2024-10-14T19:58:37.966271-03:00", "user": 1, "card": null}, "chargeId": null, "e2eId": null, "acquirerType": "payup", "baseAmount": "3.20", "installments": 1, "refundId": null, "feeByItem": false, "status": "pending", "internalReason": null, "paymentMethod": "pix", "description": null, "checkoutUrl": "https://google.com", "refererUrl": null, "receipt": null, "nextStep": null, "fine": "0.00", "gatewayFee": "0.00", "interest": "0.00", "fixedFee": "5.49", "percentageFee": "0.00", "costRelease": 0, "cost": "0.00", "fixedCost": "0.00", "percentageCost": "0.00", "rawPercentageCost": "0.00", "profit": "5.49", "items": [{"title": "Boneca Gatucha Pet", "quantity": 1, "tangible": true, "unitPrice": 100, "externalRef": "1737"}], "threeDSecure": null, "postbackUrl": "https://homolog.classepay.com.br/api/v1/webhook/morganpay/payments", "refusedByAntifraud": false, "antifraud_recomendation_id": null, "antifraud_profiling_attempt_reference": null, "antifraud_transaction_id": null, "trackingCode": null, "createdAt": "2024-10-14T19:58:37.854998-03:00", "updatedAt": "2024-10-14T19:58:38.083614-03:00", "paidAt": null, "chargedbackAt": null, "refundedAt": null, "expiresInDays": null, "due_date": null, "retry_count": 0, "retry_scheduled": false, "acquirer": 3}, "status": "inactive", "paymentMethod": "pix", "recurrence_period": 2, "quantity_recurrences": 2, "trial_days": 0, "max_retries": 3, "retry_interval": 1, "retry_count": 0, "next_payment_date": "2024-10-14T19:58:37.965662-03:00", "amount": null, "createdAt": "2024-10-14T19:58:37.966261-03:00", "updatedAt": "2024-10-14T19:58:37.966271-03:00", "user": 1, "card": null}}