{"status": "success", "data": {"id": "2041b4a3-c25c-4ff5-b710-6894f2b8c115", "customer": {"id": 190, "name": "Test checkout", "email": "<EMAIL>", "phone": "34999999999", "docNumber": "79792364080", "docType": "cpf"}, "address": null, "splits": [{"amount": 0.0, "amountReserve": 0.0, "type": "producer", "percentage": 0.0, "user": {"id": 97, "email": "<EMAIL>"}, "pendingBalance": null, "pendingBalanceReserve": null}, {"amount": 0.94, "amountReserve": 0.24, "type": "coproducer", "percentage": 50.0, "user": {"id": 103, "email": "<EMAIL>"}, "pendingBalance": null, "pendingBalanceReserve": null}], "affiliateSplit": null, "producerSplit": {"amount": 0.0, "amountReserve": 0.0, "type": "producer", "percentage": 0.0, "user": {"id": 97, "email": "<EMAIL>"}, "pendingBalance": null, "pendingBalanceReserve": null}, "card": null, "pix": null, "boleto": null, "picpay": {"qrCode": "00020101021226860014COM.PICPAY.P2B0164https://app.picpay.com/checkout/NjZlMzMxMmU2MjQ2OTMzOWM4MDdhYTI05204000053039865802BR5906PICPAY6009SAO PAULO6228052466e3312e62469339c807aa2480550020COM.PICPAY.ECOMMERCE0107nc=true0216checkout=oneshot6304AA58", "paymentURL": "https://app.picpay.com/checkout/NjZlMzMxMmU2MjQ2OTMzOWM4MDdhYTI0", "expirationDate": "2024-09-12 15:36:34-03:00"}, "fee": 2.6395, "pending": null, "pendingReserve": null, "liquidAmount": 2.3605, "refundedAmount": 0.0, "amount": 10.0, "paidAmount": 0.0, "reason": null, "exId": "3CEPQxf", "externalId": "", "chargeId": null, "e2eId": null, "acquirerType": "asaas", "baseAmount": 5.0, "installments": 1, "status": "waiting_payment", "internalReason": null, "paymentMethod": "picpay", "description": null, "checkoutUrl": null, "receipt": null, "fine": 0.0, "gatewayFee": 0.0, "interest": 0.0, "fixedFee": 2.49, "percentageFee": 0.15, "costRelease": 0, "cost": 0.0, "fixedCost": 0.0, "percentageCost": 0.0, "rawPercentageCost": 0.0, "profit": 2.64, "postbackUrl": null, "refusedByAntifraud": false, "createdAt": "2024-06-09T15:22:12.413476-03:00", "updatedAt": "2024-06-09T15:22:16.069756-03:00", "paidAt": null, "expiresInDays": 1, "acquirer": 7}}