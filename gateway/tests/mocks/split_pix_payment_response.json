{"status": "success", "data": {"id": "2041b4a3-c25c-4ff5-b710-6894f2b8c115", "customer": {"id": 190, "name": "Test checkout", "email": "<EMAIL>", "phone": "34999999999", "docNumber": "79792364080", "docType": "cpf"}, "address": null, "splits": [{"amount": 0.0, "amountReserve": 0.0, "type": "producer", "percentage": 0.0, "user": {"id": 97, "email": "<EMAIL>"}, "pendingBalance": null, "pendingBalanceReserve": null}, {"amount": 0.94, "amountReserve": 0.24, "type": "coproducer", "percentage": 50.0, "user": {"id": 103, "email": "<EMAIL>"}, "pendingBalance": null, "pendingBalanceReserve": null}], "affiliateSplit": null, "producerSplit": {"amount": 0.0, "amountReserve": 0.0, "type": "producer", "percentage": 0.0, "user": {"id": 97, "email": "<EMAIL>"}, "pendingBalance": null, "pendingBalanceReserve": null}, "pix": {"expirationDate": "2030-03-21", "qrCode": "00020101021226840014br.gov.bcb.pix2562qr.iugu.com/public/payload/v2/81EAAB8316AE400DB5D376B72ED6634C52040000530398654045.005802BR5925ABMEX PAGAMENTOS INTELIGE6009SAO PAULO62070503***63044B00"}, "fee": 2.6395, "pending": null, "pendingReserve": null, "liquidAmount": 2.3605, "refundedAmount": 0.0, "amount": 10.0, "paidAmount": 0.0, "reason": null, "exId": "3CEPQxf", "externalId": "", "chargeId": null, "e2eId": null, "acquirerType": "asaas", "baseAmount": 5.0, "installments": 1, "status": "waiting_payment", "internalReason": null, "paymentMethod": "pix", "description": null, "checkoutUrl": null, "receipt": null, "fine": 0.0, "gatewayFee": 0.0, "interest": 0.0, "fixedFee": 2.49, "percentageFee": 0.15, "costRelease": 0, "cost": 0.0, "fixedCost": 0.0, "percentageCost": 0.0, "rawPercentageCost": 0.0, "profit": 2.64, "postbackUrl": null, "refusedByAntifraud": false, "createdAt": "2024-06-09T15:22:12.413476-03:00", "updatedAt": "2024-06-09T15:22:16.069756-03:00", "paidAt": null, "expiresInDays": 1, "acquirer": 7}}