import decimal

from django.contrib.auth.models import Permission
from django.urls import reverse
from django.utils import timezone

from cakto.tests.base import BaseTestCase


class OrderAdminListAPIViewTest(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.create_paymentMethods()

        permissions = Permission.objects.filter(codename__in=['view_order', 'refund_order'])
        cls.user.user_permissions.add(*permissions)
        cls.user.is_staff = True
        cls.user.save(update_fields=['is_staff'])
        cls.token = cls.get_user_access_token(cls.user)
        cls.headers = cls.create_headers(cls.token)

        cls.another_product = cls.create_product(user=cls.user, name='Another Product')
        cls.customer = cls.create_customer(email=cls.user.email)
        cls.url = reverse('admin-orders-list')
        cls.orders = [cls.create_order(cls.customer) for _ in range(2)]
        cls.different_order = cls.create_order(
            paymentMethod='pix',
            status='paid',
            customer=cls.create_customer(email='<EMAIL>', name='Second', docNumber='12345678902'),
            couponCode='COUPON123',
            product=cls.another_product,
            amount=decimal.Decimal('29.90'),
            baseAmount=decimal.Decimal('29.90'),
            offer_type='upsell',
            type='subscription',
            utm_source='google',
            utm_medium='cpc',
            utm_campaign='summer_sale',
            utm_content='landing_page',
            utm_term='hr+ebooks'
        )
        cls.different_order.createdAt = timezone.now() + timezone.timedelta(days=365)
        cls.different_order.save()
        cls.orders.append(cls.different_order)

    def test_list_orders(self):
        response = self.client.get(self.url, format='json', headers=self.headers)
        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertEqual(len(response.data['results']), 3)

    def test_filter_by_id(self):
        order_id = self.orders[0].id
        response = self.client.get(f'{self.url}?id={order_id}', format='json', headers=self.headers)
        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['id'], order_id)

    def test_filter_by_refid(self):
        ref_id = self.orders[0].refId
        response = self.client.get(f'{self.url}?refId={ref_id}', format='json', headers=self.headers)
        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['refId'], ref_id)

    def test_filter_by_refid_in(self):
        ref_ids = self.orders[0].refId + ',' + self.orders[1].refId
        response = self.client.get(f'{self.url}?refId={ref_ids}', format='json', headers=self.headers)
        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertEqual(len(response.data['results']), 2)
        received_ref_ids = [order['refId'] for order in response.data['results']]
        self.assertEqual(sorted(received_ref_ids), sorted(ref_ids.split(',')))

    def test_filter_by_status(self):
        status = 'waiting_payment'
        response = self.client.get(f'{self.url}?status={status}', format='json', headers=self.headers)
        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertEqual(len(response.data['results']), 2)
        for order in response.data['results']:
            self.assertEqual(order['status'], status)

    def test_filter_by_status_in(self):
        statuses = 'waiting_payment,paid'
        response = self.client.get(f'{self.url}?status={statuses}', format='json', headers=self.headers)
        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertEqual(len(response.data['results']), 3)

    def test_search_by_customer_name(self):
        name = self.customer.name
        response = self.client.get(f'{self.url}?search={name}', format='json', headers=self.headers)
        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertEqual(len(response.data['results']), 2)
        for order in response.data['results']:
            self.assertEqual(order['customer']['name'], name)

    def test_filter_by_customer_email(self):
        email = self.customer.email
        response = self.client.get(f'{self.url}?customer={email}', format='json', headers=self.headers)
        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertEqual(len(response.data['results']), 2)
        for order in response.data['results']:
            self.assertEqual(order['customer']['email'], email)

    def test_filter_by_customer_doc_number(self):
        doc_number = self.customer.docNumber
        response = self.client.get(f'{self.url}?customer={doc_number}', format='json', headers=self.headers)
        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertEqual(len(response.data['results']), 2)
        for order in response.data['results']:
            self.assertEqual(order['customer']['docNumber'], doc_number)

    def test_filter_by_payment_method(self):
        payment_method = self.orders[0].paymentMethod.type
        response = self.client.get(f'{self.url}?paymentMethod={payment_method}', format='json', headers=self.headers)
        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertEqual(len(response.data['results']), 2)
        for order in response.data['results']:
            self.assertEqual(order['paymentMethod'], payment_method)

    def test_filter_by_created_at(self):
        created_at = self.orders[0].createdAt
        start = (created_at - timezone.timedelta(days=3)).strftime('%Y-%m-%d')
        end = (created_at + timezone.timedelta(days=3)).strftime('%Y-%m-%d')

        response = self.client.get(f'{self.url}?createdAt__gte={start}&createdAt__lte={end}', format='json', headers=self.headers)
        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertEqual(len(response.data['results']), 2)
        for order in response.data['results']:
            self.assertTrue(start <= order['createdAt'] <= end)

    def test_filter_by_amount(self):
        amount = self.orders[2].amount
        response = self.client.get(f'{self.url}?amount={amount}', format='json', headers=self.headers)
        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertEqual(len(response.data['results']), 1)
        for order in response.data['results']:
            self.assertEqual(decimal.Decimal(order['amount']), amount)

    def test_filter_by_amount_range(self):
        amount = self.orders[2].amount
        amountMin = amount - 10  # type:ignore
        amountMax = amount + 10  # type:ignore

        response = self.client.get(f'{self.url}?amount__gte={amountMin}&amount__lte={amountMax}', format='json', headers=self.headers)
        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertEqual(len(response.data['results']), 1)
        for order in response.data['results']:
            self.assertTrue(amountMin <= decimal.Decimal(order['amount']) <= amountMax)
