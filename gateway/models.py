import uuid
from decimal import ROUND_HALF_DOWN, Decimal

from django.db import models
from django.db.models import OuterRef, Subquery, Sum, UniqueConstraint, Max
from django.utils import timezone
from shortuuid.django_fields import ShortUUIDField

from cakto.enums import LabeledEnum
from gateway.enums import SubscriptionStatus
from product.enums import ProductType


def generate_random_id():
    return str(uuid.uuid4())

def generate_7_random_id():
    return str(uuid.uuid4().hex)[:7].upper()

def get_releaseDate():
    ...


class CoproductionType(LabeledEnum):
    PRODUCER = ('producer', 'Sou produtor')
    COPRODUCER = ('coproducer', 'Sou co-produtor')
    AFFILIATE = ('affiliate', 'Sou afiliado')

    @classmethod
    def as_list(cls):
        return [{'type': status.value[0], 'name': status.value[1]} for status in cls]

class OfferType(LabeledEnum):
    MAIN = ('main', 'Principal')
    UPSELL = ('upsell', 'Upsell')
    DOWNSELL = ('downsell', 'Downsell')
    ORDERBUMP = ('orderbump', 'Order Bump')

    @classmethod
    def as_list(cls):
        return [{'type': status.value[0], 'name': status.value[1]} for status in cls]


OFFER_TYPE_CHOICES = [status.value for status in OfferType]

class PaymentMethodType(LabeledEnum):
    PIX = ('pix', 'Pix')
    BOLETO = ('boleto', 'Boleto')
    CREDIT_CARD = ('credit_card', 'Cartão de Crédito')
    THREEDS = ('threeDs', 'Cartão de Crédito 3DS')
    PICPAY = ('picpay', 'Pic Pay')
    GOOGLEPAY = ('googlepay', 'Google Pay')
    APPLEPAY = ('applepay', 'Apple Pay')
    NUBANK = ('openfinance_nubank', 'Nubank')

    @classmethod
    def as_list(cls):
        return [{'type': status.value[0], 'name': status.value[1]} for status in cls]

class PaymentStatus(LabeledEnum):
    PROCESSING = ('processing', 'Processando')
    AUTHORIZED = ('authorized', 'Autorizado')
    PAID = ('paid', 'Pago')
    REFUND_REQUESTED = ('refund_requested', 'Reembolso solicitado')
    REFUNDED = ('refunded', 'Reembolsado')
    WAITING_PAYMENT = ('waiting_payment', 'Aguardando pagamento')
    REFUSED = ('refused', 'Recusado')
    BLOCKED = ('blocked', 'Recusado')
    CHARGEDBACK = ('chargedback', 'Chargeback')
    CANCELED = ('canceled', 'Cancelado')
    IN_PROTEST = ('in_protest', 'Em protesto')
    PARTIALLY_PAID = ('partially_paid', 'Parcialmente pago')
    PRECHARGEBACK = ('prechargeback', 'Prechargeback')
    SCHEDULED = ('scheduled', 'Scheduled')
    RETRYING = ('retrying', 'Retrying')
    MED = ('MED', 'Med')

    @classmethod
    def as_list(cls):
        return [{'type': status.value[0], 'name': status.value[1]} for status in cls]

class Order(models.Model):
    class Meta:
        verbose_name = 'Pedido'
        verbose_name_plural = 'Pedidos'
        ordering = ['-createdAt']
        permissions = [
            ('refund_order', 'Can refund Pedido'),
        ]

    # Basic info
    id = models.CharField(max_length=255, primary_key=True, unique=True, default=generate_random_id)
    refId = ShortUUIDField(length=7, max_length=40, unique=True, db_index=True, help_text='ID do Pedido')  # type:ignore
    externalId = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        db_index=True,
        help_text='ID do pedido no Gateway de Pagamento'
    )
    status = models.CharField(
        max_length=255,
        choices=PaymentStatus.choices(),
        default=PaymentStatus.WAITING_PAYMENT.id,
        db_index=True,
        help_text='Status do pedido',
    )

    # Customer info
    customer = models.ForeignKey(
        'customer.Customer',
        on_delete=models.PROTECT,
        related_name='orders',
        verbose_name='Cliente',
        help_text='Cliente que fez o pedido',
    )
    address = models.ForeignKey(
        'customer.Address',
        on_delete=models.PROTECT,
        null=True,
        blank=True,
        related_name='orders',
        verbose_name='Endereço do Cliente',
        help_text='Endereço do cliente para envio físico',
    )
    client_user_agent = models.TextField(null=True, blank=True)
    client_ip = models.CharField(max_length=255, null=True, blank=True)
    checkoutUrl = models.TextField(null=True, blank=True)
    refererUrl = models.TextField(null=True, blank=True)

    # Payment info
    baseAmount = models.DecimalField(max_digits=10, decimal_places=2)
    discount = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    amount = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    coupon = models.ForeignKey('product.Coupon', on_delete=models.PROTECT, null=True, blank=True)
    couponCode = models.CharField(max_length=255, null=True, blank=True)
    reason = models.CharField(max_length=255, null=True, blank=True)
    parent_order = models.ForeignKey('gateway.Order', related_name='children_orders', on_delete=models.PROTECT, null=True, blank=True)
    checkout = models.ForeignKey('product.Checkout', on_delete=models.SET_NULL, null=True, blank=True, related_name='orders')
    product = models.ForeignKey('product.Product', on_delete=models.PROTECT)
    offer = models.ForeignKey('product.Offer', on_delete=models.PROTECT, null=True, blank=True)
    offer_type = models.CharField(max_length=255, choices=OFFER_TYPE_CHOICES, default='main', verbose_name='Tipo de Oferta')
    type = models.CharField(max_length=255, choices=ProductType.choices(), default=ProductType.UNIQUE.id)
    paymentMethod = models.ForeignKey('product.PaymentMethod', on_delete=models.PROTECT, related_name='orders')
    installments = models.IntegerField(default=1)
    saveCard = models.BooleanField(default=False)
    card = models.ForeignKey('customer.Card', on_delete=models.PROTECT, null=True, blank=True, editable=False)
    subscription = models.ForeignKey('gateway.Subscription', on_delete=models.PROTECT, null=True, blank=True, related_name='orders')
    subscription_period = models.IntegerField(null=True, blank=True, verbose_name='Período da Assinatura')

    # Acquirer info
    acquirerType = models.CharField(max_length=255)

    # Affiliate
    affiliate = models.ForeignKey('product.Affiliate', null=True, blank=True, on_delete=models.PROTECT)

    # Coproduction info
    commissionedUsers = models.ManyToManyField('user.User', db_index=True, blank=True)
    commissions = models.JSONField(default=list)

    # fees
    fees = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)

    # refund info
    refunded_by = models.CharField(choices=(
        ('customer', 'Cliente'),
        ('producer', 'Produtor'),
        ('admin', 'Admin'),
    ), max_length=255, null=True, blank=True)
    refunded_by_user = models.ForeignKey('user.User', on_delete=models.PROTECT, null=True, blank=True, related_name='refunded_orders')
    refund_reason = models.TextField(null=True, blank=True)

    # UTM
    utm_source = models.CharField(max_length=255, null=True, blank=True)
    utm_medium = models.CharField(max_length=255, null=True, blank=True)
    utm_campaign = models.CharField(max_length=255, null=True, blank=True)
    utm_term = models.CharField(max_length=255, null=True, blank=True)
    utm_content = models.CharField(max_length=255, null=True, blank=True)
    sck = models.CharField(max_length=255, null=True, blank=True)

    # Facebook Cookies
    fbc = models.CharField(max_length=255, null=True, blank=True)
    fbp = models.CharField(max_length=255, null=True, blank=True)

    # Dates
    createdAt = models.DateTimeField(auto_now_add=True, db_index=True, help_text='Data de criação do pedido')
    updatedAt = models.DateTimeField(default=timezone.now, help_text='Data da última atualização do pedido')
    due_date = models.DateTimeField(null=True, blank=True, help_text='Data de agendamento do pagamento')
    paidAt = models.DateTimeField(null=True, blank=True, help_text='Data que o pagamento foi realizado')
    releaseDate = models.DateTimeField(blank=True, null=True, help_text='Data esperada de liberação das comissões')
    refundedAt = models.DateTimeField(null=True, blank=True, help_text='Data que o pedido foi reembolsado')
    chargedbackAt = models.DateTimeField(null=True, blank=True, help_text='Data de chargeback do pedido')
    canceledAt = models.DateTimeField(null=True, blank=True, help_text='Data de cancelamento do pedido')

    def __str__(self):
        return self.id

    def save(self, *args, **kwargs):
        if self.offer_id and ('type' not in kwargs.get('update_fields', [])):
            self.type = self.offer.type
        self.updatedAt = timezone.now()
        return super().save(*args, **kwargs)

    @staticmethod
    def get_all_user_values_from_queryset(order_queryset, user) -> Decimal:
        """Sum all commission values that belong to a user in all orders in the order_queryset"""
        user_commissions = (
            Split.objects.filter(order=OuterRef('id'), user=user)
            .values('order')
            .annotate(total=Sum('totalAmount'))
            .values('total')
        )

        order_queryset = order_queryset.annotate(userCommissions=Subquery(user_commissions))

        total_commissions = order_queryset.aggregate(total_commissions=Sum('userCommissions')).get('total_commissions') or 0

        return Decimal(total_commissions).quantize(Decimal('0.00'), ROUND_HALF_DOWN)

    @property
    def paymentMethodName(self):
        return self.paymentMethod.name

    @property
    def paymentMethodType(self):
        return self.paymentMethod.type

class Split(models.Model):
    order = models.ForeignKey('gateway.Order', on_delete=models.PROTECT, related_name='splits')
    user = models.ForeignKey('user.User', on_delete=models.PROTECT, related_name='splits', db_index=True)
    type = models.CharField(verbose_name='Tipo de comissão', max_length=40, choices=(
        ('affiliate', 'Afiliado'),
        ('coproducer', 'Coprodutor'),
        ('producer', 'Produtor')
    ))
    totalAmount = models.DecimalField('Valor total', max_digits=10, decimal_places=2, default=Decimal("0"))
    amountReserve = models.DecimalField('Reserva', max_digits=10, decimal_places=2, default=Decimal("0"))
    percentage = models.DecimalField('Porcentagem', max_digits=5, decimal_places=2, default=Decimal("0"))
    createdAt = models.DateTimeField('Criado em', auto_now_add=True)
    updatedAt = models.DateTimeField('Atualizado em', auto_now=True)

    class Meta:
        verbose_name = 'Comissão'
        verbose_name_plural = 'Comissões'
        ordering = ['-createdAt']
        constraints = [UniqueConstraint(fields=['order', 'user', 'type'], name='unique_split')]

    def __str__(self):
        return f"{self.order.refId} - {self.percentage} - {self.user.email}"

class Payment(models.Model):
    class Meta:
        verbose_name = 'Pagamento'
        verbose_name_plural = 'Pagamentos'
        ordering = ['-createdAt']

    # Basic info
    id = models.CharField(max_length=255, primary_key=True, unique=True, default=generate_random_id)
    externalId = models.CharField(max_length=255, null=True, blank=True)
    acquirerType = models.CharField(max_length=255, null=True, blank=True)
    status = models.CharField(max_length=255, choices=PaymentStatus.choices(), default=PaymentStatus.WAITING_PAYMENT.id)

    # Relations
    orders = models.ManyToManyField(Order)
    paymentMethod = models.ForeignKey('product.PaymentMethod', on_delete=models.PROTECT, related_name='payments')
    subscriptions = models.ManyToManyField('gateway.Subscription', related_name='payments')

    # Financial info
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    installments = models.IntegerField(default=1)
    refunded = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    reason = models.CharField(max_length=255, null=True, blank=True)

    # Payment info
    pix = models.JSONField(null=True, blank=True)
    boleto = models.JSONField(null=True, blank=True)
    card = models.JSONField(null=True, blank=True)
    picpay = models.JSONField(null=True, blank=True)
    googlepay = models.JSONField(null=True, blank=True)
    applepay = models.JSONField(null=True, blank=True)
    openFinanceNubank = models.JSONField(null=True, blank=True)
    threeDs = models.JSONField(null=True, blank=True, help_text='Dados do 3DS.')
    nextStep = models.TextField(null=True, blank=True, help_text='Url para verificação do 3DS.')
    threeDsFinished = models.BooleanField(default=False, help_text='Processo de 3DS finalizado?')

    # Dates
    createdAt = models.DateTimeField(auto_now_add=True, help_text='Data de criação do pagamento')
    updatedAt = models.DateTimeField(default=timezone.now, help_text='Data da última atualização do pagamento')
    paidAt = models.DateTimeField(null=True, blank=True, help_text='Data que o pagamento foi realizado')
    due_date = models.DateTimeField(null=True, blank=True, help_text='Data de agendamento do pagamento')
    refundedAt = models.DateTimeField(null=True, blank=True, help_text='Data de reembolso do pagamento')
    chargedbackAt = models.DateTimeField(null=True, blank=True, help_text='Data de chargeback do pagamento')

    def __str__(self):
        return self.id

    @property
    def paymentMethodName(self):
        return self.paymentMethod.name

    @property
    def paymentMethodType(self):
        return self.paymentMethod.type

class Subscription(models.Model):
    class Meta:
        verbose_name = 'Assinatura'
        verbose_name_plural = 'Assinaturas'
        ordering = ['-createdAt']

    # Basic info
    id = models.CharField(max_length=255, primary_key=True, unique=True, default=generate_random_id, help_text='ID da Assinatura')
    externalId = models.IntegerField(null=True, blank=True, db_index=True, help_text='ID da Assinatura no Gateway de Pagamento')
    status = models.CharField(max_length=255, choices=SubscriptionStatus.choices(), default=SubscriptionStatus.INACTIVE.id, help_text='Status da Assinatura')

    # Financial info
    amount = models.DecimalField(max_digits=10, decimal_places=2, help_text='Valor da Assinatura')
    recurrence_period = models.IntegerField(verbose_name='Período de recorrência (em dias)', default=30, help_text='Período de recorrência (dias)')
    quantity_recurrences = models.IntegerField(verbose_name='Quantidade de recorrências', default=12, help_text='Quantidade de recorrências')
    trial_days = models.IntegerField(verbose_name='Dias de teste', default=0, help_text='Dias de teste')
    max_retries = models.IntegerField(verbose_name='Número máximo de tentativas de cobrança', default=3, help_text='Número máximo de tentativas de cobrança')
    retry_interval = models.IntegerField(verbose_name='Intervalo entre tentativas (em dias)', default=1, help_text='Intervalo entre tentativas de cobrança (dias)')

    # General info
    current_period = models.IntegerField(default=0, verbose_name='Período Atual', help_text='Período atual')
    paid_payments_quantity = models.IntegerField(default=0, help_text='Quantidade de pagamentos pagos')
    retention = models.DurationField(default=timezone.timedelta(days=0), help_text='Tempo de retenção')

    # Relations
    paymentMethod = models.ForeignKey('product.PaymentMethod', on_delete=models.PROTECT, related_name='subscriptions', help_text='Método de pagamento')
    customer = models.ForeignKey('customer.Customer', on_delete=models.PROTECT, related_name='subscriptions', help_text='Cliente da Assinatura')
    product = models.ForeignKey('product.Product', on_delete=models.PROTECT, related_name='subscriptions', help_text='Produto da Assinatura')
    offer = models.ForeignKey('product.Offer', on_delete=models.PROTECT, related_name='subscriptions', help_text='Oferta da Assinatura')
    parent_order = models.ForeignKey('gateway.Order', on_delete=models.PROTECT, verbose_name='Pedido de Origem', related_name='subscriptions', help_text='Pedido de Origem da Assinatura')

    # Dates
    next_payment_date = models.DateTimeField(null=True, blank=True, help_text='Data do próximo pagamento')
    createdAt = models.DateTimeField(auto_now_add=True, db_index=True, help_text='Data de criação')
    updatedAt = models.DateTimeField(default=timezone.now, help_text='Data de atualização')
    canceledAt = models.DateTimeField(null=True, blank=True, help_text='Data de cancelamento')

    def __str__(self) -> str:
        return f'{self.customer.email} - {self.product.name}'

    def calculate_retention(self, last_payment_date) -> timezone.timedelta:
        if self.canceledAt:
            return self.canceledAt - self.createdAt

        if last_payment_date:
            return last_payment_date - self.createdAt

        return timezone.timedelta(days=0)

    def update_recurrence_metrics(self, commit: bool = True) -> None:
        payments_count = self.payments.all().aggregate(
            paid=Sum(
                models.Case(
                    models.When(status=PaymentStatus.PAID.id, then=1),
                    default=0,
                    output_field=models.IntegerField()
                )
            ),
            non_scheduled=Sum(
                models.Case(
                    models.When(status=PaymentStatus.SCHEDULED.id, then=0),
                    default=1,
                    output_field=models.IntegerField()
                )
            ),
            last_paid_payment_date=Max(
                models.Case(
                    models.When(status=PaymentStatus.PAID.id, then='paidAt'),
                    default=None,
                    output_field=models.DateTimeField()
                )
            )
        )

        self.paid_payments_quantity = payments_count.get('paid') or 0
        self.current_period = (payments_count.get('non_scheduled') or 0)
        self.retention = self.calculate_retention(
            last_payment_date=payments_count.get('last_paid_payment_date')
        )

        if commit:
            self.save(update_fields=['paid_payments_quantity', 'current_period', 'retention'])

    def set_config_fields_from_offer(self, offer, save: bool = True) -> None:
        self.offer = offer
        self.product = offer.product
        self.recurrence_period = offer.recurrence_period
        self.quantity_recurrences = offer.quantity_recurrences
        self.trial_days = offer.trial_days
        self.max_retries = offer.max_retries
        self.amount = offer.price
        self.retry_interval = offer.retry_interval

        if save:
            self.save(update_fields=[
                'offer', 'product', 'recurrence_period', 'quantity_recurrences',
                'trial_days', 'max_retries', 'amount', 'retry_interval']
            )

    @classmethod
    def get_supported_paymentMethodTypes(cls) -> list[str]:
        return [
            PaymentMethodType.PIX.value[0],
            PaymentMethodType.CREDIT_CARD.value[0],
            PaymentMethodType.BOLETO.value[0]
        ]

    def set_next_payment_date(self, datetime: str | timezone.datetime, commit: bool = True) -> None:
        if isinstance(datetime, str):
            datetime = timezone.datetime.fromisoformat(datetime)

        self.next_payment_date = datetime

        if commit:
            self.save(update_fields=['next_payment_date'])
