from decimal import Decimal

from django.conf import settings
from django.db.models import Sum
from django.shortcuts import get_object_or_404
from django.utils import timezone
from rest_framework import serializers
from rest_framework_simplejwt.tokens import RefreshToken

from cakto.serializers import CustomFlexFieldsSerializer
from checkout.serializers import CheckoutSerializer
from customer.serializers import (AddressFullSerializer, AddressProcessPaymentSerializer, CardAdminFlexSerializer,
                                  CardPublicFlexSerializer, CardPublicSerializer, CustomerAdminFlexSerializer,
                                  CustomerOrderSerializer, CustomerProcessPaymentSerializer,
                                  CustomerPublicFlexSerializer, CustomerPublicSerializer)
from email_service.mail import get_product_supportEmail
from gateway.models import Order, Payment, Split, Subscription
from gateway.utils import user_has_access_to_order_customer_data
from product.models import Offer, PaymentMethod
from product.serializers import (AffiliateAdminFlexSerializer, AffiliateOwnerFlexSerializer, CouponOwnerSerializer,
                                 CouponPublicSerializer, OfferAdminFlexSerializer, OfferOwnerFlexSerializer,
                                 OfferPublicSerializer, OfferSerializer, ProductAdminFlexSerializer,
                                 ProductOwnerFlexSerializer, ProductSerializer)
from user.enums import UserAccessLocations
from user.models import User
from user.serializers import UserAdminFlexSerializer, UserPublicFlexSerializer, UserReadOnlySerializer
from user.utils import log_user_login_history


class ItemProcessPaymentSerializer(serializers.Serializer):
    id = serializers.CharField()
    offerType = serializers.ChoiceField(('main', 'orderbump', 'upsell', 'downsell'))
    default = serializers.BooleanField(required=False, allow_null=True)
    installments = serializers.IntegerField(required=False, allow_null=True, min_value=1, max_value=12)

class MetadataProcessPaymentSerializer(serializers.Serializer):
    ip = serializers.IPAddressField(required=False, allow_blank=True, allow_null=True)
    country = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    sessionid = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    utm_source = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    utm_medium = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    utm_campaign = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    utm_term = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    utm_content = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    sck = serializers.CharField(required=False, allow_blank=True, allow_null=True)

class CardProcessPaymentSerializer(serializers.Serializer):
    token = serializers.CharField(required=False, allow_blank=True)
    externalToken = serializers.CharField(required=False)
    number = serializers.RegexField(r'^\d{4}.?\d{6}.?\d{5}$|^\d{4}.?\d{4}.?\d{4}.?\d{4}$', error_messages={'invalid': 'Número de cartão inválido.'}, required=False)
    holderName = serializers.CharField(required=False)
    expMonth = serializers.CharField(max_length=4, required=False)
    expYear = serializers.CharField(max_length=4, required=False)
    cvv = serializers.CharField(max_length=4, required=False)

class GooglePayProcessPaymentSerializer(serializers.Serializer):
    signature = serializers.CharField()
    signedMessage = serializers.CharField()

class ApplePayProcessPaymentSerializer(serializers.Serializer):
    signature = serializers.CharField()
    signedMessage = serializers.CharField()

class ThreeDsProcessPaymentSerializer(serializers.Serializer):
    Cavv = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    Eci = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    Xid = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    ReferenceId = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    Version = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    DataOnly = serializers.BooleanField(required=False)


class CheckoutProcessPaymentSerializer(serializers.Serializer):
    customer = CustomerProcessPaymentSerializer()
    address = AddressProcessPaymentSerializer(
        required=settings.ADDRESS_REQUIRED_FOR_PAYMENT,
        allow_null=not settings.ADDRESS_REQUIRED_FOR_PAYMENT,
    )
    paymentMethod = serializers.CharField(max_length=30)
    items = ItemProcessPaymentSerializer(many=True)
    installments = serializers.IntegerField(required=False, allow_null=True, min_value=1, max_value=12)
    metadata = MetadataProcessPaymentSerializer(required=False)
    type = serializers.CharField(max_length=60)
    card = CardProcessPaymentSerializer(required=False)
    saveCard = serializers.BooleanField(required=False)
    checkoutUrl = serializers.CharField(required=False)
    refererUrl = serializers.CharField(required=False, allow_blank=True)
    fbc = serializers.CharField(required=False, allow_blank=True)
    fbp = serializers.CharField(required=False, allow_blank=True)
    affiliateShortId = serializers.CharField(required=False, allow_blank=True)
    affiliateCookies = serializers.JSONField(required=False)
    captchaToken = serializers.CharField(required=False)
    coupon = serializers.CharField(required=False, allow_blank=True)
    antifraud_profiling_attempt_reference = serializers.CharField(required=False, allow_blank=True)
    googlepay = GooglePayProcessPaymentSerializer(required=False)
    applepay = ApplePayProcessPaymentSerializer(required=False)
    deviceId = serializers.CharField(required=False, allow_blank=True)
    threeDSecure = ThreeDsProcessPaymentSerializer(required=False, allow_null=True)

    def validate(self, data):
        assert 'product_user' in self.context, f'"product_user" instance is required as context in {self.__class__.__name__}.'

        self._validate_payment_data_fields(data)

        self._validate_paymentMethod(data)

        self._validate_installments(data)

        return data

    def _validate_payment_data_fields(self, data):
        if data.get('paymentMethod') == 'credit_card' and not data.get('card'):
            raise serializers.ValidationError({'card': 'Este campo é obrigatório quando o método de pagamento é credit_card.'})
        if data.get('paymentMethod') == 'googlepay' and not data.get('googlepay'):
            raise serializers.ValidationError({'googlepay': 'Este campo é obrigatório quando o método de pagamento é googlepay.'})
        if data.get('paymentMethod') == 'applepay' and not data.get('applepay'):
            raise serializers.ValidationError({'applepay': 'Este campo é obrigatório quando o método de pagamento é applepay.'})

    def _validate_paymentMethod(self, data):
        paymentMethod = data.get('paymentMethod', '')

        if paymentMethod not in PaymentMethod.get_valid_payment_methods_types(user=self.context['product_user']):
            raise serializers.ValidationError('Método de pagamento inválido.')

    def _validate_installments(self, data):
        main_offer_item = data.get('items', [{}])[0]

        installments = data.get('installments') or main_offer_item.get('installments') or 1

        if installments == 1:
            return

        main_offer = get_object_or_404(
            Offer.objects
            .filter(id=main_offer_item.get('id'), status='active')
            .select_related('product__user___company'),
        )

        item_ids = [item.get('id') for item in data.get('items')]

        total_price = (
            Offer.objects
            .filter(id__in=item_ids)
            .only('price')
            .aggregate(total_price=Sum('price'))
            .get('total_price')
        )

        calculatedInstallments = main_offer.calculatedInstallments(
            total_price=Decimal(total_price)  # type:ignore
        )

        # Runs through the calculated installments and check if the informed
        # installments is valid or raise an error if the loop ends normally
        for installment in calculatedInstallments:
            if installments == installment.get('installment'):
                break
        else:
            raise serializers.ValidationError('Quantidade de parcelas inválida.')

class SplitCommissionsSerializer(serializers.Serializer):
    userId = serializers.CharField(source='user.id')
    type = serializers.CharField()
    commissionPercentage = serializers.FloatField(source='percentage')
    commissionValue = serializers.FloatField(source='totalAmount')

class SplitAdminFlexSerializer(CustomFlexFieldsSerializer):
    class Meta:
        model = Split
        fields = '__all__'
        expandable_fields = {
            'user': UserAdminFlexSerializer,
            'order': 'gateway.serializers.OrderAdminFlexSerializer',
        }

class SplitOwnerFlexSerializer(CustomFlexFieldsSerializer):
    class Meta:
        model = Split
        fields = '__all__'
        expandable_fields = {
            'user': UserPublicFlexSerializer,
            'order': 'gateway.serializers.OrderPublicFlexSerializer',
        }

class SplitPublicSerializer(serializers.ModelSerializer):
    class Meta:
        model = Split
        fields = '__all__'
        expandable_fields = {
            'user': UserPublicFlexSerializer,
            'order': 'gateway.serializers.OrderPublicSerializer',
        }

class OrderAdminFlexSerializer(CustomFlexFieldsSerializer):
    class Meta:
        model = Order
        fields = '__all__'
        expandable_fields = {
            'customer': CustomerAdminFlexSerializer,
            'product': ProductAdminFlexSerializer,
            'checkout': CheckoutSerializer,
            'subscription': 'gateway.serializers.SubscriptionAdminFlexSerializer',
            'offer': OfferAdminFlexSerializer,
            'affiliate': AffiliateAdminFlexSerializer,
            'commissionedUsers': UserAdminFlexSerializer,
            'splits': SplitAdminFlexSerializer,
            'parent_order': 'gateway.serializers.OrderAdminFlexSerializer',
            'payment_set': 'gateway.serializers.PaymentAdminFlexSerializer',
        }

class OrderOwnerFlexSerializer(CustomFlexFieldsSerializer):
    class Meta:
        model = Order
        fields = [
            # Basic info
            'id',
            'refId',
            'status',

            # Customer info
            'customer',
            'checkoutUrl',
            'refererUrl',

            # Payment info
            'baseAmount',
            'discount',
            'amount',
            'coupon',
            'couponCode',
            'reason',
            'parent_order',
            'product',
            'checkout',
            'offer',
            'offer_type',
            'type',
            'paymentMethod',
            'installments',
            'card',
            'subscription',
            'subscription_period',

            # Affiliate info
            'affiliate',

            # Commission info
            'commissionedUsers',
            'commissions',
            'splits',

            # Fees info
            'fees',

            # Refund info
            'refund_reason',

            # UTM info
            'utm_source',
            'utm_medium',
            'utm_campaign',
            'utm_term',
            'utm_content',
            'sck',

            # Dates
            'createdAt',
            'updatedAt',
            'paidAt',
            'releaseDate',
            'refundedAt',
            'chargedbackAt',
            'due_date',
            'canceledAt',
        ]
        expandable_fields = {
            'customer': CustomerPublicFlexSerializer,
            'coupon': CouponOwnerSerializer,
            'parent_order': 'gateway.serializers.OrderOwnerFlexSerializer',
            'product': ProductOwnerFlexSerializer,
            'checkout': CheckoutSerializer,
            'offer': OfferOwnerFlexSerializer,
            'card': CardPublicFlexSerializer,
            'subscription': 'gateway.serializers.SubscriptionOwnerFlexSerializer',
            'affiliate': AffiliateOwnerFlexSerializer,
            'commissionedUsers': (UserPublicFlexSerializer, {'many': True}),
            'splits': SplitOwnerFlexSerializer,
        }

class OrderPublicFlexSerializer(CustomFlexFieldsSerializer):
    baseAmount = serializers.FloatField()
    amount = serializers.FloatField()
    offerId = serializers.CharField(source='offer.id', read_only=True)
    offerName = serializers.CharField(source='offer.name', read_only=True)
    paymentMethod = serializers.CharField(source='paymentMethodType')

    class Meta:
        model = Order
        fields = [
            'id',
            'refId',
            'offerId',
            'offerName',
            'offer_type',
            'status',
            'baseAmount',
            'amount',
            'reason',
            'refund_reason',
            'paymentMethod',
            'installments',
            'createdAt',
        ]
        ordering = ['-createdAt']

class OrderAdminRetrieveSerializer(serializers.ModelSerializer):
    product = ProductSerializer(read_only=True)
    customer = CustomerAdminFlexSerializer(read_only=True)
    paymentMethod = serializers.CharField(source='paymentMethodType')
    commissions = serializers.SerializerMethodField()
    offer = OfferSerializer(read_only=True)
    card = CardPublicSerializer(read_only=True)
    affiliate = serializers.SerializerMethodField()
    commissionedUsers = UserReadOnlySerializer(many=True, read_only=True)
    refunded_by_user = serializers.CharField(source='refunded_by_user.email', read_only=True)

    class Meta:
        model = Order
        fields = [
            'id',
            'refId',
            'status',
            'customer',
            'checkoutUrl',
            'refererUrl',
            'baseAmount',
            'discount',
            'amount',
            'coupon',
            'couponCode',
            'reason',
            'parent_order',
            'product',
            'offer',
            'subscription',
            'subscription_period',
            'offer_type',
            'type',
            'paymentMethod',
            'installments',
            'card',
            'affiliate',
            'commissionedUsers',
            'commissions',
            'fees',
            'refund_reason',
            'refunded_by',
            'refunded_by_user',

            # UTM
            'utm_source',
            'utm_medium',
            'utm_campaign',
            'utm_term',
            'utm_content',
            'sck',

            # Facebook Pixel
            'fbc',
            'fbp',

            # Dates
            'createdAt',
            'updatedAt',
            'paidAt',
            'releaseDate',
            'refundedAt',
            'chargedbackAt',
            'due_date',
            'canceledAt',
        ]
        ordering = ['-createdAt']

    def get_affiliate(self, order):
        if not order.affiliate:
            return None
        return {
            'id': order.affiliate.id,
            'email': order.affiliate.user.email,
            'short_id': order.affiliate.short_id,
        }

    def get_commissions(self, order):
        commissions: list[dict] = []
        for split in order.splits.all():
            commissions.append({
                'user': split.user.email,
                'name': ' '.join([split.user.first_name, split.user.last_name]),
                'totalAmount': float(split.totalAmount) if split.totalAmount else 0,
                'percentage': float(split.percentage) if split.percentage else 0,
                'type': split.type,
            })
        return commissions

class OrderAdminListSerializer(serializers.ModelSerializer):
    customer = CustomerOrderSerializer(read_only=True)
    paymentMethod = serializers.CharField(source='paymentMethodType')
    commissions = serializers.SerializerMethodField()

    class Meta:
        model = Order
        fields = [
            'id',
            'refId',
            'status',
            'customer',
            'amount',
            'offer_type',
            'paymentMethod',
            'commissions',
            'createdAt',
            'updatedAt',
            'paidAt',
        ]
        ordering = ['-createdAt']

    def get_commissions(self, order):
        commissions: list[dict] = []
        for split in order.splits.all():
            commissions.append({
                'user': split.user.email,
                'name': ' '.join([split.user.first_name, split.user.last_name]),
                'totalAmount': float(split.totalAmount) if split.totalAmount else 0,
                'percentage': float(split.percentage) if split.percentage else 0,
                'type': split.type,
            })
        return commissions

class OrderCustomerDataMixin:
    def get_customer(self, order: Order):
        assert self.context.get('request'), 'Request is required in context for this serializer.'  # type:ignore

        user = self.context.get('request').user  # type:ignore
        if user_has_access_to_order_customer_data(order=order, user=user):
            return CustomerOrderSerializer(order.customer).data
        return {'name': order.customer.name}

    def get_address(self, order: Order):
        if order.address is None:
            return None

        assert self.context.get('request'), 'Request is required in context for this serializer.'  # type:ignore

        user = self.context.get('request').user  # type:ignore
        if user_has_access_to_order_customer_data(order=order, user=user):
            fields = [
                'country', 'state', 'city', 'zipcode', 'street',
                'neighborhood', 'complement', 'number',
            ]
            return AddressFullSerializer(order.address, permitted_fields=fields).data

        return None

class OrderSerializer(OrderCustomerDataMixin, serializers.ModelSerializer):
    customer = serializers.SerializerMethodField(read_only=True)
    address = serializers.SerializerMethodField(read_only=True)
    product = ProductSerializer(read_only=True)
    commissionedUsers = UserReadOnlySerializer(many=True, read_only=True)
    coupon = CouponPublicSerializer(read_only=True)
    paymentMethod = serializers.CharField(source='paymentMethodType')
    commissions = SplitCommissionsSerializer(source='splits', many=True, read_only=True)

    class Meta:
        model = Order
        fields = [
            'id',
            'refId',
            'status',
            'type',
            'offer_type',
            'baseAmount',
            'discount',
            'amount',
            'coupon',
            'couponCode',
            'reason',
            'refund_reason',
            'product',
            'checkout',
            'subscription',
            'subscription_period',
            'installments',
            'paymentMethod',

            # Dates
            'createdAt',
            'due_date',
            'paidAt',
            'releaseDate',
            'refundedAt',
            'chargedbackAt',
            'canceledAt',

            'customer',
            'address',
            'baseAmount',
            'fees',
            'commissionedUsers',
            'commissions',
            'releaseDate',
            'utm_source',
            'utm_medium',
            'utm_campaign',
            'utm_term',
            'utm_content',
            'sck',
            'checkoutUrl',
        ]
        ordering = ['-createdAt']

class OrderPublicSerializer(serializers.ModelSerializer):
    baseAmount = serializers.FloatField()
    amount = serializers.FloatField()
    offerId = serializers.CharField(source='offer.id', read_only=True)
    offerName = serializers.CharField(source='offer.name', read_only=True)
    paymentMethod = serializers.CharField(source='paymentMethodType')

    class Meta:
        model = Order
        fields = [
            'id',
            'refId',
            'offerId',
            'offerName',
            'offer_type',
            'status',
            'baseAmount',
            'amount',
            'reason',
            'refund_reason',
            'paymentMethod',
            'installments',
            'createdAt',
        ]
        ordering = ['-createdAt']

class OrderWebhookSerializer(serializers.ModelSerializer):
    customer = CustomerPublicSerializer(read_only=True)
    baseAmount = serializers.FloatField()
    commissions = serializers.SerializerMethodField()
    amount = serializers.FloatField()
    fees = serializers.FloatField()
    affiliate = serializers.CharField(source='affiliate.user.email', default='')
    parent_order = serializers.CharField(source='parent_order.refId', default='')
    offer = OfferPublicSerializer(read_only=True)
    product = serializers.SerializerMethodField(read_only=True)
    paymentMethod = serializers.CharField(source='paymentMethodType')
    paymentMethodName = serializers.CharField()
    subscription = serializers.SerializerMethodField()

    class Meta:
        model = Order
        fields = [
            'id',
            'refId',
            'customer',
            'affiliate',
            'offer',
            'offer_type',
            'product',
            'checkout',
            'parent_order',
            'subscription',
            'subscription_period',
            'checkoutUrl',
            'status',
            'baseAmount',
            'discount',
            'amount',
            'commissions',
            'fees',
            'couponCode',
            'reason',
            'refund_reason',
            'paymentMethod',
            'paymentMethodName',
            'installments',

            # UTM
            'utm_source',
            'utm_medium',
            'utm_campaign',
            'utm_term',
            'utm_content',
            'sck',

            # Facebook Pixel
            'fbc',
            'fbp',

            # Dates
            'createdAt',
            'due_date',
            'paidAt',
            'refundedAt',
            'chargedbackAt',
            'canceledAt',
        ]
        ordering = ['-createdAt']

    def get_subscription(self, order: Order):
        if order.subscription is None:
            return None

        serializer = SubscriptionOwnerFlexSerializer(
            order.subscription,
            context={'user': self.context.get('user')}
        )
        return serializer.data

    def get_commissions(self, order: Order):
        commissions: list[dict] = []
        for split in order.splits.all():
            commissions.append({
                'user': split.user.email,
                'totalAmount': float(split.totalAmount) if split.totalAmount else 0,
                'percentage': float(split.percentage) if split.percentage else 0,
                'type': split.type,
            })
        return commissions

    def get_product(self, order):
        product = order.product
        return {
            'name': product.name,
            'id': product.id,
            'short_id': product.short_id,
            'supportEmail': get_product_supportEmail(product),
            'type': product.type,
            'invoiceDescription': product.invoiceDescription,
        }

    # Just add card, pix, boleto, picpay, googlepay, applepay if exists data in it
    def to_representation(self, order):
        response = super().to_representation(order)
        payment = order.payment_set.order_by('-createdAt').first()

        if payment.card:
            response['card'] = CardPublicSerializer(payment.card).data
        if payment.pix:
            response['pix'] = payment.pix
        if payment.boleto:
            response['boleto'] = payment.boleto
        if payment.picpay:
            response['picpay'] = payment.picpay
        if payment.googlepay:
            response['googlepay'] = payment.googlepay
        if payment.applepay:
            response['applepay'] = payment.applepay
        if payment.openFinanceNubank:
            response['openFinanceNubank'] = payment.openFinanceNubank

        return response

class OrderExportSerializer(OrderCustomerDataMixin, serializers.ModelSerializer):
    refId = serializers.CharField(label='ID da Venda')
    product = serializers.CharField(source='product.name', label='Produto')
    checkout = serializers.SerializerMethodField(label='Checkout')
    offerName = serializers.CharField(source='offer.name', label='Oferta')
    offerId = serializers.CharField(source='offer.id', label='Id da Oferta')
    baseAmount = serializers.CharField(label='Valor Base do Produto')
    discount = serializers.CharField(label='Desconto')
    amount = serializers.CharField(label='Valor Pago pelo Cliente')
    fees = serializers.CharField(label='Taxas')
    couponCode = serializers.CharField(label='Cupom de desconto')
    reason = serializers.CharField(label='Motivo de Recusa')
    refund_reason = serializers.CharField(label='Motivo do reembolso')
    refundedAt = serializers.DateTimeField(label='Data do Reembolso')
    chargedbackAt = serializers.DateTimeField(label='Data do Chargeback')
    commission = serializers.SerializerMethodField(label='Comissão')
    customer = serializers.SerializerMethodField(default=CustomerOrderSerializer().data)
    paymentMethod = serializers.CharField(source='paymentMethodName', label='Método de Pagamento')
    installments = serializers.IntegerField(label='Parcelas')
    type = serializers.SerializerMethodField(label='Tipo do Produto')
    affiliate = serializers.CharField(source='affiliate.user.email', default='', label='Afiliado')
    createdAt = serializers.DateTimeField(label='Data da Venda')
    paidAt = serializers.DateTimeField(label='Data de Pagamento')
    releaseDate = serializers.DateTimeField(label='Data estimada de Liberação')
    status = serializers.CharField(label='Status da Venda')
    checkoutUrl = serializers.CharField(label='URL de Checkout')
    couponPercentage = serializers.SerializerMethodField(label='Porcentagem de desconto')
    offer_type = serializers.CharField(label='Tipo da Venda')
    parent_order = serializers.CharField(source='parent_order.refId', label='Venda Pai', default='')
    subscription = serializers.CharField(source='subscription.id', label='Assinatura', default='')
    subscription_period = serializers.IntegerField(label='Período da Assinatura')
    due_date = serializers.DateTimeField(label='Data de Agendamento do Pagamento')
    canceledAt = serializers.DateTimeField(label='Data de Cancelamento do Pagamento')

    class Meta:
        model = Order
        fields = (
            'refId',
            'status',
            'customer',
            'checkoutUrl',
            'product',
            'checkout',
            'parent_order',
            'subscription',
            'subscription_period',
            'offer_type',
            'offerId',
            'offerName',
            'offer_type',
            'baseAmount',
            'discount',
            'amount',
            'fees',
            'reason',
            'couponCode',
            'couponPercentage',
            'refund_reason',
            'commission',
            'paymentMethod',
            'installments',
            'type',
            'affiliate',
            'checkoutUrl',

            # UTM
            'utm_source',
            'utm_medium',
            'utm_campaign',
            'utm_term',
            'utm_content',
            'sck',

            # Facebook Pixel
            'fbc',
            'fbp',

            # Dates
            'createdAt',
            'due_date',
            'paidAt',
            'releaseDate',
            'refundedAt',
            'chargedbackAt',
            'canceledAt',
        )
        ordering = ['-createdAt']

    def get_couponPercentage(self, order):
        return order.coupon.discount if order.coupon else ''

    def get_commission(self, order):
        user = self.context['request'].user
        return order.splits.filter(user=user).aggregate(commission=Sum('totalAmount')).get('commission', 0)

    def get_type(self, order):
        if order.type == 'subscription':
            return 'Assinatura'
        elif order.type == 'unique':
            return 'Pagamento Único'

    def get_checkout(self, order):
        return order.checkout.name if order.checkout else ''

class SubscriptionExportSerializer(serializers.ModelSerializer):
    id = serializers.CharField(label='ID')
    status = serializers.CharField(label='Status')
    current_period = serializers.CharField(label='Período Atual da Assinatura')
    amount = serializers.CharField(label='Valor')
    paid_payments_quantity = serializers.CharField(label='Quantidade de pagamentos realizados')
    retention = serializers.CharField(source='retention.days', label='Dias de retenção')
    paymentMethod = serializers.CharField(label='Método de Pagamento')
    customer = serializers.SerializerMethodField(default=CustomerOrderSerializer().data)
    productName = serializers.CharField(source='product.name', label='Produto')
    offerId = serializers.CharField(source='offer.id', label='Id da Oferta')
    offerName = serializers.CharField(source='offer.name', label='Oferta')
    parent_order = serializers.CharField(source='parent_order.refId', label='Venda Pai', default='')
    next_payment_date = serializers.DateTimeField(label='Próximo pagamento')
    createdAt = serializers.DateTimeField(label='Data de Criação')
    canceledAt = serializers.DateTimeField(label='Data de Cancelamento')
    recurrence_period = serializers.CharField(label='Período de Recorrência (dias)')
    quantity_recurrences = serializers.CharField(label='Quantidade de Recorrências')
    trial_days = serializers.CharField(label='Dias de Teste')
    max_retries = serializers.CharField(label='Tentativas Máximas')

    class Meta:
        model = Subscription
        fields = (
            'id',
            'status',
            'current_period',
            'amount',
            'retry_interval',
            'paid_payments_quantity',
            'retention',
            'paymentMethod',
            'customer',
            'productName',
            'offerId',
            'offerName',
            'parent_order',
            'next_payment_date',
            'createdAt',
            'canceledAt',
            'recurrence_period',
            'quantity_recurrences',
            'trial_days',
            'max_retries',
        )

    def get_customer(self, subscription):
        assert self.context.get('request'), 'Request is required in context for this serializer.'

        user = self.context.get('request').user  # type:ignore

        if user_has_access_to_order_customer_data(order=subscription.parent_order, user=user):
            return CustomerOrderSerializer(subscription.customer).data

        return {'name': subscription.customer.name}

class RefundOrderPublicSerializer(serializers.ModelSerializer):
    type = serializers.CharField(source='product.type')
    supportEmail = serializers.SerializerMethodField()
    supportWhatsapp = serializers.CharField(source='product.supportWhatsapp')
    producerName = serializers.SerializerMethodField()
    companyName = serializers.CharField(source='product.user.company.companyName')
    productName = serializers.CharField(source='product.name')
    productUrlImage = serializers.CharField(source='product.image')
    refundDeadline = serializers.SerializerMethodField()
    isDeadlinePast = serializers.SerializerMethodField()
    couponPercentage = serializers.SerializerMethodField()
    paymentMethod = serializers.CharField(source='paymentMethodType')

    class Meta:
        model = Order
        fields = [
            'type',
            'supportEmail',
            'supportWhatsapp',
            'producerName',
            'companyName',
            'productName',
            'productUrlImage',
            'refundDeadline',
            'isDeadlinePast',
            'id',
            'refId',
            'status',
            'baseAmount',
            'discount',
            'amount',
            'couponPercentage',
            'paymentMethod',
            'installments',
            'createdAt',
            'refundedAt',
            'chargedbackAt',
        ]

    def get_couponPercentage(self, order):
        return order.coupon.discount if order.coupon else 0

    def get_supportEmail(self, order):
        return order.product.supportEmail or order.product.user.email

    def get_producerName(self, order):
        producerName = order.product.producerName
        username = f'{order.product.user.first_name} {order.product.user.last_name}'.strip()
        return producerName or username or order.product.user.email

    def get_refundDeadline(self, order):
        guarantee_days = order.product.guarantee
        return str((order.paidAt or order.createdAt) + timezone.timedelta(days=guarantee_days))

    def get_isDeadlinePast(self, order):
        guarantee_days = order.product.guarantee
        return (order.paidAt or order.createdAt) + timezone.timedelta(days=guarantee_days) < timezone.now()

class PaymentAlowAnySerializer(serializers.ModelSerializer):
    class Meta:
        model = Payment
        fields = [
            'id',
            'status',
        ]

    def to_representation(self, payment):
        response = super().to_representation(payment)

        if payment.status == 'paid':
            user = User.objects.filter(email__iexact=payment.customer_email).first()

            if user and not user.has_usable_password():
                refresh = RefreshToken.for_user(user)
                response['accessToken'] = str(refresh.access_token)
                log_user_login_history(user=user, location=UserAccessLocations.FIRST_PURCHASE.id)

        return response

class PaymentPublicSerializer(serializers.ModelSerializer):
    orders = OrderPublicSerializer(many=True)
    paymentMethod = serializers.CharField(source='paymentMethodType')

    class Meta:
        model = Payment
        fields = [
            'id',
            'status',
            'amount',
            'paymentMethod',
            'reason',
            'installments',
            'createdAt',
            'threeDs',
            'nextStep',
            'threeDsFinished',
            'orders',
        ]

    # Just add card, pix, boleto, picpay if exists data in it
    def to_representation(self, instance):
        response = super().to_representation(instance)
        if instance.card:
            response['card'] = instance.card
        if instance.pix:
            response['pix'] = instance.pix
        if instance.boleto:
            response['boleto'] = instance.boleto
        if instance.picpay:
            response['picpay'] = instance.picpay
        if instance.googlepay:
            response['googlepay'] = instance.googlepay
        if instance.applepay:
            response['applepay'] = instance.applepay
        if instance.openFinanceNubank:
            response['openFinanceNubank'] = instance.openFinanceNubank
        return response

class PaymentAdminFlexSerializer(CustomFlexFieldsSerializer):
    class Meta:
        model = Payment
        fields = '__all__'
        expandable_fields = {
            'orders': OrderAdminFlexSerializer,
            'card': CardAdminFlexSerializer,
            'subscription': 'gateway.serializers.SubscriptionAdminFlexSerializer',
        }

class PaymentOwnerFlexSerializer(CustomFlexFieldsSerializer):
    class Meta:
        model = Payment
        fields = [
            # Basic info
            'id',
            'status',

            # Relations
            'orders',
            'paymentMethod',
            'subscription',

            # Financial info
            'amount',
            'installments',
            'refunded',
            'reason',

            # Payment info
            'pix',
            'card',
            'boleto',
            'picpay',
            'googlepay',
            'applepay',

            # Dates
            'createdAt',
            'updatedAt',
            'paidAt',
        ]
        expandable_fields = {
            'orders': OrderOwnerFlexSerializer,
            'card': CardPublicFlexSerializer,
            'subscription': 'gateway.serializers.SubscriptionOwnerFlexSerializer',
        }

class SubscriptionOwnerFlexSerializer(CustomFlexFieldsSerializer):
    customer = serializers.SerializerMethodField()

    class Meta:
        model = Subscription
        fields = [
            # Basic info
            'id',
            'status',
            'current_period',

            # Financial info
            'recurrence_period',
            'quantity_recurrences',
            'trial_days',
            'max_retries',
            'amount',
            'retry_interval',
            'paid_payments_quantity',
            'retention',

            # Relations
            'parent_order',
            'paymentMethod',
            'customer',
            'product',
            'offer',
            'orders',

            # Dates
            'next_payment_date',
            'createdAt',
            'updatedAt',
            'canceledAt',
        ]
        expandable_fields = {
            'parent_order': (OrderOwnerFlexSerializer, {'exclude_fields': ['customer', 'parent_order']}),
            'product': ProductOwnerFlexSerializer,
            'offer': OfferOwnerFlexSerializer,
            'orders': (OrderOwnerFlexSerializer, {'exclude_fields': ['customer', 'parent_order'], 'many': True}),
        }

    def get_customer(self, subscription):
        request = self.context.get('request')
        user = self.context.get('user')

        assert request or user, 'Request or User owner is required in context for this serializer.'

        user = request.user if request else user

        if user_has_access_to_order_customer_data(order=subscription.parent_order, user=user):  # type:ignore
            return CustomerPublicFlexSerializer(subscription.customer).data
        return {'name': subscription.customer.name}

class SubscriptionAdminFlexSerializer(CustomFlexFieldsSerializer):
    class Meta:
        model = Subscription
        fields = '__all__'
        expandable_fields = {
            'parent_order': OrderAdminFlexSerializer,
            'customer': CustomerAdminFlexSerializer,
            'product': ProductAdminFlexSerializer,
            'offer': OfferAdminFlexSerializer,
            'payments': (PaymentAdminFlexSerializer, {'many': True}),
        }
