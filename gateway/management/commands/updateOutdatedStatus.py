from django.core.management.base import BaseCommand


from gateway.models import Order, Payment
from gateway.utils import STATUS_MAPPING

class Command(BaseCommand):
    help = 'Update outdated orders and payments status.'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Updating outdated orders and payments...'))

        for split_status, status in STATUS_MAPPING.items():
            for order in Order.objects.filter(status=split_status):
                order.status = status
                order.save()
            for payment in Payment.objects.filter(status=split_status):
                payment.status = status
                payment.save()

        self.stdout.write(self.style.SUCCESS('Update successful!'))
