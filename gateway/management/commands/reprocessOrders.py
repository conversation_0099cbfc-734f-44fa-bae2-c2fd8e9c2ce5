import argparse
import sys

from django.core.management.base import BaseCommand
from django.utils import timezone
from rest_framework import status

from gateway.models import Order
from gateway.sdk.splitpay import SplitPay
from gateway.strategies.card_strategy import CardStrategy
from gateway.strategies.pix_strategy import PixStrategy
from gateway.utils import STATUS_MAPPING


class Command(BaseCommand):
    help = 'Get order\'s payment in Split and reprocess it on cakto to calculate commissions and other fields.'
    split = SplitPay()

    def add_arguments(self, parser):
        parser.add_argument('--startDate', type=str, help='Start Date to reprocess orders (format: YYYY-MM-DD or ISO format). Default is 3 days ago.')
        parser.add_argument('--endDate', type=str, help='End Date to reprocess orders (format: YYYY-MM-DD or ISO format)')
        parser.add_argument('--refIds', type=str, help='Comma separated Order refIds to reprocess. If not provided, all orders in the period that haven\'t externalId will be reprocessed.')

    def run_from_argv(self, argv: list[str]) -> None:
        parser = argparse.ArgumentParser(prog=argv[0])
        self.add_arguments(parser)  # type:ignore
        options = parser.parse_args(argv[2:])
        return self.handle(**vars(options))

    def handle(self, *args, startDate=None, endDate=None, refIds: str | None = None, **options):
        self.stdout.write(self.style.SUCCESS('Updating Orders...'))

        error_ids: list = []
        error_messages: list = []

        startDate = timezone.datetime.fromisoformat(startDate) if startDate else timezone.now() - timezone.timedelta(days=3)
        endDate = timezone.datetime.fromisoformat(endDate) if endDate else timezone.now()

        orderRefIds_list = refIds.split(',') if refIds else None

        self.process_card_orders(error_ids, error_messages, startDate, endDate, orderRefIds_list)

        self.process_pix_boleto_picpay_orders(error_ids, error_messages, startDate, endDate, orderRefIds_list)

        if error_ids:
            self.stdout.write(self.style.ERROR('\nErrors:'))
            [self.stdout.write(self.style.ERROR(msg)) for msg in error_messages]

            self.stdout.write(self.style.ERROR('\nOrders with errors:'))
            self.stdout.write(self.style.WARNING(','.join(id for id in error_ids)))
        else:
            self.stdout.write(self.style.SUCCESS('\nOrders updated successfully.'))

    def process_card_orders(self, error_ids, error_messages, startDate, endDate, orderRefIds_list):
        card_orders = Order.objects.filter(paymentMethod__type='credit_card')
        card_orders = self.filter_qs(card_orders, startDate, endDate, orderRefIds_list)
        for order in card_orders:
            try:
                data = self.get_order_data(order, error_messages)
                if not data:
                    continue

                CardStrategy().set_orders_split_data(data, [order])
                CardStrategy().process_payment(paymentData=data, orders=[order])
            except Exception:
                error_ids.append(order.refId)
                self.stdout.write(self.style.ERROR('.'), ending='')
                sys.stdout.flush()
                continue

            self.stdout.write(self.style.SUCCESS('.'), ending='')
            sys.stdout.flush()

    def process_pix_boleto_picpay_orders(self, error_ids, error_messages, startDate, endDate, orderRefIds_list):
        pix_boleto_picpay_orders = Order.objects.filter(
            offer_type='main',
            paymentMethod__in=['pix', 'boleto', 'picpay']
        )
        pix_boleto_picpay_orders = self.filter_qs(pix_boleto_picpay_orders, startDate, endDate, orderRefIds_list)

        for pix_boleto_order in pix_boleto_picpay_orders:
            try:
                data = self.get_order_data(pix_boleto_order, error_messages)
                if not data:
                    continue

                refIds = data.get('exId', '').split(',')
                orders = list(Order.objects.filter(refId__in=refIds))
                orders.sort(key=lambda x: not x.offer_type == 'main')

                PixStrategy().set_orders_split_data(data, orders)
                PixStrategy().process_payment(paymentData=data, orders=orders)
            except Exception:
                error_ids.append(pix_boleto_order.refId)
                self.stdout.write(self.style.ERROR('.'), ending='')
                sys.stdout.flush()
                continue

            self.stdout.write(self.style.SUCCESS('.'), ending='')
            sys.stdout.flush()

    def filter_qs(self, qs, startDate, endDate, orderRefIds_list):
        if orderRefIds_list:
            qs = qs.filter(refId__in=orderRefIds_list)
        else:
            qs = qs.filter(externalId__isnull=True)

        if startDate:
            qs = qs.filter(createdAt__gte=startDate)
        if endDate:
            qs = qs.filter(createdAt__lte=endDate)

        return qs

    def get_order_data(self, order, errors):
        response = self.split.getPaymentList(order.refId)
        if not status.is_success(response.status_code):
            errors.append(f'>> Error getting payment data for order {response.status_code}, {response.content}')
            raise Exception

        data = response.json().get('results', [])
        if not data:
            errors.append(f'>> No payment data found for order {order.refId}')
            raise Exception

        data = data[0]
        data['status'] = STATUS_MAPPING.get(data['status'], data['status'])
        return data
