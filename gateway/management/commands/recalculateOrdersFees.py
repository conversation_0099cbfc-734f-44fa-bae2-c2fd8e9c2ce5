from decimal import ROUND_FLOOR, Decimal

from django.core.management.base import BaseCommand
from rest_framework import status

from gateway.models import Order, Payment
from gateway.sdk.splitpay import SplitPay


def get_recalculated_commissions(liquidAmount: Decimal, order: Order):
    commissionsOrdened = sorted(order.commissions, key=lambda x: x.get('type'))
    commissions = []
    totalPercentage = float(100)
    totalValue = float(liquidAmount)

    for commission in commissionsOrdened:
        commissionPercentage = Decimal(commission.get('commissionPercentage', 0))
        if commissionPercentage < 1:
            commissionPercentage = Decimal(commissionPercentage * 100)

        if commission.get('type') == 'producer':
            commissions.append({
                'userId': commission.get('userId'),
                'type': commission.get('type'),
                'commissionPercentage': float(totalPercentage),
                'commissionValue': totalValue,
            })
        else:
            commissionValue = float(round(liquidAmount * (commissionPercentage / 100)
                                    .quantize(Decimal('1.00'), ROUND_FLOOR), 2))
            commissions.append({
                'userId': commission.get('userId'),
                'type': commission.get('type'),
                'commissionPercentage': float(commissionPercentage),
                'commissionValue': commissionValue,
            })
            totalValue -= commissionValue

        totalPercentage -= float(commissionPercentage)

    return commissions

class Command(BaseCommand):
    help = 'Update outdated orders and payments status.'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Recalculating orders fees...'))
        split = SplitPay()
        errors = []

        payments = Payment.objects.filter(
            createdAt__gte='2024-03-12',
            externalId__regex=r'^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}'
        ).order_by('-createdAt')

        for payment in payments:
            if payment.externalId:
                self.stdout.write(self.style.SUCCESS(f'Processing payment {payment.pk}'))
                res = split.getPayment(payment.externalId)

                if status.is_success(res.status_code):
                    data = res.json()
                    fee = Decimal(data.get('fee'))

                    for order in payment.orders.all():
                        orderTotalPercentage = order.baseAmount / Decimal(data.get('amount'))  # will change to baseAmount

                        orderFee = fee * orderTotalPercentage
                        orderLiquidAmount = order.amount - orderFee

                        order.commissions = get_recalculated_commissions(
                            liquidAmount=orderLiquidAmount,
                            order=order,
                        )
                        order.fees = orderFee
                        order.save()
                else:
                    errors.append((res.text, payment.pk))
                    self.stdout.write(self.style.ERROR(f'pk: {payment.pk}, error: {res.text}'))

        if not errors:
            self.stdout.write(self.style.SUCCESS('Update successful!'))
        else:
            self.stdout.write(self.style.WARNING('Update finished with errors:'))
        self.stdout.write(self.style.SUCCESS(f'total: {payments.count()}, errors: {len(errors)}'))
