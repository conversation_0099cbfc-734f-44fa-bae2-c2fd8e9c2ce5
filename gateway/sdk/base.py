import requests


class BaseAPI:
    URL = ''

    def __init__(self) -> None:
        self.api = requests.Session()

    def post(self, uri, **kwargs):
        if not self.URL:
            raise NotImplementedError('URL not defined')
        return self.api.post(self.URL + uri, **kwargs)

    def get(self, uri, **kwargs):
        if not self.URL:
            raise NotImplementedError('URL not defined')
        return self.api.get(self.URL + uri, **kwargs)
