from rest_framework import status
import os
from urllib.parse import urlencode

import requests
from django.conf import settings

from gateway.sdk.base import BaseAPI


class SplitPay(BaseAPI):
    URL = settings.GATEWAY_URL
    token = os.getenv('SPLIT_TOKEN')

    def __init__(self) -> None:
        super().__init__()

        self.api.headers.update({'Authorization': f'Token {self.token}'})

    # # Payment
    def createTransaction(self, **kwargs) -> requests.Response:
        if 'antifraud_profiling_attempt_reference' in kwargs and not kwargs['antifraud_profiling_attempt_reference']:
            del kwargs['antifraud_profiling_attempt_reference']
        return self.api.post(self.URL + 'api/checkout/', json=kwargs)

    def createSubscription(self, **kwargs) -> requests.Response:
        required_fields = ['recurrence_period', 'quantity_recurrences', 'trial_days', 'max_retries', 'retry_interval']
        for field in required_fields:
            assert field in kwargs, f'Missing one of the required fields: "{field}"'

        return self.createTransaction(**kwargs)

    def getSubscription(self, subscription_id) -> requests.Response:
        response = self.api.get(self.URL + f'api/subscriptions/{subscription_id}/')

        if not status.is_success(response.status_code):
            raise Exception(f'Error retrieving subscription from split, error {response.status_code}, response: {response.content.decode()}')

        return response

    def subscriptionUpdate(self, subscription_id, **kwargs) -> requests.Response:
        return self.api.patch(self.URL + f'api/subscriptions/{subscription_id}/update/', json=kwargs)

    def getPayment(self, id) -> requests.Response:
        return self.api.get(self.URL + f'api/payment/{id}/')

    def getPaymentList(self, search) -> requests.Response:
        return self.api.get(self.URL + f'api/payments/?search={search}')

    def refund(self, id, **kwargs) -> requests.Response:
        return self.api.post(self.URL + f'api/payment/{id}/refund/', json=kwargs)

    # Financial
    def createWithdraw(self, account_id, **kwargs) -> requests.Response:
        return self.api.post(self.URL + f'api/seller/{account_id}/withdraw/', json=kwargs)

    def getWithdraws(self, account_id, **params) -> requests.Response:
        params['account_id'] = account_id
        query_string = urlencode(params)
        return self.api.get(self.URL + f'api/seller/withdraws/?{query_string}')

    def getWithdrawalFee(self, account_id) -> requests.Response:
        return self.api.get(self.URL + f'api/seller/fee/withdraw/?account_id={account_id}')

    def getInstallmentsFees(self, account_id) -> requests.Response:
        return self.api.get(self.URL + f'api/checkout/fee/?account_id={account_id}')

    def getUserFees(self, account_id) -> requests.Response:
        return self.api.get(self.URL + f'api/seller/fee/?account_id={account_id}')

    def getBalance(self, account_id) -> requests.Response:
        return self.api.get(self.URL + f'api/seller/balance/?account_id={account_id}')

    # Account
    def createAccount(self, **kwargs) -> requests.Response:
        return self.api.post(self.URL + 'api/seller/add/', json=kwargs)

    def onboardAccount(self, account_id, **kwargs) -> requests.Response:
        return self.api.patch(self.URL + f'api/seller/{account_id}/', json=kwargs)

    def getAccount(self, account_id, **kwargs) -> requests.Response:
        return self.api.get(self.URL + f'api/seller/{account_id}/')

    def sendDocuments(self, account_id, files, **kwargs) -> requests.Response:
        return self.api.post(self.URL + f'api/seller/{account_id}/documents/', files=files, json=kwargs)

    def reset_verification(self, account_id) -> requests.Response:
        return self.api.post(self.URL + f'api/seller/{account_id}/reset_verification/')

    # Reports
    def getPendingBalance(self, account_id, query_params) -> requests.Response:
        params = query_params.copy()
        params['user'] = account_id
        return self.api.get(self.URL + f'api/pending_balance/by_date/?{params.urlencode()}')

    def getPendingBalanceXLSX(self, account_id, query_params) -> requests.Response:
        params = query_params.copy()
        params['user'] = account_id
        return self.api.get(self.URL + f'api/pending_balance/by_date/export/xlsx/?{params.urlencode()}')

    def getPendingBalanceCSV(self, account_id, query_params) -> requests.Response:
        params = query_params.copy()
        params['user'] = account_id
        return self.api.get(self.URL + f'api/pending_balance/by_date/export/csv/?{params.urlencode()}')

    def getPendingBalanceProduct(self, account_id, query_params) -> requests.Response:
        params = query_params.copy()
        params['user'] = account_id
        return self.api.get(self.URL + f'api/pending_balance/product/?{params.urlencode()}')

    # Plan Fee
    def list_plan_fees(self, query_params: dict | None = None) -> requests.Response:
        # Monta os parâmetros de URL
        url_params = ''
        if query_params:
            url_params = '?' + urlencode(query_params)

        return self.api.get(self.URL + f'api/plan_fee/{url_params}')

    def update_user_plan_fee(self, external_id: int, plan_fee_id: int) -> requests.Response:
        # Verifica se externalId foi passado
        if not external_id:
            resp = requests.Response()
            resp.status_code = 400
            resp._content = b'{"detail": "external_id nao fornecido."}'
            return resp

        url_patch = self.URL + f"api/seller/{external_id}/"
        return self.api.patch(url_patch, json={"plan_fee": plan_fee_id})

    def get_plan_fee_by_id(self, plan_fee_id: int) -> requests.Response:
        url_get = f"{self.URL}api/plan_fee/{plan_fee_id}/"
        return self.api.get(url_get)

    def get_cielo_three_ds_token(self) -> requests.Response:
        return self.api.get(self.URL + 'api/cielo/3ds/token/', timeout=15)
