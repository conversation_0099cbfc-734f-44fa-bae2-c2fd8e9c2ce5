from rest_framework.exceptions import ValidationError

from gateway.strategies.applepay_strategy import ApplePayStrategy
from gateway.strategies.base import PaymentStrategy
from gateway.strategies.boleto_strategy import BoletoStrategy
from gateway.strategies.card_strategy import CardStrategy
from gateway.strategies.googlepay_strategy import GooglePayStrategy
from gateway.strategies.openfinance_nubank_strategy import OpenFinanceNubankStrategy
from gateway.strategies.picpay_strategy import PicPayStrategy
from gateway.strategies.pix_strategy import PixStrategy
from gateway.strategies.threeds_strategy import ThreeDSPayStrategy


class PaymentStrategyFactory:
    strategies: dict = {
        'pix': PixStrategy,
        'boleto': BoletoStrategy,
        'openfinance_nubank': OpenFinanceNubankStrategy,
        'picpay': PicPayStrategy,
        'credit_card': CardStrategy,
        'threeDs': ThreeDSPayStrategy,
        'googlepay': GooglePayStrategy,
        'applepay': ApplePayStrategy,
    }

    @staticmethod
    def get_strategy(payment_method: str) -> PaymentStrategy:
        strategy = PaymentStrategyFactory.strategies.get(payment_method)
        if not strategy:
            raise ValidationError(f'Invalid payment method: {payment_method}')
        return strategy()
