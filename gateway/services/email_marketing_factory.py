from gateway.strategies.mailchimp_strategy import MailchimpStrategy
from gateway.strategies.base_email_marketing import BaseEmailMarketingStrategy


class EmailMarketingStrategyFactory:
    strategies = {
        'MailChimp': MailchimpStrategy,
    }

    @staticmethod
    def get_strategy(provider: str) -> BaseEmailMarketingStrategy:
        strategy = EmailMarketingStrategyFactory.strategies.get(provider)
        if not strategy:
            raise NotImplementedError(f'Email Marketing Provider {provider} not implemented')
        return strategy()
