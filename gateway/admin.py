from django.contrib import admin
from django.db.models import Sum

from gateway.models import Order, Payment, Split, Subscription

admin.site.site_header = 'Painel de Controle'
admin.site.site_title = 'Painel de Controle'
admin.site.index_title = 'Painel de Controle'

class PaymentInline(admin.TabularInline):
    model = Payment
    extra = 0
    fields = ['id', 'externalId', 'status', 'amount', 'paymentMethod', 'installments']
    readonly_fields = [f.name for f in model._meta.get_fields()]
    can_delete = False
    show_change_link = True

    def has_add_permission(self, request, obj=None):
        return False

class OrderInline(admin.TabularInline):
    model = Order
    extra = 0
    fields = ['id', 'refId', 'externalId', 'status', 'amount', 'paymentMethod', 'createdAt', 'paidAt']
    readonly_fields = [f.name for f in model._meta.concrete_fields]
    can_delete = False
    show_change_link = True

    def has_add_permission(self, request, obj=None):
        return False

class OrdersInlineBase(admin.TabularInline):
    extra = 0
    can_delete = False

    def __init__(self, *args, **kwargs):
        if not self.model:
            raise ValueError(f"The 'model' attribute must be set for {self.__class__.__name__}.")
        super().__init__(*args, **kwargs)

    def get_readonly_fields(self, request, obj=...):
        return [f.name for f in self.model._meta.concrete_fields] + ['status', 'refId', 'amount', 'baseAmount']  # type:ignore

    def has_add_permission(self, request, obj=None):
        return False

    def externalId(self, payment_order):
        return payment_order.order.externalId
    externalId.short_description = 'ExternalId'  # type:ignore

    def status(self, payment_order):
        return payment_order.order.status
    status.short_description = 'Status'  # type:ignore

    def refId(self, payment_order):
        return payment_order.order.refId
    refId.short_description = 'RefId'  # type:ignore

    def amount(self, payment_order):
        return payment_order.order.amount
    amount.short_description = 'Amount'  # type:ignore

    def baseAmount(self, payment_order):
        return payment_order.order.baseAmount
    baseAmount.short_description = 'Base Amount'  # type:ignore

class PaymentOrdersInline(OrdersInlineBase):
    model = Payment.orders.through

class PaymentSubscriptionsInline(admin.TabularInline):
    model = Payment.subscriptions.through
    extra = 0
    readonly_fields = [f.name for f in model._meta.concrete_fields] + ['externalId', 'status', 'amount']
    can_delete = False

    def has_add_permission(self, request, obj=None):
        return False

    def externalId(self, payment_subscription):
        return payment_subscription.subscription.externalId
    externalId.short_description = 'ExternalId'  # type:ignore

    def status(self, payment_subscription):
        return payment_subscription.subscription.status
    status.short_description = 'Status'  # type:ignore

    def amount(self, payment_subscription):
        return payment_subscription.subscription.amount
    amount.short_description = 'Amount'  # type:ignore

class SubscriptionPaymentsInline(admin.TabularInline):
    model = Subscription.payments.through
    extra = 0
    readonly_fields = [f.name for f in model._meta.concrete_fields] + ['externalId', 'status', 'amount', 'createdAt', 'paidAt']
    can_delete = False

    def has_add_permission(self, request, obj=None):
        return False

    def externalId(self, payment_subscription):
        return payment_subscription.payment.externalId
    externalId.short_description = 'ExternalId'  # type:ignore

    def status(self, payment_subscription):
        return payment_subscription.payment.status
    status.short_description = 'Status'  # type:ignore

    def amount(self, payment_subscription):
        return payment_subscription.payment.amount
    amount.short_description = 'Amount'  # type:ignore

    def createdAt(self, payment_subscription):
        return payment_subscription.payment.createdAt.strftime('%d/%m/%Y %H:%M:%S')
    createdAt.short_description = 'Criado em'  # type:ignore

    def paidAt(self, payment_subscription):
        return payment_subscription.payment.paidAt.strftime('%d/%m/%Y %H:%M:%S')
    paidAt.short_description = 'Pago em'  # type:ignore

class SplitInline(admin.TabularInline):
    model = Split
    extra = 0
    readonly_fields = [f.name for f in model._meta.concrete_fields]
    can_delete = False

    def has_add_permission(self, request, obj=None):
        return False

class CommissionedUsersOrderInline(admin.TabularInline):
    model = Order.commissionedUsers.through
    extra = 0
    readonly_fields = [f.name for f in model._meta.concrete_fields]
    can_delete = False

    def has_add_permission(self, request, obj=None):
        return False

    def commissionValue(self, commissioned_user_order):
        total = Split.objects.filter(order=commissioned_user_order.order, user=commissioned_user_order.user).aggregate(total=Sum('totalAmount'))['total']
        return round(float(total), 4)
    commissionValue.short_description = 'Valor Total'  # type:ignore

    def commissionTypes(self, commissioned_user_order):
        types = Split.objects.filter(order=commissioned_user_order.order, user=commissioned_user_order.user).values_list('type', flat=True)
        return ', '.join(types)
    commissionTypes.short_description = 'Tipos de comissões'  # type:ignore

class PaymentSetOrderInline(admin.TabularInline):
    model = Order.payment_set.through  # type:ignore
    extra = 0
    readonly_fields = [f.name for f in model._meta.concrete_fields] + ['status', 'amount']
    can_delete = False

    def has_add_permission(self, request, obj=None):
        return False

    def externalId(self, payment_set_order):
        return payment_set_order.payment.externalId
    externalId.short_description = 'ExternalId'  # type:ignore

    def status(self, payment_set_order):
        return payment_set_order.payment.status
    status.short_description = 'Status'  # type:ignore

    def amount(self, payment_set_order):
        return payment_set_order.payment.amount
    amount.short_description = 'Valor'  # type:ignore

@admin.register(Order)
class OrderAdmin(admin.ModelAdmin):
    model = Order
    list_display = ('refId', 'status', 'amount', 'customer', 'product', 'paymentMethod', 'createdAt', 'paidAt')
    search_fields = (
        'id', 'refId', 'externalId', 'product__pk', 'product__id',
        'product__user__email', 'offer__id', 'customer__email', 'customer__docNumber'
    )
    list_filter = ('status', 'type', 'paymentMethod', 'createdAt', 'paidAt', 'releaseDate', 'updatedAt')
    exclude = ['commissionedUsers']
    readonly_fields = [f.name for f in model._meta.concrete_fields if f.name]
    inlines = [CommissionedUsersOrderInline, PaymentSetOrderInline, SplitInline]

    def payment_set(self, order):
        return order.payment_set.all()

@admin.register(Payment)
class PaymentAdmin(admin.ModelAdmin):
    model = Payment
    list_display = (
        'id', 'status', 'amount', 'paymentMethod', 'installments',
        'paidAt', 'createdAt', 'updatedAt'
    )
    search_fields = (
        'id', 'orders__id', 'orders__refId', 'orders__product__name',
        'orders__product__user__email', 'orders__product__id', 'externalId'
    )
    list_filter = ('status', 'paymentMethod', 'paidAt', 'createdAt', 'updatedAt')
    inlines = [PaymentOrdersInline, PaymentSubscriptionsInline]
    exclude = ['orders', 'subscriptions']
    readonly_fields = [f.name for f in model._meta.concrete_fields]

@admin.register(Subscription)
class SubscriptionAdmin(admin.ModelAdmin):
    model = Subscription
    list_display = ['id', 'externalId', 'status', 'amount', 'paymentMethod', 'customer', 'product', 'createdAt', ]
    search_fields = [
        'id', 'externalId', 'customer__email', 'customer__docNumber',
        'product__id', 'product__short_id', 'product__name', 'product__user__email', 'offer__id',
        'parent_order__id', 'parent_order__refId', 'parent_order__externalId',
    ]
    list_filter = ['status', 'paymentMethod', 'next_payment_date', 'createdAt', 'updatedAt', 'canceledAt']
    readonly_fields = [f.name for f in model._meta.concrete_fields if f.name not in ['status', 'recurrence_period', 'quantity_recurrences', 'trial_days', 'max_retries', 'amount']]
    inlines = [OrderInline, SubscriptionPaymentsInline]
