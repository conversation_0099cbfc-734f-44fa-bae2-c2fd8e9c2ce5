import os
import random
import traceback

from django.core.cache import cache
from django.db import transaction
from django.db.models import Avg, Count, F, OuterRef, Q, Subquery, Sum
from django.forms.models import model_to_dict
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django_filters.rest_framework import DjangoFilterBackend
from drf_excel.renderers import XLSXRenderer
from rest_framework import filters, generics, status, viewsets
from rest_framework.decorators import action
from rest_framework.permissions import AllowAny, IsAdminUser, IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.viewsets import ModelViewSet
from rest_framework_csv.renderers import <PERSON><PERSON>enderer
from rest_framework_simplejwt.tokens import RefreshToken

from apps.services.event_manager import dispatch_app_events
from cakto.permissions import IsOwner
from customer.models import Address, Customer
from customer.utils import AddressPaymentProcessor, CustomerPaymentProcessor
from email_service.mail import (dispatch_approved_mail, send_company_approval_email, send_company_resubmit_email,
                                send_order_request_refund_customer_email, send_order_request_refund_producer_email,
                                send_otp_token_email)
from financial.enums import CompanyStatus, CompanyType
from financial.utils import (add_external_accesses, handle_subscription_canceled,
                             handle_subscription_renewal_refused_payment)
from gateway.filters import OrderFilter, SubscriptionFilter
from gateway.models import (CoproductionType, OfferType, Order, Payment, PaymentMethodType, PaymentStatus, Split,
                            Subscription)
from gateway.permissions import CanRefundOrder, CanViewOrder
from gateway.sdk.splitpay import SplitPay
from gateway.serializers import (CheckoutProcessPaymentSerializer, OrderAdminListSerializer,
                                 OrderAdminRetrieveSerializer, OrderExportSerializer, OrderSerializer,
                                 PaymentAlowAnySerializer, PaymentPublicSerializer, RefundOrderPublicSerializer,
                                 SubscriptionExportSerializer, SubscriptionOwnerFlexSerializer)
from gateway.services.payment_factory import PaymentStrategyFactory
from gateway.strategies.base import PaymentStrategy
from gateway.utils import (STATUS_MAPPING, card_anti_fraud, get_client_ip, get_order_by_offer_and_fingerprint,
                           get_orders_by_paymentMethod_and_status, validate_applepay_merchant)
from main import unpack_offer_id_and_checkout_id
from members.sdk.members_v2 import add_membersV2_course_to_user
from members.utils import add_user_course_access
from product.enums import ProductType
from product.models import Affiliate, Checkout, Coupon, Offer, PaymentMethod, Product
from product.utils import get_coupon, validate_coupon_with_dynamic_fees
from reports.filters import CreatedAtDateFilter
from reports.utils import CustomizeXLSXRenderer, CutomizeCSVRenderer, get_response_with_updated_keys
from user.enums import UserAccessLocations
from user.models import User
from user.permissions import ScopePermission, UserIsValidated
from user.utils import get_or_create_user, log_user_login_history


class PaymentAPIView(viewsets.GenericViewSet):
    permission_classes = [AllowAny]
    serializer_class = CheckoutProcessPaymentSerializer

    def to_order_model(
        self,
        main_product: Product,
        item: dict,
        customer: Customer,
        affiliate: Affiliate | None,
        offer: Offer,
        coupon: Coupon | None,
        parent_order: Order | None,
        installments: int,
        checkout: Checkout | None = None,
        address: Address | None = None,
    ) -> Order:
        data: dict = self.serializer.validated_data  # type:ignore
        metadata = data.get('metadata', {}) or {}
        offer_type = item.get('offerType')

        # if offer_type == 'orderbump' and not main_product.affiliateShareBump:
        #     affiliate = None
        if offer_type == 'upsell' and not main_product.affiliateShareUpsell:
            affiliate = None

        order_amount = offer.price

        if coupon and coupon.validate_coupon(offerType=offer_type or 'main'):
            if not validate_coupon_with_dynamic_fees(offer, coupon):
                coupon = None
            else:
                order_amount = coupon.calculate_price(offer.price)
        else:
            coupon = None

        paymentMethod = PaymentMethod.get_payment_method_instance(
            data.get('paymentMethod', ''),
            user=main_product.user,
        )

        order = Order(
            parent_order=parent_order,
            customer=customer,
            product=offer.product,
            affiliate=affiliate,
            coupon=coupon,
            couponCode=coupon.code if coupon else None,
            discount=offer.price - order_amount,
            offer=offer,
            offer_type=offer_type or 'main',
            type=offer.type,
            baseAmount=offer.price,
            amount=order_amount,
            saveCard=data.get('saveCard') or False,
            paymentMethod=paymentMethod,
            installments=installments,
            utm_source=metadata.get('utm_source'),
            utm_medium=metadata.get('utm_medium'),
            utm_campaign=metadata.get('utm_campaign'),
            utm_term=metadata.get('utm_term'),
            utm_content=metadata.get('utm_content'),
            sck=metadata.get('sck'),
            client_user_agent=metadata.get('client_user_agent') or self.request.META.get('HTTP_USER_AGENT'),
            client_ip=get_client_ip(self.request),
            checkoutUrl=data.get('checkoutUrl'),
            refererUrl=data.get('refererUrl'),
            fbc=data.get('fbc'),
            fbp=data.get('fbp'),
            checkout=checkout,
            address=address,
        )
        order.save()

        return order

    def post(self, request, product_short_id):
        return self.process_payment(product_short_id, request.data, get_client_ip(request))

    def handle_affiliate(
        self,
        product: Product,
        query_param_afid=None,
        cookies: list | None = None,
        upsell_payment: bool = False,
    ) -> Affiliate | None:
        if not query_param_afid and not isinstance(cookies, list):
            return None

        if upsell_payment and product.affiliateShareUpsell:
            return Affiliate.objects.filter(
                product__user=product.user,
                short_id=query_param_afid,
                status='active',
            ).first()

        queryParamAffiliate = None
        # Get affiliate by checkout url query param
        if query_param_afid:
            queryParamAffiliate = Affiliate.objects.filter(
                product=product, short_id=query_param_afid, status='active'
            ).first()

            if queryParamAffiliate and product.affiliateClick == 'last':
                return queryParamAffiliate

        # Get affiliate by frontend cookies
        if isinstance(cookies, list):
            cookies = self.process_affiliate_cookies(product, cookies)

            def get_first_or_last_affiliate(cookieList) -> dict[str, str | timezone.datetime]:
                if product.affiliateClick == 'first':
                    return cookieList.pop(0)
                else:
                    return cookieList.pop(-1)

            affiliate = None
            while not affiliate and cookies:
                affiliateCookie = get_first_or_last_affiliate(cookies)
                affiliate = Affiliate.objects.filter(
                    product=product,
                    short_id=affiliateCookie['afid'],
                    status='active',
                ).first()

            if affiliate:
                return affiliate

        # Get affiliate by custom link cookies
        if self.request.COOKIES.get('affiliate_short_id'):
            affiliate = Affiliate.objects.filter(
                product=product,
                short_id=self.request.session.get('affiliate_short_id'),
                status='active',
            ).first()
            if affiliate:
                return affiliate

        return queryParamAffiliate

    def process_affiliate_cookies(self, product, cookies):
        # filter invalid cookies
        cookies = list(filter(lambda x: 'setedAt' in x.keys() and 'afid' in x.keys(), cookies))

        # update setedAt date from timestamp to datetime
        cookies = list(
            map(
                lambda x: {
                    'setedAt': timezone.datetime.fromtimestamp(
                        x['setedAt'] // 1000, timezone.utc
                    ),
                    'afid': x['afid'],
                },
                cookies,
            )
        )

        # filter expired cookies
        if product.cookieTime > 0:
            cookies = list(
                filter(
                    lambda x: x['setedAt'] + timezone.timedelta(days=product.cookieTime)
                    > timezone.now(),
                    cookies,
                )
            )

        # sort list by the setedAt date
        cookies = sorted(cookies, key=lambda x: x['setedAt'])
        return cookies

    def process_payment(
        self,
        product_short_id,
        data: dict,
        request_ip: str,
        upsell: bool = False,
        parent_order: Order | None = None,
    ):
        for item in data.get('items', {}):
            item['id'], item['checkout_id'] = unpack_offer_id_and_checkout_id(item.get('id', ''))

        product = get_object_or_404(
            Product.objects.
            filter(short_id=product_short_id, status='active')
            .select_related('user___company')
            .prefetch_related('user__experimental_features', 'paymentMethods')
        )

        if product.user._company.status == 'blocked':
            return Response({'detail': 'Conta bloqueada.'}, status=status.HTTP_400_BAD_REQUEST)

        serializer_context = self.get_serializer_context()
        serializer_context.update({'product_user': product.user})

        self.serializer = self.get_serializer(data=data, context=serializer_context)
        self.serializer.is_valid(raise_exception=True)

        data = self.serializer.validated_data  # type:ignore

        if not upsell:
            card_anti_fraud(product, data)

        affiliate = self.handle_affiliate(
            product,
            data.get('affiliateShortId'),
            data.get('affiliateCookies'),
            upsell_payment=upsell,
        )

        coupon = get_coupon(data.get('coupon', ''), product_id=product.id)

        paymentMethod: str = data.get('paymentMethod', '')

        payment_strategy: PaymentStrategy = PaymentStrategyFactory.get_strategy(paymentMethod)

        customer_handler = CustomerPaymentProcessor(data.get('customer', {}), request_ip)
        customer = customer_handler.get_or_new_customer()

        orders = self.process_items_to_orders(
            data=data,
            parent_order=parent_order,
            product=product,
            affiliate=affiliate,
            customer=customer,
            coupon=coupon,
        )

        payments: list[Payment] = []

        has_subscription_order = any(order.type == ProductType.SUBSCRIPTION.id for order in orders)
        if paymentMethod == PaymentMethodType.CREDIT_CARD.id and has_subscription_order:
            self.execute_payments_with_subscription(
                data,
                payment_strategy,
                orders,
                payments,
            )
        elif paymentMethod in PaymentMethodType.as_id_list():
            self.execute_payments(
                data,
                payment_strategy,
                orders,
                payments,
            )

        [payment_strategy.handle_notifications(order) for order in orders]

        response_data = {
            'payments': PaymentPublicSerializer(payments, many=True).data,
            'totalAmount': sum(order.amount for order in orders),  # type:ignore
        }

        payment_strategy.afterPaymentProcess(response_data=response_data, customer=customer)

        return Response(response_data)

    def get_extra_transaction_data(self, data):
        extra_data = {
            'antifraud_profiling_attempt_reference': data.get('antifraud_profiling_attempt_reference', ''),
            'mercadopago': {'deviceId': data.get('deviceId', '')},
        }

        if data.get('threeDSecure'):
            extra_data['threeDSecure'] = data.get('threeDSecure', {})

        return extra_data

    def execute_payments(self, data, payment_strategy, orders, payments):
        extra_tx_data = self.get_extra_transaction_data(data)
        payment_strategy.beforePayment(
            orders=orders,
            card=data.get('card'),
            googlepay=data.get('googlepay'),
            applepay=data.get('applepay'),
            extra_tx_data=extra_tx_data,
        )
        payment = payment_strategy.executePayment()
        payments.append(payment)

    def execute_payments_with_subscription(self, data, payment_strategy, orders, payments):
        extra_tx_data = self.get_extra_transaction_data(data)
        for order in orders:
            payment_strategy.beforePayment(
                orders=[order],
                card=data.get('card'),
                googlepay=data.get('googlepay'),
                applepay=data.get('applepay'),
                extra_tx_data=extra_tx_data,
            )
            payment = payment_strategy.executePayment()
            payments.append(payment)
            if payment.status != 'paid' and order.offer_type == OfferType.MAIN.id:
                break

    def process_items_to_orders(self, data, parent_order, product, affiliate, customer, coupon) -> list[Order]:
        items: list[dict] = sorted(
            data.get('items', {}), key=lambda x: not x.get('offerType') == 'main'
        )

        orders: list[Order] = []

        checkout = parent_order.checkout if parent_order else None

        address_processor = AddressPaymentProcessor(customer, data.get('address', {}))
        address = address_processor.get_or_new_address()

        for item in items:
            offer = Offer.objects.filter(id__iexact=item.get('id'), status='active').first()
            if not offer:
                continue

            # Get checkout for the first offer and use it for all subsequent offers
            if not checkout:
                checkout = offer.checkout(item.get('checkout_id'))

            order = self.to_order_model(
                item=item,
                customer=customer,
                affiliate=affiliate,
                offer=offer,
                main_product=product,
                coupon=coupon,
                parent_order=parent_order or (orders[0] if orders else None),
                installments=item.get('installments') or data.get('installments', 1),
                checkout=checkout,
                address=address,
            )
            orders.append(order)

        return orders

    @action(detail=False, methods=['post'])
    def process_upsell(self, request, offer_id, fingerprint):
        offer = Offer.objects.filter(id__iexact=offer_id, status='active').select_related('product').first()

        order = get_order_by_offer_and_fingerprint(offer=offer, fingerprint=fingerprint)
        if not order or not offer:
            return Response(
                {'detail': 'Erro ao validar identidade'}, status=status.HTTP_401_UNAUTHORIZED
            )

        customer_data = model_to_dict(order.customer, exclude=['id', 'createdAt', 'updatedAt'])
        installments = (
            request.data.get('installments', 1) if order.paymentMethodType == 'credit_card' else 1
        )

        base_url = os.getenv('CHECKOUT_BASE_URL', 'https://pay.cakto.com.br')
        payment_data = {
            'customer': customer_data,
            'paymentMethod': order.paymentMethodType,
            'installments': installments,
            'items': [
                {
                    'id': offer_id,
                    'installments': installments,
                    'default': True,
                    'offerType': request.data.get('offerType', 'upsell'),
                }
            ],
            'type': 'product',
            'checkoutUrl': request.data.get('checkoutUrl', f'{base_url}/{offer_id}'),
        }

        if order.affiliate:
            payment_data.update({'affiliateShortId': order.affiliate.short_id})

        # Include card
        if order.paymentMethodType == 'credit_card':
            payment_data.update(
                {'card': {'token': order.card.token if order.card and order.card.token else ''}}
            )  # type:ignore

        return self.process_payment(
            product_short_id=offer.product.short_id,
            data=payment_data,
            request_ip=get_client_ip(request),
            upsell=True,
            parent_order=order,
        )

class OrderAdminAPI(viewsets.GenericViewSet, generics.RetrieveAPIView):
    serializer_class = OrderAdminRetrieveSerializer

    def get_permissions(self):
        if self.action == 'get':
            return [IsAdminUser(), CanViewOrder()]
        elif self.action == 'refund':
            return [IsAdminUser(), CanRefundOrder()]

    def get_object(self, *args, **kwargs):
        return get_object_or_404(Order.objects.filter(pk=self.kwargs['pk']))

    @action(detail=True, methods=['post'])
    def refund(self, request, pk=None):
        order = self.get_object()

        if order.status == 'refunded':
            return Response({'detail': 'Pedido já reembolsado'}, status=status.HTTP_400_BAD_REQUEST)
        if order.status != 'paid':
            return Response(
                {'detail': 'Não é possível reembolsar uma ordem que não está paga'},
                status=status.HTTP_400_BAD_REQUEST,
            )

        split = SplitPay()
        response = split.refund(order.externalId)
        if not status.is_success(response.status_code):
            return Response(
                {'detail': response.json().get('detail', '')}, status=response.status_code
            )

        order.refunded_by = 'admin'
        order.refunded_by_user = request.user
        order.save(update_fields=['refunded_by', 'refunded_by_user'])

        return Response({'detail': 'Reembolso solicitado com sucesso!'})

class OrderRetrieveAPI(viewsets.ModelViewSet):
    scope = 'orders'
    permission_classes = [IsAuthenticated, UserIsValidated, ScopePermission, IsOwner]
    serializer_class = OrderSerializer

    def get_object(self):
        return get_object_or_404(Order, id=self.kwargs['pk'], product__user=self.request.user)

    @action(detail=True, methods=['post'])
    def refund(self, request, pk=None):
        order = self.get_object()

        if order.product.user.company.status == CompanyStatus.BLOCKED.id:
            return Response(
                {'detail': 'Conta do produtor bloqueada.'}, status=status.HTTP_400_BAD_REQUEST
            )
        if order.status == 'refunded':
            return Response({'detail': 'Pedido já reembolsado'}, status=status.HTTP_400_BAD_REQUEST)
        if order.status != 'paid':
            return Response(
                {'detail': 'Não é possível reembolsar uma ordem que não está paga'},
                status=status.HTTP_400_BAD_REQUEST,
            )

        split = SplitPay()
        response = split.refund(order.externalId)
        if not status.is_success(response.status_code):
            return Response(
                {'detail': response.json().get('detail', '')}, status=response.status_code
            )

        order.refunded_by = 'producer'
        order.refunded_by_user = request.user
        order.save(update_fields=['refunded_by', 'refunded_by_user'])

        return Response({'detail': 'Reembolso solicitado com sucesso!'})

    @action(detail=True, methods=['post'])
    def resend_approved_email(self, request, pk=None):
        order = self.get_object()

        if order.status != 'paid':
            return Response(
                {
                    'detail': 'Não é possível reenviar o email de aprovação de uma ordem que não está paga'
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        customer_user = get_or_create_user(order.customer.email)

        # send_new_password = not customer_user.has_usable_password() and order.product.has_internal_contentDelivery()
        dispatch_approved_mail(
            order=order,
            user=customer_user,
            send_new_password=False,
        )

        return Response({'detail': 'Email reenviado com sucesso!'})

    @action(detail=True, methods=['post'])
    def resend_access(self, *args, **kwargs):
        order = self.get_object()

        user = get_or_create_user(order.customer.email, cellphone=order.customer.phone, emailValidated=True, is_customer=True)
        user.handle_otp_token()

        product: Product = order.product

        if product.has_contentDelivery('cakto'):
            access_time = order.offer.calculate_access_time()
            access_time = access_time.isoformat() if isinstance(access_time, timezone.datetime) else access_time

            # Add access to members v1
            response, success = add_user_course_access(user=user, product=product, access_time=access_time)
            if not success:
                return Response({'detail': response}, status=status.HTTP_400_BAD_REQUEST)

            if product.membersV2Id:
                add_membersV2_course_to_user(user=user, product=product, expires_at=access_time)

        add_external_accesses(order=order, user=user)

        dispatch_app_events('purchase_approved', order=order, payment=order.payment_set.order_by('-createdAt').first())

        return Response({'detail': 'Acesso enviado com sucesso!'})

class OrderListQuerySetMixin:
    scope = 'orders'
    filter_backends = [CreatedAtDateFilter, filters.SearchFilter, filters.OrderingFilter, DjangoFilterBackend]
    filterset_class = OrderFilter
    search_fields = [
        'customer__name',
        'customer__email',
        'customer__docNumber',
        'customer__phone',
        'product__name',
        'product__price',
        'refId',
        'amount',
        'baseAmount',
    ]

    def get_queryset(self):
        orders = (
            Order.objects.filter(commissionedUsers=self.request.user)  # type:ignore
            .select_related(
                'product__user',
                'customer',
                'offer',
                'affiliate',
                'product',
                'paymentMethod',
                'coupon',
            )
            .prefetch_related('commissionedUsers', 'splits__user')
        )
        return orders

class OrderListAPIView(OrderListQuerySetMixin, generics.ListAPIView):
    permission_classes = [IsAuthenticated, UserIsValidated, ScopePermission, IsOwner]
    serializer_class = OrderSerializer

    def list(self, request):
        response = super().list(request)

        user_commissions = (
            Split.objects.filter(order=OuterRef('id'), user=self.request.user)
            .values('order')
            .annotate(total=Sum('totalAmount'))
            .values('total')
        )

        qs = self.get_queryset().annotate(userCommissions=Subquery(user_commissions))
        orders = self.filter_queryset(qs)
        orders_before_status_filter = orders.queryset_before_status_filter

        orders_aggregated = get_orders_by_paymentMethod_and_status(orders)

        ordersAmount = sum(
            [
                status.get('commissions', 0)
                for paymentMethod in orders_aggregated.values()
                for status in paymentMethod.values()
            ]
        )
        ordersCount = sum(
            [
                status.get('count', 0)
                for paymentMethod in orders_aggregated.values()
                for status in paymentMethod.values()
            ]
        )

        ordersPixAmount = sum(
            [status.get('commissions', 0) for status in orders_aggregated.get('pix', {}).values()]
        )
        ordersChargebackAmount = sum(
            [
                paymentMethod.get('chargedback', {}).get('commission', 0)
                for paymentMethod in orders_aggregated.values()
            ]
        )

        pre_status_filter_data = get_orders_by_paymentMethod_and_status(orders_before_status_filter)

        ordersRefundAmount = sum(
            [
                paymentMethod.get('refunded', {}).get('commissions', 0)
                for paymentMethod in pre_status_filter_data.values()
            ]
        )
        paid_count = float(
            sum(
                [
                    paymentMethod.get('paid', {}).get('count', 0)
                    for paymentMethod in pre_status_filter_data.values()
                ]
            )
        )
        refund_count = float(
            sum(
                [
                    paymentMethod.get('refunded', {}).get('count', 0)
                    for paymentMethod in pre_status_filter_data.values()
                ]
            )
        )
        paid_or_refunded_count = paid_count + refund_count

        refund_percentage = (
            (refund_count / paid_or_refunded_count) * 100
            if (refund_count and paid_or_refunded_count)
            else 0
        )

        if response.data:
            response.data['ordersCount'] = ordersCount
            response.data['ordersAmount'] = round(ordersAmount, 2)
            response.data['ordersRefundAmount'] = round(ordersRefundAmount, 2)
            response.data['ordersPixAmount'] = round(ordersPixAmount, 2)
            response.data['refundPercentage'] = round(refund_percentage, 2)
            response.data['ordersChargebackAmount'] = ordersChargebackAmount

        return response

    def get_orders_by_paymentMethod(self, all_orders):
        orders_filter_by_paymentMethod = all_orders.values('paymentMethod').annotate(
            commissions=Sum('userCommissions'), count=Count('id')
        )
        orders_by_paymentMethod: dict = {}
        for data in orders_filter_by_paymentMethod:
            orders_by_paymentMethod[data['paymentMethod']] = {
                'commissions': data['commissions'],
                'count': data['count'],
            }

        return orders_by_paymentMethod

    def get_orders_by_status(self, all_orders):
        orders_filter_by_status = all_orders.values('status').annotate(
            commissions=Sum('userCommissions'), count=Count('id')
        )
        orders_by_status: dict = {}
        for data in orders_filter_by_status:
            orders_by_status['total'] = orders_by_status.get('total', {})
            orders_by_status['total']['commissions'] = (
                orders_by_status.get('total', {}).get('commissions', 0) + data['commissions']
            )
            orders_by_status['total']['count'] = (
                orders_by_status.get('total', {}).get('count', 0) + data['count']
            )
            orders_by_status[data['status']] = {
                'commissions': data['commissions'],
                'count': data['count'],
            }

        return orders_by_status

class OrderListExportXLSX(OrderListQuerySetMixin, generics.ListAPIView, CustomizeXLSXRenderer):
    renderer_classes = [XLSXRenderer]  # Use XLSXRenderer for this view
    serializer_class = OrderExportSerializer
    xlsx_use_labels = True
    xlsx_ignore_headers = ['customer']
    xlsx_custom_cols = {
        'customer.name': {'label': 'Nome do Cliente'},
        'customer.email': {'label': 'Email do Cliente'},
        'customer.phone': {'label': 'Telefone do Cliente'},
        'customer.birthDate': {'label': 'Data de Nascimento do Cliente'},
        'customer.docType': {'label': 'Tipo de Documento do Cliente'},
        'customer.docNumber': {'label': 'Número do Documento do Cliente'},
    }

    def list(self, request, *args, **kwargs):
        serializer = self.get_serializer(
            self.filter_queryset(self.get_queryset()), many=True, context={'request': self.request}
        )

        # Create a response with XLSX content type
        response = Response(
            serializer.data,
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        )
        response['Content-Disposition'] = 'attachment; filename="report.xlsx"'
        return response

class OrderListExportCSV(OrderListQuerySetMixin, CutomizeCSVRenderer, generics.ListAPIView):
    renderer_classes = [CSVRenderer]  # Use CSVRenderer for this view
    serializer_class = OrderExportSerializer

    labels = {
        'ID da Venda': 'refId',
        'Produto': 'product',
        'Checkout': 'checkout',
        'Id da Oferta': 'offerId',
        'Oferta': 'offerName',
        'Status da Venda': 'status',
        'Comissão': 'commission',
        'Afiliado': 'affiliate',
        'Nome do Cliente': 'customer.name',
        'Email do Cliente': 'customer.email',
        'Telefone do Cliente': 'customer.phone',
        'Data de Nascimento do Cliente': 'customer.birthDate',
        'Tipo de Documento do Cliente': 'customer.docType',
        'Número do Documento do Cliente': 'customer.docNumber',
        'Método de Pagamento': 'paymentMethod',
        'Valor Base do Produto': 'baseAmount',
        'Desconto': 'discount',
        'Valor Pago pelo Cliente': 'amount',
        'Taxas': 'fees',
        'Cupom de Desconto': 'couponCode',
        'Porcentagem de Desconto': 'couponPercentage',
        'Parcelas': 'installments',
        'Motivo de Recusa': 'reason',
        'Tipo do Produto': 'type',
        'Venda Pai': 'parent_order',
        'Tipo da Venda': 'offer_type',
        'Data da Venda': 'createdAt',
        'Data de Pagamento': 'paidAt',
        'Data do Reembolso': 'refundedAt',
        'Data do Chargeback': 'chargedbackAt',
        'Data de Agendamento do Pagamento': 'due_date',
        'Data de Cancelamento do Pagamento': 'canceledAt',
        'Data estimada de Liberação': 'releaseDate',
        'fbc - Pixel Facebook': 'fbc',
        'fbp - Pixel Facebook': 'fbp',
        'utm_source': 'utm_source',
        'utm_medium': 'utm_medium',
        'utm_campaign': 'utm_campaign',
        'utm_term': 'utm_term',
        'utm_content': 'utm_content',
        'sck': 'sck',
    }

    def list(self, request, *args, **kwargs):
        serializer = self.get_serializer(
            self.filter_queryset(self.get_queryset()), many=True, context={'request': self.request}
        )

        # Create a response with CSV content type
        response = Response(
            get_response_with_updated_keys(serializer, self.labels), content_type='text/csv'
        )
        response['Content-Disposition'] = 'attachment; filename="report.csv"'
        return response

class OrderAdminListAPIView(generics.ListAPIView):
    permission_classes = [IsAdminUser, CanViewOrder]
    serializer_class = OrderAdminListSerializer
    queryset = (
        Order.objects.all()
        .select_related('customer', 'paymentMethod')
        .prefetch_related('splits__user')
    )
    filter_backends = [filters.SearchFilter, DjangoFilterBackend, filters.OrderingFilter]
    search_fields = ['product__name', 'customer__name']
    filterset_class = OrderFilter
    ordering_fields = ['createdAt']

class PaymentStatusPublicAPIView(APIView):
    permission_classes = [AllowAny]

    def get(self, request, *args, **kwargs):
        payment = (
            Payment.objects
            .filter(pk=self.kwargs['pk'])
            .only('id', 'status')
            .annotate(customer_email=F('orders__customer__email'))
            .first()
        )

        if not payment:
            return Response({'detail': 'Pagamento não encontrado'}, status=status.HTTP_404_NOT_FOUND)

        serializer = PaymentAlowAnySerializer(payment)

        return Response(serializer.data)

class RefundOtpView(viewsets.ViewSet):
    permission_classes = [AllowAny]

    def post(self, request):
        email = request.data.get('email')
        order_exists = Order.objects.filter(customer__email__iexact=email, status='paid').exists()

        if not order_exists:
            return Response(
                {'detail': 'Não foi encontrado nenhum pedido pago para este e-mail'},
                status=status.HTTP_400_BAD_REQUEST,
            )

        else:
            customer = Customer.objects.filter(email__iexact=email).order_by('-createdAt').first()
            if not customer:
                return Response(
                    {'detail': 'Não foi encontrado nenhum cliente com este e-mail'},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            token = ''.join([str(random.randint(0, 9)) for _ in range(6)])
            user = get_or_create_user(email=email)
            user.recovery_password_token = token
            user.recovery_password_token_expires = timezone.now() + timezone.timedelta(minutes=30)
            user.save()

            email_sent = send_otp_token_email(
                user=user,
                title=f'Código de verificação: {token}',
                message='Este é o seu código de verificação:',
                token=token,
            )
            return Response({'customerEmail': customer.email, 'email_sent': bool(email_sent)})

    @action(detail=False, methods=['post'])
    def confirm(self, request):
        email = request.data.get('email')
        token = request.data.get('token')
        user = get_object_or_404(User.objects.filter(email__iexact=email))

        if not (user.recovery_password_token == token) or (user.recovery_password_token_expires < timezone.now()):  # type:ignore
            return Response(
                {'detail': 'Token inválido ou expirado. Refaça o processo de verificação.'},
                status=status.HTTP_400_BAD_REQUEST,
            )

        refresh = RefreshToken.for_user(user)

        log_user_login_history(
            user=user,
            request=request,
            location=UserAccessLocations.REFUND_VIEW.id
        )

        return Response(
            {
                'refresh': str(refresh),
                'access': str(refresh.access_token),  # type:ignore
            }
        )

class RefundAPIView(ModelViewSet):
    permission_classes = [IsAuthenticated]
    model = Order
    serializer_class = RefundOrderPublicSerializer

    def get_queryset(self):
        return Order.objects.filter(
            customer__email__iexact=self.request.user.email,  # type:ignore
            status__in=['paid', 'refunded', 'chargedback'],
        ).order_by('-createdAt')

    def get_object(self):
        return get_object_or_404(
            Order.objects.filter(
                customer__email__iexact=self.request.user.email,  # type:ignore
                id=self.kwargs['pk'],
            )
        )

    def list(self, request, *args, **kwargs):
        response = super().list(request, *args, **kwargs)
        response.data['user_email'] = request.user.email  # type:ignore
        return response

    @action(detail=True, methods=['post'])
    def refund(self, request, pk=None):
        order = self.get_object()
        refund_reason = request.data.get('refundReason')

        if order.product.user.company.status == CompanyStatus.BLOCKED.id:
            return Response(
                {'detail': 'Conta do produtor bloqueada.'}, status=status.HTTP_400_BAD_REQUEST
            )

        if order.status == 'refunded':
            return Response({'detail': 'Pedido já reembolsado'}, status=status.HTTP_400_BAD_REQUEST)

        if order.status != 'paid':
            return Response(
                {'detail': 'Não é possível reembolsar um pedido que não está pago'},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if timezone.now() > (order.paidAt or order.createdAt) + timezone.timedelta(
            days=order.product.guarantee
        ):
            return Response(
                {'detail': 'Não é possível reembolsar um pedido após o prazo de garantia'},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if order.product.user.id in [124, 12233, 801987]:
            return Response(
                {'detail': 'Recebido! Em até 24h o produtor entrará em contato com você'},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if order.product.refundRequest and order.product.user.refundRequest:
            order.status = 'refund_requested'
            send_order_request_refund_customer_email.delay(order)
            send_order_request_refund_producer_email.delay(order)
        else:
            split = SplitPay()
            response = split.refund(order.externalId)
            if not status.is_success(response.status_code):
                return Response(
                    {'detail': response.json().get('detail', '')}, status=response.status_code
                )

        order.refunded_by = 'customer'
        order.refunded_by_user = request.user
        order.refund_reason = refund_reason
        order.save(update_fields=['refunded_by', 'refunded_by_user', 'refund_reason'])

        return Response({'detail': 'Reembolso solicitado com sucesso!'})

class SubscriptionMixin:
    scope = 'subscriptions'
    filter_backends = [filters.SearchFilter, DjangoFilterBackend, filters.OrderingFilter]
    filterset_class = SubscriptionFilter
    search_fields = [
        'customer__name', 'customer__email', 'customer__docNumber', 'product__id',
        'product__name', 'product__short_id', 'offer__id', 'offer__name',
        'parent_order__refId', 'parent_order__id', 'orders__refId', 'orders__id',
        'payments__id'
    ]
    ordering_fields = ['status', 'amount', 'paymentMethod', 'createdAt', 'updatedAt', 'canceledAt', 'next_payment_date']

    def get_queryset(self):
        return (Subscription.objects
                .filter(parent_order__commissionedUsers=self.request.user)  # type:ignore
                .select_related('product', 'customer', 'offer', 'parent_order')
                .prefetch_related('orders'))

class SubscriptionAPIView(SubscriptionMixin, viewsets.ModelViewSet):
    serializer_class = SubscriptionOwnerFlexSerializer

    @action(detail=True, methods=['post'])
    def cancel(self, request, *args, **kwargs):
        subscription = self.get_object()

        if subscription.status == 'canceled':
            return Response({'detail': 'Assinatura já cancelada'}, status=status.HTTP_400_BAD_REQUEST)

        if subscription.status != 'active':
            return Response({'detail': 'Não foi possível cancelar a assinatura'}, status=status.HTTP_400_BAD_REQUEST)

        split = SplitPay()
        response = split.subscriptionUpdate(subscription.externalId, status='canceled')

        if status.is_client_error(response.status_code):
            return Response(response.json(), status=response.status_code)
        elif not status.is_success(response.status_code):
            raise Exception('Error canceling subscription, gateway response:', response.content.decode())

        subscription.status = 'canceled'
        subscription.canceledAt = timezone.now()
        subscription.save(update_fields=['status', 'canceledAt'])

        handle_subscription_canceled(subscription)

        return Response({'detail': 'Assinatura cancelada com sucesso!'})

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            response = self.get_paginated_response(serializer.data)
        else:
            serializer = self.get_serializer(queryset, many=True)
            response = Response(serializer.data)

        self.populate_response_metrics(queryset, response)

        return response

    def aggregate_data(self, queryset):
        return queryset\
            .annotate(
                paid_payments_count=Count('payments', filter=Q(payments__status='paid')),
                paid_payments_amount=Sum('payments__amount', filter=Q(payments__status='paid'))
            )\
            .aggregate(
                avg_amount=Avg('amount'),
                active_amount=Sum('amount', filter=Q(status='active')),
                active_count=Count('id', filter=Q(status='active')),
                canceled_count=Count('id', filter=Q(status='canceled')),
                renewed_count=Sum('paid_payments_count'),
                renewed_amount=Sum('paid_payments_amount'),
                retention_avg=Avg('retention'),
            )

    def populate_response_metrics(self, queryset, response) -> None:
        data = getattr(response, 'data', {})

        aggregated_data = self.aggregate_data(queryset)

        subs_active_count = aggregated_data['active_count'] or 0

        # MRR -> Número de Assinaturas Ativas × Valor Médio da Assinatura Mensal.
        data['mrr'] = subs_active_count * (aggregated_data['avg_amount'] or 0)
        data['ltv'] = self.calculate_ltv(queryset, aggregated_data)
        data['churn_rate'] = self.calculate_churn_rate(queryset, aggregated_data)

        data['active_count'] = subs_active_count
        data['active_amount'] = aggregated_data['active_amount'] or 0

        data['renewed_count'] = aggregated_data['renewed_count'] or 0
        data['renewed_amount'] = aggregated_data['renewed_amount'] or 0

        # Round values
        for key in ['ltv', 'mrr', 'churn_rate', 'active_amount', 'renewed_amount']:
            data[key] = round(data.get(key, 0), 2)

        response.data = data

    def calculate_ltv(self, queryset, aggregated_data):
        # LTV -> Valor Médio do Pedido × Número de Pedidos por Cliente × Tempo de Retenção.
        customer_order_num = queryset.values('customer').distinct().count()
        retention_avg = aggregated_data['retention_avg'] or timezone.timedelta(days=0)
        retention_time = retention_avg.days / 365 if retention_avg.days > 365 else 1
        return (aggregated_data['avg_amount'] or 0) * customer_order_num * retention_time

    def calculate_churn_rate(self, queryset, aggregated_data):
        # Churn Rate -> (Nº de clientes perdidos no periodo / Nº de clientes no inicio do periodo) * 100
        first_filtered_subscription = queryset.first()
        if first_filtered_subscription is None:
            return 0

        customer_losts = aggregated_data['canceled_count'] or 0
        initial_customers_num = Subscription.objects.filter(product__user=self.request.user, createdAt__lte=first_filtered_subscription.createdAt).count()

        return ((customer_losts / initial_customers_num) * 100) if customer_losts else 0

class SubscriptionExportXLSX(SubscriptionMixin, generics.ListAPIView, CustomizeXLSXRenderer):
    renderer_classes = [XLSXRenderer]  # Use XLSXRenderer for this view
    serializer_class = SubscriptionExportSerializer
    xlsx_use_labels = True
    xlsx_ignore_headers = ['customer']
    xlsx_custom_cols = {
        'customer.name': {'label': 'Nome do Cliente'},
        'customer.email': {'label': 'Email do Cliente'},
        'customer.phone': {'label': 'Telefone do Cliente'},
        'customer.birthDate': {'label': 'Data de Nascimento do Cliente'},
        'customer.docType': {'label': 'Tipo de Documento do Cliente'},
        'customer.docNumber': {'label': 'Número do Documento do Cliente'},
    }

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        serializer = self.get_serializer(queryset, many=True)

        # Create a response with XLSX content type
        response = Response(serializer.data, content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        response['Content-Disposition'] = 'attachment; filename="report.xlsx"'
        return response

class SubscriptionExportCSV(SubscriptionMixin, CutomizeCSVRenderer, generics.ListAPIView):
    renderer_classes = [CSVRenderer]  # Use CSVRenderer for this view
    scope = 'orders'
    serializer_class = SubscriptionExportSerializer

    labels = {
        'ID': 'id',
        'Produto': 'productName',
        'Oferta': 'offerName',
        'Id da Oferta': 'offerId',
        'Status': 'status',
        'Valor': 'amount',
        'Quantidade de pagamentos realizados': 'paid_payments_count',
        'Dias de Retenção': 'retention',
        'Período de recorrência (em dias)': 'recurrence_period',
        'Quantidade de recorrências': 'quantity_recurrences',
        'Dias de teste': 'trial_days',
        'Número máximo de tentativas de cobrança': 'max_retries',
        'Intervalo entre tentativas de cobrança (em dias)': 'retry_interval',
        'Nome do Cliente': 'customer.name',
        'Email do Cliente': 'customer.email',
        'Telefone do Cliente': 'customer.phone',
        'Data de Nascimento do Cliente': 'customer.birthDate',
        'Tipo de Documento do Cliente': 'customer.docType',
        'Número do Documento do Cliente': 'customer.docNumber',
        'Método de Pagamento': 'paymentMethod',
        'Venda Pai': 'parent_order',
        'Próximo Pagamento': 'next_payment_date',
        'Data de Criação': 'createdAt',
        'Data de Cancelamento': 'canceledAt',
    }

    def list(self, request, *args, **kwargs):
        serializer = self.get_serializer(self.get_queryset(), many=True, context={'request': self.request})

        # Create a response with CSV content type
        response = Response(get_response_with_updated_keys(serializer, self.labels), content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="report.csv"'
        return response

class WebhookAPIView(APIView):
    permission_classes = [AllowAny]
    throttle_classes = []

    def post(self, request):
        if request.data.get('secret') not in os.getenv('WEBHOOK_SECRETS', '').split(','):
            self.not_processed_warning('Webhook secret not found!')
            return Response(status=status.HTTP_404_NOT_FOUND)

        data = request.data.get('data', {})
        transaction_type = request.data.get('type', '')
        print(
            f'Processing webhook: {transaction_type:<30}'
            f' | id {data.get("id", ""):<40}'
            f' | status {data.get("status", ""):<20}'
            f' | email {data.get("email", "")}'
        )

        if transaction_type == 'transaction.retry_scheduled':
            self.process_payment_retry_scheduled(data)
        elif transaction_type == 'transaction.changed':
            self.process_transaction_data(data)
        elif transaction_type == 'subscription.subscription_status_changed':
            self.process_subscription_status_change(data)
        elif transaction_type in [
            'account.changed', 'account.approved', 'account.rejected',
            'user.three_ds_changed',
        ]:
            self.handle_company(data)
        else:
            self.not_processed_warning('Webhook type not found!')

        return Response({'status': 'ok'})

    def process_transaction_data(self, data):
        data_status = data.get('status')
        data['status'] = STATUS_MAPPING.get(data_status, data_status)

        # Skip credit_card transactions that are created just now
        # this prevent processing it before the actual payment flow is finished
        if data.get('paymentMethod') == 'credit_card' and data_status != PaymentStatus.SCHEDULED.id:
            createdAt = timezone.datetime.fromisoformat(data.get('createdAt')) if data.get('createdAt') else None
            if createdAt and (timezone.now() - createdAt).total_seconds() < 30:
                return

        payment = Payment.objects.filter(externalId=data.get('id')).prefetch_related('orders', 'subscriptions').first()

        if not payment and data.get('subscription') is not None:
            subscription_externalId = data.get('subscription', {}).get('id')

            subscription = (
                Subscription.objects
                .filter(externalId=subscription_externalId)
                .select_related('parent_order')
                .first()
            )

            if subscription:
                return self.handle_new_subscription_payment(data, subscription)

        if payment:
            return self.handle_payment(data, payment)

        orders_processed = self.handle_not_found_payment(data)

        if orders_processed is False:
            self.not_processed_warning('Payment not found and not processed!')

    def handle_payment(self, data, payment: Payment):
        payment_strategy = PaymentStrategyFactory.get_strategy(payment.paymentMethodType)
        payment_strategy.handle_payment_status_change(
            payment=payment,
            current_status=payment.status,
            new_status=data.get('status'),
            webhook_data=data
        )

    def handle_new_subscription_payment(self, data, subscription: Subscription):
        parent_order = subscription.parent_order
        payment_strategy = PaymentStrategyFactory.get_strategy(parent_order.paymentMethodType)

        payment = payment_strategy.process_new_subscription_payment(subscription=subscription, webhook_data=data)
        payment_strategy.handle_payment_status_change(
            payment=payment,
            current_status='scheduled',
            new_status=data.get('status'),
            webhook_data=data
        )

    def process_subscription_status_change(self, webhook_data):
        subscription = Subscription.objects.filter(externalId=webhook_data.get('id')).first()

        if not subscription:
            self.not_processed_warning('Subscription not found!')
            return

        PaymentStrategy.handle_subscription_status_change(
            subscription=subscription,
            current_status=subscription.status,
            new_status=webhook_data.get('status'),
            webhook_data=webhook_data,
        )

    def process_payment_retry_scheduled(self, webhook_data: dict) -> None:
        subscription_id = webhook_data.get('subscription', {}).get('id')

        order = (
            Order.objects
            .filter(externalId=webhook_data.get('id'))
            .prefetch_related('payment_set')
            .select_related('subscription')
            .first()
        )

        if not order:
            raise Exception(
                'Order not found on retry scheduled! '
                f'payment id: {webhook_data.get("id", "")} '
                f'subscription id: {subscription_id}'
            )

        subscription = order.subscription

        next_payment_date: str | timezone.datetime = webhook_data.get('due_date') or subscription.next_payment_date  # type:ignore

        # Update subscription, order and payments due date
        with transaction.atomic():
            subscription.set_next_payment_date(next_payment_date)

            order.due_date = next_payment_date
            order.save(update_fields=['due_date'])

            order.payment_set.update(due_date=next_payment_date)

        handle_subscription_renewal_refused_payment(
            order=order,
            payment=order.payment_set.first(),
            webhook_data=webhook_data
        )

    def handle_not_found_payment(self, data):
        """Reprocess payment data from webhook"""
        try:
            with transaction.atomic():
                print(f'Reprocessing payment id {data.get("id")}')
                PaymentStrategy().process_payment_from_split_data(paymentData=data)
        except Exception as e:
            print('Error processing payment from webhook data:', e)
            traceback.print_exc()
            return False
        return True

    def handle_company(self, data):
        user = User.objects.filter(email__iexact=data.get('email')).select_related('_company').first()
        if not user:
            self.not_processed_warning('Account webhook not processed! >> User Not Found')
            return False

        company = user.company

        received_status = data.get('status')
        current_status = company.status

        if received_status:
            if received_status not in CompanyStatus.as_id_list():
                self.not_processed_warning(f'Account webhook not processed! >> Status "{received_status}" not found')
                return False
            company.status = received_status

        if 'commercialName' in data:
            company.companyName = data.get('commercialName')

        if 'cnpj' in data:
            company.companyCnpj = data.get('cnpj')
            if not company.companyCnpj:
                company.type = CompanyType.INDIVIDUAL.id

        if 'cpf' in data:
            company.cpf = data.get('cpf')

        if 'first_name' in data and 'last_name' in data:
            company.completeName = (
                f"{data.get('first_name', '')} {data.get('last_name', '')}".strip()
            )

        if 'cellphone' in data:
            company.phone = data.get('cellphone')

        threeDsEnabled = data.get('threeDs')
        if threeDsEnabled is not None and (threeDsEnabled != company.threeDsEnabled):
            company.threeDsEnabled = threeDsEnabled
            cache.delete_many(key for product in user.product_set.all() for key in product.get_cache_keys())

        if 'companyType' in data:
            company.companyType = data.get('companyType')

        if company.has_changed_to_approved(current_status, received_status):
            send_company_approval_email.delay(user)

        if received_status == CompanyStatus.REJECTED.id and current_status != CompanyStatus.REJECTED.id:
            company.rejectedReasons = data.get('rejectedReasons')
            company.sessionId = None
            company.sessionUrl = None
            company.sessionToken = None
            company.sessionTokenExpires = None
            send_company_resubmit_email.delay(user)

        # check if user need to resubmit to veriff
        # if received_status == CompanyStatus.RESUBMISSION_REQUESTED.id:
        #     company.rejectedReasons = data.get('rejectedReasons')

        #     if company.sessionTokenExpires and company.sessionTokenExpires > timezone.now():
        #         url = company.sessionUrl
        #     else:
        #         url = None
        #     send_company_resubmit_email.delay(user, url)

        company.save()

        return True

    def not_processed_warning(self, message):
        print('Not processed Webhook error:', message)
        data = getattr(self.request, 'data', {}).copy()
        data.pop('secret', None)
        print('request data ->', data)

class ValidateApplePayView(APIView):
    permission_classes = [AllowAny]

    def post(self, request):
        validation_url = request.data.get('validationURL')

        if not validation_url:
            return Response({'error': 'Requisição inválida!'}, status=400)

        merchant_session = validate_applepay_merchant(validation_url)
        return Response(merchant_session)

class PaymentMethodListAPIView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        statuses = PaymentMethodType.as_list()
        return Response(statuses)

class PaymentStatusesAPIView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        statuses = PaymentStatus.as_list()
        return Response(statuses)

class CoproductionTypeAPIView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        statuses = CoproductionType.as_list()
        return Response(statuses)

class OfferTypeAPIView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        statuses = OfferType.as_list()
        return Response(statuses)

class ReprocessSplitPaymentAPI(APIView):
    permission_classes = [IsAdminUser]

    def post(self, request):
        data = request.data
        payment_ids = data.get('payment_ids', [])

        failed_ids = []
        success_ids = []

        for payment_id in payment_ids:
            try:
                split_res = SplitPay().getPayment(payment_id)
                if not status.is_success(split_res.status_code):
                    raise Exception('Error getting split payment data, response:', split_res.content.decode())

                paymentData = split_res.json()

                PaymentStrategy().process_payment_from_split_data(paymentData=paymentData)

                success_ids.append(payment_id)
            except Exception as e:
                print(f'Error reprocessing split payment {payment_id}: {e}')
                traceback.print_exc()
                failed_ids.append(payment_id)
                continue

        return Response({
            'detail': 'Processamento concluído.',
            'error_count': len(failed_ids),
            'payments_with_errors': failed_ids,
            'processed_count': len(success_ids),
            'payments_processed': success_ids,
        })
