# Generated by Django 4.2.5 on 2024-03-15 23:51

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='EmailLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('to', models.<PERSON><PERSON><PERSON><PERSON>(max_length=255)),
                ('subject', models.Char<PERSON>ield(max_length=255)),
                ('template_name', models.CharField(max_length=255)),
                ('is_sent', models.BooleanField(default=False)),
                ('createdAt', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'Email Log',
                'verbose_name_plural': 'Email Logs',
                'ordering': ('-createdAt',),
            },
        ),
    ]
