from django.utils import timezone

from cakto.tests.base import BaseTestCase
from email_service.mail import send_approved_mail
from user.enums import UserAccessLocations
from user.models import UserLoginHistory


class TestMail(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.create_paymentMethods()

        cls.product = cls.create_product(user=cls.user)

        cls.order = cls.create_order(product=cls.product)

    def test_send_approved_mail_creates_user_login_history(self):
        UserLoginHistory.objects.all().delete()  # Clear any existing login history

        send_approved_mail(self.order, user=self.user, user_password='1234')

        self.assertEqual(UserLoginHistory.objects.count(), 1)

        history: UserLoginHistory = UserLoginHistory.objects.first()  # type:ignore

        self.assertEqual(history.user, self.user)
        self.assertIsNone(history.ip)
        self.assertIsNone(history.user_agent)
        self.assertEqual(
            history.access_token_source,
            UserAccessLocations.PURCHASE_EMAIL.id
        )
        self.assertAlmostEqual(
            history.createdAt,
            timezone.now(),
            delta=timezone.timedelta(seconds=2)
        )
