from django import template

register = template.Library()

@register.filter
def has_content_delivery(product, delivery_type):
    return product.has_contentDelivery(delivery_type)

@register.filter
def has_internal_contentDelivery(product):
    return product.has_internal_contentDelivery()

@register.filter
def divide(value, arg):
    try:
        return float(value) / int(arg)
    except (ValueError, ZeroDivisionError):
        return 0
