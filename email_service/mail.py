import os
import random

from django.core.mail import send_mail
from django.template.loader import render_to_string
from django_rq import job
from rest_framework.request import Request
from rq import Retry

from customer.models import Customer
from email_service.models import EmailLog
from gateway.models import Order, Payment, PaymentStatus, Subscription
from product.models import Product
from user.enums import UserAccessLocations
from user.models import User
from user.utils import create_user_refreshToken, get_or_create_user
from django.conf import settings

FRONT_END_BASE_URL = os.getenv('FRONT_END_BASE_URL', 'https://app.cakto.com.br')

def get_product_offer_name(product_name: str, offer_name: str) -> str:
    productName = shorten_name(product_name)
    offer_name = shorten_name(offer_name)

    if product_name == offer_name:
        return productName
    else:
        return f'{productName} - {offer_name}'

def shorten_name(name: str) -> str:
    return name if len(name) < 30 else name[:30] + '...'

def get_customer_name(*, user: User | None = None, customer: Customer | None = None):
    """Return the user name based on the provided user or customer instance. if not found, return the email."""

    if not user and not customer:
        raise ValueError('User or Customer must be provided.')

    if user:
        user_name = user.company.completeName.split(' ')[0] if user.company.completeName else ''
        if not user_name:
            customer = Customer.objects.filter(email__iexact=user.email).first()
            if customer:
                user_name = customer.name.split(' ')[0] if customer.name else ''
        if not user_name:
            user_name = user.email

        return user_name

    if customer:
        user_name = customer.name.split(' ')[0] if customer.name else ''
        if not user_name:
            user = User.objects.filter(email__iexact=customer.email).first()
            if user:
                user_name = user.company.completeName.split(' ')[0] if user.company.completeName else ''
        if not user_name:
            user_name = customer.email

        return user_name


def get_product_supportEmail(product):
    return product.supportEmail or product.user.email

# Payment
@job
def send_approved_mail(order: Order, user: User | None = None, user_password: str | None = None):
    product: Product = order.product
    if user is None:
        user = get_or_create_user(order.customer.email, cellphone=order.customer.phone, emailValidated=True, is_customer=True)
        user.handle_otp_token()

    refreshToken = create_user_refreshToken(
        user,
        accessToken_lifetime={'days': 7},
        refreshToken_lifetime={'days': 7},
        access_location=UserAccessLocations.PURCHASE_EMAIL
    )
    permanent_access_url = f'{FRONT_END_BASE_URL}/auth/login?accessToken={refreshToken.access_token}&refreshToken={refreshToken}'

    content = {
        'producerName': product.producerName or get_product_supportEmail(product),
        'offerName': get_product_offer_name(product.name, order.offer.name),
        'supportEmail': get_product_supportEmail(product),
        'supportWhatsapp': product.supportWhatsapp,
        'product': product,
        'permanentAccessUrl': permanent_access_url,
        'amount': round(order.amount, 2) if order.amount else 0,
        'installments': order.installments,
        'paymentMethod': order.paymentMethodName,
        'invoiceDescription': (product.invoiceDescription and order.paymentMethodType == 'credit_card') or '',
        'customerName': order.customer.name,
        'userEmail': order.customer.email,
        'userPassword': user_password,
    }
    email_content = render_to_string('mail/purchase_approval.html', content)

    if content.get('userPassword'):
        content['userPassword'] = 'REDACTED'

    productName = shorten_name(product.name)
    title = ('Acessar seu Produto' if product.has_contentDelivery('cakto') else 'Compra Aprovada') + f' - {productName}'

    message = "O acesso ao seu produto já está disponível."
    email_sent = send_mail(
        title,
        message,
        settings.DEFAULT_FROM_MAIL,
        [order.customer.email],
        html_message=email_content,
        fail_silently=True,
    )
    EmailLog.objects.create(
        subject=title,
        message=message,
        to=order.customer.email,
        template_name='purchase_approval.html',
        data=content,
        is_sent=email_sent,
    )

    return email_sent

@job('default', retry=Retry(max=3, interval=20))
def dispatch_approved_mail(order: Order, user: User, send_new_password: bool) -> None:
    """Send the approved email to the user. Raises an Exception if the email fails to send."""
    password_before = user.password
    user_password = None

    if send_new_password:
        alphabet = '23456789abcdefghijkmnopqrstuvwxyz'
        user_password = ''.join(random.choices(alphabet, k=6))
        user.set_password(user_password)
        user.save(update_fields=['password'])

    email_sent = send_approved_mail(
        order=order,
        user=user,
        user_password=user_password if send_new_password else None,
    )

    if not email_sent:
        if send_new_password:
            user.set_password(password_before)
            user.save(update_fields=['password'])
        raise Exception(f'Error sending approved email to user {user.email}')

@job
def send_payment_mail(payment):
    if getattr(payment, 'pix'):
        return send_pix_payment_mail(payment)
    elif getattr(payment, 'boleto'):
        return send_ticket_payment_mail(payment)
    elif getattr(payment, 'picpay'):
        return send_picpay_payment_mail(payment)

def send_ticket_payment_mail(payment):
    orders = payment.orders.all()
    order = orders[0]

    productName = shorten_name(order.product.name)
    if len(orders) == 2:
        productName += (' e ' + shorten_name(orders[1].product.name))
    elif len(orders) > 2:
        productName += 'e outros itens'

    ticketExpirationDate = '/'.join(payment.boleto.get('expirationDate', '').split('-')[::-1])

    content = {
        'orders': orders,
        'first_order': orders[0],
        'payment': payment,
        'productName': productName,
        'producerName': order.product.producerName or '',
        'supportEmail': order.product.supportEmail or order.product.user.email,
        'supportWhatsapp': order.product.supportWhatsapp,
        'ticketLink': payment.boleto.get('url'),
        'ticketCodeBar': payment.boleto.get('barcode'),
        'ticketExpirationDate': ticketExpirationDate,
        'amount': payment.amount,
        'customerName': order.customer.name,
    }
    email_content = render_to_string('mail/payment_boleto.html', content)

    title = f"Imprima seu boleto - {productName}"
    message = "Seu boleto já está disponível."
    email_sent = send_mail(
        title,
        message,
        settings.DEFAULT_FROM_MAIL,
        [orders[0].customer.email],
        html_message=email_content,
        fail_silently=True,
    )
    EmailLog.objects.create(
        subject=title,
        message=message,
        to=order.customer.email,
        template_name='payment_boleto.html',
        data=content,
        is_sent=email_sent,
    )
    return email_sent

def send_pix_payment_mail(payment):
    orders = payment.orders.all()
    order = orders[0]

    productName = shorten_name(order.product.name)
    if len(orders) == 2:
        productName += (' e ' + shorten_name(orders[1].product.name))
    elif len(orders) > 2:
        productName += 'e outros itens'

    from gateway.utils import generate_qr_code
    qr_code = generate_qr_code(payment.pix.get('qrCode'))

    content = {
        'orders': orders,
        'first_order': orders[0],
        'payment': payment,
        'productName': productName,
        'producerName': order.product.producerName or '',
        'supportEmail': order.product.supportEmail or order.product.user.email,
        'supportWhatsapp': order.product.supportWhatsapp,
        'pixCode': payment.pix.get('qrCode'),
        'pixQrCode': qr_code,
        'amount': payment.amount,
        'customerName': order.customer.name,
    }
    email_content = render_to_string('mail/payment_pix.html', content)

    message = "Seu pix já está disponível."
    title = f"Pague seu Pix - {productName}"
    email_sent = send_mail(
        title,
        message,
        settings.DEFAULT_FROM_MAIL,
        [orders[0].customer.email],
        html_message=email_content,
        fail_silently=True,
    )
    EmailLog.objects.create(
        subject=title,
        message=message,
        to=order.customer.email,
        template_name='payment_pix.html',
        data=content,
        is_sent=email_sent,
    )
    return email_sent

def send_picpay_payment_mail(payment):
    orders = payment.orders.all()
    order = orders[0]

    productName = shorten_name(order.product.name)
    if len(orders) == 2:
        productName += (' e ' + shorten_name(orders[1].product.name))
    elif len(orders) > 2:
        productName += 'e outros itens'

    from gateway.utils import generate_qr_code
    qr_code = generate_qr_code(payment.picpay.get('qrCode'))

    content = {
        'orders': orders,
        'first_order': orders[0],
        'payment': payment,
        'productName': productName,
        'producerName': order.product.producerName or '',
        'supportEmail': order.product.supportEmail or order.product.user.email,
        'supportWhatsapp': order.product.supportWhatsapp,
        'qrCodeImg': qr_code,
        'qrCode': payment.picpay.get('qrCode'),
        'amount': payment.amount,
        'customerName': order.customer.name,
    }
    email_content = render_to_string('mail/payment_picpay.html', content)

    message = "Seu pagamento PicPay já está disponível."
    title = f"Pague seu Pedido - {productName}"
    email_sent = send_mail(
        title,
        message,
        settings.DEFAULT_FROM_MAIL,
        [orders[0].customer.email],
        html_message=email_content,
        fail_silently=True,
    )
    EmailLog.objects.create(
        subject=title,
        message=message,
        to=order.customer.email,
        template_name='payment_picpay.html',
        data=content,
        is_sent=email_sent,
    )
    return email_sent


# Subscriptions
def send_subscription_renewal_payment_mail(order: Order, payment: Payment):
    if getattr(payment, 'pix'):
        return send_subscription_renewal_email_pix(order, payment)
    elif getattr(payment, 'boleto'):
        return send_subscription_renewal_email_boleto(order, payment)
    elif getattr(payment, 'picpay'):
        return send_subscription_renewal_email_picpay(order, payment)

def send_subscription_renewal_email_pix(order: Order, payment: Payment) -> int:
    from gateway.utils import generate_qr_code

    productName = get_product_offer_name(order.product.name, order.offer.name)

    pixCode = (payment.pix or {}).get('qrCode', '')
    qr_code = generate_qr_code(pixCode)

    content = {
        'payment': payment,
        'productName': productName,
        'producerName': order.product.producerName or '',
        'supportEmail': order.product.supportEmail or order.product.user.email,
        'supportWhatsapp': order.product.supportWhatsapp,
        'pixCode': payment.pix.get('qrCode'),
        'pixQrCode': qr_code,
        'amount': payment.amount,
        'customerName': get_customer_name(customer=order.customer),
    }
    email_content = render_to_string('mail/subscription_renewal_pix.html', content)

    title = f"Ação Necessária - {productName}"
    message = f"Ação Necessária - {productName}"
    email_sent = send_mail(
        title,
        message,
        settings.DEFAULT_FROM_MAIL,
        [order.customer.email],
        html_message=email_content,
        fail_silently=True,
    )
    EmailLog.objects.create(
        subject=title,
        message=message,
        to=order.customer.email,
        template_name='subscription_renewal_pix.html',
        data=content,
        is_sent=email_sent,
    )
    return email_sent

def send_subscription_renewal_email_boleto(order: Order, payment: Payment) -> int:
    productName = get_product_offer_name(order.product.name, order.offer.name)

    ticketExpirationDate = '/'.join(payment.boleto.get('expirationDate', '').split('-')[::-1])

    content = {
        'payment': payment,
        'productName': productName,
        'producerName': order.product.producerName or '',
        'supportEmail': order.product.supportEmail or order.product.user.email,
        'supportWhatsapp': order.product.supportWhatsapp,
        'ticketLink': payment.boleto.get('url'),
        'ticketCodeBar': payment.boleto.get('barcode'),
        'ticketExpirationDate': ticketExpirationDate,
        'amount': payment.amount,
        'customerName': get_customer_name(customer=order.customer),
    }
    email_content = render_to_string('mail/subscription_renewal_boleto.html', content)

    title = f"Ação Necessária - {productName}"
    message = f"Ação Necessária - {productName}"
    email_sent = send_mail(
        title,
        message,
        settings.DEFAULT_FROM_MAIL,
        [order.customer.email],
        html_message=email_content,
        fail_silently=True,
    )
    EmailLog.objects.create(
        subject=title,
        message=message,
        to=order.customer.email,
        template_name='subscription_renewal_boleto.html',
        data=content,
        is_sent=email_sent,
    )
    return email_sent

def send_subscription_renewal_email_picpay(order: Order, payment: Payment) -> int:
    productName = get_product_offer_name(order.product.name, order.offer.name)

    from gateway.utils import generate_qr_code
    qr_code = generate_qr_code(payment.picpay.get('qrCode'))

    content = {
        'orders': [order],
        'first_order': order,
        'payment': payment,
        'productName': productName,
        'producerName': order.product.producerName or '',
        'supportEmail': order.product.supportEmail or order.product.user.email,
        'supportWhatsapp': order.product.supportWhatsapp,
        'qrCodeImg': qr_code,
        'qrCode': payment.picpay.get('qrCode'),
        'amount': payment.amount,
        'customerName': get_customer_name(customer=order.customer),
    }
    email_content = render_to_string('mail/subscription_renewal_picpay.html', content)

    title = f"Ação Necessária - {productName}"
    message = f"Ação Necessária - {productName}"
    email_sent = send_mail(
        title,
        message,
        settings.DEFAULT_FROM_MAIL,
        [order.customer.email],
        html_message=email_content,
        fail_silently=True,
    )
    EmailLog.objects.create(
        subject=title,
        message=message,
        to=order.customer.email,
        template_name='subscription_renewal_picpay.html',
        data=content,
        is_sent=email_sent,
    )
    return email_sent

@job
def send_subscription_renewed_email(order: Order, payment: Payment) -> int:
    product: Product = order.product

    productName = get_product_offer_name(order.product.name, order.offer.name)

    content = {
        'producerName': order.product.producerName or '',
        'productName': productName,
        'supportEmail': get_product_supportEmail(product),
        'supportWhatsapp': order.product.supportWhatsapp,
        'amount': round(payment.amount, 2) if payment.amount else 0,
        'paymentMethod': payment.paymentMethodName,
        'invoiceDescription': (product.invoiceDescription and order.paymentMethodType == 'credit_card') or '',
        'customerName': get_customer_name(customer=order.customer),
    }
    email_content = render_to_string('mail/subscription_renewed.html', content)

    title = f'Assinatura Renovada - {productName}'
    message = "Sua assinatura foi renovada."
    email_sent = send_mail(
        title,
        message,
        settings.DEFAULT_FROM_MAIL,
        [order.customer.email],
        html_message=email_content,
        fail_silently=True,
    )
    EmailLog.objects.create(
        subject=title,
        message=message,
        to=order.customer.email,
        template_name='subscription_renewed.html',
        data=content,
        is_sent=email_sent,
    )

    return email_sent

@job('default', retry=Retry(max=3, interval=10))
def send_subscription_renewal_refused_payment_mail(subscription: Subscription, order: Order) -> int:
    productName = get_product_offer_name(order.product.name, order.offer.name)

    nextPaymentDate = subscription.next_payment_date

    content = {
        'nextPaymentDate': nextPaymentDate.strftime('%d/%m/%Y') if nextPaymentDate else '',
        'cardLastDigits': order.card.lastDigits if order.card else '',
        'amount': order.amount,
        'reason': order.reason,
        'producerName': order.product.producerName or '',
        'productName': productName,
        'supportEmail': get_product_supportEmail(order.product),
        'supportWhatsapp': order.product.supportWhatsapp,
        'customerName': get_customer_name(customer=order.customer),
    }
    email_content = render_to_string('mail/subscription_renewal_payment_refused.html', content)

    title = f"Ação Necessária - {productName}"
    message = f"Ação Necessária - {productName}"
    email_sent = send_mail(
        title,
        message,
        settings.DEFAULT_FROM_MAIL,
        [order.customer.email],
        html_message=email_content,
        fail_silently=True,
    )
    EmailLog.objects.create(
        subject=title,
        message=message,
        to=order.customer.email,
        template_name='subscription_renewal_payment_refused.html',
        data=content,
        is_sent=email_sent,
    )

    if not email_sent:
        raise Exception(f'Email "subscription_renewal_payment_refused" not sent to {subscription.customer.email}.')

    return email_sent

@job('default', retry=Retry(max=3, interval=10))
def send_subscription_canceled_email(subscription: Subscription) -> int:
    product: Product = subscription.product

    productName = get_product_offer_name(subscription.product.name, subscription.offer.name)

    content = {
        'producerName': subscription.product.producerName or '',
        'productName': productName,
        'supportEmail': get_product_supportEmail(product),
        'supportWhatsapp': product.supportWhatsapp,
        'customerName': get_customer_name(customer=subscription.customer),
    }
    email_content = render_to_string('mail/subscription_canceled.html', content)

    title = f'Assinatura Cancelada - {product.name}'
    message = "Sua assinatura foi cancelada."
    email_sent = send_mail(
        title,
        message,
        settings.DEFAULT_FROM_MAIL,
        [subscription.customer.email],
        html_message=email_content,
        fail_silently=True,
    )
    EmailLog.objects.create(
        subject=title,
        message=message,
        to=subscription.customer.email,
        template_name='subscription_canceled.html',
        data=content,
        is_sent=email_sent,
    )

    if not email_sent:
        raise Exception(f'Email "subscription_canceled" not sent to {subscription.customer.email}.')

    return email_sent

# Orders
@job
def send_order_refunded_customer_email(order: Order):
    content = {
        'userName': get_customer_name(customer=order.customer),
        'productName': order.offer.product.name,
        'orderRefId': order.refId,
        'orderAmount': order.amount,
        'purchaseDate': order.createdAt.strftime('%d/%m/%Y'),
        'paymentMethod': order.paymentMethodName,
    }
    email_content = render_to_string('mail/order_refunded_customer.html', content)

    title = f'Reembolso confirmado - Produto {shorten_name(order.product.name)}'

    message = "Reembolso confirmado."
    email_sent = send_mail(
        title,
        message,
        settings.DEFAULT_FROM_MAIL,
        [order.customer.email],
        html_message=email_content,
        fail_silently=True,
    )
    EmailLog.objects.create(
        subject=title,
        message=message,
        to=order.customer.email,
        template_name='order_refunded_customer.html',
        data=content,
        is_sent=email_sent,
    )

    return email_sent

@job
def send_order_refunded_producer_email(order: Order):
    info_text = "Gostaríamos de informá-lo que {} para um pedido do produto"
    if order.status == PaymentStatus.MED.id:
        status_label = "MED"
        process_name = "MED"
        info_text = info_text.format('uma MED foi processada')
    elif order.status == PaymentStatus.CHARGEDBACK.id:
        status_label = "Chargeback"
        process_name = "chargeback"
        info_text = info_text.format('um chargeback foi processado')
    else:
        status_label = "Reembolso"
        process_name = "reembolso"
        info_text = info_text.format('um reembolso foi processado')

    content = {
        'userName': get_customer_name(user=order.product.user),
        'productName': order.offer.product.name,
        'orderRefId': order.refId,
        'orderAmount': order.amount,
        'purchaseDate': order.createdAt.strftime('%d/%m/%Y'),
        'paymentMethod': order.paymentMethodName,
        'customerName': order.customer.name,
        'customerEmail': order.customer.email,
        'customerPhone': order.customer.phone,
        'infoText': info_text,
        'processName': process_name,
    }
    email_content = render_to_string('mail/order_refunded_producer.html', content)

    title = f'Notificação de {status_label} - Produto {shorten_name(order.product.name)}'

    message = f"Notificação de {status_label}."
    email_sent = send_mail(
        title,
        message,
        settings.DEFAULT_FROM_MAIL,
        [order.product.user.email],
        html_message=email_content,
        fail_silently=True,
    )
    EmailLog.objects.create(
        subject=title,
        message=message,
        to=order.product.user.email,
        template_name='order_refunded_producer.html',
        data=content,
        is_sent=email_sent,
    )

    return email_sent

@job
def send_order_request_refund_producer_email(order: Order):
    content = {
        'userName': get_customer_name(customer=order.customer),
        'order': order,
        'paymentMethod': order.paymentMethodName,
    }
    email_content = render_to_string('mail/order_refund_requested_producer.html', content)

    title = f'Reembolso solicitado - Produto {shorten_name(order.product.name)}'

    message = "Reembolso solicitado."
    email_sent = send_mail(
        title,
        message,
        settings.DEFAULT_FROM_MAIL,
        [order.product.user.email],
        html_message=email_content,
        fail_silently=True,
    )
    EmailLog.objects.create(
        subject=title,
        message=message,
        to=order.product.user.email,
        template_name='order_refund_requested_producer.html',
        data=content,
        is_sent=email_sent,
    )

    return email_sent

@job
def send_order_request_refund_customer_email(order: Order):
    content = {
        'userName': get_customer_name(customer=order.customer),
        'supportEmail': get_product_supportEmail(order.product),
        'supportWhatsapp': order.product.supportWhatsapp,
        'order': order,
        'paymentMethod': order.paymentMethodName,
    }
    email_content = render_to_string('mail/order_refund_requested_customer.html', content)

    title = f'Reembolso solicitado - Produto {shorten_name(order.product.name)}'

    message = "Reembolso solicitado."
    email_sent = send_mail(
        title,
        message,
        settings.DEFAULT_FROM_MAIL,
        [order.customer.email],
        html_message=email_content,
        fail_silently=True,
    )
    EmailLog.objects.create(
        subject=title,
        message=message,
        to=order.customer.email,
        template_name='order_refund_requested_customer.html',
        data=content,
        is_sent=email_sent,
    )

    return email_sent


# Company
@job
def send_company_approval_email(user):
    content = {
        'ownerName': get_customer_name(user=user),
        'companyName': user.company.companyName or user.company.completeName,
    }
    email_content = render_to_string('mail/company_approval.html', content)

    title = f'Identidade Confirmada - {shorten_name(user.company.companyName or user.company.completeName)}'

    message = "Acesso à saque e antecipações liberado."
    email_sent = send_mail(
        title,
        message,
        settings.DEFAULT_FROM_MAIL,
        [user.email],
        html_message=email_content,
        fail_silently=True,
    )
    EmailLog.objects.create(
        subject=title,
        message=message,
        to=user.email,
        template_name='company_approval.html',
        data=content,
        is_sent=email_sent,
    )

    return email_sent

@job
def send_company_resubmit_email(user, identity_verification_url=None):
    if identity_verification_url is None:
        identity_verification_url = f'{FRONT_END_BASE_URL}/dashboard/financial?tab=identity'

    content = {
        'ownerName': get_customer_name(user=user),
        'identity_verification_url': identity_verification_url,
        'rejectedReasons': user._company.rejectedReasons,
    }
    email_content = render_to_string('mail/company_resubmit.html', content)

    title = 'Ação necessária: Revisão do processo de verificação de identidade'

    message = "Revisão do processo de verificação de identidade."
    email_sent = send_mail(
        title,
        message,
        settings.DEFAULT_FROM_MAIL,
        [user.email],
        html_message=email_content,
        fail_silently=True,
    )
    EmailLog.objects.create(
        subject=title,
        message=message,
        to=user.email,
        template_name='company_resubmit.html',
        data=content,
        is_sent=email_sent,
    )

    return email_sent

@job
def send_company_verify_email(user: User):
    content = {
        'ownerName': get_customer_name(user=user),
        'onboardUrl': f'{FRONT_END_BASE_URL}/dashboard/financial?tab=identity',
    }
    email_content = render_to_string('mail/company_verify.html', content)

    message = "Ação necessária - Verificação de conta."
    email_sent = send_mail(
        message,
        message,
        settings.DEFAULT_FROM_MAIL,
        [user.email],
        html_message=email_content,
        fail_silently=True,
    )
    EmailLog.objects.create(
        subject=message,
        message=message,
        to=user.email,
        template_name='company_verify.html',
        data=content,
        is_sent=email_sent,
    )

    return email_sent


# User
@job
def send_new_password_email(user, new_password):
    content = {
        'userName': get_customer_name(user=user),
        'newPassword': new_password,
    }
    email_content = render_to_string('mail/user_new_password.html', content)

    title = 'Nova Senha Gerada'

    message = "Confirmação de nova senha."
    email_sent = send_mail(
        title,
        message,
        settings.DEFAULT_FROM_MAIL,
        [user.email],
        html_message=email_content,
        fail_silently=True,
    )
    EmailLog.objects.create(
        subject=title,
        message=message,
        to=user.email,
        template_name='user_new_password.html',
        data=content,
        is_sent=email_sent,
    )

    return email_sent

@job
def send_new_course_access_email(user, product):
    permanent_access_url = f'{FRONT_END_BASE_URL}/student/courses/?id={product.id}'

    content = {
        'userName': get_customer_name(user=user),
        'product': product,
        'permanentAccessUrl': permanent_access_url,
    }

    create_password_url = None
    if user.has_otp_token:
        create_password_url = f'{FRONT_END_BASE_URL}/auth/create-password/?token={user.recovery_password_token}'
    content.update(createPasswordUrl=create_password_url) if create_password_url else None

    email_content = render_to_string('mail/course_access_added.html', content)

    title = 'Seus dados de acesso'

    message = "Novo acesso adicionado para você."
    email_sent = send_mail(
        title,
        message,
        settings.DEFAULT_FROM_MAIL,
        [user.email],
        html_message=email_content,
        fail_silently=True,
    )
    EmailLog.objects.create(
        subject=title,
        message=message,
        to=user.email,
        template_name='course_access_added.html',
        data=content,
        is_sent=email_sent,
    )

    return email_sent

@job
def send_otp_token_email(user: User, title, message, token):
    content = {
        'userName': get_customer_name(user=user),
        'code': token,
        'message': message,
    }
    email_content = render_to_string('mail/otp_token.html', content)

    message = "Código Cakto."
    email_sent = send_mail(
        title,
        message,
        settings.DEFAULT_FROM_MAIL,
        [user.email],
        html_message=email_content,
        fail_silently=True,
    )
    EmailLog.objects.create(
        subject=title,
        message=message,
        to=user.email,
        template_name='otp_token.html',
        data=content,
        is_sent=email_sent,
    )

    return email_sent

@job
def send_password_reset_email(user: User):
    create_password_url = f'{FRONT_END_BASE_URL}/auth/create-password/?token={user.recovery_password_token}'

    content = {
        'userName': get_customer_name(user=user),
        'createPasswordUrl': create_password_url,
    }
    email_content = render_to_string('mail/user_password_reset.html', content)

    title = ('Criação de Senha - Ação Necessária')

    message = 'Criação de Senha - Ação Necessária'
    email_sent = send_mail(
        title,
        message,
        settings.DEFAULT_FROM_MAIL,
        [user.email],
        html_message=email_content,
        fail_silently=True,
    )
    EmailLog.objects.create(
        subject=title,
        message=message,
        to=user.email,
        template_name='user_password_reset.html',
        data=content,
        is_sent=email_sent,
    )

    return email_sent

def send_customer_login_link_email(user: User, request: Request):
    refreshToken = create_user_refreshToken(
        user,
        accessToken_lifetime={'minutes': 10},
        refreshToken_lifetime={'minutes': 10},
        access_location=UserAccessLocations.LOGIN_EMAIL,
        request=request,
    )
    accessUrl = f'{FRONT_END_BASE_URL}/auth/login?accessToken={refreshToken.access_token}&refreshToken={refreshToken}&next=/student/courses/'

    content = {
        'userName': get_customer_name(user=user),
        'accessUrl': accessUrl,
    }
    email_content = render_to_string('mail/customer_login_link_email.html', content)

    title = ('Link de Acesso - Cakto')

    message = 'Link de Acesso - Cakto'

    email_sent = send_mail(
        title,
        message,
        settings.DEFAULT_FROM_MAIL,
        [user.email],
        html_message=email_content,
        fail_silently=True,
    )

    EmailLog.objects.create(
        subject=title,
        message=message,
        to=user.email,
        template_name='customer_login_link_email.html',
        data=content,
        is_sent=email_sent,
    )

    return email_sent
