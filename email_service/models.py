from decimal import Decimal
import json

from django.core.serializers import serialize
from django.db import models
from django.db.models.query import QuerySet
from django.forms.models import model_to_dict
from phonenumber_field.phonenumber import PhoneNumber


class EmailLog(models.Model):
    """Model to store email logs"""
    to = models.CharField(max_length=255)
    message = models.CharField(max_length=255)
    subject = models.CharField(max_length=255)
    template_name = models.Char<PERSON>ield(max_length=255)
    is_sent = models.BooleanField(default=False)
    data = models.JSONField(null=True, blank=True)
    createdAt = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = 'Email Log'
        verbose_name_plural = 'Email Logs'
        ordering = ('-createdAt',)

    def __str__(self):
        return f'{self.to} - {self.subject}'

    def save(self, *args, **kwargs):
        self.data = universal_serializer(self.data)
        super().save(*args, **kwargs)


def universal_serializer(data):
    if isinstance(data, QuerySet):
        return json.loads(serialize('json', data))
    if isinstance(data, models.Model):
        return json.loads(serialize('json', [data]))
    elif isinstance(data, dict):
        return {k: universal_serializer(v) for k, v in data.items()}
    elif isinstance(data, list):
        return [universal_serializer(v) for v in data]
    elif isinstance(data, PhoneNumber):
        return str(data)
    elif hasattr(data, '__dict__'):
        return model_to_dict(data)
    elif isinstance(data, Decimal):
        return float(data)
    else:
        return data
