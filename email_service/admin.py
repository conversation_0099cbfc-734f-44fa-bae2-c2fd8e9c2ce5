from django.contrib import admin
from .models import EmailLog

@admin.register(EmailLog)
class EmailLogAdmin(admin.ModelAdmin):
    list_display = ('to', 'subject', 'template_name', 'is_sent', 'createdAt')
    search_fields = ('to', 'subject', 'template_name')
    list_filter = ('is_sent', 'createdAt', 'template_name')
    readonly_fields = ('to', 'message', 'subject', 'template_name', 'is_sent', 'data', 'createdAt')
