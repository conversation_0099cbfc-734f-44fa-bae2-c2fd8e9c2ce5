from cakto.enums import LabeledEnum


class MFAType(LabeledEnum):
    EMAIL = ("email", "Email")
    TOTP = ("totp", "TOTP")
    APP_CAKTO = ("app_cakto", "APP Cakto")

class UserAccessLocations(LabeledEnum):
    LOGIN = ('login', 'Login')
    LOGIN_EMAIL = ('login_email', 'Link de login por e-mail')
    PURCHASE_EMAIL = ('purchase_email', 'E-mail de compra')
    REFUND_VIEW = ('refund_otp_view', 'View de reembolso com OTP')
    FIRST_PURCHASE = ('first_purchase', 'Primeira compra (token no checkout)')
    IMPERSONATE = ('impersonate', '<PERSON><PERSON> via admin (impersonate)')
    OTP_TOKEN = ('otp_token', 'View de OTP Token')
