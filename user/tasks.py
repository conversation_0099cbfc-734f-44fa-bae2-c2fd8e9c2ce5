
from django.db.models import Subquery
from django.utils import timezone
from django_rq import job

from gateway.models import Order
from user.models import User
from user.utils import create_user_awards


@job('default')
def check_users_awards(days=None):
    user_ids = Order.objects.filter(status='paid').values('product__user')
    if days is not None:
        user_ids.filter(
            createdAt__gte=timezone.now() - timezone.timedelta(days=3)
        )

    users = User.objects.filter(id__in=Subquery(user_ids))

    for user in users:
        create_user_awards(user)
