import random
import string
from abc import ABC, abstractmethod
from datetime import <PERSON><PERSON><PERSON>

from django.conf import settings
from django.db import transaction
from django.db.models import Case, F, IntegerField, Q, Value, When
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.utils.crypto import get_random_string
from django_filters.rest_framework import DjangoFilterBackend
from fcm_django.models import FCMDevice
from rest_framework import filters, generics, status, views, viewsets
from rest_framework.decorators import action
from rest_framework.exceptions import ValidationError
from rest_framework.permissions import AllowAny, IsAdminUser, IsAuthenticated
from rest_framework.response import Response
from rest_framework_simplejwt.exceptions import InvalidToken, TokenError
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from rest_framework_simplejwt.tokens import RefreshToken

from customer.models import Customer, CustomerHistory
from email_service.mail import send_customer_login_link_email, send_new_password_email, send_otp_token_email
from financial.utils import updateSplitEmail
from gateway.sdk.splitpay import SplitPay
from gateway.utils import cloudflarecheck, get_client_ip, validate_recaptcha
from members.sdk.members_v2 import update_membersV2_user_info
from product.pixel_strategies.facebook import send_login_event_to_fb, send_register_event_to_fb
from system_log.utils import log_action
from user.enums import MFAType, UserAccessLocations
from user.filters import AwardAdminFilter, UserAdminFilter, UserAwardAdminFilter
from user.models import Award, Colaborator, MFAMethod, RecoveryTokenHistory, User, UserAward, next_nps_survey_date, Rank
from user.notification import notify_fcm_mfa_action, send_notification
from user.permissions import UserIsSuperUser
from user.sdk.crm import send_crm_create_user_event
from user.sdk.infobip import InfoBipWhatsApp
from user.serializers import (AwardAdminSerializer, ChangePasswordSerializer, ColaboratorCreateSerializer,
                              ColaboratorSerializer, CreateNPSSurveySerializer, CreatePasswordSerializer,
                              MFAGeneralSerializer, MFAMethodSerializer, NPSSurveySerializer, UserAdminSerializer,
                              UserAdminSerializerFull, UserAdminUpdateSerializer, UserAwardAdminSerializer, UserAwardCreateSerializer,
                              UserOwnerSerializer, UserRegisterSerializer, UserUpdateSerializer, RankSerializer)
from user.throttling import RecoveryPasswordConfirmThrottle, RecoveryPasswordRequestThrottle
from user.utils import log_user_login_history, user_token_generator


class UserAPI(viewsets.ViewSet):
    permission_classes = (IsAuthenticated,)

    def get(self, request, *args, **kwargs):
        user = request.user
        user.company  # type:ignore

        # Omit is_staff field if not requested
        omit = request.query_params.get('omit', '').split(',')
        fields = request.query_params.get('fields', '').split(',')
        if 'is_staff' not in fields:
            omit.append('is_staff')

        serializer = UserOwnerSerializer(user, context={'request': request}, omit=omit)
        return Response(serializer.data)

    def get_permissions(self):
        if self.action == 'register':
            self.permission_classes = (AllowAny,)
        return super().get_permissions()

    def update(self, request, *args, **kwargs):
        data = request.data.copy()
        if data.get('cnpj'):
            data['cnpj'] = data['cnpj'].replace('.', '').replace('/', '').replace('-', '')

        user = request.user

        old_first_name, old_last_name = user.first_name, user.last_name

        serializer = UserUpdateSerializer(user, data=data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()

        if old_first_name != user.first_name or old_last_name != user.last_name:
            update_membersV2_user_info(user=user)

        user_data = UserOwnerSerializer(serializer.instance).data
        return Response(user_data, status=status.HTTP_200_OK)

    @action(detail=False, methods=['post'])
    def register(self, request):
        data = request.data.copy()
        if data.get('cnpj'):
            data['cnpj'] = data['cnpj'].replace('.', '').replace('/', '').replace('-', '')

        # Validate captcha, raises ValidationError if invalid
        cloudflarecheck(request=request, token=data.get('captchaToken', ''), action='register', userEmail=data.get('email'))

        serializer = UserRegisterSerializer(data=data)
        if serializer.is_valid(raise_exception=True):
            user: User = serializer.save()  # type:ignore

            MFAMethod.get_email_method(user, confirmed=True)

            send_crm_create_user_event.delay(user=user)

            refresh = RefreshToken.for_user(user)

            user_agent = self.request.META.get('HTTP_USER_AGENT')
            send_register_event_to_fb.delay(
                user=user,
                ip=get_client_ip(request=request),
                user_agent=user_agent,
            )

            return Response({
                'refresh': str(refresh),
                'access': str(refresh.access_token)  # type:ignore
            })

    @action(detail=False, methods=['post'])
    def changePassword(self, request):
        serializer = ChangePasswordSerializer(data=request.data)
        if serializer.is_valid(raise_exception=True):
            if not request.user.check_password(serializer.data.get("oldPassword")):  # type:ignore
                return Response({"oldPassword": ["Senha incorreta."]}, status=status.HTTP_400_BAD_REQUEST)

            request.user.set_password(serializer.data.get("newPassword"))  # type:ignore
            request.user.save()
            return Response({"success": True}, status=status.HTTP_200_OK)

    @action(detail=False, methods=['post'])
    def createPassword(self, request):
        serializer = CreatePasswordSerializer(data=request.data)
        if serializer.is_valid(raise_exception=True):
            if not request.user.has_usable_password():
                request.user.set_password(serializer.validated_data.get("password"))  # type:ignore
                request.user.save()
                return Response({"success": True}, status=status.HTTP_200_OK)
            return Response({"detail": "Senha já cadastrada."}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'])
    def userPicture(self, request):
        if not request.FILES.get('picture'):
            return Response({'detail': 'Imagem é obrigatória.'}, status=status.HTTP_400_BAD_REQUEST)

        request.user.picture.delete()
        request.user.picture = request.FILES.get('picture', request.user.picture)
        request.user.save()

        return Response({
            'success': True,
            'user': UserOwnerSerializer(request.user).data
        })

    @action(detail=False, methods=['post'])
    def validateEmailToken(self, request):
        token = request.data.get('token')
        user: User = request.user

        if user.emailValidated:
            return Response({'detail': 'E-mail já validado.'}, status=status.HTTP_400_BAD_REQUEST)

        if user.emailToken != token:
            return Response({'detail': 'Token inválido.'}, status=status.HTTP_400_BAD_REQUEST)

        if not user.emailTokenExpires or user.emailTokenExpires < timezone.now():
            return Response({'detail': 'Token expirado.'}, status=status.HTTP_400_BAD_REQUEST)

        user.emailToken = None
        user.emailTokenExpires = None
        user.emailValidated = True
        user.emailValidatedAt = timezone.now()
        user.save()

        return Response({'detail': 'E-mail verificado com sucesso.'}, status=status.HTTP_200_OK)

    @action(detail=False, methods=['post'])
    def validateWhatsappToken(self, request):
        token = request.data.get('token')
        user: User = request.user

        if settings.WHATSAPP_VALIDATION_ENABLED:
            if user.whatsappValidated:
                return Response({'detail': 'Whatsapp já validado.'}, status=status.HTTP_400_BAD_REQUEST)

            if user.whatsappToken != token:
                return Response({'detail': 'Token inválido.'}, status=status.HTTP_400_BAD_REQUEST)

            if not user.whatsappTokenExpires or user.whatsappTokenExpires < timezone.now():
                return Response({'detail': 'Token expirado.'}, status=status.HTTP_400_BAD_REQUEST)

            user.whatsappToken = None
            user.whatsappTokenExpires = None
            user.whatsappValidated = True
            user.whatsappValidatedAt = timezone.now()
            user.save()

        return Response({'detail': 'Whatsapp verificado com sucesso.'}, status=status.HTTP_200_OK)

class UserEmailTokenRequest(viewsets.ViewSet):
    permission_classes = (IsAuthenticated,)
    throttle_scope = 'emailToken'

    @action(detail=False, methods=['post'])
    def requestEmailToken(self, request):
        user: User = request.user
        token = ''.join([str(random.randint(0, 9)) for _ in range(6)])
        user.emailToken = token
        user.emailTokenExpires = timezone.now() + timedelta(minutes=15)
        user.save()

        email_sent = send_otp_token_email(
            user,
            'Verifique seu e-mail',
            'Este é o seu código de verificação de e-mail:',
            token,
        )

        if not email_sent:
            return Response({'detail': 'Erro ao enviar e-mail de verificação.'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        return Response({'detail': 'Código de verificação enviado com sucesso.'}, status=status.HTTP_200_OK)

class UserWhatsappTokenRequest(viewsets.ViewSet):
    permission_classes = (IsAuthenticated,)
    throttle_scope = 'whatsappToken'

    @action(detail=False, methods=['post'])
    def requestWhatsappToken(self, request):
        whatsapp_sdk = InfoBipWhatsApp()
        user: User = request.user

        if not user.cellphone:
            return Response({'detail': 'Número de Whatsapp não cadastrado.'}, status=status.HTTP_400_BAD_REQUEST)

        if user.whatsappValidated:
            return Response({'detail': 'Número de Whatsapp já validado.'}, status=status.HTTP_400_BAD_REQUEST)

        token = ''.join([str(random.randint(0, 9)) for _ in range(6)])
        user.whatsappToken = token
        user.whatsappTokenExpires = timezone.now() + timedelta(minutes=15)
        user.save()

        whatsapp_sdk.send_token(user, token)

        return Response({'detail': 'Código de verificação enviado com sucesso.'}, status=status.HTTP_200_OK)

class UserAdminList(generics.ListAPIView):
    permission_classes = [IsAdminUser]
    queryset = User.objects.all().select_related('_company')
    serializer_class = UserAdminSerializer
    filter_backends = [filters.SearchFilter, DjangoFilterBackend, filters.OrderingFilter]
    search_fields = [
        'username', 'first_name', 'last_name', 'email', 'cpf', 'cnpj', 'cellphone',
    ]
    filterset_class = UserAdminFilter
    ordering_fields = ['id', 'email', 'cpf', 'createdAt', 'updatedAt']

class UserAdminAPI(viewsets.ModelViewSet):
    permission_classes = [IsAdminUser]
    queryset = User.objects.all().select_related('_company')
    serializer_class = UserAdminSerializerFull

    def update(self, request, *args, **kwargs):
        user = self.get_object()

        serializer = UserAdminUpdateSerializer(
            user,
            data=request.data,
            partial=True
        )

        serializer.is_valid(raise_exception=True)
        serializer.save()

        return Response(self.get_serializer(user).data)

    @action(detail=True, methods=['post'])
    def impersonate(self, request, pk=None):
        user = self.get_object()
        refresh = RefreshToken.for_user(user)

        log_user_login_history(
            user=user,
            request=request,
            location=UserAccessLocations.IMPERSONATE.id,
        )

        # 🔽 Log da ação -> logou no usuário do suporte
        log_action(
            user=request.user,
            action='support_user_login',
            details=f'{request.user.email} fez impersonate de {user.email}',
            affected_user=user
        )

        return Response({
            'refresh': str(refresh),
            'access': str(refresh.access_token),  # type:ignore
        })

    @action(detail=True, methods=['post'])
    def send_new_password(self, request, pk=None):
        user = self.get_object()
        password = get_random_string(12)

        with transaction.atomic():
            user.set_password(password)
            user.save()

            email_sended = send_new_password_email(user, password)
            if not email_sended:
                raise Exception(f'Erro ao enviar e-mail de nova senha para {user.email}')

            # Log da ação -> enviou nova senha
            log_action(
                user=request.user,
                action='send_new_password',
                details=f'Nova senha enviada para o usuário {user.email}',
                affected_user=user
            )

        return Response(f'Nova senha enviado com sucesso para {user.email}')

    @action(detail=True, methods=['post'])
    def refund_request(self, request, pk=None):
        user = self.get_object()
        user.refundRequest = not user.refundRequest
        user.save()
        return Response(self.get_serializer(user).data, status=status.HTTP_200_OK)

    @action(detail=True, methods=['post'])
    def reset_mfa_throttle(self, request, pk=None):
        user = self.get_object()

        for mfa in user.mfa_methods.all():
            if hasattr(mfa.device, 'throttle_reset'):
                mfa.device.throttle_reset()
            mfa.reset_send_code_trottle()

        return Response(
            {'detail': 'Throttle resetado com sucesso.'},
            status=status.HTTP_200_OK
        )

class UserAdminSuperUserAPI(viewsets.ModelViewSet):
    permission_classes = [UserIsSuperUser]
    queryset = User.objects.all().select_related('_company')
    serializer_class = UserAdminSerializerFull

    @action(detail=True, methods=['post'], permission_classes=[UserIsSuperUser])
    def change_email(self, request, *args, **kwargs):
        new_email = request.data.get('email')

        exists = User.objects.filter(email__iexact=new_email).exists()
        if exists:
            return Response({'detail': 'E-mail já cadastrado.'}, status=status.HTTP_400_BAD_REQUEST)

        # Change user e-mail
        user = self.get_object()
        if user.username == user.email:
            user.username = new_email
        user.email = new_email

        # Change split account e-mail
        updateSplitEmail(user=user, new_email=new_email)

        update_membersV2_user_info(user=user)

        # Change customer e-mail
        customer = Customer.objects.filter(email__iexact=new_email).order_by('-createdAt').first()
        customer_history = None
        if customer is not None:
            customer.email = new_email
            customer_history = CustomerHistory(
                customer=customer,
                name=customer.name,
                email=new_email,
                phone=customer.phone,
                docNumber=customer.docNumber,
                docType=customer.docType,
                ip=customer.ip,
                fingerprint=customer.fingerprint,
            )

        with transaction.atomic():
            user.save()
            if customer is not None:
                customer.save()
            if customer_history is not None:
                customer_history.save()
        return Response({'detail': 'E-mail alterado com sucesso.'}, status=status.HTTP_200_OK)

class RankList(viewsets.ReadOnlyModelViewSet):
    permission_classes = (IsAuthenticated,)
    model = Rank
    queryset = Rank.objects.all()
    serializer_class = RankSerializer

class AwardAdminList(viewsets.ModelViewSet):
    permission_classes = (IsAdminUser,)
    model = Award
    queryset = Award.objects.all()
    serializer_class = AwardAdminSerializer
    filter_backends = [filters.SearchFilter, DjangoFilterBackend, filters.OrderingFilter]
    search_fields = ['title', 'target_sales', 'description']
    filterset_class = AwardAdminFilter
    ordering_fields = ['id', 'target_sales', 'title', 'status', 'description', 'createdAt', 'updatedAt']

class AwardAdminAPI(generics.RetrieveUpdateDestroyAPIView):
    permission_classes = (IsAdminUser,)
    model = Award
    queryset = Award.objects.all()
    serializer_class = AwardAdminSerializer

    def update(self, request, *args, **kwargs):
        kwargs['partial'] = True
        return super().update(request, *args, **kwargs)

class UserAwardAdminList(viewsets.ModelViewSet):
    permission_classes = (IsAdminUser,)
    model = UserAward
    queryset = UserAward.objects.all()
    serializer_class = UserAwardAdminSerializer
    filter_backends = [filters.SearchFilter, DjangoFilterBackend, filters.OrderingFilter]
    search_fields = ['user__email', 'award__title', 'shipping_method', 'tracking_code']
    filterset_class = UserAwardAdminFilter
    ordering_fields = ['id', 'user', 'award', 'status', 'state', 'tracking_code', 'shipping_method', 'createdAt', 'updatedAt']

    def create(self, request, *args, **kwargs):
        serializer = UserAwardCreateSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(self.serializer_class(serializer.instance).data, status=status.HTTP_200_OK)

class UserAwardAdminAPI(generics.RetrieveUpdateDestroyAPIView):
    permission_classes = (IsAdminUser,)
    model = UserAward
    serializer_class = UserAwardAdminSerializer
    queryset = UserAward.objects.all()

    def update(self, request, *args, **kwargs):
        kwargs['partial'] = True
        return super().update(request, *args, **kwargs)

class ColaboratorAPIView(viewsets.ModelViewSet):
    model = Colaborator
    serializer_class = ColaboratorSerializer
    scope = 'collaborators'

    def get_queryset(self):
        user_type = self.request.query_params.get('type')  # type:ignore
        user_email = self.request.query_params.get('userEmail')  # type:ignore
        status = self.request.query_params.get('status')  # type:ignore

        if user_type == 'owner':
            query_set = Colaborator.objects.filter(owner=self.request.user).order_by('-createdAt')
        elif user_type == 'collaborator':
            query_set = Colaborator.objects.filter(user=self.request.user).order_by('-createdAt')
        else:
            query_set = Colaborator.objects.filter(
                Q(user=self.request.user) | Q(owner=self.request.user)
            ).order_by('-createdAt')

        if user_email:
            query_set = query_set.filter(user__email=user_email)

        if status:
            query_set = query_set.filter(status__in=status.split(','))

        return query_set

    def create(self, request, *args, **kwargs):
        user = User.objects.filter(email__iexact=request.data.get('email')).first()
        if user is None:
            return Response({'detail': 'Usuário não cadastrado na Cakto.'}, status=status.HTTP_400_BAD_REQUEST)

        request.data['user'] = user.pk
        request.data['owner'] = request.user.id

        if self.request.user == user:
            return Response({'detail': 'Não é possível convidar a si mesmo.'}, status=status.HTTP_400_BAD_REQUEST)

        already_exists = Colaborator.objects.filter(owner=self.request.user, user=user).first()
        if already_exists is not None:
            return Response({'detail': 'Já existe um convite ou usuário com esse e-mail!'}, status=status.HTTP_400_BAD_REQUEST)

        serializer = ColaboratorCreateSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()

        send_notification(
            user,
            'Convite para Colaboração',
            f'Você foi convidado para ser um(a) colaborador(a) de {request.user.email}.',
        )

        serializer = ColaboratorSerializer(serializer.instance)

        return Response(serializer.data)

    def update(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = ColaboratorCreateSerializer(instance, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        if serializer.validated_data:
            serializer.validate_permissions(serializer.validated_data.get('permissions'))  # type: ignore
        serializer.save()

        serializer = ColaboratorSerializer(serializer.instance)

        return Response(serializer.data)

    def destroy(self, request, *args, **kwargs):
        instance = get_object_or_404(Colaborator, owner=self.request.user, pk=self.kwargs['pk'])
        instance.delete()
        return Response({'detail': 'Deletado com sucesso.'}, status=status.HTTP_200_OK)

    @action(detail=True, methods=['post'])
    def reject(self, request, pk=None):
        instance = self.get_object()
        instance.status = 'rejected'
        instance.save()
        send_notification(
            instance.owner,
            'Pedido de Colaboração rejeitado',
            f'{instance.user} rejeitou o seu pedido para colaboração.'
        )
        return Response({'status': 'Pedido rejeitado com sucesso'}, status=status.HTTP_200_OK)

    @action(detail=True, methods=['post'])
    def accept(self, request, pk=None):
        instance = self.get_object()
        instance.status = 'approved'
        instance.save()
        send_notification(
            instance.owner,
            'Pedido de Colaboração aceito',
            f'{instance.user} aceitou o seu pedido para colaboração.'
        )
        return Response({'status': 'Pedido aceito com sucesso'}, status=status.HTTP_200_OK)

class WithdrawalAPIView(generics.ListAPIView):
    scope = 'financial'

    def get(self, request, *args, **kwargs):
        split = SplitPay()
        response = split.getWithdraws(request.user.company.externalId, **{k: v for k, v in request.query_params.items()})
        if status.is_success(response.status_code):
            return Response(response.json(), status=response.status_code)
        if status.is_client_error(response.status_code):
            return Response(response.json(), status=response.status_code)
        raise Exception(response.text)

class PushNotificationTokenAPI(viewsets.GenericViewSet):
    permission_classes = (IsAuthenticated,)

    def post(self, request, *args, **kwargs):
        token = request.data.get('token')
        os_type = request.data.get('os')
        device_id = request.data.get('device_id')

        fcm_device: FCMDevice
        fcm_device, created = FCMDevice.objects.get_or_create(
            registration_id=token,
            defaults={
                'user': request.user,
                'device_id': device_id,
                'type': os_type,
                'active': True,
            }
        )

        if not created:
            if request.user != fcm_device.user:
                fcm_device.mfa_methods.update(fcm_device=None)

            fcm_device.device_id = device_id
            fcm_device.user = request.user
            fcm_device.type = os_type
            fcm_device.active = True
            fcm_device.save(update_fields=['device_id', 'user', 'type', 'active'])

        return Response({
            'detail': 'Token salvo com sucesso.'
        }, status=status.HTTP_201_CREATED)

    @action(detail=False, methods=['post'])
    def logout(self, request, *args, **kwargs):
        FCMDevice.objects.filter(registration_id=request.data.get('token')).delete()
        return Response({'detail': 'Token removido com sucesso.'}, status=status.HTTP_200_OK)

class RequestRecoveryPasswordTokenAPI(viewsets.ViewSet):
    permission_classes = (AllowAny,)
    throttle_classes = [RecoveryPasswordRequestThrottle]

    def request_token(self, request, *args, **kwargs):
        validate_recaptcha(token=request.data.get('recaptchaToken', ''), action='recovery_password', userEmail=request.data.get('email'))
        user = get_object_or_404(User, email__iexact=request.data.get('email'))

        with transaction.atomic():
            self.process_password_recovery_gen(request, user)

        return Response({'detail': 'E-mail enviado com sucesso.'})

    def process_password_recovery_gen(self, request, user: User):
        chars = string.ascii_letters + string.digits
        to_sort = [random.choice(chars) for _ in range(5)] + [random.choice(string.digits)]
        token = ''.join(random.sample(to_sort, len(to_sort)))

        user.recovery_password_token = token
        user.recovery_password_token_expires = timezone.now() + timedelta(minutes=30)
        user.save(update_fields=['recovery_password_token', 'recovery_password_token_expires'])

        email_sent = send_otp_token_email(
            user=user,
            title=f'Seu token de recuperação é {token}',
            message='Este é o token de recuperação:',
            token=token
        )
        if not email_sent:
            raise Exception('Erro ao enviar e-mail de recuperação de senha.')

        RecoveryTokenHistory.objects.create(
            user=user,
            token=token,
            token_expires_at=user.recovery_password_token_expires,
            event_type='created',
            requester_ip_address=get_client_ip(request=request),
            requester_user_agent=request.META.get('HTTP_USER_AGENT', ''),
            timestamp=timezone.now(),
        )

class ConfirmRecoveryPasswordTokenAPI(viewsets.ViewSet):
    permission_classes = (AllowAny,)
    throttle_classes = [RecoveryPasswordConfirmThrottle]

    @action(detail=False, methods=['post'])
    def change_password(self, request):
        token = request.data.get('token')
        user = get_object_or_404(User, recovery_password_token=token, recovery_password_token_expires__gte=timezone.now())

        if not user.recovery_password_token_expires:
            return Response({'detail': 'Token inválido.'}, status=status.HTTP_400_BAD_REQUEST)

        if user.recovery_password_token_expires < timezone.now():
            return Response({'detail': 'Token expirado.'}, status=status.HTTP_400_BAD_REQUEST)

        with transaction.atomic():
            self.process_password_update(request, token, user)

        return Response({'detail': 'Senha alterada com sucesso.'})

    def process_password_update(self, request, token, user):
        RecoveryTokenHistory.objects.create(
            user=user,
            token=token,
            token_expires_at=user.recovery_password_token_expires,
            event_type='used',
            requester_ip_address=get_client_ip(request=request),
            requester_user_agent=request.META.get('HTTP_USER_AGENT', ''),
            timestamp=timezone.now(),
        )

        user.set_password(request.data.get('password'))
        user.recovery_password_token = None
        user.recovery_password_token_expires = None
        user.save()

class OTPAccessAPI(views.APIView):
    permission_classes = (AllowAny,)

    def get(self, request, *args, **kwargs):
        fingerprint = request.query_params.get('fingerprint')
        if not fingerprint:
            return Response({'detail': 'fingerprint é obrigatório.'}, status=status.HTTP_400_BAD_REQUEST)

        customer_history = CustomerHistory.objects.filter(fingerprint=fingerprint).order_by('-createdAt').first()
        if not customer_history:
            return Response({'detail': 'Token inválido.'}, status=status.HTTP_400_BAD_REQUEST)

        user = User.objects.filter(email__iexact=customer_history.email).first()
        if user is None or not user.has_otp_token:
            return Response({'detail': 'Token inválido.'}, status=status.HTTP_400_BAD_REQUEST)

        return Response({
            'otpAccessToken': user.recovery_password_token,
            'expires_in': user.recovery_password_token_expires
        },
            status=status.HTTP_200_OK
        )

    def post(self, request, *args, **kwargs):
        token = request.data.get('token')
        user = User.objects.filter(recovery_password_token=token).first()

        if user is None or not user.has_otp_token:
            return Response({'detail': 'Token inválido.'}, status=status.HTTP_400_BAD_REQUEST)

        user.recovery_password_token = None
        user.recovery_password_token_expires = None
        user.save()

        refresh = RefreshToken.for_user(user)

        log_user_login_history(
            user=user,
            request=request,
            location=UserAccessLocations.OTP_TOKEN.id,
        )

        return Response({
            'refresh': str(refresh),
            'access': str(refresh.access_token),  # type:ignore
            'email': user.email,
        })

class MFAMethodActivationView(views.APIView):
    def post(self, request, *args, **kwargs):
        serializer = MFAGeneralSerializer(required_fields=['method'], data=request.data)
        serializer.is_valid(raise_exception=True)

        method = serializer.validated_data.get('method')  # type:ignore
        fcm_device_id = serializer.validated_data.get('fcm_device_id', '')  # type:ignore

        if method == MFAType.EMAIL.id:
            return self.activate_email()

        if method == MFAType.TOTP.id:
            return self.activate_totp()

        if method == MFAType.APP_CAKTO.id:
            return self.activate_app_cakto(fcm_device_id=fcm_device_id)

        return Response({'detail': 'Método inválido.'}, status=status.HTTP_400_BAD_REQUEST)

    def activate_email(self):
        user: User = self.request.user  # type:ignore

        already_active = MFAMethod.objects.filter(
            user=user, confirmed=True,
            type=MFAType.EMAIL.id
        ).exists()

        if already_active:
            return Response({'detail': f'Autenticação de dois fatores por {MFAType.EMAIL.description} já ativado.'}, status=status.HTTP_400_BAD_REQUEST)

        mfa_method = MFAMethod.get_email_method(user)

        email_sent = mfa_method.send_email_token(user, 'Código de Verificação - Cakto', 'Este é o seu código de verificação:')

        if not email_sent:
            raise Exception('Erro ao enviar e-mail de confirmação de dois fatores.')

        return Response({'detail': 'E-mail de verificação enviado com sucesso.'}, status=status.HTTP_200_OK)

    def activate_totp(self):
        user: User = self.request.user  # type:ignore

        already_active = MFAMethod.objects.filter(
            user=user,
            confirmed=True,
            type=MFAType.TOTP.id
        ).exists()

        if already_active:
            return Response(
                {'detail': f'Autenticação de dois fatores por {MFAType.TOTP.description} já ativado.'},
                status=status.HTTP_400_BAD_REQUEST
            )

        MFAMethod.get_email_method(user, confirmed=True, primary=False)

        mfa_method = MFAMethod.get_totp_method(user)

        config_url = mfa_method.get_totp_config_url()

        return Response({'config_url': config_url}, status=status.HTTP_200_OK)

    def activate_app_cakto(self, fcm_device_id: str):
        user: User = self.request.user  # type:ignore

        mfa_existent = MFAMethod.objects.filter(
            user=user,
            confirmed=True,
            type=MFAType.APP_CAKTO.id
        ).first()

        if mfa_existent:
            if mfa_existent.fcm_device and mfa_existent.fcm_device.device_id == fcm_device_id:  # type:ignore
                mfa_existent.delete()
            else:
                return Response(
                    {'detail': f'Autenticação de dois fatores por {MFAType.APP_CAKTO.description} já ativado.'},
                    status=status.HTTP_400_BAD_REQUEST
                )

        MFAMethod.get_email_method(user, confirmed=True, primary=False)

        mfa_method = MFAMethod.get_app_cakto_method(user)

        config_url = mfa_method.get_totp_config_url()

        return Response({'config_url': config_url}, status=status.HTTP_200_OK)

class MFAMethodConfirmView(views.APIView):
    def post(self, request, *args, **kwargs):
        self.serializer = MFAGeneralSerializer(required_fields=['method', 'code'], data=request.data)
        self.serializer.is_valid(raise_exception=True)

        user: User = self.request.user  # type:ignore
        method = self.serializer.validated_data.get('method')  # type:ignore
        code = self.serializer.validated_data.get('code', '')  # type:ignore
        fcm_device_id = self.serializer.validated_data.get('fcm_device_id', '')  # type:ignore

        if method == MFAType.EMAIL.id:
            return self.confirm_email(user=user, code=code)

        elif method == MFAType.TOTP.id:
            return self.confirm_totp(user=user, code=code)

        elif method == MFAType.APP_CAKTO.id:
            return self.confirm_app_cakto(
                user=user,
                code=code,
                fcm_device_id=fcm_device_id
            )

        return Response({'detail': 'Método inválido.'}, status=status.HTTP_400_BAD_REQUEST)

    def confirm_email(self, user: User, code: str):
        mfa_method = MFAMethod.objects.filter(user=user, type=MFAType.EMAIL.id, confirmed=False).first()
        if not mfa_method:
            return Response({'detail': f'Autenticação de dois fatores {MFAType.EMAIL.description} não existente ou já ativa.'}, status=status.HTTP_400_BAD_REQUEST)

        if not mfa_method.verify_token(code):  # type:ignore
            return Response({'detail': 'Token inválido.'}, status=status.HTTP_400_BAD_REQUEST)

        user.emailValidated = True
        user.save(update_fields=['emailValidated'])

        mfa_method.confirmed = True
        mfa_method.save(update_fields=['confirmed'])

        return Response({'detail': 'Autenticação de dois fatores ativado com sucesso.'}, status=status.HTTP_200_OK)

    def confirm_totp(self, user: User, code: str):
        mfa_method = MFAMethod.objects.filter(user=user, type=MFAType.TOTP.id, confirmed=False).first()
        if not mfa_method:
            return Response({'detail': f'Autenticação de dois fatores por {MFAType.TOTP.description} não existente ou já ativa.'}, status=status.HTTP_400_BAD_REQUEST)

        if not mfa_method.verify_token(code):  # type:ignore
            return Response({'detail': 'Token inválido.'}, status=status.HTTP_400_BAD_REQUEST)

        mfa_method.confirmed = True
        mfa_method.save(update_fields=['confirmed'])

        return Response({'detail': 'Autenticação de dois fatores ativado com sucesso.'}, status=status.HTTP_200_OK)

    def confirm_app_cakto(self, user: User, code: str, fcm_device_id: str | None = None):
        if not fcm_device_id:
            return Response({'detail': '"fcm_device_id" é obrigatório.'}, status=status.HTTP_400_BAD_REQUEST)

        mfa_method = MFAMethod.objects.filter(user=user, type=MFAType.APP_CAKTO.id, confirmed=False).first()
        if not mfa_method:
            return Response({'detail': f'Autenticação de dois fatores por {MFAType.APP_CAKTO.description} não existente ou já ativa.'}, status=status.HTTP_400_BAD_REQUEST)

        if not mfa_method.verify_token(code):  # type:ignore
            return Response({'detail': 'Token inválido.'}, status=status.HTTP_400_BAD_REQUEST)

        fcm_device = FCMDevice.objects.filter(user=user, device_id=fcm_device_id).first()
        if not fcm_device:
            return Response({'detail': 'Dispositivo FCM não encontrado.'}, status=status.HTTP_400_BAD_REQUEST)

        mfa_method.confirmed = True
        mfa_method.fcm_device = fcm_device
        mfa_method.save(update_fields=['confirmed', 'fcm_device'])

        notify_fcm_mfa_action(
            devices=FCMDevice.objects.filter(user=user),
            action='confirm',
            fcm_device_id=fcm_device_id,
        )

        return Response({'detail': 'Autenticação de dois fatores ativado com sucesso.'}, status=status.HTTP_200_OK)

class MFADeactivateView(views.APIView):
    def post(self, request, *args, **kwargs):
        serializer = MFAGeneralSerializer(required_fields=['method', 'code'], data=request.data)
        serializer.is_valid(raise_exception=True)

        method = serializer.validated_data.get('method')  # type:ignore
        code = serializer.validated_data.get('code', '')  # type:ignore

        mfa_method = MFAMethod.objects.filter(user=request.user, type=method, confirmed=True).first()

        self.validate_deactivation(request.user, mfa_method, code)

        is_primary_method = mfa_method.primary  # type:ignore

        if mfa_method.type == MFAType.APP_CAKTO.id and mfa_method.fcm_device:  # type:ignore
            notify_fcm_mfa_action(
                devices=FCMDevice.objects.filter(user=request.user),
                action='deactivate',
                fcm_device_id=mfa_method.fcm_device.device_id,  # type:ignore
            )

        mfa_method.delete()  # type:ignore

        if is_primary_method:
            self.change_primary_mfa_method(request)

        return Response({'detail': 'Método de autenticação dois fatores desativado com sucesso.'}, status=status.HTTP_200_OK)

    def change_primary_mfa_method(self, request):
        other_active_mfa = MFAMethod.objects.filter(user=request.user, confirmed=True, primary=False).first()
        if other_active_mfa:
            other_active_mfa.primary = True
            other_active_mfa.save(update_fields=['primary'])

    def validate_deactivation(self, user: User, mfa_method: MFAMethod | None, code: str) -> None:
        if not mfa_method:
            raise ValidationError({'detail': 'Método de autenticação dois fatores não ativo.'})

        if mfa_method.email_device:
            raise ValidationError({'detail': f'Método de autenticação dois fatores por {MFAType.EMAIL.description} não pode ser desativado.'})

        is_app_cakto = mfa_method.type == MFAType.APP_CAKTO.id

        if (is_app_cakto and not mfa_method.verify_token(code)) or is_app_cakto is False:
            email_method = MFAMethod.get_email_method(user, confirmed=True)
            if not email_method.verify_token(code):
                raise ValidationError({'detail': 'Token inválido.'})

class MFAListAPIView(generics.ListAPIView):
    serializer_class = MFAMethodSerializer

    def get_queryset(self):
        return MFAMethod.objects.filter(user=self.request.user, confirmed=True).only('type', 'primary', 'fcm_device').distinct()

class MFASendCodeView(views.APIView):
    permission_classes = (AllowAny,)
    throttle_scope = 'MFASendToken'

    def post(self, request):
        required_fields = ['method']
        required_fields.append('email') if not request.user.is_authenticated else None

        serializer = MFAGeneralSerializer(required_fields=required_fields, data=request.data)
        serializer.is_valid(raise_exception=True)

        method = serializer.validated_data.get('method')  # type:ignore

        if not request.user.is_authenticated:
            email = serializer.validated_data.get('email')  # type:ignore
            mfa_method = MFAMethod.objects.filter(user__email=email, type=method, confirmed=True).first()
        else:
            mfa_method = MFAMethod.objects.filter(user=request.user, type=method, confirmed=True).first()

        if not mfa_method:
            return Response({'detail': 'Método de autenticação dois fatores não ativo.'}, status=status.HTTP_400_BAD_REQUEST)

        if mfa_method.type == MFAType.TOTP.id:
            return Response({'detail': f'Dispositivo {MFAType.TOTP.description} não suporta envio de código.'}, status=status.HTTP_400_BAD_REQUEST)

        user_agent = request.headers.get('User-Agent', '')
        if mfa_method.type == MFAType.APP_CAKTO.id and 'CaktoAPP' in user_agent:
            return Response({"detail": f'Dispositivo {MFAType.APP_CAKTO.description} não suporta envio de código.'}, status=status.HTTP_400_BAD_REQUEST)

        token_sent = mfa_method.send_token()

        if not token_sent:
            return Response({'detail': 'Erro ao enviar token de autenticação de dois fatores.'}, status=status.HTTP_400_BAD_REQUEST)

        if mfa_method.type == MFAType.APP_CAKTO.id and 'CaktoAPP' not in user_agent:
            return Response({'detail': 'Digite o iToken do seu app cakto para completar o login.'}, status=status.HTTP_200_OK)

        return Response({'detail': 'Código enviado com sucesso.'}, status=status.HTTP_200_OK)

class MFAChangePrimaryView(views.APIView):
    def post(self, request):
        serializer = MFAGeneralSerializer(required_fields=['method', 'currentPrimaryCode'], data=request.data)
        serializer.is_valid(raise_exception=True)

        method = serializer.validated_data.get('method')  # type:ignore
        currentPrimaryCode = serializer.validated_data.get('currentPrimaryCode', '')  # type:ignore

        mfa_method = MFAMethod.objects.filter(user=request.user, confirmed=True, primary=True).first()

        if not mfa_method:
            return Response({'detail': 'Autenticação dois fatores não ativa.'}, status=status.HTTP_400_BAD_REQUEST)

        is_code_correct = mfa_method.verify_token(currentPrimaryCode)
        if not is_code_correct:
            return Response({'detail': 'Código inválido.'}, status=status.HTTP_400_BAD_REQUEST)

        new_primary = MFAMethod.objects.filter(user=request.user, confirmed=True, type=method).first()
        if not new_primary:
            return Response({'detail': 'Método de autenticação dois fatores não encontrado.'}, status=status.HTTP_400_BAD_REQUEST)

        with transaction.atomic():
            mfa_method.primary = False
            mfa_method.save(update_fields=['primary'])
            new_primary.primary = True
            new_primary.save(update_fields=['primary'])

        return Response({'detail': 'Método de autenticação dois fatores padrão alterado com sucesso.'}, status=status.HTTP_200_OK)

class MFAStepMixin(views.APIView, ABC):
    permission_classes = (AllowAny,)

    @abstractmethod
    def _successful_authentication_response(self, user: User) -> Response:
        raise NotImplementedError

class MFAFirstStepLoginMixin(MFAStepMixin, ABC):
    def get_serializer(self, *args, **kwargs):
        return TokenObtainPairSerializer(*args, **kwargs)

    def post(self, request: views.Request, *args, **kwargs) -> Response:
        serializer = self.get_serializer(data=request.data)

        try:
            serializer.is_valid(raise_exception=True)
        except TokenError as e:
            raise InvalidToken(e.args[0])

        user: User = serializer.user  # type:ignore

        user_agent = request.headers.get('User-Agent')

        is_customer_only = user.is_producer is False and user.is_customer
        if is_customer_only and user_agent and 'CaktoAPP' in user_agent:
            return Response({
                'code': 'producer_only_app',
                'detail': 'Apenas produtores podem acessar o aplicativo. Utilize a plataforma web.',
                'is_customer': user.is_customer,
                'is_producer': user.is_producer,
            }, status=status.HTTP_400_BAD_REQUEST)

        mfa_methods = MFAMethod.objects.filter(user=user, confirmed=True)

        if not mfa_methods:
            return self._successful_authentication_response(user=user)

        if len(mfa_methods) == 1:
            email_method = mfa_methods.first()
            email_method.send_token()  # type:ignore

        return Response(
            data={
                "ephemeral_token": user_token_generator.make_token(user),
                "methods": MFAMethodSerializer(mfa_methods, many=True).data,
            }
        )

class MFASecondStepMixin(MFAStepMixin, ABC):
    def post(self, request) -> Response:
        serializer = MFAGeneralSerializer(required_fields=['ephemeral_token', 'code'], data=request.data)
        serializer.is_valid(raise_exception=True)

        ephemeral_token = serializer.validated_data.get('ephemeral_token')  # type:ignore
        code = serializer.validated_data.get('code')  # type:ignore

        user = user_token_generator.check_token(user=None, token=ephemeral_token)  # type:ignore

        self.is_authenticated(user=user, code=code)  # type:ignore

        user_agent = request.headers.get('User-Agent')
        if user_agent and 'CaktoAPP' in user_agent:
            send_login_event_to_fb.delay(
                user=user,
                ip=get_client_ip(request=request),
                user_agent=self.request.META.get('HTTP_USER_AGENT', ''),
            )

        return self._successful_authentication_response(user=user)  # type:ignore

    def is_authenticated(self, user: User | None, code: str) -> None:
        if user is None:
            raise InvalidToken({'detail': 'Token inválido.'})

        user_methods = (
            MFAMethod.objects
            .filter(user=user, confirmed=True)
            .annotate(
                failure_count=Case(
                    When(email_device__throttling_failure_count__isnull=False, then=F('email_device__throttling_failure_count')),
                    When(totp_device__throttling_failure_count__isnull=False, then=F('totp_device__throttling_failure_count')),
                    When(app_cakto_device__throttling_failure_count__isnull=False, then=F('app_cakto_device__throttling_failure_count')),
                    default=Value(0),
                    output_field=IntegerField(),
                )
            )
            .order_by('failure_count')
        )

        for mfa_method in user_methods:
            if mfa_method.verify_token(code):
                self.validated_mfa_method = mfa_method
                return
        raise InvalidToken({'detail': 'Token inválido.'})

class UserLoginJWT(MFAStepMixin, ABC):
    def _successful_authentication_response(self, user: User) -> Response:
        refresh = RefreshToken.for_user(user)

        data = {
            'refresh': str(refresh),
            'access': str(refresh.access_token),  # type:ignore
        }

        validated_mfa_method = getattr(self, 'validated_mfa_method', None)
        if validated_mfa_method and isinstance(validated_mfa_method, MFAMethod):
            data['validated_method'] = MFAMethodSerializer(validated_mfa_method).data  # type:ignore

        log_user_login_history(user=user, request=self.request, location=UserAccessLocations.LOGIN.id)  # type:ignore

        return Response(data)

class MFALoginFirstStepView(UserLoginJWT, MFAFirstStepLoginMixin):
    pass

class MFALoginSecondStepView(UserLoginJWT, MFASecondStepMixin):
    pass

class NPSSurveyAPI(viewsets.ViewSet):
    permission_classes = (IsAuthenticated,)

    @action(detail=False, methods=['post'])
    def post(self, request):
        user = request.user
        serializer = CreateNPSSurveySerializer(data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)

        serializer.save(user=user)
        user.next_nps_survey_date = next_nps_survey_date()
        user.save(update_fields=['next_nps_survey_date'])

        nps_data = NPSSurveySerializer(serializer.instance).data

        nps_data['next_nps_survey_date'] = user.next_nps_survey_date  # type:ignore

        return Response(nps_data, status=status.HTTP_201_CREATED)

    @action(detail=False, methods=['post'])
    def skip_survey(self, request):
        user = request.user
        user.next_nps_survey_date = timezone.now() + timezone.timedelta(days=7)
        user.save(update_fields=['next_nps_survey_date'])

        return Response({
            'detail': 'Pesquisa de satisfação pulada com sucesso.',
            'next_nps_survey_date': user.next_nps_survey_date.isoformat()
        })

class RequestLoginLinkEmailView(views.APIView):
    permission_classes = [AllowAny]
    throttle_scope = 'requestLoginLinkEmail'

    def post(self, request, *args, **kwargs):
        user = get_object_or_404(
            User.objects.filter(email=request.data.get('email'))
        )

        if user.is_producer:
            raise ValidationError(
                {'detail': 'Usuário com vendas deve realizar login como "Produtor".'}
            )

        email_sent = send_customer_login_link_email(
            user=user,
            request=request,
        )

        if not email_sent:
            raise Exception('Error sending Login Link Email')

        return Response("Email enviado com sucesso.")

class AppReviewAPI(views.APIView):
    permission_classes = (IsAuthenticated,)

    def post(self, request):
        user = request.user
        user.app_reviewed_at = timezone.now()
        user.save()

        return Response({
            'detail': 'Avaliação na loja salva com sucesso.'
        }, status=status.HTTP_200_OK)
