from django.core.management.base import BaseCommand
from financial.models import Company
from gateway.sdk.splitpay import SplitPay

from rest_framework import status
from user.models import User


class Command(BaseCommand):
    help = 'Create Split Accounts for all users that haven\'t it yet'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Creating Accounts...'))
        errors = []
        for user in User.objects.all():
            company, _ = Company.objects.get_or_create(user=user)
            if not company.externalId:
                split = SplitPay()
                response = split.createAccount(email=company.user.email)

                if status.is_success(response.status_code):
                    data = response.json()
                    if data.get('id'):
                        company.externalId = data.get('id')
                        company.save()
                else:
                    message = {
                        'error': f'Error creating account for {user.email}',
                        'split_response': response.text
                    }
                    errors.append(message)

        if not errors:
            self.stdout.write(self.style.SUCCESS('Successfully created the accounts'))
        else:
            self.stdout.write(self.style.WARNING('Command finished with errors:'))
            for error in errors:
                self.stdout.write(self.style.ERROR(error))
