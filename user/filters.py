from django_filters import rest_framework as filters

from product.filters import CharFieldInFilter
from user.models import Award, User, UserAward


class UserAdminFilter(filters.FilterSet):
    id = filters.CharFilter(lookup_expr='exact')
    email = filters.CharFilter(lookup_expr='icontains')
    threeDsRetryEnabled = filters.BooleanFilter()

    class Meta:
        model = User
        fields = {
            'createdAt': ['exact', 'gt', 'lt', 'lte', 'gte'],
            'updatedAt': ['exact', 'gt', 'lt', 'lte', 'gte'],
        }

class AwardAdminFilter(filters.FilterSet):
    id = filters.CharFilter(lookup_expr='exact')
    title = filters.CharFilter(lookup_expr='icontains')
    status = CharFieldInFilter()

    class Meta:
        model = Award
        fields = {
            'target_sales': ['exact', 'gt', 'lt', 'lte', 'gte'],
            'createdAt': ['exact', 'gt', 'lt', 'lte', 'gte'],
            'updatedAt': ['exact', 'gt', 'lt', 'lte', 'gte'],
        }

class UserAwardAdminFilter(filters.FilterSet):
    id = filters.CharFilter(lookup_expr='exact')
    user = filters.CharFilter(field_name='user', lookup_expr='exact')
    userEmail = filters.CharFilter(field_name='user__email', lookup_expr='icontains')
    award = filters.CharFilter(field_name='award', lookup_expr='exact')
    awardTitle = filters.CharFilter(field_name='award__title', lookup_expr='icontains')
    status = CharFieldInFilter()
    tracking_code = filters.CharFilter(lookup_expr='icontains')
    shipping_method = filters.CharFilter(lookup_expr='icontains')

    class Meta:
        model = UserAward
        fields = {
            'createdAt': ['exact', 'gt', 'lt', 'lte', 'gte'],
            'updatedAt': ['exact', 'gt', 'lt', 'lte', 'gte'],
            'cep': ['iexact'],
            'city': ['icontains'],
            'state': ['iexact'],
        }
