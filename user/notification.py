from typing import Iterable, Literal

from babel.numbers import format_currency
from fcm_django.models import FCMDevice
from firebase_admin.messaging import (AndroidConfig, AndroidNotification, APNSConfig, APNSPayload, Aps, Message,
                                      Notification)

from notifications.models import Notification as Notify
from user.models import User
topic = 'br.com.cakto'

def send_notification(user, title, message, amount=None, url=None, data=None, sound=True):
    devices = FCMDevice.objects.filter(user=user)

    if amount is not None:
        convertAmountToBrl = format_currency(amount, 'BRL', locale='pt_BR')

        title = title.replace('{amount}', convertAmountToBrl)
        message = message.replace('{amount}', convertAmountToBrl)

    Notify.objects.create(
        user=user,
        title=title,
        description=message,
        amount=amount,
        link=url
    )

    if not devices:
        return

    android = AndroidConfig(
        notification=AndroidNotification(
            sound='cashcakto.wav'
        )
    ) if sound else None

    apns = APNSConfig(
        payload=APNSPayload(
            aps=Aps(
                sound='cashcakto.wav'
            )
        )
    ) if sound else None

    devices.send_message(
        Message(
            notification=Notification(title=title, body=message),
            data=data,
            android=android,
            apns=apns,
        )
    )

def notify_fcm_mfa_action(devices: Iterable[FCMDevice], action: Literal['confirm', 'deactivate'], fcm_device_id: str):
    """Notify FCM devices about a app_cakto MFAMethod action."""
    devices.send_message(
        Message(
            data={
                'action': action,
                'fcm_device_id': fcm_device_id,
            }
        )
    )


def send_device_notification(user: User, device: FCMDevice, title: str, message: str, data: dict):
    Notify.objects.create(user=user, title=title, description=message)

    device.send_message(
        Message(
            notification=Notification(title=title, body=message),
            data=data,
        )
    )
