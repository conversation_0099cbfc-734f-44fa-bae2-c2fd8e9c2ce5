from django.db.models.signals import m2m_changed
from django.dispatch import receiver
from user.models import User
from product.models import PaymentMethod

@receiver(m2m_changed, sender=User.experimental_features.through)
def experimental_features_changed(sender, instance, action, reverse, model, pk_set, **kwargs):
    """
    Handles changes to the User.experimental_features ManyToManyField.
    """
    if action == "pre_remove":
        for feature_id in pk_set:
            payment_method = PaymentMethod.get_payment_method_instance(feature_id, user=instance)

            if not payment_method:
                continue

            for product in instance.product_set.all():
                product.paymentMethods.remove(payment_method)
                product.paymentsOrder = [payment_method for payment_method in product.paymentsOrder or [] if payment_method != feature_id]
                product.invalidate_cache()
