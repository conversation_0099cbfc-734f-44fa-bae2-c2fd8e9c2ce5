# Generated by Django 4.2.5 on 2023-10-01 12:18

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0003_withdrawal'),
    ]

    operations = [
        migrations.CreateModel(
            name='Colaborator',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('pending', 'Pendente'), ('approved', 'Aprovado'), ('rejected', 'Rejeitado')], default='pending', max_length=255, verbose_name='Status')),
                ('createdAt', models.DateTimeField(auto_now_add=True, verbose_name='Criado em')),
                ('owner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='colaborator_owner', to=settings.AUTH_USER_MODEL)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='colaborators', to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
