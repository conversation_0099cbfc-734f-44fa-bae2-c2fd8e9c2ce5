# Generated by Django 4.2.5 on 2024-03-11 18:50

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0024_alter_user_boleto_alter_user_credit_card_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='user',
            name='antecipation',
        ),
        migrations.RemoveField(
            model_name='user',
            name='boleto',
        ),
        migrations.RemoveField(
            model_name='user',
            name='caktoFee',
        ),
        migrations.RemoveField(
            model_name='user',
            name='caktoFixedFee',
        ),
        migrations.RemoveField(
            model_name='user',
            name='credit_card',
        ),
        migrations.RemoveField(
            model_name='user',
            name='installments1',
        ),
        migrations.RemoveField(
            model_name='user',
            name='installments10',
        ),
        migrations.RemoveField(
            model_name='user',
            name='installments11',
        ),
        migrations.RemoveField(
            model_name='user',
            name='installments12',
        ),
        migrations.RemoveField(
            model_name='user',
            name='installments2',
        ),
        migrations.RemoveField(
            model_name='user',
            name='installments3',
        ),
        migrations.RemoveField(
            model_name='user',
            name='installments4',
        ),
        migrations.RemoveField(
            model_name='user',
            name='installments5',
        ),
        migrations.RemoveField(
            model_name='user',
            name='installments6',
        ),
        migrations.RemoveField(
            model_name='user',
            name='installments7',
        ),
        migrations.RemoveField(
            model_name='user',
            name='installments8',
        ),
        migrations.RemoveField(
            model_name='user',
            name='installments9',
        ),
        migrations.RemoveField(
            model_name='user',
            name='pix',
        ),
        migrations.RemoveField(
            model_name='user',
            name='stuck',
        ),
        migrations.RemoveField(
            model_name='user',
            name='stuckPercentage',
        ),
    ]
