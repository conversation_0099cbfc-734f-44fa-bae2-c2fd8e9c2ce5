# Generated by Django 4.2.5 on 2024-07-25 01:59

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0028_user_refundrequest'),
    ]

    operations = [
        migrations.AddField(
            model_name='useraward',
            name='cep',
            field=models.CharField(blank=True, max_length=8, null=True),
        ),
        migrations.AddField(
            model_name='useraward',
            name='city',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='useraward',
            name='complement',
            field=models.Char<PERSON>ield(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='useraward',
            name='neighborhood',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='useraward',
            name='number',
            field=models.Char<PERSON>ield(blank=True, max_length=255, null=True),
        ),
        migrations.Add<PERSON>ield(
            model_name='useraward',
            name='state',
            field=models.Char<PERSON><PERSON>(blank=True, max_length=2, null=True),
        ),
        migrations.AddField(
            model_name='useraward',
            name='street',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
    ]
