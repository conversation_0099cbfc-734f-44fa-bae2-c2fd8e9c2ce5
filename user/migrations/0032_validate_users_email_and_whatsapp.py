# Generated by Django 4.2.5 on 2024-08-23 18:30

from django.db import migrations
from django.utils import timezone

def validate_users_email_and_whatsapp(apps, schema_editor):
    User = apps.get_model('user', 'User')
    now = timezone.now()
    User.objects.update(emailValidated=True, whatsappValidated=True, emailValidatedAt=now, whatsappValidatedAt=now)

class Migration(migrations.Migration):

    dependencies = [
        ('user', '0031_user_manychat_id_user_phonecountrycode'),
    ]

    operations = [
        migrations.RunPython(validate_users_email_and_whatsapp, reverse_code=migrations.RunPython.noop),
    ]
