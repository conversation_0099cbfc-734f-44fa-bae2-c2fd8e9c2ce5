# Generated by Django 4.2.5 on 2023-11-28 17:22

from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0011_alter_colaborator_options_colaborator_permissions'),
    ]

    operations = [
        migrations.AddField(
            model_name='user',
            name='antecipateCardIncomes',
            field=models.BooleanField(default=False, verbose_name='Antecipar recebimento de cartões'),
        ),
        migrations.AddField(
            model_name='user',
            name='caktoFee',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.05000000000000000277555756156289135105907917022705078125'), max_digits=10, verbose_name='Taxa Cakto'),
        ),
        migrations.AddField(
            model_name='user',
            name='cardAntecipationFee',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.0200000000000000004163336342344337026588618755340576171875'), max_digits=10, verbose_name='Taxa de antecipação'),
        ),
        migrations.AddField(
            model_name='user',
            name='fixedFee',
            field=models.FloatField(default=1.0, verbose_name='Taxa fixa em R$'),
        ),
    ]
