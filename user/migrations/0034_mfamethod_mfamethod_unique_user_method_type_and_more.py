# Generated by Django 4.2.5 on 2024-11-01 17:09

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('otp_email', '0006_add_timestamps'),
        ('otp_totp', '0003_add_timestamps'),
        ('user', '0033_user_actualplatform_user_biggerpain_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='MFAMethod',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('type', models.CharField(choices=[('email', 'Email'), ('totp', 'TOTP')], max_length=255)),
                ('primary', models.BooleanField(default=False)),
                ('confirmed', models.BooleanField(default=False)),
                ('email_device', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='otp_email.emaildevice')),
                ('totp_device', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='otp_totp.totpdevice')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='mfa_methods', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'MFA Method',
                'verbose_name_plural': 'MFA Methods',
                'ordering': ('-primary',),
            },
        ),
        migrations.AddConstraint(
            model_name='mfamethod',
            constraint=models.UniqueConstraint(fields=('user', 'type'), name='unique_user_method_type'),
        ),
        migrations.AddConstraint(
            model_name='mfamethod',
            constraint=models.UniqueConstraint(condition=models.Q(('primary', True)), fields=('user',), name='unique_user_primary'),
        ),
    ]
