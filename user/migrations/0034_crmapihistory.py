# Generated by Django 4.2.5 on 2024-11-29 01:06

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('gateway', '0061_order_chargedbackat_order_refundedat'),
        ('user', '0033_user_actualplatform_user_biggerpain_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='CRMApiHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.CharField(max_length=255)),
                ('payload', models.JSONField()),
                ('response', models.J<PERSON><PERSON>ield()),
                ('response_status_code', models.IntegerField()),
                ('createdAt', models.DateTimeField(auto_now_add=True)),
                ('order', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='crm_histories', to='gateway.order')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='crm_histories', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Histórico CRM',
                'verbose_name_plural': 'Históricos CRM',
                'ordering': ['-createdAt'],
            },
        ),
    ]
