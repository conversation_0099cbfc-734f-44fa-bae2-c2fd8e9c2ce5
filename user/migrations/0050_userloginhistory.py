# Generated by Django 4.2.5 on 2025-06-03 21:58

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0049_user_fbc_user_fbp_user_sck_user_utm_campaign_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='UserLoginHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ip', models.CharField(blank=True, db_index=True, help_text='IP do usuário no momento do registro', max_length=255, null=True)),
                ('user_agent', models.TextField(blank=True, help_text='Informações do navegador do usuário no momento do registro', null=True, verbose_name='User Agent')),
                ('access_token_source', models.CharField(blank=True, choices=[('login', 'Login'), ('login_email', 'Link de login por e-mail'), ('purchase_email', 'E-mail de compra'), ('refund_otp_view', 'View de reembolso com OTP'), ('first_purchase', 'Primeira compra (token no checkout)'), ('impersonate', 'Acesso via admin (impersonate)'), ('otp_token', 'View de OTP Token')], db_index=True, help_text='Parte do código que gerou o token de acesso, ex.: "login", "purchase_email", "refund_otp_view", etc.', max_length=255, verbose_name='Local da API')),
                ('createdAt', models.DateTimeField(auto_now_add=True, db_index=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='producer_histories', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Histórico de Login de Usuário',
                'verbose_name_plural': 'Histórico de Login de Usuários',
                'ordering': ['-createdAt'],
            },
        ),
    ]
