# Generated by Django 4.2.5 on 2025-02-27 18:53

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.FCM_DJANGO_FCMDEVICE_MODEL),
        ('user', '0043_alter_recoverytokenhistory_requester_user_agent'),
    ]

    operations = [
        migrations.AddField(
            model_name='mfamethod',
            name='fcm_device',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='mfa_methods', to=settings.FCM_DJANGO_FCMDEVICE_MODEL),
        ),
    ]
