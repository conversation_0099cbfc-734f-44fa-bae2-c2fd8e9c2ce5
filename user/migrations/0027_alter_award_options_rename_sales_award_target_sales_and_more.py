# Generated by Django 4.2.5 on 2024-05-17 14:29

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0026_alter_user_options_remove_user_balance_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='award',
            options={'ordering': ['-target_sales'], 'verbose_name': 'Premiação', 'verbose_name_plural': 'Premiações'},
        ),
        migrations.RenameField(
            model_name='award',
            old_name='sales',
            new_name='target_sales',
        ),
        migrations.AddField(
            model_name='award',
            name='description',
            field=models.TextField(blank=True, null=True, verbose_name='Descrição'),
        ),
        migrations.AddField(
            model_name='award',
            name='status',
            field=models.CharField(choices=[('active', 'Ativo'), ('deleted', 'Deletado')], default='active', max_length=255, verbose_name='Status'),
        ),
        migrations.AddField(
            model_name='award',
            name='updatedAt',
            field=models.DateTimeField(auto_now=True, verbose_name='Atualizado em'),
        ),
        migrations.CreateModel(
            name='UserAward',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('pending', 'Pendente'), ('sent', 'Enviado'), ('received', 'Recebido')], default='pending', max_length=255, verbose_name='Status')),
                ('tracking_code', models.CharField(blank=True, max_length=255, null=True, verbose_name='Código de rastreio')),
                ('shipping_method', models.CharField(blank=True, max_length=255, null=True, verbose_name='Método de envio')),
                ('createdAt', models.DateTimeField(auto_now_add=True, verbose_name='Criado em')),
                ('updatedAt', models.DateTimeField(auto_now=True, verbose_name='Atualizado em')),
                ('award', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='users', to='user.award')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='awards', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Premiação do Usuário',
                'verbose_name_plural': 'Premiações dos Usuários',
                'ordering': ['-createdAt'],
            },
        ),
    ]
