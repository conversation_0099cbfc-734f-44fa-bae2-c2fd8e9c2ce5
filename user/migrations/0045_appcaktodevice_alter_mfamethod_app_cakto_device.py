# Generated by Django 4.2.5 on 2025-03-14 15:15

from django.db import migrations, models
import django.db.models.deletion

from user.enums import MFAType


def remove_old_app_cakto_devices(apps, schema_editor):
    MFAMethod = apps.get_model('user', 'MFAMethod')
    mfa_methods = MFAMethod.objects.filter(type=MFAType.APP_CAKTO.id)
    for mfa_method in mfa_methods:
        mfa_method.delete()

class Migration(migrations.Migration):

    dependencies = [
        ('otp_totp', '0003_add_timestamps'),
        ('user', '0044_mfamethod_fcm_device'),
    ]

    operations = [
        migrations.CreateModel(
            name='AppCaktoDevice',
            fields=[
                ('totpdevice_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='otp_totp.totpdevice')),
            ],
            options={
                'abstract': False,
            },
            bases=('otp_totp.totpdevice',),
        ),
        migrations.RunPython(remove_old_app_cakto_devices, migrations.RunPython.noop),
        migrations.AlterField(
            model_name='mfamethod',
            name='app_cakto_device',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='app_cakto_device', to='user.appcaktodevice'),
        ),
    ]
