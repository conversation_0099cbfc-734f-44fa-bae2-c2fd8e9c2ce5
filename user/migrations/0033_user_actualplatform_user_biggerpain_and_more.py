# Generated by Django 4.2.5 on 2024-09-03 19:40

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0032_validate_users_email_and_whatsapp'),
    ]

    operations = [
        migrations.AddField(
            model_name='user',
            name='actualPlatform',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Plataforma atual'),
        ),
        migrations.AddField(
            model_name='user',
            name='biggerPain',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='<PERSON><PERSON> dor'),
        ),
        migrations.AddField(
            model_name='user',
            name='businessModel',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Modelo de negócio'),
        ),
        migrations.AddField(
            model_name='user',
            name='companyName',
            field=models.Char<PERSON>ield(blank=True, max_length=255, null=True, verbose_name='Nome da empresa'),
        ),
        migrations.AddField(
            model_name='user',
            name='companyProduct',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Tipo de produto vendido'),
        ),
        migrations.AddField(
            model_name='user',
            name='indication',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='instagram',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='mostSells',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Nicho mais vendido'),
        ),
        migrations.AddField(
            model_name='user',
            name='recurringAmount',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=11, null=True, verbose_name='Faturamento médio'),
        ),
    ]
