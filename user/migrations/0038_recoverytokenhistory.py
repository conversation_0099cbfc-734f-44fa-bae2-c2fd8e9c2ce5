# Generated by Django 4.2.5 on 2025-01-22 14:02

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0037_merge_20241129_1838'),
    ]

    operations = [
        migrations.CreateModel(
            name='RecoveryTokenHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('token', models.CharField(max_length=255, verbose_name='Token')),
                ('token_expires_at', models.DateTimeField(verbose_name='Data de expiração do Token')),
                ('event_type', models.CharField(choices=[('created', 'Token Solicitado'), ('used', 'Token Usado')], max_length=255, verbose_name='Tipo do evento')),
                ('requester_ip_address', models.GenericIPAddressField(verbose_name='IP do solicitante')),
                ('requester_user_agent', models.CharField(max_length=255, verbose_name='User Agent do solicitante')),
                ('timestamp', models.DateTimeField(auto_now_add=True, verbose_name='Data do evento')),
                ('updatedAt', models.DateTimeField(auto_now=True, verbose_name='Última atualização')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='recovery_token_histories', to=settings.AUTH_USER_MODEL, verbose_name='Usuário')),
            ],
            options={
                'verbose_name': 'Histórico de Tokens de Recuperação',
                'verbose_name_plural': 'Históricos de Tokens de Recuperação',
                'ordering': ['-timestamp'],
            },
        ),
    ]
