# Generated by Django 4.2.5 on 2023-12-06 18:16

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0016_alter_user_caktofee_alter_user_cardantecipationfee'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='user',
            name='antecipateCardIncomes',
        ),
        migrations.RemoveField(
            model_name='user',
            name='cardAntecipationFee',
        ),
        migrations.RemoveField(
            model_name='user',
            name='fixedFee',
        ),
        migrations.AddField(
            model_name='user',
            name='antecipation',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='Antecipação (%)'),
        ),
        migrations.AddField(
            model_name='user',
            name='boleto',
            field=models.IntegerField(blank=True, default=1, null=True, verbose_name='<PERSON><PERSON><PERSON> (D+)'),
        ),
        migrations.AddField(
            model_name='user',
            name='caktoFixedFee',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='Taxa Cakto fixa (R$)'),
        ),
        migrations.AddField(
            model_name='user',
            name='credit_card',
            field=models.IntegerField(blank=True, default=15, null=True, verbose_name='Cartão de crédito (D+)'),
        ),
        migrations.AddField(
            model_name='user',
            name='installments1',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='1x (% da taxa)'),
        ),
        migrations.AddField(
            model_name='user',
            name='installments10',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='10x (% da taxa)'),
        ),
        migrations.AddField(
            model_name='user',
            name='installments11',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='11x (% da taxa)'),
        ),
        migrations.AddField(
            model_name='user',
            name='installments12',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='12x (% da taxa)'),
        ),
        migrations.AddField(
            model_name='user',
            name='installments2',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='2x (% da taxa)'),
        ),
        migrations.AddField(
            model_name='user',
            name='installments3',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='3x (% da taxa)'),
        ),
        migrations.AddField(
            model_name='user',
            name='installments4',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='4x (% da taxa)'),
        ),
        migrations.AddField(
            model_name='user',
            name='installments5',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='5x (% da taxa)'),
        ),
        migrations.AddField(
            model_name='user',
            name='installments6',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='6x (% da taxa)'),
        ),
        migrations.AddField(
            model_name='user',
            name='installments7',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='7x (% da taxa)'),
        ),
        migrations.AddField(
            model_name='user',
            name='installments8',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='8x (% da taxa)'),
        ),
        migrations.AddField(
            model_name='user',
            name='installments9',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='9x (% da taxa)'),
        ),
        migrations.AddField(
            model_name='user',
            name='pix',
            field=models.IntegerField(blank=True, default=0, null=True, verbose_name='Pix (D+)'),
        ),
        migrations.AddField(
            model_name='user',
            name='stuck',
            field=models.IntegerField(blank=True, default=0, null=True, verbose_name='Dias para retenção'),
        ),
        migrations.AddField(
            model_name='user',
            name='stuckPercentage',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='Retenção (%)'),
        ),
        migrations.AlterField(
            model_name='user',
            name='caktoFee',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='Taxa Cakto (%)'),
        ),
    ]
