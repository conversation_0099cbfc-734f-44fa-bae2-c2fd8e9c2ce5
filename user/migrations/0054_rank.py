# Generated by Django 4.2.5 on 2025-07-10 11:38

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0053_alter_user_register_profile_completed'),
    ]

    operations = [
        migrations.CreateModel(
            name='Rank',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=100, verbose_name='Código')),
                ('label', models.Char<PERSON>ield(max_length=255, verbose_name='<PERSON><PERSON><PERSON><PERSON>')),
                ('min_value', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Valor mínimo')),
                ('max_value', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='Valor máximo')),
                ('status', models.<PERSON>r<PERSON><PERSON>(choices=[('active', 'Ativo'), ('deleted', 'Deletado')], default='active', max_length=255, verbose_name='Status')),
                ('createdAt', models.DateTimeField(auto_now_add=True, verbose_name='Criado em')),
                ('updatedAt', models.DateTimeField(auto_now=True, verbose_name='Atualizado em')),
            ],
            options={
                'verbose_name': 'Rank',
                'verbose_name_plural': 'Ranks',
                'ordering': ['-min_value'],
            },
        ),
    ]
