# Generated by Django 4.2.5 on 2025-02-20 18:07

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('otp_totp', '0003_add_timestamps'),
        ('user', '0038_user_membersv2id'),
    ]

    operations = [
        migrations.AddField(
            model_name='mfamethod',
            name='app_cakto_device',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='app_cakto_device', to='otp_totp.totpdevice'),
        ),
        migrations.AlterField(
            model_name='mfamethod',
            name='type',
            field=models.CharField(choices=[('email', 'Email'), ('totp', 'TOTP'), ('app_cakto', 'APP Cakto')], max_length=255),
        ),
    ]
