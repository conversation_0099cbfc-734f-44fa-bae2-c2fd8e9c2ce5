# Generated by Django 4.2.5 on 2024-11-19 21:20

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import user.models


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0034_mfamethod_mfamethod_unique_user_method_type_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='user',
            name='next_nps_survey_date',
            field=models.DateTimeField(blank=True, default=user.models.next_nps_survey_date, null=True),
        ),
        migrations.CreateModel(
            name='NPSSurvey',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('note', models.PositiveSmallIntegerField(validators=[user.models._validate_nps_note])),
                ('justification', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Net Promoter Score',
                'verbose_name_plural': 'Net Promoter Scores',
                'ordering': ['-created_at'],
            },
        ),
    ]
