# Generated by Django 4.2.5 on 2023-12-28 14:55

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0020_alter_user_balance'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='withdrawal',
            options={'ordering': ['-updatedAt', 'createdAt']},
        ),
        migrations.AddField(
            model_name='withdrawal',
            name='caktoWithdrawFee',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='Taxa do saque'),
        ),
        migrations.AddField(
            model_name='withdrawal',
            name='externalId',
            field=models.CharField(max_length=255, null=True, verbose_name='Id externo da transferência'),
        ),
        migrations.AddField(
            model_name='withdrawal',
            name='transferredAt',
            field=models.DateTimeField(blank=True, null=True, verbose_name='Efetivado em'),
        ),
        migrations.AddField(
            model_name='withdrawal',
            name='updatedAt',
            field=models.DateTimeField(auto_now=True, verbose_name='Atualizado em'),
        ),
        migrations.AlterField(
            model_name='withdrawal',
            name='amount',
            field=models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Valor'),
        ),
        migrations.AlterField(
            model_name='withdrawal',
            name='status',
            field=models.CharField(choices=[('pending', 'Pendente'), ('bank_processing', 'Processamento do Banco'), ('success', 'Sucesso'), ('failed', 'Falha')], default='pending', max_length=255, verbose_name='Status'),
        ),
    ]
