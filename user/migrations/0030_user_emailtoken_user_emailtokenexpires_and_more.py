# Generated by Django 4.2.5 on 2024-08-17 13:58

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0029_useraward_cep_useraward_city_useraward_complement_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='user',
            name='emailToken',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='emailTokenExpires',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='emailValidated',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='user',
            name='emailValidatedAt',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='whatsappToken',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='whatsappTokenExpires',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='whatsappValidated',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='user',
            name='whatsappValidatedAt',
            field=models.DateTimeField(blank=True, null=True),
        ),
    ]
