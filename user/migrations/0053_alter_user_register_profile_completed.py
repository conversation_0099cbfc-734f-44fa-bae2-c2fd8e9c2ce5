# Generated by Django 4.2.5 on 2025-06-26 15:44

# We had to first create the field as the default equals True on 0052_user_register_profile_completed
# so all existing users would have it set to True, then we can change the default to False.
# On this we are changing the default to False, so all new users will have it set to False.
# This is a workaround to avoid having to update all existing users manually.

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0052_user_register_profile_completed'),
    ]

    operations = [
        migrations.AlterField(
            model_name='user',
            name='register_profile_completed',
            field=models.BooleanField(default=False, help_text='Indica se o usuário completou o cadastro de perfil', verbose_name='Cadastro de perfil completo'),
        ),
    ]
