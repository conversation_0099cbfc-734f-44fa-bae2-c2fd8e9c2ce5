# Generated by Django 4.2.5 on 2024-01-24 18:13

from django.db import migrations, models


def set_configs_to_null(apps, schema_editor):
    User = apps.get_model('user', 'User')
    User.objects.all().update(boleto=None, credit_card=None, pix=None, stuck=None)

class Migration(migrations.Migration):

    dependencies = [
        ('user', '0023_delete_withdrawal'),
    ]

    operations = [
        migrations.AlterField(
            model_name='user',
            name='boleto',
            field=models.IntegerField(blank=True, null=True, verbose_name='Boleto (D+)'),
        ),
        migrations.AlterField(
            model_name='user',
            name='credit_card',
            field=models.IntegerField(blank=True, null=True, verbose_name='Cartão de crédito (D+)'),
        ),
        migrations.AlterField(
            model_name='user',
            name='pix',
            field=models.IntegerField(blank=True, null=True, verbose_name='Pix (D+)'),
        ),
        migrations.<PERSON><PERSON><PERSON>ield(
            model_name='user',
            name='stuck',
            field=models.IntegerField(blank=True, null=True, verbose_name='Dias para retenção'),
        ),
        migrations.RunPython(set_configs_to_null, reverse_code=migrations.RunPython.noop),
    ]
