# Generated by Django 4.2.5 on 2025-03-14 14:10

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0043_alter_recoverytokenhistory_requester_user_agent'),
    ]

    operations = [
        migrations.CreateModel(
            name='ExperimentalFeature',
            fields=[
                ('id', models.CharField(db_index=True, help_text='Identificador da funcionalidade no sistema, ex.: "openfinance_nubank", "membersV2", etc.', max_length=80, primary_key=True, serialize=False, unique=True, verbose_name='Identificador')),
                ('name', models.CharField(db_index=True, help_text='Nome descritivo da funcionalidade no sistema, ex.: "Método de pagamento Nubank", "Área de membros v2", etc.', max_length=255, verbose_name='Nome')),
            ],
            options={
                'verbose_name': 'Função Experimental',
                'verbose_name_plural': 'Funções Experimentais',
                'ordering': ['name'],
            },
        ),
        migrations.AddField(
            model_name='user',
            name='experimental_features',
            field=models.ManyToManyField(blank=True, help_text='Funcionalidades em teste que o usuário tem acesso', related_name='users', to='user.experimentalfeature', verbose_name='Funcionalidades Experimentais'),
        ),
    ]
