# Generated by Django 4.2.5 on 2025-06-02 17:16

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0048_merge_20250404_1110'),
    ]

    operations = [
        migrations.AddField(
            model_name='user',
            name='fbc',
            field=models.CharField(blank=True, help_text='ID do clique em anúncio do pixel do Facebook', max_length=255, null=True, verbose_name='Facebook Click ID'),
        ),
        migrations.AddField(
            model_name='user',
            name='fbp',
            field=models.CharField(blank=True, help_text='ID do pixel do Facebook', max_length=255, null=True, verbose_name='Facebook Pixel ID'),
        ),
        migrations.AddField(
            model_name='user',
            name='sck',
            field=models.CharField(blank=True, help_text='Código de rastreamento de campanha (sck)', max_length=255, null=True, verbose_name='SCK'),
        ),
        migrations.Add<PERSON>ield(
            model_name='user',
            name='utm_campaign',
            field=models.CharField(blank=True, help_text='Nome da campanha de marketing (utm_campaign)', max_length=255, null=True, verbose_name='Utm Campaign'),
        ),
        migrations.AddField(
            model_name='user',
            name='utm_content',
            field=models.CharField(blank=True, help_text='Conteúdo da campanha de marketing (utm_content)', max_length=255, null=True, verbose_name='Utm Content'),
        ),
        migrations.AddField(
            model_name='user',
            name='utm_medium',
            field=models.CharField(blank=True, help_text='Meio da campanha de marketing (utm_medium)', max_length=255, null=True, verbose_name='Utm Medium'),
        ),
        migrations.AddField(
            model_name='user',
            name='utm_source',
            field=models.CharField(blank=True, help_text='Fonte da campanha de marketing (utm_source)', max_length=255, null=True, verbose_name='Utm Source'),
        ),
        migrations.AddField(
            model_name='user',
            name='utm_term',
            field=models.CharField(blank=True, help_text='Termo da campanha de marketing (utm_term)', max_length=255, null=True, verbose_name='Utm Term'),
        ),
    ]
