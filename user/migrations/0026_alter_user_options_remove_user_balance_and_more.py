# Generated by Django 4.2.5 on 2024-05-04 13:01

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0025_remove_user_antecipation_remove_user_boleto_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='user',
            options={'ordering': ['-createdAt'], 'verbose_name': 'Usuário', 'verbose_name_plural': 'Usuários'},
        ),
        migrations.RemoveField(
            model_name='user',
            name='balance',
        ),
        migrations.RemoveField(
            model_name='user',
            name='companyName',
        ),
        migrations.RemoveField(
            model_name='user',
            name='isApproved',
        ),
        migrations.AddField(
            model_name='user',
            name='createdAt',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now, verbose_name='Criado em'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='user',
            name='updatedAt',
            field=models.DateTimeField(auto_now=True, verbose_name='Atualizado em'),
        ),
    ]
