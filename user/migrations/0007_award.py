# Generated by Django 4.2.5 on 2023-10-17 12:25

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0006_user_companyname'),
    ]

    operations = [
        migrations.CreateModel(
            name='Award',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sales', models.FloatField(verbose_name='Valor em vendas')),
                ('title', models.CharField(max_length=255, verbose_name='Título')),
                ('createdAt', models.DateTimeField(auto_now_add=True, verbose_name='Criado em')),
            ],
            options={
                'verbose_name': 'Premiação',
                'verbose_name_plural': 'Premiações',
                'ordering': ['-sales'],
            },
        ),
    ]
