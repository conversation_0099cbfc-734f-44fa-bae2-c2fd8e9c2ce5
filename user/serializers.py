import re

from django.conf import settings
from django.contrib.auth.password_validation import validate_password as check_password
from django.db import transaction
from rest_framework import serializers

from cakto.serializers import CustomFlexFieldsSerializer
from user.models import Award, Colaborator, MFAMethod, NPSSurvey, Rank, User, UserAward, is_valid_nps_note


class UserFlexFieldsSerializer(CustomFlexFieldsSerializer):
    class Meta:
        model = User


class UserAffiliateSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = [
            'first_name',
            'last_name',
            'email',
            'phoneCountryCode',
            'cellphone',
        ]


class UserRegisterSerializer(serializers.ModelSerializer):
    name = serializers.CharField()

    class Meta:
        model = User
        fields = [
            'name',
            'instagram',
            'companyName',
            'companyProduct',
            'mostSells',
            'recurringAmount',
            'businessModel',
            'actualPlatform',
            'biggerPain',
            'indication',
            'password',
            'email',
            'is_producer',
            'fbp',
            'fbc',
            'utm_source',
            'utm_medium',
            'utm_campaign',
            'utm_term',
            'utm_content',
            'sck',
        ]
        required_fields = [
            'name',
            'instagram',
            'companyProduct',
            'mostSells',
            'recurringAmount',
            'businessModel',
            'actualPlatform',
            'biggerPain',
            'indication',
            'password',
            'email',
        ]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        for field in self.Meta.required_fields:
            self.fields[field].required = True

    def validate_password(self, value):
        check_password(value)
        return value

    def validate(self, data):
        name_list = data.pop('name', '').title().split()
        data['first_name'] = name_list[0]
        data['last_name'] = ' '.join(name_list[1:]) if len(name_list) > 1 else ''

        data['username'] = data['email']
        data['email'] = data.get('email', '').lower()

        data['viewPageAs'] = 'producer'

        return data

    def to_internal_value(self, data):
        data['is_producer'] = True

        if 'phoneCountryCode' in data:
            data['phoneCountryCode'] = re.sub(r'\D', '', data.get('phoneCountryCode', ''))
        if 'cellphone' in data:
            data['cellphone'] = re.sub(r'\D', '', data.get('cellphone', ''))
        if 'cnpj' in data:
            data['cnpj'] = re.sub(r'\D', '', data.get('cnpj', ''))

        data['utm_source'] = data.get('utm_source', 'direto')

        return super().to_internal_value(data)

    def create(self, validated_data):
        # transaction.atomic will revert db changes if an error occurs
        with transaction.atomic():
            user: User = super().create(validated_data)
            user.set_password(validated_data['password'])
            user.save(raise_exception=True)

        return user


class UserUpdateSerializer(serializers.ModelSerializer):
    name = serializers.CharField(required=False)

    class Meta:
        model = User
        fields = [
            'id',
            'email',
            'first_name',
            'last_name',
            'cpf',
            'cnpj',
            'cellphone',
            'phoneCountryCode',
            'address',
            'country',
            'state',
            'cep',
            'city',
            'viewPageAs',
            'name',
            'instagram',
            'companyName',
            'companyProduct',
            'mostSells',
            'recurringAmount',
            'businessModel',
            'actualPlatform',
            'biggerPain',
            'indication',
        ]
        read_only_fields = ['id', 'email']

    def validate(self, data):
        name_list = data.pop('name', '').title().split()
        if name_list:
            data['first_name'] = name_list[0]
            data['last_name'] = ' '.join(name_list[1:]) if len(name_list) > 1 else ''

        if data.get('email'):
            data['username'] = data['email']
            data['email'] = data.get('email', '').lower()

        cellphone = data.get('cellphone')
        phoneCountryCode = data.get('phoneCountryCode')

        self.validade_phoneCountryCode_cellphone(data, cellphone, phoneCountryCode)

        return data

    def validade_phoneCountryCode_cellphone(self, data, cellphone, phoneCountryCode) -> None:
        if cellphone and not phoneCountryCode:
            raise serializers.ValidationError(
                {
                    'cellphone': 'Se você informar um DDI, é necessário informar um número de celular.',
                }
            )

        if not cellphone and phoneCountryCode:
            raise serializers.ValidationError(
                {
                    'phoneCountryCode': 'Se você informar um número de celular, é necessário informar o DDI.',
                }
            )

        if cellphone and phoneCountryCode:
            number_already_validated = (
                User.objects.filter(
                    cellphone=cellphone,
                    phoneCountryCode=phoneCountryCode,
                    whatsappValidated=True,
                )
                .exclude(pk=data.get('id'))
                .exists()
            )

            if number_already_validated:
                raise serializers.ValidationError(
                    {
                        'cellphone': 'Esse número de celular já foi validado por outro usuário.',
                        'phoneCountryCode': 'Esse número de celular já foi validado por outro usuário.',
                    }
                )

    def to_internal_value(self, data):
        data = super().to_internal_value(data)
        # removes non-digits
        if 'phoneCountryCode' in data:
            data['phoneCountryCode'] = re.sub(r'\D', '', data.get('phoneCountryCode', ''))
        if 'cellphone' in data:
            data['cellphone'] = re.sub(r'\D', '', data.get('cellphone', ''))
        if 'cnpj' in data:
            data['cnpj'] = re.sub(r'\D', '', data.get('cnpj', ''))
        return data


class UserAdminSerializer(serializers.ModelSerializer):
    cpf = serializers.CharField(source='_company.cpf', read_only=True)
    cnpj = serializers.CharField(source='_company.companyCnpj', read_only=True)
    companyStatus = serializers.CharField(source='_company.status', read_only=True)

    class Meta:
        model = User
        fields = [
            'id',
            'email',
            'phoneCountryCode',
            'cellphone',
            'cpf',
            'cnpj',
            'companyStatus',
            'refundRequest',
            'threeDsRetryEnabled',
            'createdAt',
            'updatedAt',
        ]


class UserAdminSerializerFull(serializers.ModelSerializer):
    company = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = [
            'id',
            'email',
            'picture',
            'refundRequest',
            'threeDsRetryEnabled',
            'company',
            'createdAt',
            'updatedAt',
        ]
        read_only_fields = ['id', 'email', 'refundRequest', 'createdAt', 'updatedAt']

    def get_company(self, user):
        from financial.serializers import CompanySerializerFull

        return CompanySerializerFull(user._company).data

class UserAdminUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for admin update that has fields that can be updated directly
    and does not affect other user fields so we don't need to create a new action
    for every field that needs to be updated.
    """

    class Meta:
        model = User
        fields = [
            'threeDsRetryEnabled',
            'refundRequest',
        ]

class UserReadOnlySerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['id', 'email']


class ChangePasswordSerializer(serializers.Serializer):
    model = User

    """
    Serializer for password change endpoint.
    """
    oldPassword = serializers.CharField(required=True)
    newPassword = serializers.CharField(required=True)

    def validate_newPassword(self, value):
        check_password(value)
        return value


class CreatePasswordSerializer(serializers.Serializer):
    password = serializers.CharField(required=True)


class ColaboratorSerializer(serializers.ModelSerializer):
    user = UserReadOnlySerializer(read_only=True)
    owner = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = Colaborator
        fields = ['id', 'user', 'owner', 'permissions', 'status', 'createdAt']
        read_only_fields = ['id', 'createdAt']

    def get_owner(self, colaborator):
        owner = colaborator.owner
        return {'id': owner.pk, 'email': owner.email, 'companyName': owner.company.companyName}


class ColaboratorCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Colaborator
        fields = ['id', 'owner', 'status', 'user', 'permissions', 'createdAt']
        read_only_fields = ['id', 'createdAt']

    def validate_permissions(self, value):
        permitted_values = [perm for perm, _ in Colaborator.permited_permissions]

        invalid_permissions = [perm for perm in value if perm not in permitted_values]
        if invalid_permissions:
            raise serializers.ValidationError(f'Permissões inválidas: {invalid_permissions}.')
        return value


class RankSerializer(serializers.ModelSerializer):
    class Meta:
        model = Rank
        fields = ['code', 'label', 'min_value', 'max_value',]


class AwardAdminSerializer(serializers.ModelSerializer):
    class Meta:
        model = Award
        fields = [
            'id',
            'title',
            'description',
            'status',
            'target_sales',
            'createdAt',
            'updatedAt',
        ]
        read_only_fields = ['id', 'createdAt', 'updatedAt']


class UserAwardAdminSerializer(serializers.ModelSerializer):
    award = AwardAdminSerializer()
    user = UserReadOnlySerializer()

    class Meta:
        model = UserAward
        fields = [
            'id',
            'user',
            'award',
            'status',
            # Shipping data
            'tracking_code',
            'shipping_method',
            # Address data
            'cep',
            'street',
            'number',
            'complement',
            'neighborhood',
            'city',
            'state',
            # Dates
            'createdAt',
            'updatedAt',
        ]
        read_only_fields = ['id', 'user', 'award', 'createdAt', 'updatedAt']

    def to_internal_value(self, instance):
        ret = super().to_internal_value(instance)

        # Normalize state
        if ret.get('state'):
            ret['state'] = ret['state'].upper()

        # Normalize cep
        if ret.get('cep'):
            ret['cep'] = ''.join([c for c in ret['cep'] if c.isdigit()])
        return ret


class UserAwardCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserAward
        fields = [
            'user',
            'award',
            'tracking_code',
            'shipping_method',
        ]


class UserOwnerSerializer(CustomFlexFieldsSerializer):
    target_rank = serializers.CharField(read_only=True, source='getNextRank')
    nextAward = AwardAdminSerializer(read_only=True, source='getNextAward')
    companyStatus = serializers.CharField(source='company.status', read_only=True)
    threeDsEnabled = serializers.CharField(source='company.threeDsEnabled', read_only=True)
    email = serializers.EmailField(read_only=True)
    whatsappValidated = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = User
        fields = [
            'id',
            'email',
            'first_name',
            'last_name',
            'picture',
            'cpf',
            'cnpj',
            'phoneCountryCode',
            'cellphone',
            'address',
            'country',
            'state',
            'viewPageAs',
            'cep',
            'city',
            'totalSales',
            'target_rank',
            'nextAward',
            'companies',
            'companyStatus',
            'threeDsEnabled',
            'refundRequest',
            'is_staff',
            'is_customer',
            'is_producer',
            'has_usable_password',
            'experimental_features',

            # Validations
            'emailValidated',
            'emailValidatedAt',
            'whatsappValidated',
            'whatsappValidatedAt',
            'next_nps_survey_date',
            'app_reviewed_at',
        ]
        read_only_fields = [
            'id',
            'refundRequest',
            'emailValidated',
            'emailValidatedAt',
            'whatsappValidated',
            'whatsappValidatedAt',
            'next_nps_survey_date',
            'app_reviewed_at',
        ]

    def to_representation(self, user: User):
        res = super().to_representation(user)

        if user.is_staff:
            res['permissions'] = user.get_all_permissions()

        if not res['experimental_features']:
            del res['experimental_features']

        return res

    def get_whatsappValidated(self, user):
        if settings.WHATSAPP_VALIDATION_ENABLED:
            return user.whatsappValidated

        return True


class UserAdminFlexSerializer(CustomFlexFieldsSerializer):
    class Meta:
        model = User
        fields = '__all__'


class UserPublicFlexSerializer(CustomFlexFieldsSerializer):
    class Meta:
        model = User
        fields = [
            'email',
            'first_name',
            'last_name',
            'picture',
        ]


class MFAMethodSerializer(serializers.ModelSerializer):
    type = serializers.CharField(required=True)
    primary = serializers.BooleanField(required=True)
    fcm_device_id = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = MFAMethod
        fields = ['type', 'primary', 'fcm_device_id', 'createdAt', 'updatedAt']
        read_only_fields = ['createdAt', 'updatedAt']

    def get_fcm_device_id(self, mfa_method):
        return mfa_method.fcm_device.device_id if mfa_method.fcm_device else None


class MFAGeneralSerializer(serializers.Serializer):
    method = serializers.CharField(required=False)
    code = serializers.CharField(required=False)
    currentPrimaryCode = serializers.CharField(required=False)
    ephemeral_token = serializers.CharField(required=False)
    email = serializers.EmailField(required=False)
    fcm_device_id = serializers.CharField(required=False)

    def __init__(self, required_fields: list[str], *args, **kwargs):
        for field in required_fields:
            self.fields[field].required = True

        super().__init__(*args, **kwargs)


class CreateNPSSurveySerializer(serializers.ModelSerializer):
    note = serializers.IntegerField(required=True)
    justification = serializers.CharField(required=False, allow_null=True, allow_blank=True)

    class Meta:
        model = NPSSurvey
        fields = [
            'note',
            'justification',
        ]

    def validate_note(self, value):
        if not is_valid_nps_note(value):
            raise serializers.ValidationError(
                f"Nota inválida: '{value}'. Apenas valores inteiros entre 1 e 5 são permitidos."
            )

        return value


class NPSSurveySerializer(serializers.ModelSerializer):
    class Meta:
        model = NPSSurvey
        fields = ['id', 'note', 'justification', 'created_at', 'user']
