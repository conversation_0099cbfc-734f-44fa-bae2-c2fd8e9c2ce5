from allauth.socialaccount.providers.google.views import GoogleOAuth2Adapter
from allauth.socialaccount.providers.facebook.views import FacebookOAuth2Adapter
from dj_rest_auth.registration.views import SocialLoginView
from allauth.socialaccount.providers.oauth2.client import OAuth2<PERSON>lient
from user.models import User

class NewSocialLoginView(SocialLoginView):
    def post(self, request, *args, **kwargs):
        # Call the parent post method to handle the social login
        response = super().post(request, *args, **kwargs)

        # Get the user object from the response data
        # user_id = response.data['user']['pk']
        # user = User.objects.get(id=user_id)

        # if user is not None:
        #     affiliate = Affiliate.objects.filter(code=request.data.get('affiliateCode')).last()
        #     if affiliate and not user.affiliate and user != affiliate.user:
        #         user.affiliate = affiliate
        #         user.save()

        return response

class GoogleLogin(NewSocialLoginView):
    adapter_class = GoogleOAuth2Adapter
    callback_url = "https://app.turbotools.com.br/login/google/callback/"
    client_class = OAuth2Client

# class FacebookLogin(NewSocialLoginView):
#     adapter_class = FacebookOAuth2Adapter
#     callback_url = "https://app.turbotools.com.br/login/facebook/callback/"
#     client_class = OAuth2Client
