import requests
from django.conf import settings
from rest_framework.exceptions import ValidationError

from user.models import User


class InfoBipWhatsApp:
    def __init__(self) -> None:
        self.url = settings.INFOBIP_BASE_URL
        self.token = settings.INFOBIP_API_TOKEN
        self.whatsapp_number = settings.INFOBIP_WHATSAPP_NUMBER
        self.template_name = settings.INFOBIP_AUTH_TEMPLATE_NAME
        self.api = requests.Session()

        # https://www.infobip.com/docs/essentials/api-essentials/api-authentication
        self.api.headers.update({
            'Authorization': f'App {self.token}',
            'Content-Type': 'application/json',
            'Accept': 'application/json',
        })

    def send_token(self, user: User, token: str) -> None:
        # https://www.infobip.com/docs/api/channels/whatsapp/whatsapp-outbound-messages/send-whatsapp-template-message
        # https://www.infobip.com/docs/whatsapp/message-types#authentication-template-message-templates

        self.validate_user(user)

        user_whatsapp_number = user.phoneCountryCode + user.cellphone  # type: ignore

        endpoint = self.url + '/whatsapp/1/message/template'

        payload = {
            'messages': [
                {
                    'from': self.whatsapp_number,
                    'to': user_whatsapp_number,
                    'content': {
                        'templateName': self.template_name,
                        'templateData': {
                            'body': {
                                'placeholders': [token]
                            },
                            'buttons': [
                                {
                                    'type': 'URL',
                                    'parameter': token,
                                }
                            ]
                        },
                        'language': 'pt_BR'
                    },
                }
            ]
        }

        self.handle_response(self.api.post(endpoint, json=payload))

    def handle_response(self, response: requests.Response) -> requests.Response:
        response.raise_for_status()
        status = response.json().get('messages', [{}])[0].get('status', {}).get('name', '')

        # https://www.infobip.com/docs/essentials/api-essentials/response-status-and-error-codes
        if 'DELIVERED' not in status and 'PENDING' not in status:
            print('Erro ao enviar mensagem de Whatsapp (Info Bip)', response.json())
            raise ValidationError({'detail': 'Erro ao enviar mensagem.'})

        return response

    def validate_user(self, user: User) -> None:
        if user.whatsappValidated:
            raise ValidationError({'detail': 'Whatsapp já validado.'})

        if not user.phoneCountryCode or not user.cellphone:
            raise ValidationError({'detail': 'Número de Whatsapp não cadastrado corretamente.'})

        if self.check_existing_verified_user(user):
            raise ValidationError({'detail': 'Número de Whatsapp em uso.'})

    def check_existing_verified_user(self, user: User) -> bool:
        return User.objects.filter(
            phoneCountryCode=user.phoneCountryCode,
            cellphone=user.cellphone,
            whatsappValidated=True
        ).exists()
