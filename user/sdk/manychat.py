import os
import re
from typing import Any

import requests
from rest_framework import status
from rest_framework.exceptions import ValidationError

from user.models import User


class ManyChatWhatsapp:
    # https://api.manychat.com/swagger?urls.primaryName=Page%20API#/

    auto_phone_id = os.getenv('MANYCHAT_AUTO_PHONE_ID')  # can be found on ManyChat Settings > Fields

    url = 'https://api.manychat.com'
    token = os.getenv('MANYCHAT_TOKEN', '')
    api = requests.Session()

    def __init__(self) -> None:
        self.api.headers.update({'Authorization': f'Bearer {self.token}'})

    def createSubscriber(self, user: User) -> requests.Response:
        phone = self.get_user_phone(user)
        payload = {
            'first_name': user.first_name,
            'last_name': user.last_name,
            'whatsapp_phone': phone,
        }
        return self.api.post(self.url + '/fb/subscriber/createSubscriber', json=payload)

    def setCustomFieldByName(self, manychat_id: int, field_name: str, value: Any) -> requests.Response:
        payload = {
            'subscriber_id': int(manychat_id),
            'field_name': field_name,
            'field_value': str(value),
        }

        return self.api.post(self.url + '/fb/subscriber/setCustomFieldByName', json=payload)

    def findByCustomField(self, field_id: int, field_value: str) -> requests.Response:
        params: dict[str, int | str] = {
            'field_id': field_id,
            'field_value': field_value,
        }

        return self.api.get(self.url + '/fb/subscriber/findByCustomField', params=params)

    # Utils functions #
    def get_user_phone(self, user: User) -> str:
        return "+" + user.phoneCountryCode + (user.cellphone or '')

    def check_existing_whatsapp_id(self, manychat_response: str) -> bool:
        return bool(re.search(r'This WhatsApp ID already exists: (\d+)', manychat_response))

    def handle_manychat_subscriber(self, user: User) -> None:
        if user.manychat_id:
            return

        if not user.cellphone:
            raise ValidationError({'detail': 'Número de whatsapp não cadastrado.'})

        self.handle_existing_manychat_subscriber(user)

        res = self.createSubscriber(user)

        if not status.is_success(res.status_code):
            raise Exception('Erro ao criar subscriber no ManyChat. ' + res.text)

        user.manychat_id = res.json().get('data', {}).get('id')
        user.save()
        self.setCustomFieldByName(user.manychat_id, 'auto_phone', self.get_user_phone(user).replace('+', ''))

    def handle_existing_manychat_subscriber(self, user: User) -> None:
        res = self.findByCustomField(field_id=self.auto_phone_id, field_value=self.get_user_phone(user).replace('+', ''))  # type: ignore
        if not status.is_success(res.status_code):
            raise Exception('Erro ao buscar subscriber no ManyChat. ' + res.text)

        data = res.json().get('data', [])
        if data and data[0].get('status') == 'active':
            if self.check_existing_verified_user(user):
                raise ValidationError({'detail': 'Número de whatsapp já cadastrado.'})

            # Seting auto_phone field to null will trigger an automation that unsubscribe the user from WhatsApp
            self.setCustomFieldByName(data[0]['id'], 'auto_phone', 'reset')
            User.objects.filter(phoneCountryCode=user.phoneCountryCode, cellphone=user.cellphone).update(manychat_id=None)

    def check_existing_verified_user(self, user: User) -> bool:
        return User.objects.filter(
            phoneCountryCode=user.phoneCountryCode,
            cellphone=user.cellphone,
            whatsappValidated=True
        ).exists()

    def send_token(self, user: User, token: str) -> None:
        self.handle_manychat_subscriber(user)

        # Changing whatsappToken will trigger an automation that sends the token to the user's Whatspapp
        res = self.setCustomFieldByName(user.manychat_id, 'whatsappToken', token)  # type:ignore

        if not status.is_success(res.status_code):
            raise Exception('Erro no update de whatsappToken no manychat. ' + res.text)
