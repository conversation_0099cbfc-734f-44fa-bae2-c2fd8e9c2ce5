import json
from urllib.parse import urljoin

import requests
from django.conf import settings
from django.utils import timezone
from django_rq import job
from rest_framework import status
from rq import Retry

from cakto.utils import normalize_datetime_input
from gateway.models import Order
from user.models import CRMApiHistory, User


class CRMSdk:
    def __init__(self) -> None:
        self.URL = settings.CRM_WEBHOOK_URL
        self.TOKEN = settings.CRM_WEBHOOK_TOKEN

    def get_user_phone(self, user: User) -> str:
        return (user.phoneCountryCode or '') + (user.cellphone or '')

    def get_user_document(self, user: User) -> str:
        return str(user.cnpj or user.cpf or user.company.companyCnpj or user.company.cpf)

    def get_seller_object(self, user: User) -> dict:
        phone = self.get_user_phone(user)

        user_document = self.get_user_document(user)

        return {
            "caktoId": user.id,
            "email": user.email,
            "name": ' '.join([user.first_name, user.last_name]),
            'document': user_document,
            "segment": user.mostSells,
            "organizationName": user.companyName,  # Opcional
            "phone": phone,
            "birthdate": None,
            "whatsapp": phone,
            "instagram": user.instagram,
            "url": None,  # Opcional
            "questions": {
                'companyProduct': user.companyProduct,
                'recurringAmount': float(user.recurringAmount or 0),
                'businessModel': user.businessModel,
                'actualPlatform': user.actualPlatform,
                'biggerPain': user.biggerPain,
                'indication': user.indication,
            }
        }

    def get_create_payload(self, user: User) -> dict:
        seller = self.get_seller_object(user)

        payload = {
            'chargebacks': [],
            'refunds': [],
            'sells': [],
            **seller,
        }

        return payload

    def get_chargeback_payload(self, order: Order) -> dict:
        seller = self.get_seller_object(order.product.user)

        chargebackDate = normalize_datetime_input(order.chargedbackAt or timezone.now()).isoformat()

        return {
            'orderId': order.id,
            'total': int(round((order.amount or 0) * 100, 2)),  # Valor do chargeback como inteiro
            'chargebackDate': chargebackDate,  # '2021-09-01T00:00:00.000Z',
            'productName': order.product.name,
            'buyerEmail': order.customer.email,
            'seller': seller,
        }

    def get_refund_payload(self, order: Order) -> dict:
        seller = self.get_seller_object(order.product.user)

        refundDate = normalize_datetime_input(order.refundedAt or timezone.now()).isoformat()

        return {
            'orderId': order.id,
            'total': int(round((order.amount or 0) * 100, 2)),  # Valor do chargeback como inteiro
            'refundDate': refundDate,  # '2021-09-01T00:00:00.000Z',
            'productName': order.product.name,
            'reason': order.refund_reason,  # Motivo do reembolso
            'buyerEmail': order.customer.email,
            'seller': seller,
        }

    def get_sale_payload(self, order: Order) -> dict:
        seller = self.get_seller_object(order.product.user)

        return {
            'orderId': order.id,
            'total': int(round((order.amount or 0) * 100, 2)),  # Valor do chargeback como inteiro
            'orderDate': normalize_datetime_input(order.createdAt).isoformat(),
            'buyerEmail': order.customer.email,
            'seller': seller,
        }

    def dispatch_crm_webhook(self, payload: dict, endpoint) -> requests.Response | None:
        if not self.URL:
            return None

        url = urljoin(self.URL, endpoint)
        headers = {
            'Content-Type': 'application/json',
            'X-Webhook-Secret': self.TOKEN,
        }

        return requests.post(url, json=payload, headers=headers)

    def handle_response(self, response: requests.Response | None, action: str, user: User | None = None, order: Order | None = None) -> requests.Response | None:
        if response is None:
            return None

        self.create_history(response=response, action=action, user=user, order=order)

        if not status.is_success(response.status_code):
            raise Exception(f'Error {response.status_code} on CRM webhook -> ' + response.content.decode())

        return response

    def create_history(self, response: requests.Response, action: str, user: User | None = None, order: Order | None = None) -> CRMApiHistory:
        request_body = response.request.body
        request_body = (json.loads(request_body.decode()) if isinstance(request_body, bytes) else request_body) or ''

        return CRMApiHistory.objects.create(
            order=order,
            user=user,
            action=action,
            payload=request_body,
            response=json.loads(response.content.decode()),
            response_status_code=response.status_code,
        )

@job('default', retry=Retry(max=4, interval=[30]))
def send_crm_create_user_event(user: User) -> requests.Response | None:
    crm_sdk = CRMSdk()
    payload = crm_sdk.get_create_payload(user)
    endpoint = '/api/v1/loader'

    response = crm_sdk.dispatch_crm_webhook(payload=payload, endpoint=endpoint)

    return crm_sdk.handle_response(user=user, order=None, action='create_user', response=response)

@job('default', retry=Retry(max=4, interval=[30]))
def send_crm_chargeback_event(order: Order) -> requests.Response | None:
    crm_sdk = CRMSdk()
    payload = crm_sdk.get_chargeback_payload(order=order)
    endpoint = f'/api/v1/loader/{order.id}/chargeback'

    response = crm_sdk.dispatch_crm_webhook(payload=payload, endpoint=endpoint)

    return crm_sdk.handle_response(user=None, order=order, action='chargeback', response=response)

@job('default', retry=Retry(max=4, interval=[30]))
def send_crm_refund_event(order: Order) -> requests.Response | None:
    crm_sdk = CRMSdk()
    payload = crm_sdk.get_refund_payload(order=order)
    endpoint = f'/api/v1/loader/{order.id}/refund'

    response = crm_sdk.dispatch_crm_webhook(payload=payload, endpoint=endpoint)

    return crm_sdk.handle_response(user=None, order=order, action='refund', response=response)

@job('default', retry=Retry(max=4, interval=[30]))
def send_crm_sale_event(order: Order) -> requests.Response | None:
    crm_sdk = CRMSdk()
    payload = crm_sdk.get_sale_payload(order=order)
    endpoint = f'/api/v1/loader/{order.id}/sell'

    response = crm_sdk.dispatch_crm_webhook(payload=payload, endpoint=endpoint)

    return crm_sdk.handle_response(user=None, order=order, action='create_order', response=response)
