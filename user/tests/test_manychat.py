from rest_framework.exceptions import ValidationError

from cakto.tests.base import BaseTestCase
from user.sdk.manychat import ManyChatWhatsapp


class CallOnce:
    def __init__(self, func):
        self.func = func
        self.called = False

    def __call__(self, *args, **kwargs):
        if not self.called:
            self.called = True
            return self.func(*args, **kwargs)

        return None

class ManyChatSDKTest(BaseTestCase):
    def setUp(self):
        self.manychat = ManyChatWhatsapp()
        self.user = self.create_user(
            cellphone='123456789',
            phoneCountryCode='55',
        )
        return super().setUp()

    def test_manychat_url(self):
        self.assertEqual(self.manychat.url, 'https://api.manychat.com')

    def test_manychat_token(self):
        self.assertIsNotNone(self.manychat.token)

    def test_manychat_api(self):
        self.assertIsNotNone(self.manychat.api)

    def test_handle_manychat_subscriber_raises_ValidationError_when_user_has_no_cellphone(self):
        self.user.cellphone = None
        self.user.save()

        with self.assertRaises(ValidationError):
            self.manychat.handle_manychat_subscriber(self.user)
