from unittest import mock
from rest_framework.exceptions import ValidationError
from cakto.tests.base import BaseTestCase
from user.sdk.infobip import InfoBipWhatsApp
from django.test import override_settings

TEST_BASE_URL = 'https://api.infobip.com'
TEST_API_TOKEN = '1234'
TEST_WHATSAPP_NUMBER = '11999998888'
TEST_TEMPLATE_NAME = 'cakto_auth'


@override_settings(
    INFOBIP_BASE_URL=TEST_BASE_URL,
    INFOBIP_API_TOKEN=TEST_API_TOKEN,
    INFOBIP_WHATSAPP_NUMBER=TEST_WHATSAPP_NUMBER,
    INFOBIP_AUTH_TEMPLATE_NAME=TEST_TEMPLATE_NAME,
)
class InfobipTestCase(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.infobip = InfoBipWhatsApp()

        cls.user.cellphone = '123456789'
        cls.user.phoneCountryCode = '55'
        cls.user.save(update_fields=['cellphone', 'phoneCountryCode'])

    def test_infobip_url(self):
        self.assertEqual(self.infobip.url, TEST_BASE_URL)

    def test_infobip_token(self):
        self.assertEqual(self.infobip.token, TEST_API_TOKEN)

    def test_infobip_whatsapp_number(self):
        self.assertEqual(self.infobip.whatsapp_number, TEST_WHATSAPP_NUMBER)

    def test_infobip_template_name(self):
        self.assertEqual(self.infobip.template_name, TEST_TEMPLATE_NAME)

    def test_infobip_api_headers(self):
        self.assertEqual(self.infobip.api.headers['Authorization'], f'App {TEST_API_TOKEN}')

    def test_infobip_validade_user_raises_ValidationError_when_user_has_no_cellphone(self):
        self.user.cellphone = None
        self.user.save(update_fields=['cellphone'])

        with self.assertRaises(ValidationError):
            self.infobip.validate_user(self.user)

    def test_infobip_validade_user_raises_ValidationError_when_user_already_validated(self):
        self.user.whatsappValidated = True
        self.user.save(update_fields=['whatsappValidated'])

        with self.assertRaises(ValidationError):
            self.infobip.validate_user(self.user)

    def test_infobip_validade_user_raises_ValidationError_when_a_user_already_validated_with_same_phone(self):
        self.user.whatsappValidated = True
        self.user.save(update_fields=['whatsappValidated'])

        self.create_user(
            cellphone=self.user.cellphone,
            phoneCountryCode=self.user.phoneCountryCode,
            whatsappValidated=True,
        )

        with self.assertRaises(ValidationError):
            self.infobip.validate_user(self.user)

    def test_infobip_handle_response_raises_ValidationError_when_message_isnt_delivered_or_pending(self):
        with self.assertRaises(ValidationError):
            self.infobip.handle_response(self.get_response_mock(status=400, content={'messages': [{'status': {'name': 'ERROR'}}]}))

    def test_infobip_send_token_calls_validate_user(self):
        mock.patch('user.sdk.infobip.InfoBipWhatsApp.handle_response', return_value=None).start()
        self.infobip.api = mock.MagicMock()

        with mock.patch('user.sdk.infobip.InfoBipWhatsApp.validate_user') as mock_validate_user:
            self.infobip.send_token(self.user, '1234')

        mock_validate_user.assert_called_once_with(self.user)

    def test_infobip_send_token_calls_handle_response(self):
        mock.patch('user.sdk.infobip.InfoBipWhatsApp.validate_user', return_value=None).start()
        self.infobip.api = mock.MagicMock()

        with mock.patch('user.sdk.infobip.InfoBipWhatsApp.handle_response') as mock_handle_response:
            self.infobip.send_token(self.user, '1234')

        mock_handle_response.assert_called_once()

    def test_infobip_send_token_payload(self):
        mock.patch('user.sdk.infobip.InfoBipWhatsApp.handle_response', return_value=None).start()
        mock.patch('user.sdk.infobip.InfoBipWhatsApp.validate_user', return_value=None).start()
        self.infobip.api = mock.MagicMock()

        self.user.whatsappToken = '1234'
        self.user.save(update_fields=['whatsappToken'])

        expected_endpoint = f'{TEST_BASE_URL}/whatsapp/1/message/template'

        expected_payload = {
            'messages': [
                {
                    'from': TEST_WHATSAPP_NUMBER,
                    'to': self.user.phoneCountryCode + self.user.cellphone,  # type: ignore
                    'content': {
                        'templateName': TEST_TEMPLATE_NAME,
                        'templateData': {
                            'body': {
                                'placeholders': [self.user.whatsappToken]
                            },
                            'buttons': [
                                {
                                    'type': 'URL',
                                    'parameter': self.user.whatsappToken,
                                }
                            ]
                        },
                        'language': 'pt_BR'
                    },
                }
            ]
        }

        self.infobip.send_token(self.user, self.user.whatsappToken)

        self.infobip.api.post.assert_called_once_with(expected_endpoint, json=expected_payload)
