import json
import logging
import time
from datetime import timedelta
from unittest import mock
from unittest.mock import patch

from django.core.cache import cache
from django.test import override_settings
from django.urls import reverse
from django.utils import timezone
from fcm_django.models import FCMDevice
from requests.models import Response
from rest_framework import status

from cakto.tests.base import BaseTestCase
from financial.models import Company
from financial.utils import handle_approval
from gateway.sdk.splitpay import SplitPay
from user.enums import MFAType, UserAccessLocations
from user.models import MFAMethod, RecoveryTokenHistory, User, UserLoginHistory
from user.utils import get_or_create_user


def mock_createAccount(*args, **kwargs):
    mock_response = Response()
    company_id = 100 + (Company.objects.count() + 1)
    mock_response._content = json.dumps({"id": company_id}).encode('utf-8')
    mock_response.status_code = status.HTTP_200_OK
    return lambda *args, **kwargs: mock_response

@override_settings(WHATSAPP_VALIDATION_ENABLED=True)
class UserTest(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.create_paymentMethods()

        cls.login_data = {
            'email': '<EMAIL>',
            'password': 'teste1234'
        }

        cls.user_headers = cls.build_user_auth_headers(cls.user)

        cls.user.emailValidated = False
        cls.user.whatsappValidated = False
        cls.user.save(update_fields=['emailValidated', 'whatsappValidated'])

    def get_register_payload(self):
        return {
            'name': 'Test User',
            'instagram': '@testuser',
            'companyProduct': 'Test Product',
            'mostSells': 'Softwares',
            'recurringAmount': 10_000,
            'businessModel': 'Testing Business',
            'actualPlatform': 'Cakto',
            'biggerPain': 'Some Pain',
            'indication': 'Test Indicator',
            'password': 'Test12345@',
            'email': '<EMAIL>',
        }

    @patch.object(SplitPay, 'createAccount', new_callable=mock_createAccount)
    def test_user_register(self, *args, **kwargs):
        payload = self.get_register_payload()

        send_crm_create_user_event_mock = mock.patch(
            'user.views.send_crm_create_user_event.delay',
        ).start()

        mock.patch('user.views.send_register_event_to_fb').start()
        mock.patch('user.views.cloudflarecheck').start()

        # Call
        response = self.client.post('/api/register/', payload)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())
        self.assertTrue('access' in response.data)  # type:ignore
        self.assertTrue('refresh' in response.data)  # type:ignore

        user = User.objects.get(email=payload['email'])

        self.assertTrue(user.is_producer)

        send_crm_create_user_event_mock.assert_called_once_with(user=user)

    @patch.object(SplitPay, 'createAccount', new_callable=mock_createAccount)
    def test_user_register_saves_facebook_cookies(self, *args, **kwargs):
        payload = self.get_register_payload()

        # Add Facebook cookies to the payload
        payload['fbc'] = 'fb_click_value'
        payload['fbp'] = 'fb_pixel_value'

        mock.patch('user.views.send_crm_create_user_event.delay').start()
        mock.patch('user.views.send_register_event_to_fb').start()
        mock.patch('user.views.cloudflarecheck').start()

        # Call
        response = self.client.post('/api/register/', payload)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())

        user = User.objects.get(email=payload['email'])

        self.assertEqual(user.fbc, payload['fbc'])
        self.assertEqual(user.fbp, payload['fbp'])

    @patch.object(SplitPay, 'createAccount', new_callable=mock_createAccount)
    def test_user_register_saves_utm_values(self, *args, **kwargs):
        payload = self.get_register_payload()

        # Add utm values to the payload
        payload['utm_source'] = 'google'
        payload['utm_medium'] = 'cpc'
        payload['utm_campaign'] = 'test_campaign'
        payload['utm_content'] = 'test_content'
        payload['utm_term'] = 'test_term'
        payload['sck'] = '********'

        mock.patch('user.views.send_crm_create_user_event.delay').start()
        mock.patch('user.views.send_register_event_to_fb').start()
        mock.patch('user.views.cloudflarecheck').start()

        # Call
        response = self.client.post('/api/register/', payload)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())

        user = User.objects.get(email=payload['email'])

        self.assertEqual(user.utm_source, payload['utm_source'])
        self.assertEqual(user.utm_medium, payload['utm_medium'])
        self.assertEqual(user.utm_campaign, payload['utm_campaign'])
        self.assertEqual(user.utm_content, payload['utm_content'])
        self.assertEqual(user.utm_term, payload['utm_term'])
        self.assertEqual(user.sck, payload['sck'])

    @patch.object(SplitPay, 'createAccount', new_callable=mock_createAccount)
    def test_user_register_utm_source_defaults_to_direto(self, *args, **kwargs):
        payload = self.get_register_payload()

        # remove utm_source from the payload
        payload.pop('utm_source', None)

        mock.patch('user.views.send_crm_create_user_event.delay').start()
        mock.patch('user.views.send_register_event_to_fb').start()
        mock.patch('user.views.cloudflarecheck').start()

        # Call
        response = self.client.post('/api/register/', payload)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())

        user = User.objects.get(email=payload['email'])

        # assert utm_source defaults to 'direto'
        self.assertEqual(user.utm_source, 'direto')

    @patch.object(SplitPay, 'createAccount', new_callable=mock_createAccount)
    def test_user_register_send_register_event_to_fb(self, *args, **kwargs):
        payload = self.get_register_payload()

        mock.patch('user.views.send_crm_create_user_event.delay').start()
        mock.patch('user.views.cloudflarecheck').start()

        send_register_event_to_fb_mock = mock.patch(
            'user.views.send_register_event_to_fb.delay'
        ).start()

        headers = {'User-Agent': 'CaktoAPP/1.0'}

        expected_ip = '********'

        # Call
        response = self.client.post('/api/register/', payload, headers=headers, REMOTE_ADDR=expected_ip)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())

        user = User.objects.get(email=payload['email'])

        send_register_event_to_fb_mock.assert_called_once_with(
            user=user,
            ip=expected_ip,
            user_agent=headers['User-Agent'],
        )

    def test_user_register_does_not_save_user_when_error_creating_split_account(self, *args, **kwargs):
        User.objects.all().delete()

        payload = self.get_register_payload()

        mock.patch('user.views.send_crm_create_user_event.delay').start()
        mock.patch('user.views.send_register_event_to_fb').start()
        mock.patch('user.views.cloudflarecheck').start()

        create_account_mock = self.get_response_mock(status=400)

        mock.patch('user.models.SplitPay.createAccount', return_value=create_account_mock).start()

        # Suppress api exception logging
        self.logger = logging.getLogger('django.request')
        self.logger.setLevel(logging.CRITICAL)

        # Call
        with self.assertRaises(Exception):
            self.client.post('/api/register/', payload)

        self.assertIsNone(User.objects.first(), 'User should not be created when SplitPay account creation fails')

    @patch.object(SplitPay, 'createAccount', new_callable=mock_createAccount)
    def test_cant_create_without_required_fields(self, *args, **kwargs):
        with mock.patch('user.views.cloudflarecheck', mock.Mock()):
            data = self.get_register_payload()
            with self.subTest():
                for field in data:
                    data_copy = data.copy()
                    data_copy.pop(field)

                    response = self.client.post('/api/register/', data_copy)

                    self.assertEqual(
                        response.status_code, status.HTTP_400_BAD_REQUEST,
                        'The field "%s" should be required on user register api' % field
                    )
                    User.objects.all().delete()

    @patch.object(SplitPay, 'createAccount', new_callable=mock_createAccount)
    def test_all_fields_are_set_on_user_create(self, *args, **kwargs):
        data = self.get_register_payload()

        mock.patch('user.views.cloudflarecheck', mock.Mock()).start()
        mock.patch('user.views.send_register_event_to_fb', mock.Mock()).start()

        response = self.client.post('/api/register/', data)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())
        user = User.objects.get(email=data['email'])
        self.assertEqual(user.first_name, data['name'].split()[0])
        self.assertEqual(user.last_name, ' '.join(data['name'].split()[1:]))
        self.assertEqual(user.username, data['email'])
        self.assertEqual(user.viewPageAs, 'producer')
        self.assertEqual(user.instagram, data['instagram'])
        self.assertEqual(user.companyProduct, data['companyProduct'])
        self.assertEqual(user.mostSells, data['mostSells'])
        self.assertEqual(user.recurringAmount, data['recurringAmount'])
        self.assertEqual(user.businessModel, data['businessModel'])
        self.assertEqual(user.actualPlatform, data['actualPlatform'])
        self.assertEqual(user.biggerPain, data['biggerPain'])
        self.assertEqual(user.indication, data['indication'])

    @patch.object(SplitPay, 'createAccount', new_callable=mock_createAccount)
    def test_viewPageAs_equals_producer_when_register(self, *args, **kwargs):
        mock.patch('user.views.cloudflarecheck', mock.Mock()).start()
        mock.patch('user.views.send_register_event_to_fb', mock.Mock()).start()

        # Call
        registrer_response = self.client.post(
            '/api/register/',
            self.get_register_payload()
        )

        self.assertEqual(registrer_response.status_code, 200, registrer_response.content.decode())

        response = self.client.get('/api/user/', HTTP_AUTHORIZATION='Bearer ' + str(registrer_response.json()['access']))
        self.assertEqual(response.json()['viewPageAs'], 'producer')

    def test_user_request_email_token(self, *args, **kwargs):
        url = reverse('user:request-email-token')

        with mock.patch('user.views.send_otp_token_email', mock.Mock(return_value=True)):
            response = self.client.post(url, headers=self.user_headers)
        self.user.refresh_from_db()

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())
        self.assertTrue(isinstance(self.user.emailToken, str))
        self.assertTrue(len(self.user.emailToken) == 6)  # type:ignore

    def test_user_request_email_throttle(self, *args, **kwargs):
        url = reverse('user:request-email-token')
        user = self.create_user()
        user_headers = self.create_headers(self.get_user_access_token(user))

        with mock.patch('user.views.send_otp_token_email', mock.Mock(return_value=True)):
            response = self.client.post(url, headers=user_headers)
            response_2 = self.client.post(url, headers=user_headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())
        self.assertEqual(response_2.status_code, status.HTTP_429_TOO_MANY_REQUESTS, response_2.content)

    def test_user_validate_email_token(self, *args, **kwargs):
        url = reverse('user:email-validate')

        self.user.emailToken = '123456'
        self.user.emailTokenExpires = timezone.now() + timezone.timedelta(minutes=5)
        self.user.save(update_fields=['emailToken', 'emailTokenExpires'])

        response = self.client.post(url, headers=self.user_headers, data={'token': self.user.emailToken}, format='json')
        self.user.refresh_from_db()

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())
        self.assertEqual(self.user.emailToken, None)
        self.assertEqual(self.user.emailTokenExpires, None)
        self.assertEqual(self.user.emailValidated, True)
        self.assertIsNotNone(self.user.emailValidatedAt)

    def test_user_validate_email_token_with_wrong_token(self, *args, **kwargs):
        url = reverse('user:email-validate')
        self.user.emailToken = '123456'
        self.user.emailTokenExpires = timezone.now() + timezone.timedelta(minutes=5)
        self.user.save()

        response = self.client.post(url, headers=self.user_headers, data={'token': '987654'}, format='json')
        self.user.refresh_from_db()

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST, response.content.decode())
        self.assertEqual(self.user.emailValidated, False)
        self.assertIsNone(self.user.emailValidatedAt)

    def test_user_validate_email_token_with_expired_token(self, *args, **kwargs):
        url = reverse('user:email-validate')
        self.user.emailToken = '123456'
        self.user.emailTokenExpires = timezone.now() - timezone.timedelta(minutes=5)
        self.user.save()

        response = self.client.post(url, headers=self.user_headers, data={'token': self.user.emailToken}, format='json')
        self.user.refresh_from_db()

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST, response.content.decode())
        self.assertEqual(self.user.emailValidated, False)
        self.assertIsNone(self.user.emailValidatedAt)

    def test_user_validate_email_token_already_validated(self, *args, **kwargs):
        url = reverse('user:email-validate')
        self.user.emailValidated = True
        self.user.save()

        response = self.client.post(url, headers=self.user_headers, data={'token': self.user.emailToken}, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST, response.content.decode())

    def test_user_validate_whatsapp_token(self, *args, **kwargs):
        url = reverse('user:whatsapp-validate')
        self.user.whatsappToken = '123456'
        self.user.whatsappTokenExpires = timezone.now() + timezone.timedelta(minutes=5)
        self.user.save()

        response = self.client.post(url, headers=self.user_headers, data={'token': self.user.whatsappToken}, format='json')
        self.user.refresh_from_db()

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())
        self.assertEqual(self.user.whatsappToken, None)
        self.assertEqual(self.user.whatsappTokenExpires, None)
        self.assertEqual(self.user.whatsappValidated, True)
        self.assertIsNotNone(self.user.whatsappValidatedAt)

    def test_user_validate_whatsapp_token_with_wrong_token(self, *args, **kwargs):
        url = reverse('user:whatsapp-validate')
        self.user.whatsappToken = '123456'
        self.user.whatsappTokenExpires = timezone.now() + timezone.timedelta(minutes=5)
        self.user.save()

        response = self.client.post(url, headers=self.user_headers, data={'token': '987654'}, format='json')
        self.user.refresh_from_db()

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST, response.content.decode())
        self.assertEqual(self.user.whatsappValidated, False)
        self.assertIsNone(self.user.whatsappValidatedAt)

    def test_user_validate_whatsapp_token_with_expired_token(self, *args, **kwargs):
        url = reverse('user:whatsapp-validate')
        self.user.whatsappToken = '123456'
        self.user.whatsappTokenExpires = timezone.now() - timezone.timedelta(minutes=5)
        self.user.save()

        response = self.client.post(url, headers=self.user_headers, data={'token': self.user.whatsappToken}, format='json')
        self.user.refresh_from_db()

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST, response.content.decode())
        self.assertEqual(self.user.whatsappValidated, False)
        self.assertIsNone(self.user.whatsappValidatedAt)

    def test_user_validate_whatsapp_token_already_validated(self, *args, **kwargs):
        url = reverse('user:whatsapp-validate')
        self.user.whatsappValidated = True
        self.user.save()

        response = self.client.post(url, headers=self.user_headers, data={'token': self.user.whatsappToken}, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST, response.content.decode())

    def test_user_totalSales_cache(self, *args, **kwargs):
        url = reverse('user:user')
        cache_key = f'{User.totalSales_cache_prefix}{self.user.pk}'

        response = self.client.get(url, headers=self.user_headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())
        self.assertEqual(cache.get(cache_key), self.user.totalSales())

    def test_handle_approval_deletes_users_totalSales_cache(self, *args, **kwargs):
        user_2 = self.create_user()
        cache_key = f'{User.totalSales_cache_prefix}{self.user.pk}'
        cache_key_2 = f'{User.totalSales_cache_prefix}{user_2.pk}'

        cache.set(cache_key, 1000)

        commissionedUsers = [self.user, user_2]
        handle_approval(self.create_order(product=self.create_product(user=self.user), commissionedUsers=commissionedUsers), payment=self.create_payment(), webhook_data={})

        self.assertEqual(cache.get(cache_key), None)
        self.assertEqual(cache.get(cache_key_2), None)

    def test_user_cant_update_with_cellphone_and_without_phoneCountryCode(self, *args, **kwargs):
        url = reverse('user:user')
        response = self.client.put(url, headers=self.user_headers, data={'cellphone': '1234567890'}, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST, response.content.decode())

    def test_user_cant_update_with_phoneCountryCode_and_without_cellphone(self, *args, **kwargs):
        url = reverse('user:user')
        response = self.client.put(url, headers=self.user_headers, data={'phoneCountryCode': '55'}, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST, response.content.decode())

    def test_user_update_cellphone_and_phoneCountryCode(self, *args, **kwargs):
        url = reverse('user:user')
        self.user.phoneCountryCode = '33'
        self.user.cellphone = '988887777'

        payload = {
            'phoneCountryCode': '55',
            'cellphone': '1234567890'
        }

        response = self.client.put(url, headers=self.user_headers, data=payload, format='json')

        self.user.refresh_from_db()

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())
        self.assertEqual(self.user.phoneCountryCode, payload['phoneCountryCode'])
        self.assertEqual(self.user.cellphone, payload['cellphone'])

    def test_user_cant_use_cellphone_and_phoneCountryCode_already_whatsappValidated(self, *args, **kwargs):
        url = reverse('user:user')
        payload = {
            'phoneCountryCode': '55',
            'cellphone': '1234567890'
        }

        self.create_user(cellphone=payload['cellphone'], phoneCountryCode=payload['phoneCountryCode'], whatsappValidated=True)

        self.user.whatsappValidated = False
        self.user.cellphone = '988887777'
        self.user.phoneCountryCode = '33'
        self.user.save(update_fields=['whatsappValidated', 'cellphone', 'phoneCountryCode'])

        response = self.client.put(url, headers=self.user_headers, data={'phoneCountryCode': '55', 'cellphone': '1234567890'}, format='json')

        self.user.refresh_from_db()

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST, response.content.decode())
        self.assertNotEqual(self.user.phoneCountryCode, payload['phoneCountryCode'])
        self.assertNotEqual(self.user.cellphone, payload['cellphone'])

    def test_user_update_cnpj(self, *args, **kwargs):
        url = reverse('user:user')
        self.user.cnpj = '02721420000192'
        self.user.save()

        payload = {'cnpj': '15635442000181'}

        response = self.client.put(url, headers=self.user_headers, data=payload, format='json')

        self.user.refresh_from_db()

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())
        self.assertEqual(self.user.cnpj, payload['cnpj'])

    def test_new_customer_user_is_created_with_is_customer_true(self, *args, **kwargs):
        product = self.create_product(user=self.user)

        customer = self.create_customer(name='Test Customer', email='<EMAIL>')
        order = self.create_order(product=product, customer=customer)

        handle_approval(order=order, payment=self.create_payment([order], product), webhook_data={})

        customer_user = User.objects.get(email=customer.email)

        self.assertTrue(customer_user.is_customer)

    def test_user_has_usable_password_is_in_get_user(self):
        url = reverse('user:user')

        response = self.client.get(url, headers=self.user_headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())

        self.assertTrue(response.data['has_usable_password'])

    def test_user_has_usable_password_is_false_in_get_user(self):
        url = reverse('user:user')

        self.user.set_unusable_password()
        self.user.save()

        response = self.client.get(url, headers=self.user_headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())

        self.assertFalse(response.data['has_usable_password'])

    def test_user_is_customer_is_in_get_user(self):
        url = reverse('user:user')

        self.user.is_customer = True
        self.user.save()

        response = self.client.get(url, headers=self.user_headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())

        self.assertTrue(response.data['is_customer'])

    def test_user_is_producer_is_in_get_user(self):
        url = reverse('user:user')

        self.user.is_producer = True
        self.user.save()

        response = self.client.get(url, headers=self.user_headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())

        self.assertTrue(response.data['is_producer'])

    def test_push_notification_token_creation(self):
        url = reverse('user:device-token')
        payload = {
            'token': 'test_token',
            'os': 'android'
        }

        # Call
        response = self.client.post(url, payload, format='json', headers=self.user_headers)

        self.assertEqual(
            response.status_code,
            status.HTTP_201_CREATED,
            msg=response.content.decode()
        )
        self.assertTrue(
            FCMDevice.objects.filter(
                user=self.user, registration_id='test_token'
            ).exists()
        )

    def test_push_notification_token_udpate(self):
        url = reverse('user:device-token')

        fcm_device = FCMDevice.objects.create(
            user=self.user,
            registration_id='test_token',
            type='android',
            device_id='test_device_id'
        )

        new_user = self.create_user()

        payload = {
            'token': 'test_token',  # same token
            'os': 'ios',
            'device_id': 'new_device_id'
        }

        # Call
        response = self.client.post(
            url,
            payload,
            format='json',
            headers=self.build_user_auth_headers(new_user)
        )

        self.assertEqual(
            response.status_code,
            status.HTTP_201_CREATED,
            msg=response.content.decode()
        )

        fcm_device.refresh_from_db()
        self.assertEqual(fcm_device.user, new_user)
        self.assertEqual(fcm_device.device_id, 'new_device_id')
        self.assertEqual(fcm_device.type, 'ios')

    def test_store_app_review(self):
        url = reverse('user:user-app-review')

        user = self.create_user()

        # Call
        response = self.client.post(url, format='json', headers=self.build_user_auth_headers(user))

        self.assertEqual(response.status_code, status.HTTP_200_OK, msg=response.content.decode())

        user.refresh_from_db()
        self.assertIsNotNone(user.app_reviewed_at)
        self.assertAlmostEqual(user.app_reviewed_at, timezone.now(), delta=timedelta(seconds=1))  # type:ignore

    def test_push_notification_token_logout(self):
        url = reverse('user:device-token-logout')

        FCMDevice.objects.create(
            user=self.user,
            registration_id='test_token',
            type='android',
        )

        # Call
        response = self.client.post(
            url,
            data={'token': 'test_token'},
            format='json',
            headers=self.user_headers
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertQuerySetEqual(FCMDevice.objects.all(), [])

    def test_get_or_create_user_creates_new_user_customer(self):
        email = "<EMAIL>"
        cellphone = "1234567890"

        user = get_or_create_user(
            email=email,
            cellphone=cellphone,
            emailValidated=True,
            is_customer=True
        )

        self.assertIsNotNone(user)
        self.assertEqual(user.email, email)
        self.assertEqual(user.cellphone, cellphone)
        self.assertTrue(user.emailValidated)
        self.assertTrue(user.is_customer)
        self.assertFalse(user.has_usable_password())

    def test_get_or_create_user_creates_new_user_is_producer(self):
        email = "<EMAIL>"
        cellphone = "1234567890"

        user = get_or_create_user(
            email=email,
            cellphone=cellphone,
            emailValidated=True,
            is_producer=True,
        )

        self.assertIsNotNone(user)
        self.assertEqual(user.email, email)
        self.assertEqual(user.cellphone, cellphone)
        self.assertTrue(user.emailValidated)
        self.assertTrue(user.is_producer)
        self.assertFalse(user.has_usable_password())
        self.assertTrue(
            MFAMethod.objects
            .filter(user=user, type=MFAType.EMAIL.id, confirmed=True)
            .exists()
        )

    def test_get_or_create_user_retrieves_existing_user(self):
        existing_user = self.create_user(
            email="<EMAIL>",
            cellphone="0987654321",
            emailValidated=False,
            is_producer=False,
            is_customer=False,
        )

        user = get_or_create_user(
            email=existing_user.email,
            cellphone='123',
            emailValidated=True,
            is_producer=True,
            is_customer=True,
        )

        self.assertEqual(user, existing_user)
        self.assertEqual(user.cellphone, "0987654321")
        self.assertFalse(user.is_producer)
        self.assertFalse(user.is_customer)
        self.assertFalse(user.emailValidated)

    def test_user_nps_survey_skip(self):
        url = reverse('user:nps-survey-skip')

        self.user.next_nps_survey_date = None
        self.user.save()

        response = self.client.post(url, headers=self.user_headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())

        self.user.refresh_from_db()

        self.assertAlmostEqual(
            self.user.next_nps_survey_date,  # type:ignore
            timezone.now() + timedelta(days=7),
            delta=timedelta(seconds=1),
            msg="next_nps_survey_date should be 7 days from now"
        )

    def test_user_otp_view_creates_user_login_history(self):
        UserLoginHistory.objects.all().delete()  # Clear any login history

        url = reverse('user:otp-access')

        token = '123456'
        self.user.recovery_password_token = token
        self.user.recovery_password_token_expires = timezone.now() + timezone.timedelta(minutes=5)
        self.user.save(update_fields=['recovery_password_token', 'recovery_password_token_expires'])

        payload = {'token': token}

        ip = '*********'
        user_agent = 'Testing User Agent'

        response = self.client.post(
            url,
            data=payload,
            format='json',
            HTTP_USER_AGENT=user_agent,
            REMOTE_ADDR=ip,
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())

        self.assertEqual(UserLoginHistory.objects.count(), 1)

        history: UserLoginHistory = UserLoginHistory.objects.first()  # type:ignore

        self.assertEqual(history.user, self.user)
        self.assertEqual(history.ip, ip)
        self.assertEqual(history.user_agent, user_agent)
        self.assertEqual(
            history.access_token_source,
            UserAccessLocations.OTP_TOKEN.id
        )
        self.assertAlmostEqual(
            history.createdAt,
            timezone.now(),
            delta=timedelta(seconds=1),
        )

    def test_saving_user_with_profile_complete_fields_setted_calls_send_register_webhook(self):
        self.user.first_name = 'Test'
        self.user.last_name = 'User'
        self.user.instagram = '@testuser'
        self.user.phoneCountryCode = '55'
        self.user.cellphone = '1234567890'
        self.user.companyName = 'Test Product'
        self.user.companyProduct = 'Test Product'
        self.user.mostSells = 'Test Sells'
        self.user.recurringAmount = 1000
        self.user.businessModel = 'Test Business'
        self.user.actualPlatform = 'Test Platform'
        self.user.biggerPain = 'Test Pain'
        self.user.indication = 'Test Indication'
        self.user.register_profile_completed = False

        send_register_webhook_mock = mock.patch(
            'user.utils.send_register_webhook',
        ).start()

        self.user.save()

        send_register_webhook_mock.assert_called_once_with(user=self.user)
        self.user.refresh_from_db()
        self.assertTrue(self.user.register_profile_completed)

    def test_saving_user_with_register_profile_completed_true_does_not_call_send_register_webhook(self):
        # Set register_profile_completed to True to simulate a completed profile
        self.user.register_profile_completed = True
        self.user.save()

        self.user.first_name = 'Test'
        self.user.last_name = 'User'
        self.user.instagram = '@testuser'
        self.user.phoneCountryCode = '55'
        self.user.cellphone = '1234567890'
        self.user.companyName = 'Test Product'
        self.user.companyProduct = 'Test Product'
        self.user.mostSells = 'Test Sells'
        self.user.recurringAmount = 1000
        self.user.businessModel = 'Test Business'
        self.user.actualPlatform = 'Test Platform'
        self.user.biggerPain = 'Test Pain'
        self.user.indication = 'Test Indication'

        send_register_webhook_mock = mock.patch(
            'user.utils.send_register_webhook',
        ).start()

        self.user.save()

        send_register_webhook_mock.assert_not_called()

    def test_saving_user_with_profile_complete_fields_not_setted_calls_send_register_webhook(self):
        # set some fields to None to simulate incomplete profile
        self.user.cellphone = None
        self.user.instagram = None
        self.user.indication = None
        self.user.register_profile_completed = False

        send_register_webhook_mock = mock.patch(
            'user.utils.send_register_webhook',
        ).start()

        self.user.save()

        send_register_webhook_mock.assert_not_called()

class UserRecoveryPasswordTest(BaseTestCase):
    def test_request_recovery_password_token(self):
        url = reverse('user:recovery-send-email')
        data = {'email': self.user.email, 'recaptchaToken': 'test_recaptcha'}

        with mock.patch('user.views.validate_recaptcha', mock.Mock()):
            with mock.patch('user.views.send_otp_token_email', mock.Mock(return_value=True)):
                cache.clear()
                response = self.client.post(url, data, format='json')

        self.user.refresh_from_db()
        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())
        self.assertTrue(isinstance(self.user.recovery_password_token, str))
        self.assertTrue(len(self.user.recovery_password_token) == 6)  # type:ignore

    def test_request_recovery_password_token_throttle(self):
        url = reverse('user:recovery-send-email')
        data = {'email': self.user.email, 'recaptchaToken': 'test_recaptcha'}

        cache.clear()
        with mock.patch('user.views.validate_recaptcha', mock.Mock()):
            with mock.patch('user.views.send_otp_token_email', mock.Mock(return_value=True)):
                with mock.patch('user.throttling.time.time', mock.Mock(return_value=time.time())) as mock_time:
                    response = self.client.post(url, data, format='json')  # pass
                    response_2 = self.client.post(url, data, format='json')  # block

                    mock_time.return_value += 61
                    response_3 = self.client.post(url, data, format='json')  # pass

                    mock_time.return_value += 10
                    response_4 = self.client.post(url, data, format='json')  # block

                    mock_time.return_value += 111
                    response_5 = self.client.post(url, data, format='json')  # pass

                    # After 3 requests, the user should be throttled for 5 minutes
                    mock_time.return_value += 299
                    response_6 = self.client.post(url, data, format='json')  # block

                    mock_time.return_value += 301
                    response_7 = self.client.post(url, data, format='json')  # pass

                    # After 1 hour, the throttle should be reset
                    mock_time.return_value += 3600
                    response_8 = self.client.post(url, data, format='json')  # pass
                    response_9 = self.client.post(url, data, format='json')  # block

                    mock_time.return_value += 61
                    response_10 = self.client.post(url, data, format='json')  # pass

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())
        self.assertEqual(response_2.status_code, status.HTTP_429_TOO_MANY_REQUESTS, response_2.content)
        self.assertEqual(response_3.status_code, status.HTTP_200_OK, response_3.content.decode())
        self.assertEqual(response_4.status_code, status.HTTP_429_TOO_MANY_REQUESTS, response_4.content.decode())
        self.assertEqual(response_5.status_code, status.HTTP_200_OK, response_5.content.decode())
        self.assertEqual(response_6.status_code, status.HTTP_429_TOO_MANY_REQUESTS, response_6.content.decode())
        self.assertEqual(response_7.status_code, status.HTTP_200_OK, response_7.content.decode())
        self.assertEqual(response_8.status_code, status.HTTP_200_OK, response_8.content.decode())
        self.assertEqual(response_9.status_code, status.HTTP_429_TOO_MANY_REQUESTS, response_9.content.decode())
        self.assertEqual(response_10.status_code, status.HTTP_200_OK, response_10.content.decode())

    def test_confirm_recovery_password_token(self):
        url = reverse('user:recovery-change-password')
        self.user.recovery_password_token = '123456'
        self.user.recovery_password_token_expires = timezone.now() + timezone.timedelta(minutes=30)
        self.user.save(update_fields=['recovery_password_token', 'recovery_password_token_expires'])

        data = {'token': self.user.recovery_password_token, 'password': 'NewPassword123'}
        cache.clear()
        response = self.client.post(url, data, format='json')

        self.user.refresh_from_db()
        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())
        self.assertTrue(self.user.check_password('NewPassword123'))
        self.assertIsNone(self.user.recovery_password_token)
        self.assertIsNone(self.user.recovery_password_token_expires)

    def test_confirm_recovery_password_token_invalid(self):
        url = reverse('user:recovery-change-password')
        self.user.recovery_password_token = '123456'
        self.user.recovery_password_token_expires = timezone.now() + timezone.timedelta(minutes=30)
        self.user.save(update_fields=['recovery_password_token', 'recovery_password_token_expires'])

        data = {'token': '654321', 'password': 'NewPassword123'}
        cache.clear()
        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND, response.content.decode())

    def test_confirm_recovery_password_token_expired(self):
        url = reverse('user:recovery-change-password')
        self.user.recovery_password_token = '123456'
        self.user.recovery_password_token_expires = timezone.now() - timezone.timedelta(minutes=1)
        self.user.save()

        data = {'token': '123456', 'password': 'NewPassword123'}
        cache.clear()
        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND, response.content.decode())

    def test_confirm_recovery_password_token_throttle(self):
        url = reverse('user:recovery-change-password')

        self.user.recovery_password_token = '123456'
        self.user.recovery_password_token_expires = timezone.now() + timezone.timedelta(minutes=30)
        self.user.save(update_fields=['recovery_password_token', 'recovery_password_token_expires'])

        data = {'token': self.user.recovery_password_token, 'password': 'NewPassword123'}

        cache.clear()
        response = self.client.post(url, data, format='json')  # pass
        response_2 = self.client.post(url, data, format='json')  # block

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())
        self.assertEqual(response_2.status_code, status.HTTP_429_TOO_MANY_REQUESTS, response_2.content)

    def test_request_recovery_password_token_creates_log_correctly(self):
        url = reverse('user:recovery-send-email')

        data = {'email': self.user.email, 'recaptchaToken': 'test_recaptcha'}

        with mock.patch('user.views.validate_recaptcha', mock.Mock()):
            with mock.patch('user.views.send_otp_token_email', mock.Mock(return_value=True)):
                cache.clear()
                response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())
        self.assertEqual(self.user.recovery_token_histories.count(), 1)

        self.user.refresh_from_db()
        history: RecoveryTokenHistory = RecoveryTokenHistory.objects.first()  # type:ignore
        self.assertEqual(history.user, self.user)
        self.assertEqual(history.token, self.user.recovery_password_token)
        self.assertEqual(history.token_expires_at, self.user.recovery_password_token_expires)
        self.assertEqual(history.event_type, 'created')
        self.assertTrue(timezone.now() - history.timestamp < timezone.timedelta(seconds=1))
        self.assertTrue(timezone.now() - history.updatedAt < timezone.timedelta(seconds=1))

    def test_confirm_recovery_password_token_creates_log_correctly(self):
        url = reverse('user:recovery-change-password')

        expected_token = '123456'
        expected_token_expires = timezone.now() + timezone.timedelta(minutes=30)
        self.user.recovery_password_token = expected_token
        self.user.recovery_password_token_expires = expected_token_expires
        self.user.save(update_fields=['recovery_password_token', 'recovery_password_token_expires'])

        data = {'token': self.user.recovery_password_token, 'password': 'NewPassword123'}

        cache.clear()
        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())
        self.assertEqual(self.user.recovery_token_histories.count(), 1)

        self.user.refresh_from_db()
        history: RecoveryTokenHistory = RecoveryTokenHistory.objects.first()  # type:ignore
        self.assertEqual(history.user, self.user)
        self.assertEqual(history.token, expected_token)
        self.assertEqual(history.token_expires_at, expected_token_expires)
        self.assertEqual(history.event_type, 'used')
        self.assertTrue(timezone.now() - history.timestamp < timezone.timedelta(seconds=1))
        self.assertTrue(timezone.now() - history.updatedAt < timezone.timedelta(seconds=1))
