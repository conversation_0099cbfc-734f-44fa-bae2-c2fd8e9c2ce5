from abc import ABC
from unittest import mock
from urllib.parse import quote

from django.test import override_settings
from django.urls import reverse
from django.utils import timezone
from django_otp.oath import totp
from django_otp.plugins.otp_email.models import EmailDevice
from django_otp.plugins.otp_totp.models import TOTPDevice
from fcm_django.models import FCMDevice

from cakto.tests.base import BaseTestCase
from user.enums import MFAType, UserAccessLocations
from user.models import AppCaktoDevice, MFAMethod, User, UserLoginHistory
from user.serializers import MFAMethodSerializer
from user.utils import user_token_generator


class MFATestMixin(ABC):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()  # type:ignore

        cls.mfa_method = cls.create_email_mfa(confirmed=True)  # type:ignore
        cls.mfa_method.email_device.generate_token()  # type:ignore

        cls.password = 'testPassword'  # type:ignore
        cls.user.set_password(cls.password)  # type:ignore
        cls.user.save(update_fields=['password'])  # type:ignore

        cls.ephemeral_token = user_token_generator.make_token(cls.user)  # type:ignore

    @classmethod
    def delete_mfa_methods(cls):
        MFAMethod.objects.all().delete()
        EmailDevice.objects.all().delete()
        TOTPDevice.objects.all().delete()
        AppCaktoDevice.objects.all().delete()

    @classmethod
    def create_email_device(cls, user: User | None = None, name: str | None = None) -> EmailDevice:
        return EmailDevice.objects.create(user=user or cls.user, name=name or cls.user.email)  # type:ignore

    @classmethod
    def create_totp_device(cls, user: User | None = None, name: str | None = None) -> TOTPDevice:
        return TOTPDevice.objects.create(user=user or cls.user, name=name or cls.user.email)  # type:ignore

    @classmethod
    def create_email_mfa(
        cls,
        user: User | None = None,
        confirmed: bool = True,
        primary: bool = False,
    ) -> MFAMethod:
        mfa = MFAMethod.get_email_method(user or cls.user, confirmed=confirmed)
        if primary:
            mfa.primary = primary
            mfa.save(update_fields=['primary'])
        return mfa

    @classmethod
    def create_totp_mfa(
        cls,
        user: User | None = None,
        confirmed: bool = True,
        primary: bool = False,
    ) -> MFAMethod:
        mfa = MFAMethod.get_totp_method(user or cls.user)

        if primary or confirmed or type:
            mfa.primary = primary
            mfa.confirmed = confirmed
            mfa.save(update_fields=['primary', 'confirmed'])

        return mfa

    @classmethod
    def create_app_cakto_mfa(
        cls,
        user: User | None = None,
        confirmed: bool = True,
        primary: bool = False,
    ) -> MFAMethod:
        mfa = MFAMethod.get_app_cakto_method(user or cls.user)

        if primary or confirmed or type:
            mfa.primary = primary
            mfa.confirmed = confirmed
            mfa.save(update_fields=['primary', 'confirmed'])

        return mfa

    @property
    def headers(self):
        return self.create_headers(self.get_user_access_token(self.user))  # type:ignore

class MFAGeneralTest(MFATestMixin, BaseTestCase):
    def test_activate_mfa_without_emailValidated_returns_403(self):
        self.user.emailValidated = False
        self.user.save(update_fields=['emailValidated'])

        url = reverse('user:activate-mfa')

        response = self.client.post(url, {'method': MFAType.EMAIL.id}, headers=self.headers)

        self.assertEqual(response.status_code, 403, response.content.decode())
        self.assertEqual(response.json(), {'detail': 'Você não tem permissão para executar essa ação.'})

    def test_activate_mfa_without_method_returns_400(self):
        url = reverse('user:activate-mfa')

        response = self.client.post(url, {}, headers=self.headers)

        self.assertEqual(response.status_code, 400, response.content.decode())
        self.assertEqual(response.json(), {'method': ['Este campo é obrigatório.']})

    def test_activate_mfa_with_invalid_method_returns_400(self):
        url = reverse('user:activate-mfa')

        response = self.client.post(url, {'method': 'invalid'}, headers=self.headers)

        self.assertEqual(response.status_code, 400, response.content.decode())
        self.assertEqual(response.json(), {'detail': 'Método inválido.'})

    def test_confirm_mfa_without_method_returns_400(self):
        url = reverse('user:confirm-mfa')

        response = self.client.post(url, {'code': '1234'}, headers=self.headers)

        self.assertEqual(response.status_code, 400, response.content.decode())
        self.assertEqual(response.json(), {'method': ['Este campo é obrigatório.']})

    def test_confirm_mfa_without_code_returns_400(self):
        url = reverse('user:confirm-mfa')

        response = self.client.post(url, {'method': MFAType.EMAIL.id}, headers=self.headers)

        self.assertEqual(response.status_code, 400, response.content.decode())
        self.assertEqual(response.json(), {'code': ['Este campo é obrigatório.']})

    def test_confirm_mfa_with_wrong_method_returns_400(self):
        url = reverse('user:confirm-mfa')

        payload = {
            'method': 'invalidMethod',
            'code': '1234',
        }

        response = self.client.post(url, payload, headers=self.headers)

        self.assertEqual(response.status_code, 400, response.content.decode())
        self.assertEqual(response.json(), {'detail': 'Método inválido.'})

    def test_list_mfa_methods_returns_200(self):
        url = reverse('user:list-mfa')

        fcm = FCMDevice.objects.create(user=self.user, registration_id='fcm_test_id', device_id='fcm_device_test_id')

        MFAMethod.objects.create(user=self.user, type=MFAType.TOTP.id, primary=False, confirmed=True)
        MFAMethod.objects.create(user=self.user, type=MFAType.APP_CAKTO.id, primary=False, confirmed=True, fcm_device=fcm)

        expected_result = [
            {'type': MFAType.EMAIL.id, 'primary': True, 'fcm_device_id': None, 'createdAt': mock.ANY, 'updatedAt': mock.ANY},
            {'type': MFAType.TOTP.id, 'primary': False, 'fcm_device_id': None, 'createdAt': mock.ANY, 'updatedAt': mock.ANY},
            {'type': MFAType.APP_CAKTO.id, 'primary': False, 'fcm_device_id': 'fcm_device_test_id', 'createdAt': mock.ANY, 'updatedAt': mock.ANY},
        ]

        response = self.client.get(url, headers=self.headers)

        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertEqual(
            sorted(response.json()['results'], key=lambda x: x['type']),
            sorted(expected_result, key=lambda x: x['type']),
        )

    def test_send_mfa_code_without_method_returns_400(self):
        url = reverse('user:send-mfa-code')

        expected_response = {
            'method': ['Este campo é obrigatório.'],
        }

        mock.patch('user.views.MFASendCodeView.throttle_scope', None).start()
        response = self.client.post(url, headers=self.headers)

        self.assertEqual(response.status_code, 400, response.content.decode())
        self.assertEqual(response.json(), expected_response)

    def test_send_mfa_code_with_invalid_method_returns_400(self):
        url = reverse('user:send-mfa-code')

        payload = {
            'method': 'invalidMethod',
            'code': '1234',
        }

        mock.patch('user.views.MFASendCodeView.throttle_scope', None).start()
        response = self.client.post(url, payload, headers=self.headers)

        self.assertEqual(response.status_code, 400, response.content.decode())
        self.assertEqual(response.json(), {'detail': 'Método de autenticação dois fatores não ativo.'})

    def test_send_mfa_code_without_mfa_active_returns_400(self):
        self.delete_mfa_methods()
        url = reverse('user:send-mfa-code')

        payload = {
            'method': MFAType.EMAIL.id,
            'code': '1234',
        }

        mock.patch('user.views.MFASendCodeView.throttle_scope', None).start()
        response = self.client.post(url, payload, headers=self.headers)

        self.assertEqual(response.status_code, 400, response.content.decode())
        self.assertEqual(response.json(), {'detail': 'Método de autenticação dois fatores não ativo.'})

    def test_send_mfa_code_calls_mfa_send_token(self):
        url = reverse('user:send-mfa-code')

        payload = {
            'method': MFAType.EMAIL.id,
            'code': '1234',
        }

        mock.patch('user.views.MFASendCodeView.throttle_scope', None).start()
        with mock.patch('user.views.MFAMethod.send_token', return_value=True) as send_token_mock:
            response = self.client.post(url, payload, headers=self.headers)

        self.assertEqual(response.status_code, 200, response.content.decode())
        send_token_mock.assert_called_once()

    def test_send_mfa_code_unauthenticated_request_calls_mfa_send_token(self):
        url = reverse('user:send-mfa-code')

        payload = {
            'method': MFAType.EMAIL.id,
            MFAType.EMAIL.id: self.user.email,
        }

        mock.patch('user.views.MFASendCodeView.throttle_scope', None).start()
        with mock.patch('user.views.MFAMethod.send_token', return_value=True) as send_token_mock:
            response = self.client.post(url, payload)

        self.assertEqual(response.status_code, 200, response.content.decode())
        send_token_mock.assert_called_once()

    def test_send_mfa_code_unauthenticated_request_without_email_returns_400(self):
        url = reverse('user:send-mfa-code')

        payload = {
            'method': MFAType.EMAIL.id,
        }

        mock.patch('user.views.MFASendCodeView.throttle_scope', None).start()
        with mock.patch('user.views.MFAMethod.send_token', return_value=True) as send_token_mock:
            response = self.client.post(url, payload)

        self.assertEqual(response.status_code, 400, response.content.decode())
        self.assertEqual(response.json(), {MFAType.EMAIL.id: ['Este campo é obrigatório.']})
        send_token_mock.assert_not_called()

    def test_send_mfa_code_throttle(self):
        url = reverse('user:send-mfa-code')

        # Creates another user for that the other tests don't affect this one
        user = self.create_user()
        MFAMethod.objects.create(user=user, type=MFAType.EMAIL.id, confirmed=True, primary=True)  # type:ignore

        payload = {'method': MFAType.EMAIL.id}

        mock.patch('user.views.MFAMethod.send_token', return_value=True).start()

        response = self.client.post(url, payload, headers=self.headers)
        self.assertEqual(response.status_code, 200, response.content.decode())

        response = self.client.post(url, payload, headers=self.headers)
        self.assertEqual(response.status_code, 429, response.content.decode())

    def test_send_mfa_code_throttle_unauthenticated_request(self):
        url = reverse('user:send-mfa-code')

        # Creates another user for that the other tests don't affect this one
        user = self.create_user()
        MFAMethod.objects.create(user=user, type=MFAType.EMAIL.id, confirmed=True, primary=True)  # type:ignore

        payload = {'method': MFAType.EMAIL.id, MFAType.EMAIL.id: user.email}

        mock.patch('user.views.MFAMethod.send_token', return_value=True).start()

        response = self.client.post(url, payload)
        self.assertEqual(response.status_code, 200, response.content.decode())

        response = self.client.post(url, payload)
        self.assertEqual(response.status_code, 429, response.content.decode())

    def test_change_mfa_primary_without_method_returns_400(self):
        url = reverse('user:change-mfa-primary')

        payload = {
            'currentPrimaryCode': '1234',
        }

        response = self.client.post(url, data=payload, headers=self.headers)

        self.assertEqual(response.status_code, 400, response.content.decode())
        self.assertEqual(response.json(), {'method': ['Este campo é obrigatório.']})

    def test_change_mfa_primary_whithout_current_method_code_returns_400(self):
        url = reverse('user:change-mfa-primary')

        response = self.client.post(url, {'method': MFAType.EMAIL.id}, headers=self.headers)

        self.assertEqual(response.status_code, 400, response.content.decode())
        self.assertEqual(response.json(), {'currentPrimaryCode': ['Este campo é obrigatório.']})

    def test_change_mfa_primary_with_invalid_method_returns_400(self):
        url = reverse('user:change-mfa-primary')

        payload = {
            'method': 'invalidMethod',
            'currentPrimaryCode': self.mfa_method.email_device.token,  # type:ignore
        }

        response = self.client.post(url, payload, headers=self.headers)

        self.assertEqual(response.status_code, 400, response.content.decode())
        self.assertEqual(response.json(), {'detail': 'Método de autenticação dois fatores não encontrado.'})

    def test_change_mfa_primary_with_wrong_currentPrimaryCode_returns_400(self):
        url = reverse('user:change-mfa-primary')

        payload = {
            'method': MFAType.EMAIL.id,
            'currentPrimaryCode': '1234',
        }

        response = self.client.post(url, payload, headers=self.headers)

        self.assertEqual(response.status_code, 400, response.content.decode())
        self.assertEqual(response.json(), {'detail': 'Código inválido.'})

    def test_change_mfa_primary_without_mfa_active_returns_400(self):
        self.delete_mfa_methods()
        url = reverse('user:change-mfa-primary')

        payload = {
            'method': 'invalidMethod',
            'currentPrimaryCode': '1234',
        }

        response = self.client.post(url, payload, headers=self.headers)

        self.assertEqual(response.status_code, 400, response.content.decode())
        self.assertEqual(response.json(), {'detail': 'Autenticação dois fatores não ativa.'})

    def test_change_mfa_primary_changes_correctly(self):
        url = reverse('user:change-mfa-primary')

        second_mfa_method = MFAMethod.objects.create(user=self.user, type=MFAType.TOTP.id, primary=False, confirmed=True)
        self.mfa_method.generate_token()

        payload = {
            'method': MFAType.TOTP.id,
            'currentPrimaryCode': self.mfa_method.email_device.token,  # type:ignore
        }

        response = self.client.post(url, data=payload, headers=self.headers)

        self.mfa_method.refresh_from_db()
        second_mfa_method.refresh_from_db()

        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertEqual(response.json(), {'detail': 'Método de autenticação dois fatores padrão alterado com sucesso.'})
        self.assertFalse(self.mfa_method.primary)
        self.assertTrue(second_mfa_method)

    def test_deactivate_mfa_without_method_returns_400(self):
        url = reverse('user:deactivate-mfa')

        response = self.client.post(url, {'code': '1234'}, headers=self.headers)

        self.assertEqual(response.status_code, 400, response.content.decode())
        self.assertEqual(response.json(), {'method': ['Este campo é obrigatório.']})

    def test_deactivate_mfa_without_code_returns_400(self):
        url = reverse('user:deactivate-mfa')

        response = self.client.post(url, {'method': MFAType.EMAIL.id}, headers=self.headers)

        self.assertEqual(response.status_code, 400, response.content.decode())
        self.assertEqual(response.json(), {'code': ['Este campo é obrigatório.']})

    def test_deactivate_mfa_with_invalid_method_returns_400(self):
        url = reverse('user:deactivate-mfa')

        payload = {
            'method': 'invalidMethod',
            'code': '1234',
        }

        response = self.client.post(url, payload, headers=self.headers)

        self.assertEqual(response.status_code, 400, response.content.decode())
        self.assertEqual(response.json(), {'detail': 'Método de autenticação dois fatores não ativo.'})

    def test_deactivate_mfa_without_mfa_active_returns_400(self):
        self.delete_mfa_methods()
        url = reverse('user:deactivate-mfa')

        payload = {
            'method': 'invalidMethod',
            'code': '1234',
        }

        response = self.client.post(url, payload, headers=self.headers)

        self.assertEqual(response.status_code, 400, response.content.decode())
        self.assertEqual(response.json(), {'detail': 'Método de autenticação dois fatores não ativo.'})

    def test_deactivate_mfa_with_primary_method_change_primary_to_another_mfa(self):
        url = reverse('user:deactivate-mfa')

        self.mfa_method.primary = False
        self.mfa_method.save(update_fields=['primary'])

        totp_mfa = self.create_totp_mfa()
        totp_mfa.primary = True
        totp_mfa.confirmed = True
        totp_mfa.save(update_fields=['primary', 'confirmed'])

        self.mfa_method.email_device.generate_token()  # type:ignore
        token = self.mfa_method.email_device.token  # type:ignore

        payload = {
            'method': MFAType.TOTP.id,
            'code': token,
        }

        response = self.client.post(url, payload, headers=self.headers, format='json')

        self.mfa_method.refresh_from_db()

        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertTrue(self.mfa_method.primary)

    def test_mfa_method_cant_have_more_than_one_device(self):
        totp_device = self.create_totp_device()

        with self.assertRaises(ValueError) as context:
            self.mfa_method.totp_device = totp_device
            self.mfa_method.save(update_fields=['totp_device'])
        self.assertEqual(str(context.exception), 'MFA Method cannot have more than one devices')

        self.mfa_method.refresh_from_db()
        self.assertIsNone(self.mfa_method.totp_device)

    def test_delete_mfa_method_deletes_email_device(self):
        self.delete_mfa_methods()

        mfa_method = self.create_email_mfa(confirmed=True, primary=True)

        self.assertEqual(EmailDevice.objects.count(), 1)

        mfa_method.delete()

        self.assertEqual(EmailDevice.objects.count(), 0)

    def test_delete_mfa_method_deletes_totp_device(self):
        self.delete_mfa_methods()

        mfa_method = self.create_totp_mfa(confirmed=True, primary=True)

        self.assertEqual(TOTPDevice.objects.count(), 1)

        mfa_method.delete()

        self.assertEqual(TOTPDevice.objects.count(), 0)

    def test_delete_mfa_method_deletes_app_cakto_device(self):
        self.delete_mfa_methods()

        mfa_method = self.create_app_cakto_mfa(confirmed=True, primary=True)

        self.assertEqual(TOTPDevice.objects.count(), 1)

        mfa_method.delete()

        self.assertEqual(TOTPDevice.objects.count(), 0)

    def test_user_can_have_3_mfa_methods_one_of_each_type(self):
        self.delete_mfa_methods()

        self.create_email_mfa(confirmed=True, primary=True)
        self.create_totp_mfa(confirmed=True)
        self.create_app_cakto_mfa(confirmed=True)

        self.assertEqual(MFAMethod.objects.count(), 3)

    def test_login_first_step_returns_list_of_confirmed_mfa_methods(self):
        url = reverse('user:mfa_first_step')

        MFAMethod.objects.create(user=self.user, type=MFAType.TOTP.id, confirmed=True, primary=False)
        MFAMethod.objects.create(user=self.user, type=MFAType.APP_CAKTO.id, confirmed=True, primary=False)

        expected_method_list = [
            {'type': MFAType.EMAIL.id, 'primary': True, 'fcm_device_id': None, 'createdAt': mock.ANY, 'updatedAt': mock.ANY},
            {'type': MFAType.TOTP.id, 'primary': False, 'fcm_device_id': None, 'createdAt': mock.ANY, 'updatedAt': mock.ANY},
            {'type': MFAType.APP_CAKTO.id, 'primary': False, 'fcm_device_id': None, 'createdAt': mock.ANY, 'updatedAt': mock.ANY},
        ]

        response = self.client.post(url, {MFAType.EMAIL.id: self.user.email, 'password': self.password})

        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertIn('methods', response.json(), 'methods key not found in response')
        self.assertEqual(
            sorted(response.json()['methods'], key=lambda x: x['type']),
            sorted(expected_method_list, key=lambda x: x['type']),
            'methods list is not correct'
        )

    @override_settings(MAX_MFA_THROTTLE_SECONDS=15 * 60)
    def test_mfa_verify_max_throttle_seconds(self):
        # increment the device throttle to a high value
        [self.mfa_method.device.throttle_increment() for _ in range(40)]

        send_token_url = reverse('user:send-mfa-code')

        # Call
        result = self.client.post(send_token_url, data={'method': 'email'}, headers=self.headers)

        # Assert
        self.assertEqual(result.status_code, 429, result.content.decode())

        # grab the seconds from the response -> {'detail': 'Pedido foi limitado. Expected available in 900 seconds.'}
        result_seconds = result.json().get('detail').split(' ')[-2]
        self.assertAlmostEqual(int(result_seconds), 15 * 60, delta=2)

    def test_reset_mfa_throttle_view(self):
        # increment the device throttle to a high value
        [self.mfa_method.device.throttle_increment() for _ in range(40)]

        user_admin = self.create_user(is_staff=True)

        url = reverse('user:admin-reset-mfa-throttle', args=[self.user.id])

        # make sure it's not allowed before
        self.assertFalse(self.mfa_method.verify_is_allowed()[0])

        # Call
        result = self.client.post(url, headers=self.build_user_auth_headers(user_admin))

        self.mfa_method.refresh_from_db()

        # Assert
        self.assertEqual(result.status_code, 200, result.content.decode())
        self.assertEqual(
            result.json(),
            {'detail': 'Throttle resetado com sucesso.'}
        )
        self.assertTrue(self.mfa_method.verify_is_allowed()[0])

class MFAEmailTest(MFATestMixin, BaseTestCase):
    def test_activate_email_mfa_with_method_already_activated_returns_400(self):
        url = reverse('user:activate-mfa')

        response = self.client.post(url, {'method': MFAType.EMAIL.id}, headers=self.headers)

        self.assertEqual(response.status_code, 400, response.content.decode())
        self.assertEqual(response.json(), {'detail': f'Autenticação de dois fatores por {MFAType.EMAIL.description} já ativado.'})

    def test_activate_email_mfa_retuns_200(self):
        self.delete_mfa_methods()
        url = reverse('user:activate-mfa')

        response = self.client.post(url, {'method': MFAType.EMAIL.id}, headers=self.headers)

        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertEqual(response.json(), {'detail': 'E-mail de verificação enviado com sucesso.'})
        self.assertEqual(MFAMethod.objects.count(), 1)

    def test_activate_email_mfa_with_existent_mfa_method_doesnt_change_primary_attr(self):
        self.mfa_method.primary = False
        self.mfa_method.confirmed = False
        self.mfa_method.save(update_fields=['primary', 'confirmed'])

        url = reverse('user:activate-mfa')

        payload = {
            'method': MFAType.EMAIL.id,
            'code': '1234'
        }

        response = self.client.post(url, payload, headers=self.headers)

        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertEqual(response.json(), {'detail': 'E-mail de verificação enviado com sucesso.'})
        self.assertEqual(MFAMethod.objects.count(), 1)

        self.mfa_method.refresh_from_db()
        self.assertEqual(self.mfa_method.primary, False)

    def test_activate_email_mfa_is_primary_when_no_mfa_methods_exists(self):
        self.delete_mfa_methods()
        url = reverse('user:activate-mfa')

        payload = {'method': MFAType.EMAIL.id}

        response = self.client.post(url, payload, headers=self.headers)

        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertEqual(MFAMethod.objects.count(), 1)
        self.assertTrue(MFAMethod.objects.first().primary)  # type:ignore

    def test_confirm_email_mfa_without_code_returns_400(self):
        self.mfa_method.confirmed = False
        self.mfa_method.save(update_fields=['confirmed'])

        url = reverse('user:confirm-mfa')

        payload = {
            'method': MFAType.EMAIL.id,
            'code': '1234'
        }

        response = self.client.post(url, payload, headers=self.headers)

        self.assertEqual(response.status_code, 400, response.content.decode())
        self.assertEqual(response.json(), {'detail': 'Token inválido.'})

    def test_confirm_email_mfa_with_wrong_token_returns_400(self):
        email_device = self.create_email_device()
        email_device.generate_token()

        self.mfa_method.confirmed = False
        self.mfa_method.email_device = email_device
        self.mfa_method.save(update_fields=['confirmed', 'email_device'])

        url = reverse('user:confirm-mfa')

        payload = {'method': MFAType.EMAIL.id, 'code': '1234'}

        response = self.client.post(url, payload, headers=self.headers)

        self.assertEqual(response.status_code, 400, response.content.decode())
        self.assertEqual(response.json(), {'detail': 'Token inválido.'})

    def test_confirm_email_mfa_returns_200(self):
        email_device = self.mfa_method.email_device
        email_device.generate_token()  # type:ignore

        self.mfa_method.confirmed = False
        self.mfa_method.save(update_fields=['confirmed'])

        self.user.emailValidated = False

        url = reverse('user:confirm-mfa')

        payload = {
            'method': MFAType.EMAIL.id,
            'code': email_device.token  # type:ignore
        }

        response = self.client.post(url, payload, headers=self.headers)

        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertEqual(response.json(), {'detail': 'Autenticação de dois fatores ativado com sucesso.'})

        self.mfa_method.refresh_from_db()
        self.assertTrue(self.mfa_method.confirmed)

        self.user.refresh_from_db()
        self.assertTrue(self.user.emailValidated)

    def test_email_mfa_cant_be_deactivate(self):
        url = reverse('user:deactivate-mfa')

        self.mfa_method.generate_token()

        payload = {
            'method': MFAType.EMAIL.id,
            'code': self.mfa_method.email_device.token,  # type:ignore
        }

        response = self.client.post(url, payload, headers=self.headers)

        self.assertEqual(response.status_code, 400, response.content.decode())
        self.assertEqual(response.json(), {'detail': f'Método de autenticação dois fatores por {MFAType.EMAIL.description} não pode ser desativado.'})

class MFATOTPTest(MFATestMixin, BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.mfa_method.delete()

        cls.mfa_method = cls.create_totp_mfa(confirmed=True, primary=True)

    def delete_totp_mfa_methods(self):
        MFAMethod.objects.filter(type=MFAType.TOTP.id).delete()
        TOTPDevice.objects.all().delete()

    def test_activate_totp_mfa_with_method_already_activated_returns_400(self):
        url = reverse('user:activate-mfa')

        payload = {'method': MFAType.TOTP.id}

        response = self.client.post(url, payload, headers=self.headers)

        self.assertEqual(response.status_code, 400, response.content.decode())
        self.assertEqual(response.json(), {'detail': f'Autenticação de dois fatores por {MFAType.TOTP.description} já ativado.'})

    def test_activate_totp_mfa_returns_200(self):
        self.delete_totp_mfa_methods()
        url = reverse('user:activate-mfa')

        quoted_label = quote(f'Cakto:{self.user.email}')
        expected_regex = rf'^otpauth://totp/{quoted_label}\?secret=.*&algorithm=SHA1&digits=6&period=30&issuer=Cakto$'

        response = self.client.post(url, {'method': MFAType.TOTP.id}, headers=self.headers)

        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertIn('config_url', response.json())
        self.assertRegex(response.json()['config_url'], expected_regex)
        self.assertEqual(MFAMethod.objects.filter(type=MFAType.TOTP.id).count(), 1)
        self.assertEqual(MFAMethod.objects.filter(type=MFAType.EMAIL.id, confirmed=True).count(), 1)

    def test_activate_totp_mfa_is_primary_when_no_mfa_methods_exists(self):
        self.delete_mfa_methods()
        url = reverse('user:activate-mfa')

        payload = {'method': MFAType.TOTP.id}

        response = self.client.post(url, payload, headers=self.headers)

        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertEqual(MFAMethod.objects.filter(type=MFAType.TOTP.id, primary=True).count(), 1)
        self.assertEqual(MFAMethod.objects.filter(type=MFAType.EMAIL.id, confirmed=True).count(), 1)

    def test_confirm_totp_mfa_with_invalid_code_returns_400(self):
        self.mfa_method.confirmed = False
        self.mfa_method.save(update_fields=['confirmed'])

        url = reverse('user:confirm-mfa')

        payload = {
            'method': MFAType.TOTP.id,
            'code': '1234'
        }

        response = self.client.post(url, payload, headers=self.headers)

        self.assertEqual(response.status_code, 400, response.content.decode())
        self.assertEqual(response.json(), {'detail': 'Token inválido.'})

    def test_confirm_totp_mfa_with_method_already_confirmed_returns_400(self):
        url = reverse('user:confirm-mfa')

        payload = {
            'method': MFAType.TOTP.id,
            'code': '1234'
        }

        response = self.client.post(url, payload, headers=self.headers)

        self.assertEqual(response.status_code, 400, response.content.decode())
        self.assertEqual(response.json(), {'detail': f'Autenticação de dois fatores por {MFAType.TOTP.description} não existente ou já ativa.'})

    def test_confirm_totp_mfa_returns_200(self):
        self.mfa_method.confirmed = False
        self.mfa_method.save(update_fields=['confirmed'])

        url = reverse('user:confirm-mfa')

        code = '1234'
        payload = {
            'method': MFAType.TOTP.id,
            'code': code
        }

        with mock.patch('user.models.MFAMethod.verify_token', return_value=True) as verify_token_mock:
            response = self.client.post(url, payload, headers=self.headers)
        self.mfa_method.refresh_from_db()

        # We need to mock the verify_token method because it's
        # not easy to generate a valid code so we just ensure
        # that the method is being called
        verify_token_mock.assert_called_once_with(code)

        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertEqual(response.json(), {'detail': 'Autenticação de dois fatores ativado com sucesso.'})
        self.assertTrue(self.mfa_method.confirmed)

    def test_send_mfa_code_with_totp_method_returns_400(self):
        url = reverse('user:send-mfa-code')

        payload = {
            'method': MFAType.TOTP.id,
        }

        expected_response = {'detail': 'Dispositivo TOTP não suporta envio de código.'}

        mock.patch('user.views.MFASendCodeView.throttle_scope', None).start()
        response = self.client.post(url, payload, headers=self.headers)

        self.assertEqual(response.status_code, 400, response.content.decode())
        self.assertEqual(response.json(), expected_response)

    def test_deactivate_totp_mfa_with_email_device_code(self):
        url = reverse('user:deactivate-mfa')

        email_mfa = self.create_email_mfa(confirmed=True, primary=False)

        email_mfa.email_device.generate_token()  # type:ignore

        payload = {
            'method': MFAType.TOTP.id,
            'code': email_mfa.email_device.token,  # type:ignore
        }

        response = self.client.post(url, payload, headers=self.headers)

        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertEqual(response.json(), {'detail': 'Método de autenticação dois fatores desativado com sucesso.'})

    def test_deactivate_totp_mfa_with_totp_device_code_returns_400(self):
        url = reverse('user:deactivate-mfa')

        totp_token = totp(self.mfa_method.totp_device.bin_key, drift=1)  # type:ignore

        payload = {
            'method': MFAType.TOTP.id,
            'code': totp_token,
        }

        response = self.client.post(url, payload, headers=self.headers)

        self.assertEqual(response.status_code, 400, response.content.decode())
        self.assertEqual(response.json(), {'detail': 'Token inválido.'})

class MFAAppCaktoTest(MFATestMixin, BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.mfa_method.delete()

        cls.mfa_method = cls.create_app_cakto_mfa(confirmed=True, primary=True)

    def delete_app_cakto_mfa_methods(self):
        MFAMethod.objects.filter(type=MFAType.APP_CAKTO.id).delete()
        TOTPDevice.objects.all().delete()

    def test_activate_app_cakto_mfa_with_method_already_activated_returns_400(self):
        url = reverse('user:activate-mfa')

        payload = {'method': MFAType.APP_CAKTO.id}

        response = self.client.post(url, payload, headers=self.headers)

        self.assertEqual(response.status_code, 400, response.content.decode())
        self.assertEqual(response.json(), {'detail': f'Autenticação de dois fatores por {MFAType.APP_CAKTO.description} já ativado.'})

    def test_activate_app_cakto_mfa_with_same_fcm_device_id_returns_200(self):
        url = reverse('user:activate-mfa')

        fcm_device = FCMDevice.objects.create(
            user=self.user,
            registration_id='0987',
            device_id='1234',
        )
        self.mfa_method.fcm_device = fcm_device
        self.mfa_method.save(update_fields=['fcm_device'])

        payload = {'method': MFAType.APP_CAKTO.id, 'fcm_device_id': fcm_device.device_id}

        response = self.client.post(url, payload, headers=self.headers)

        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertIn('config_url', response.json())

    def test_activate_app_cakto_mfa_returns_200(self):
        self.delete_app_cakto_mfa_methods()
        url = reverse('user:activate-mfa')

        quoted_label = quote(f'Cakto:{self.user.email}')
        expected_regex = rf'^otpauth://totp/{quoted_label}\?secret=.*&algorithm=SHA1&digits=6&period=30&issuer=Cakto$'

        response = self.client.post(url, {'method': MFAType.APP_CAKTO.id}, headers=self.headers)

        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertIn('config_url', response.json())
        self.assertRegex(response.json()['config_url'], expected_regex)
        self.assertEqual(MFAMethod.objects.filter(type=MFAType.APP_CAKTO.id).count(), 1)
        self.assertEqual(MFAMethod.objects.filter(type=MFAType.EMAIL.id, confirmed=True).count(), 1)

    def test_activate_app_cakto_mfa_is_primary_when_no_mfa_methods_exists(self):
        self.delete_mfa_methods()
        url = reverse('user:activate-mfa')

        payload = {'method': MFAType.APP_CAKTO.id}

        response = self.client.post(url, payload, headers=self.headers)

        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertEqual(MFAMethod.objects.filter(type=MFAType.APP_CAKTO.id, primary=True).count(), 1)
        self.assertEqual(MFAMethod.objects.filter(type=MFAType.EMAIL.id, confirmed=True).count(), 1)

    def test_confirm_app_cakto_mfa_with_invalid_code_returns_400(self):
        self.mfa_method.confirmed = False
        self.mfa_method.save(update_fields=['confirmed'])

        url = reverse('user:confirm-mfa')

        payload = {
            'method': MFAType.APP_CAKTO.id,
            'code': '1234',
            'fcm_device_id': '1234',
        }

        response = self.client.post(url, payload, headers=self.headers)

        self.assertEqual(response.status_code, 400, response.content.decode())
        self.assertEqual(response.json(), {'detail': 'Token inválido.'})

    def test_confirm_app_cakto_mfa_with_invalid_fcm_device_id_returns_400(self):
        self.mfa_method.confirmed = False
        self.mfa_method.save(update_fields=['confirmed'])

        url = reverse('user:confirm-mfa')

        payload = {
            'method': MFAType.APP_CAKTO.id,
            'code': '1234',
            'fcm_device_id': '1234',
        }

        mock.patch('user.models.MFAMethod.verify_token', return_value=True).start()

        # Call
        response = self.client.post(url, payload, headers=self.headers)

        self.assertEqual(response.status_code, 400, response.content.decode())
        self.assertEqual(response.json(), {'detail': 'Dispositivo FCM não encontrado.'})

    def test_confirm_app_cakto_mfa_with_method_already_confirmed_returns_400(self):
        url = reverse('user:confirm-mfa')

        payload = {
            'method': MFAType.APP_CAKTO.id,
            'code': '1234',
            'fcm_device_id': '1234',
        }

        response = self.client.post(url, payload, headers=self.headers)

        self.assertEqual(response.status_code, 400, response.content.decode())
        self.assertEqual(
            response.json(),
            {
                'detail': f'Autenticação de dois fatores por {MFAType.APP_CAKTO.description} não existente ou já ativa.'
            }
        )

    def test_confirm_app_cakto_mfa_returns_200(self):
        self.mfa_method.confirmed = False
        self.mfa_method.save(update_fields=['confirmed'])

        url = reverse('user:confirm-mfa')

        fcm_device = FCMDevice.objects.create(
            user=self.user,
            registration_id='0987',
            device_id='1234',
        )

        code = '1234'

        payload = {
            'method': MFAType.APP_CAKTO.id,
            'code': code,
            'fcm_device_id': fcm_device.device_id,
        }

        mock.patch('user.views.notify_fcm_mfa_action').start()
        verify_token_mock = mock.patch('user.models.MFAMethod.verify_token', return_value=True).start()

        # Call
        response = self.client.post(url, payload, headers=self.headers)

        self.mfa_method.refresh_from_db()

        # We need to mock the verify_token method because it's
        # not easy to generate a valid code so we just ensure
        # that the method is being called
        verify_token_mock.assert_called_once_with(code)

        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertEqual(response.json(), {'detail': 'Autenticação de dois fatores ativado com sucesso.'})
        self.assertTrue(self.mfa_method.confirmed)

    def test_confirm_app_cakto_mfa_sets_fcm_device_correctly(self):
        self.mfa_method.confirmed = False
        self.mfa_method.save(update_fields=['confirmed'])

        fcm_device = FCMDevice.objects.create(
            user=self.user,
            registration_id='0987',
            device_id='1234',
        )

        url = reverse('user:confirm-mfa')

        code = '1234'
        payload = {
            'method': MFAType.APP_CAKTO.id,
            'code': code,
            'fcm_device_id': '1234'
        }

        mock.patch(
            'user.models.MFAMethod.verify_token', return_value=True
        ).start()

        notify_fcm_mfa_action_mock = mock.patch('user.views.notify_fcm_mfa_action').start()

        # Call
        response = self.client.post(url, payload, headers=self.headers)

        self.mfa_method.refresh_from_db()

        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertEqual(self.mfa_method.fcm_device, fcm_device)

        kwargs = notify_fcm_mfa_action_mock.call_args.kwargs
        self.assertEqual(list(kwargs['devices']), [fcm_device])
        self.assertEqual(kwargs['fcm_device_id'], '1234')
        self.assertEqual(kwargs['action'], 'confirm')

    def test_send_mfa_code_with_app_cakto_method_returns_200(self):
        url = reverse('user:send-mfa-code')

        payload = {
            'method': MFAType.APP_CAKTO.id,
            'email': self.user.email,
        }

        expected_response = {'detail': 'Digite o iToken do seu app cakto para completar o login.'}

        mock.patch('user.views.MFASendCodeView.throttle_scope', None).start()
        with mock.patch('user.views.MFAMethod.send_token', return_value=True) as send_token_mock:
            response = self.client.post(url, payload, headers=self.headers)

        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertEqual(response.json(), expected_response)
        send_token_mock.assert_called_once()

    def test_send_mfa_code_with_app_cakto_method_returns_400(self):
        url = reverse('user:send-mfa-code')

        payload = {
            'method': MFAType.APP_CAKTO.id,
        }

        expected_response = {'detail': f'Dispositivo {MFAType.APP_CAKTO.description} não suporta envio de código.'}

        mock.patch('user.views.MFASendCodeView.throttle_scope', None).start()

        # Setup CaktoAPP in User-Agent
        headers = self.headers.copy()
        headers['User-Agent'] = 'CaktoAPP/1.0'
        response = self.client.post(url, payload, headers=headers)

        self.assertEqual(response.status_code, 400, response.content.decode())
        self.assertEqual(response.json(), expected_response)

    def test_deactivate_app_cakto_mfa_with_app_cakto_device_code(self):
        url = reverse('user:deactivate-mfa')

        totp_token = totp(self.mfa_method.app_cakto_device.bin_key, drift=1)  # type:ignore

        payload = {
            'method': MFAType.APP_CAKTO.id,
            'code': totp_token,
        }

        response = self.client.post(url, payload, headers=self.headers)

        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertEqual(response.json(), {'detail': 'Método de autenticação dois fatores desativado com sucesso.'})

    def test_deactivate_app_cakto_with_email_device_code(self):
        url = reverse('user:deactivate-mfa')

        email_mfa = self.create_email_mfa(confirmed=True, primary=False)

        email_mfa.email_device.generate_token()  # type:ignore

        payload = {
            'method': MFAType.APP_CAKTO.id,
            'code': email_mfa.email_device.token,  # type:ignore
        }

        response = self.client.post(url, payload, headers=self.headers)

        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertEqual(response.json(), {'detail': 'Método de autenticação dois fatores desativado com sucesso.'})

    def test_deactivate_app_cakto_mfa_notify_mfa_action(self):
        url = reverse('user:deactivate-mfa')

        fcm_device = FCMDevice.objects.create(
            user=self.user,
            registration_id='0987',
            device_id='1234',
        )

        self.mfa_method.fcm_device = fcm_device
        self.mfa_method.save(update_fields=['fcm_device'])

        totp_token = totp(self.mfa_method.app_cakto_device.bin_key, drift=1)  # type:ignore

        payload = {
            'method': MFAType.APP_CAKTO.id,
            'code': totp_token,
        }

        notify_fcm_mfa_action_mock = mock.patch(
            'user.views.notify_fcm_mfa_action'
        ).start()

        # Call
        response = self.client.post(url, payload, headers=self.headers)

        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertEqual(response.json(), {'detail': 'Método de autenticação dois fatores desativado com sucesso.'})

        kwargs = notify_fcm_mfa_action_mock.call_args.kwargs
        self.assertEqual(list(kwargs['devices']), [fcm_device])
        self.assertEqual(kwargs['fcm_device_id'], '1234')
        self.assertEqual(kwargs['action'], 'deactivate')

class MFALoginTest(MFATestMixin, BaseTestCase):
    def test_first_step_login_without_mfa_returns_access_token(self):
        self.delete_mfa_methods()
        url = reverse('user:mfa_first_step')

        response = self.client.post(url, {MFAType.EMAIL.id: self.user.email, 'password': self.password})

        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertIn('access', response.json())
        self.assertIn('refresh', response.json())

    def test_first_step_login_with_mfa_returns_ephemeral_token(self):
        url = reverse('user:mfa_first_step')

        response = self.client.post(url, {MFAType.EMAIL.id: self.user.email, 'password': self.password})

        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertIn('ephemeral_token', response.json())

    def test_first_step_login_with_mfa_returns_mfa_methods_list(self):
        url = reverse('user:mfa_first_step')

        MFAMethod.objects.create(user=self.user, type=MFAType.TOTP.id, confirmed=True, primary=False)  # type:ignore

        expected_method_list = [
            {
                'type': MFAType.EMAIL.id,
                'primary': True,
                'fcm_device_id': None,
                'createdAt': mock.ANY,
                'updatedAt': mock.ANY,
            },
            {
                'type': MFAType.TOTP.id,
                'primary': False,
                'fcm_device_id': None,
                'createdAt': mock.ANY,
                'updatedAt': mock.ANY,
            },
        ]

        response = self.client.post(url, {MFAType.EMAIL.id: self.user.email, 'password': self.password})

        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertIn('methods', response.json(), 'methods key not found in response')
        self.assertEqual(response.json()['methods'], expected_method_list, 'methods list is not correct')

    def test_second_step_login_success(self):
        url = reverse('user:mfa_second_step')

        payload = {
            'ephemeral_token': self.ephemeral_token,
            'code': self.mfa_method.email_device.token  # type:ignore
        }

        response = self.client.post(url, data=payload)

        self.assertEqual(response.status_code, 200, response.content.decode())

        data = response.json()

        self.assertIn('access', data)
        self.assertIn('refresh', data)
        self.assertEqual(
            data['validated_method'],
            MFAMethodSerializer(self.mfa_method).data
        )

    def test_second_step_login_success_at_app_cakto_calls_send_login_event_to_fb(self):
        url = reverse('user:mfa_second_step')

        payload = {
            'ephemeral_token': self.ephemeral_token,
            'code': self.mfa_method.email_device.token  # type:ignore
        }

        # Setup CaktoAPP in User-Agent
        headers = self.headers.copy()
        headers['User-Agent'] = 'CaktoAPP/1.0'

        expected_ip = '********'

        send_login_event_to_fb_mock = mock.patch(
            'user.views.send_login_event_to_fb.delay'
        ).start()

        response = self.client.post(url, data=payload, headers=headers, REMOTE_ADDR=expected_ip)

        self.assertEqual(response.status_code, 200, response.content.decode())

        send_login_event_to_fb_mock.assert_called_once_with(
            user=self.user,
            ip=expected_ip,
            user_agent=headers['User-Agent'],
        )

    def test_second_step_login_with_wrong_code_returns_403(self):
        url = reverse('user:mfa_second_step')

        payload = {
            'ephemeral_token': self.ephemeral_token,
            'code': '123'
        }

        response = self.client.post(url, data=payload)

        self.assertEqual(response.status_code, 403, response.content.decode())

    def test_second_step_login_with_wrong_ephemeral_token_returns_403(self):
        url = reverse('user:mfa_second_step')

        payload = {
            'ephemeral_token': '1234',
            'code': self.mfa_method.email_device.token  # type:ignore
        }

        response = self.client.post(url, data=payload)

        self.assertEqual(response.status_code, 403, response.content.decode())

    def test_login_with_only_email_mfa_active_sends_token_to_user(self):
        url = reverse('user:mfa_first_step')

        send_token_mock = mock.patch('user.views.MFAMethod.send_token').start()

        response = self.client.post(url, {MFAType.EMAIL.id: self.user.email, 'password': self.password})

        send_token_mock.assert_called_once()
        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertIn('ephemeral_token', response.json())

    def test_login_with_email_and_other_mfa_active_does_not_send_token_to_user(self):
        self.create_totp_mfa(confirmed=True, primary=False)

        url = reverse('user:mfa_first_step')

        send_token_mock = mock.patch('user.views.MFAMethod.send_token').start()

        response = self.client.post(url, {MFAType.EMAIL.id: self.user.email, 'password': self.password})

        send_token_mock.assert_not_called()
        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertIn('ephemeral_token', response.json())

    def test_login_is_authenticated_query_for_user_mfas_orders_by_less_errors_mfa_devices(self):
        url = reverse('user:mfa_second_step')

        email_device: EmailDevice = self.mfa_method.email_device  # type:ignore
        totp_device: TOTPDevice = self.create_totp_mfa(confirmed=True, primary=False).totp_device  # type:ignore
        app_cakto_device: AppCaktoDevice = self.create_app_cakto_mfa(confirmed=True, primary=False).app_cakto_device  # type:ignore

        payload = {
            'ephemeral_token': self.ephemeral_token,
            'code': email_device.token,  # Correct code for email device
        }

        # Set throttling failure count
        app_cakto_device.throttling_failure_count = 10
        app_cakto_device.save()
        email_device.throttling_failure_count = 5
        email_device.save()
        totp_device.throttling_failure_count = 0
        totp_device.save()

        # Call
        response = self.client.post(url, data=payload)

        self.assertEqual(response.status_code, 200, response.content.decode())

        email_device.refresh_from_db()
        totp_device.refresh_from_db()
        app_cakto_device.refresh_from_db()

        # Should be validated and fail count reseted because of success
        self.assertEqual(email_device.throttling_failure_count, 0)

        # Should be validated and failed once
        self.assertEqual(totp_device.throttling_failure_count, 1)

        # Should not be validated and still 10 fails
        self.assertEqual(app_cakto_device.throttling_failure_count, 10)

    def test_first_step_login_without_mfa_creates_user_login_history(self):
        UserLoginHistory.objects.all().delete()  # Clear existing login history

        self.delete_mfa_methods()

        url = reverse('user:mfa_first_step')

        ip = '*********'
        user_agent = 'Testing User Agent'

        response = self.client.post(
            url,
            data={MFAType.EMAIL.id: self.user.email, 'password': self.password},
            REMOTE_ADDR=ip,
            HTTP_USER_AGENT=user_agent,
        )

        self.assertEqual(response.status_code, 200, response.content.decode())

        self.assertEqual(UserLoginHistory.objects.count(), 1, "User login history should have one entry")

        history: UserLoginHistory = UserLoginHistory.objects.first()  # type: ignore

        self.assertEqual(history.user, self.user)
        self.assertEqual(history.ip, ip)
        self.assertEqual(history.user_agent, user_agent)
        self.assertEqual(history.access_token_source, UserAccessLocations.LOGIN.id)
        self.assertAlmostEqual(
            history.createdAt,
            timezone.now(),
            delta=timezone.timedelta(seconds=2),
        )

    def test_second_step_login_creates_user_login_history(self):
        UserLoginHistory.objects.all().delete()  # Clear existing login history

        url = reverse('user:mfa_second_step')

        payload = {
            'ephemeral_token': self.ephemeral_token,
            'code': self.mfa_method.email_device.token  # type:ignore
        }

        ip = '*********'
        user_agent = 'Testing User Agent'

        response = self.client.post(
            url,
            data=payload,
            REMOTE_ADDR=ip,
            HTTP_USER_AGENT=user_agent,
        )

        self.assertEqual(response.status_code, 200, response.content.decode())

        self.assertEqual(UserLoginHistory.objects.count(), 1, "User login history should have one entry")

        history: UserLoginHistory = UserLoginHistory.objects.first()  # type: ignore

        self.assertEqual(history.user, self.user)
        self.assertEqual(history.ip, ip)
        self.assertEqual(history.user_agent, user_agent)
        self.assertEqual(history.access_token_source, UserAccessLocations.LOGIN.id)
        self.assertAlmostEqual(
            history.createdAt,
            timezone.now(),
            delta=timezone.timedelta(seconds=2),
        )
