from unittest import mock

from django.urls import reverse
from rest_framework import serializers

from cakto.tests.base import BaseTestCase
from gateway.serializers import CheckoutProcessPaymentSerializer
from product.enums import PaymentMethodStatus
from product.models import PaymentMethod
from product.serializers import DigitalProductCreateSerializer, ProductSerializerFull
from user.models import ExperimentalFeature


class TestExperimentalFeature(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()

        cls.create_paymentMethods()

        cls.tester_payment_method = PaymentMethod.objects.create(
            type='beta_method',
            name='Beta Method',
            status=PaymentMethodStatus.TESTER_USER_ACCESS.id,
        )

        cls.experimental_feature = ExperimentalFeature.objects.create(
            id=cls.tester_payment_method.type,
            name=cls.tester_payment_method.name,
        )

        cls.product = cls.create_product()

    def test_dash_v2_experimental_feature_does_not_return_experimental_paymentMethod_for_user_without_access(self):
        url = '/api/dashboard/v2/'

        response = self.client.get(url, headers=self.build_user_auth_headers(self.user))

        self.assertEqual(response.status_code, 200)
        self.assertNotIn(
            'beta_method',
            response.json()['paymentMethods'],
        )

    def test_dash_v2_experimental_feature_returns_experimental_paymentMethod_for_user_with_access(self):
        url = '/api/dashboard/v2/'

        self.user.experimental_features.add(self.experimental_feature)

        response = self.client.get(url, headers=self.build_user_auth_headers(self.user))

        self.assertEqual(response.status_code, 200)
        self.assertIn(
            'beta_method',
            response.json()['paymentMethods'],
        )

    def _generate_checkout_data(self):
        return {
            "customer": {
                "name": "Customer Test",
                "email": "<EMAIL>",
                "phone": "16999999999",
                "docType": "cpf",
                "docNumber": "96158452050",
                "fingerprint": "g891234gsdfugusdfgvnsjejrJ"
            },
            "paymentMethod": self.tester_payment_method.type,
            "items": [
                {
                    "id": self.product.offers.first().id,
                    "offerType": "main",
                    "default": True,
                    "installments": 1,
                }
            ],
            "type": "product",
            "metadata": {
                "ip": "**************",
                "country": "br",
                "sessionid": "hy3zznrnrc2tnjtmea",
                "utm_source": "",
                "utm_medium": "",
                "utm_campaign": "",
                "utm_term": "",
                "utm_content": ""
            }
        }

    def test_CheckoutProcessPaymentSerializer_does_not_validate_experimental_paymentMethod_for_user_without_access(self):
        data = self._generate_checkout_data()

        serializer = CheckoutProcessPaymentSerializer(data=data, context={'product_user': self.user})

        with self.assertRaises(serializers.ValidationError, msg='Método de pagamento inválido.'):
            serializer.is_valid(raise_exception=True)

    def test_CheckoutProcessPaymentSerializer_validate_experimental_paymentMethod_for_user_with_access(self):
        self.user.experimental_features.add(self.experimental_feature)

        data = self._generate_checkout_data()

        serializer = CheckoutProcessPaymentSerializer(data=data, context={'product_user': self.user})

        self.assertTrue(serializer.is_valid(raise_exception=True))

    def _get_product_data(self):
        return {
            "id": self.product.id,
            "short_id": self.product.short_id,
            "name": "6",
            "status": "active",
            "type": "subscription",
            "description": "12312321http://localhost:3000/dashboard/products?tab=productshttp://localhost:3000/dashboard/products?tab=productshttp://localhost:3000/dashboard/products?tab=products",
            "category": None,
            "price": "21.31",
            "installments": 12,
            "image": None,
            "guarantee": 7,
            "salesPage": "http://localhost:3000/dashboard/products?tab=products",
            "supportEmail": None,
            "emailAccessLink": None,
            "confirmEmail": False,
            "affiliate": False,
            "affiliateRequest": False,
            "affiliateCommission": None,
            "affiliateContact": False,
            "affiliateDescription": "",
            "affiliateSupportEmail": None,
            "affiliateMarketplace": False,
            "affiliateClick": "last",
            "cookieTime": 30,
            "affiliateShareBump": True,
            "affiliateShareUpsell": False,
            "upsell": False,
            "upsellPage": None,
            "redirectUpsellWithBumpFail": True,
            "defaultPaymentMethod": "credit_card",
            "showCouponField": False,
            "invoiceDescription": None,
            "paymentMethods": [
                self.tester_payment_method.type,
            ],
            "paymentsOrder": [],
            "ticketExpiration": 7,
            "twoCardPayment": False,
            "bumps": [],
            "producerName": None,
        }

    def test_ProductSerializerFull_does_not_validate_experimental_paymentMethod_for_user_without_access(self):
        data = self._get_product_data()

        request = mock.Mock(user=self.user)

        serializer = ProductSerializerFull(
            instance=self.product,
            data=data,
            context={'request': request},
        )

        with self.assertRaises(serializers.ValidationError, msg={"paymentMethods": "Métodos de pagamentos inválidos."}):
            serializer.is_valid(raise_exception=True)

    def test_ProductSerializerFull__validate_experimental_paymentMethod_for_user_with_access(self):
        self.user.experimental_features.add(self.experimental_feature)

        data = self._get_product_data()

        request = mock.Mock(user=self.user)

        serializer = ProductSerializerFull(
            instance=self.product,
            data=data,
            context={'request': request},
        )

        self.assertTrue(serializer.is_valid(raise_exception=True))

    def test_DigitalProductCreateSerializer_does_not_validate_experimental_paymentMethod_for_user_without_access(self):
        data = self._get_product_data()

        request = mock.Mock(user=self.user)

        serializer = DigitalProductCreateSerializer(
            instance=self.product,
            data=data,
            context={'request': request},
        )

        with self.assertRaises(serializers.ValidationError, msg={"paymentMethods": "Métodos de pagamentos inválidos."}):
            serializer.is_valid(raise_exception=True)

    def test_DigitalProductCreateSerializer__validate_experimental_paymentMethod_for_user_with_access(self):
        self.user.experimental_features.add(self.experimental_feature)

        data = self._get_product_data()

        request = mock.Mock(user=self.user)

        serializer = DigitalProductCreateSerializer(
            instance=self.product,
            data=data,
            context={'request': request},
        )

        self.assertTrue(serializer.is_valid(raise_exception=True))

    def test_PaymentMethodAPIView_does_not_return_experimental_paymentMethod_for_user_without_access(self):
        url = reverse('products-payment-methods')

        response = self.client.get(url, headers=self.build_user_auth_headers(self.user))

        self.assertEqual(response.status_code, 200)
        self.assertNotIn(
            {'type': self.tester_payment_method.type, 'name': self.tester_payment_method.name},
            response.json(),
        )

    def test_PaymentMethodAPIView_returns_experimental_paymentMethod_for_user_with_access(self):
        url = reverse('products-payment-methods')

        self.user.experimental_features.add(self.experimental_feature)

        response = self.client.get(url, headers=self.build_user_auth_headers(self.user))

        self.assertEqual(response.status_code, 200)
        self.assertIn(
            {'type': self.tester_payment_method.type, 'name': self.tester_payment_method.name},
            response.json(),
        )

    def test_create_product_does_not_add_experiment_paymentMethod_to_product(self):
        data = {
            "contentDelivery": "cakto",
            "salesPage": "https://google.com",
            "price": 27,
            "description": "TESTE 3",
            "type": "unique",
            "name": "Teste"
        }

        response = self.client.post(
            reverse('products'),
            data=data,
            headers=self.build_user_auth_headers(self.user),
        )

        self.assertEqual(response.status_code, 201, response.json())

        product = response.json()

        self.assertNotIn(
            self.tester_payment_method.type,
            product['paymentMethods'],
        )

    def test_create_product_add_experiment_paymentMethod_to_product(self):
        self.user.experimental_features.add(self.experimental_feature)

        data = {
            "contentDelivery": "cakto",
            "salesPage": "https://google.com",
            "price": 27,
            "description": "TESTE 3",
            "type": "unique",
            "name": "Teste"
        }

        response = self.client.post(
            reverse('products'),
            data=data,
            headers=self.build_user_auth_headers(self.user),
        )

        self.assertEqual(response.status_code, 201, response.json())

        product = response.json()

        self.assertIn(
            self.tester_payment_method.type,
            product['paymentMethods'],
        )

    def test_UserOwnerSerializer_does_not_return_experimental_paymentMethod_for_user_without_access(self):
        url = reverse('user:user')

        response = self.client.get(url, headers=self.build_user_auth_headers(self.user))

        self.assertEqual(response.status_code, 200)

        self.assertNotIn(
            'experimental_features',
            response.json()
        )

    def test_UserOwnerSerializer_returns_experimental_paymentMethod_for_user_with_access(self):
        url = reverse('user:user')

        self.user.experimental_features.add(self.experimental_feature)

        response = self.client.get(url, headers=self.build_user_auth_headers(self.user))

        self.assertEqual(response.status_code, 200)

        self.assertEqual(
            response.json()['experimental_features'],
            [self.experimental_feature.id],
        )
