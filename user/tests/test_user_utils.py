from unittest import mock

from cakto.tests.base import BaseTestCase
from user.utils import RegisterWebhookEvent, register_webhook_job, send_register_webhook


class TestRegisterWebhookEvent(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()

        fields_to_update = {
            'first_name': '<PERSON><PERSON>',
            'last_name': 'Sabino',
            'acceptTerms': True,
            'instagram': 'tuliosalmeida',
            'companyName': 'Cakto Pay, Tecnologia e Serviços LTDA',
            'companyProduct': 'Dropshipping',
            'mostSells': 'Softwares',
            'recurringAmount': 2000,
            'businessModel': 'Webinar',
            'actualPlatform': 'Kiwify',
            'biggerPain': 'Baixas Vendas',
            'indication': 'Youtube',
            'phoneCountryCode': '55',
            'cellphone': '***********',
        }

        [setattr(cls.user, field, value) for field, value in fields_to_update.items()]
        cls.user.save()

    def test__get_payload(self):
        result = RegisterWebhookEvent()._get_payload(self.user)

        self.assertEqual(
            result['name'],
            f'{self.user.first_name} {self.user.last_name}'
        )
        self.assertEqual(result['email'], self.user.email)
        self.assertEqual(result['companyName'], self.user.companyName)
        self.assertEqual(result['instagram'], self.user.instagram)
        self.assertEqual(
            result['cellphone'],
            f'{self.user.phoneCountryCode}{self.user.cellphone}'
        )
        self.assertEqual(result['companyProduct'], self.user.companyProduct)
        self.assertEqual(result['mostSells'], self.user.mostSells)
        self.assertEqual(result['recurringAmount'], self.user.recurringAmount)
        self.assertEqual(result['businessModel'], self.user.businessModel)
        self.assertEqual(result['actualPlatform'], self.user.actualPlatform)
        self.assertEqual(result['biggerPain'], self.user.biggerPain)
        self.assertEqual(result['indication'], self.user.indication)
        self.assertEqual(result['createdAt'], self.user.createdAt.isoformat())
        self.assertEqual(result['utm_source'], self.user.utm_source)
        self.assertEqual(result['utm_medium'], self.user.utm_medium)
        self.assertEqual(result['utm_campaign'], self.user.utm_campaign)
        self.assertEqual(result['utm_term'], self.user.utm_term)
        self.assertEqual(result['utm_content'], self.user.utm_content)
        self.assertEqual(result['sck'], self.user.sck)

    @mock.patch.dict('os.environ', {'REGISTER_PRODUCER_WEBHOOK_URL': 'https://example.com/webhook'})
    def test_send_event_with_env_setted(self):
        _get_payload_mock = mock.patch(
            'user.utils.RegisterWebhookEvent._get_payload',
        ).start()

        _dispatch_event_mock = mock.patch(
            'user.utils.RegisterWebhookEvent._dispatch_event',
        ).start()

        RegisterWebhookEvent().send_event(self.user)

        _get_payload_mock.assert_called_once_with(self.user)
        _dispatch_event_mock.assert_called_once_with(
            'https://example.com/webhook',
            _get_payload_mock.return_value
        )

    @mock.patch.dict('os.environ', {'REGISTER_PRODUCER_WEBHOOK_URL': ''})
    def test_send_event_with_env_not_setted(self):
        _get_payload_mock = mock.patch(
            'user.utils.RegisterWebhookEvent._get_payload',
        ).start()

        _dispatch_event_mock = mock.patch(
            'user.utils.RegisterWebhookEvent._dispatch_event',
        ).start()

        RegisterWebhookEvent().send_event(self.user)

        _get_payload_mock.assert_not_called()
        _dispatch_event_mock.assert_not_called()

    def test__dispatch_event(self):
        url = 'https://example.com/webhook'
        payload = {'key': 'value'}

        requests_mock = mock.patch('user.utils.requests').start()

        RegisterWebhookEvent()._dispatch_event(url, payload)

        requests_mock.post.assert_called_once_with(
            url,
            json=payload,
            headers={'Content-Type': 'application/json'},
            timeout=4
        )

    @mock.patch.dict('os.environ', {'REGISTER_PRODUCER_WEBHOOK_URL': 'https://example.com/webhook'})
    def test_send_register_webhook(self):
        delay_mock = mock.patch('user.utils.register_webhook_job.delay').start()
        send_register_webhook(self.user)
        delay_mock.assert_called_once_with(self.user)

    def test_register_webhook_job(self):
        send_event_mock = mock.patch(
            'user.utils.RegisterWebhookEvent.send_event'
        ).start()

        register_webhook_job(self.user)

        send_event_mock.assert_called_once_with(self.user)
