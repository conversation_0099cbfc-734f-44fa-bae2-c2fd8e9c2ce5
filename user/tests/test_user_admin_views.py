from django.urls import reverse
from django.utils import timezone

from cakto.tests.base import BaseTestCase
from user.enums import UserAccessLocations
from user.models import UserLoginHistory


class UserAdminTest(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()

        cls.user = cls.create_user()
        cls.user_headers = cls.build_user_auth_headers(cls.user)

        cls.admin = cls.create_user(is_staff=True)
        cls.admin_headers = cls.build_user_auth_headers(cls.admin)

    def test_admin_user_impersonate_view_creates_user_login_history(self):
        UserLoginHistory.objects.all().delete()  # Clear existing login history

        url = reverse('user:admin-users-impersonate', args=[self.user.pk])

        ip = '**********'
        user_agent = 'Testing User Agent'

        # Call
        response = self.client.post(
            url,
            headers=self.admin_headers,
            REMOTE_ADDR=ip,
            HTTP_USER_AGENT=user_agent,
        )

        # Assert
        self.assertEqual(response.status_code, 200, response.content.decode())

        self.assertEqual(UserLoginHistory.objects.count(), 1, "User login history should have one entry")

        history: UserLoginHistory = UserLoginHistory.objects.first()  # type: ignore

        self.assertEqual(history.user, self.user)
        self.assertEqual(history.ip, ip)
        self.assertEqual(history.user_agent, user_agent)
        self.assertEqual(history.access_token_source, UserAccessLocations.IMPERSONATE.id)
        self.assertAlmostEqual(
            history.createdAt,
            timezone.now(),
            delta=timezone.timedelta(seconds=2),
        )

    def test_common_user_cannot_access_admin_update_user(self):
        url = reverse('user:admin-users-retrieve', args=[self.user.pk])

        response = self.client.put(url, data={'refundRequest': True}, headers=self.user_headers)

        self.assertEqual(response.status_code, 403, response.content)

    def test_common_user_cannot_access_admin_retrieve_user(self):
        url = reverse('user:admin-users-retrieve', args=[self.user.pk])

        response = self.client.get(url, headers=self.user_headers)

        self.assertEqual(response.status_code, 403, response.content)

    def test_common_user_cannot_access_admin_list_user(self):
        url = reverse('user:admin-users-list')

        response = self.client.get(url, headers=self.user_headers)

        self.assertEqual(response.status_code, 403, response.content)

    def test_admin_update_changes_user_threeDsRetryEnabled(self):
        self.user.threeDsRetryEnabled = False
        self.user.save()
        self.assertFalse(self.user.threeDsRetryEnabled)

        url = reverse('user:admin-users-retrieve', args=[self.user.pk])

        data = {
            'threeDsRetryEnabled': True,
        }

        response = self.client.put(url, headers=self.admin_headers, data=data)

        self.assertEqual(response.status_code, 200, response.content)

        self.user.refresh_from_db()

        self.assertTrue(self.user.threeDsRetryEnabled)

    def test_admin_update_changes_user_refundRequest(self):
        self.user.refundRequest = False
        self.user.save()
        self.assertFalse(self.user.refundRequest)

        url = reverse('user:admin-users-retrieve', args=[self.user.pk])

        data = {
            'refundRequest': True,
        }

        response = self.client.put(url, headers=self.admin_headers, data=data)

        self.assertEqual(response.status_code, 200, response.content)

        self.user.refresh_from_db()

        self.assertTrue(self.user.refundRequest)
