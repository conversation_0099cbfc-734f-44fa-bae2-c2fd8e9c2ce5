from django.test import TestCase, RequestFactory, override_settings
from user.permissions import UserIsValidated

class DummyUser:
    def __init__(self, is_producer, emailValidated, whatsappValidated):
        self.is_producer = is_producer
        self.emailValidated = emailValidated
        self.whatsappValidated = whatsappValidated

class DummyView:
    pass

class TestUserIsValidatedPermission(TestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()

        cls.permission = UserIsValidated()

        cls.factory = RequestFactory()

        cls.view = DummyView()

    @override_settings(WHATSAPP_VALIDATION_ENABLED=True)
    def test_permission_producer_validated(self):
        user = DummyUser(is_producer=True, emailValidated=True, whatsappValidated=True)
        request = self.factory.get('/')
        request.user = user
        self.assertTrue(self.permission.has_permission(request, self.view))

    @override_settings(WHATSAPP_VALIDATION_ENABLED=True)
    def test_permission_producer_whatsapp_invalid(self):
        user = DummyUser(is_producer=True, emailValidated=True, whatsappValidated=False)
        request = self.factory.get('/')
        request.user = user
        self.assertFalse(self.permission.has_permission(request, self.view))

    @override_settings(WHATSAPP_VALIDATION_ENABLED=True)
    def test_permission_producer_email_invalid(self):
        user = DummyUser(is_producer=True, emailValidated=False, whatsappValidated=True)
        request = self.factory.get('/')
        request.user = user
        self.assertFalse(self.permission.has_permission(request, self.view))

    @override_settings(WHATSAPP_VALIDATION_ENABLED=True)
    def test_permission_non_producer(self):
        user = DummyUser(is_producer=False, emailValidated=True, whatsappValidated=True)
        request = self.factory.get('/')
        request.user = user
        self.assertTrue(self.permission.has_permission(request, self.view))

    @override_settings(WHATSAPP_VALIDATION_ENABLED=False)
    def test_permission_non_producer_without_whatsapp_requirement(self):
        user = DummyUser(is_producer=False, emailValidated=True, whatsappValidated=False)
        request = self.factory.get('/')
        request.user = user
        self.assertTrue(self.permission.has_permission(request, self.view))

    @override_settings(WHATSAPP_VALIDATION_ENABLED=False)
    def test_permission_producer_without_whatsapp_requirement(self):
        user = DummyUser(is_producer=True, emailValidated=True, whatsappValidated=False)
        request = self.factory.get('/')
        request.user = user
        self.assertTrue(self.permission.has_permission(request, self.view))

    @override_settings(WHATSAPP_VALIDATION_ENABLED=False)
    def test_permission_email_invalid(self):
        user = DummyUser(is_producer=True, emailValidated=False, whatsappValidated=True)
        request = self.factory.get('/')
        request.user = user
        self.assertFalse(self.permission.has_permission(request, self.view))
