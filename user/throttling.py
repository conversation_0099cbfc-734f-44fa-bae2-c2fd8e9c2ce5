import time

from rest_framework.throttling import AnonRateThrottle


class CustomAnonMixinThrottle(AnonRateThrottle):
    first_stage_duration = 60  # seconds
    second_stage_duration = 60 * 5  # seconds
    num_requests = 1  # amount of requests per duration period
    reset_after = 60 * 60  # seconds

    def __init__(self):
        pass

    def allow_request(self, request, view):
        self.key = self.get_cache_key(request, view)
        if self.key is None:
            return True

        self.history = self.cache.get(self.key, [])
        self.now = time.time()

        # Remove entries older than self.reset_after
        self.history = [timestamp for timestamp in self.history if timestamp > self.now - self.reset_after]

        if not self.history:
            return self.throttle_success()

        if len(self.history) < 3:
            if self.history[0] > (self.now - self.first_stage_duration):
                return self.throttle_failure()

        if len(self.history) >= 3:
            if self.history[0] > (self.now - self.second_stage_duration):
                return self.throttle_failure()

        return self.throttle_success()

    def throttle_success(self):
        self.history.insert(0, self.now)
        self.cache.set(self.key, self.history, self.reset_after)  # type:ignore
        return True

    def throttle_failure(self):
        return False

    def wait(self):
        if not self.history:
            remaining_duration = self.first_stage_duration
        elif len(self.history) < 3:
            remaining_duration = self.first_stage_duration - (self.now - self.history[0])
        else:
            remaining_duration = self.second_stage_duration - (self.now - self.history[0])
        return remaining_duration

class RecoveryPasswordRequestThrottle(CustomAnonMixinThrottle):
    scope = 'recoveryPasswordRequest'

class RecoveryPasswordConfirmThrottle(CustomAnonMixinThrottle):
    scope = 'recoveryPasswordConfirm'
