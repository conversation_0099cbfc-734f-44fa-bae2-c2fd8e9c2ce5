from django.urls import path
from django.views.decorators.csrf import csrf_exempt
from rest_framework_simplejwt.views import TokenRefreshView

from user import views

from .auth import GoogleLogin

app_name = 'user'

urlpatterns = [
    path('token/', views.MFALoginFirstStepView.as_view(), name='mfa_first_step'),
    path('token/code/', views.MFALoginSecondStepView.as_view(), name='mfa_second_step'),
    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('login/google/', csrf_exempt(GoogleLogin.as_view()), name='googleLogin'),
    path('withdrawals/', views.WithdrawalAPIView.as_view(), name='withdrawals'),
    path('collaborators/', views.ColaboratorAPIView.as_view({'get': 'list', 'post': 'create'}), name='colaborators'),
    path('collaborators/<int:pk>/', views.ColaboratorAPIView.as_view({'delete': 'destroy', 'get': 'retrieve', 'put': 'update'}), name='colaborator'),
    path('collaborators/<int:pk>/accept/', views.ColaboratorAPIView.as_view({'post': 'accept'}), name='collaborators-accept'),
    path('collaborators/<int:pk>/reject/', views.ColaboratorAPIView.as_view({'post': 'reject'}), name='collaborators-reject'),
    path('register/', views.UserAPI.as_view({'post': 'register'}), name='register'),
    path('user/', views.UserAPI.as_view({'get': 'get', 'put': 'update'}), name='user'),
    path('user/picture/', views.UserAPI.as_view({'put': 'userPicture'}), name='userPicture'),
    path('user/change-password/', views.UserAPI.as_view({'post': 'changePassword'}), name='changePassword'),
    path('user/create-password/', views.UserAPI.as_view({'post': 'createPassword'}), name='createPassword'),
    path('device/token/', views.PushNotificationTokenAPI.as_view({'post': 'post'}), name='device-token'),
    path('device/token/logout/', views.PushNotificationTokenAPI.as_view({'post': 'logout'}), name='device-token-logout'),
    path('recovery/send-email/', views.RequestRecoveryPasswordTokenAPI.as_view({'post': 'request_token'}), name='recovery-send-email'),
    path('recovery/change-password/', views.ConfirmRecoveryPasswordTokenAPI.as_view({'post': 'change_password'}), name='recovery-change-password'),
    path('login/otp/', views.OTPAccessAPI.as_view(), name='otp-access'),

    # Multi Factor Authentication
    path('user/mfa/', views.MFAListAPIView.as_view(), name='list-mfa'),
    path('user/mfa/activate/', views.MFAMethodActivationView.as_view(), name='activate-mfa'),
    path('user/mfa/confirm/', views.MFAMethodConfirmView.as_view(), name='confirm-mfa'),
    path('user/mfa/deactivate/', views.MFADeactivateView.as_view(), name='deactivate-mfa'),
    path('user/mfa/send_code/', views.MFASendCodeView.as_view(), name='send-mfa-code'),
    path('user/mfa/change_primary/', views.MFAChangePrimaryView.as_view(), name='change-mfa-primary'),

    # Validation
    path('user/validation/email/request_token/', views.UserEmailTokenRequest.as_view({'post': 'requestEmailToken'}), name='request-email-token'),
    path('user/validation/whatsapp/request_token/', views.UserWhatsappTokenRequest.as_view({'post': 'requestWhatsappToken'}), name='request-whatsapp-token'),
    path('user/validation/email/validate/', views.UserAPI.as_view({'post': 'validateEmailToken'}), name='email-validate'),
    path('user/validation/whatsapp/validate/', views.UserAPI.as_view({'post': 'validateWhatsappToken'}), name='whatsapp-validate'),

    # Admin - User
    path('admin/users/', views.UserAdminList.as_view(), name='admin-users-list'),
    path('admin/users/<int:pk>/', views.UserAdminAPI.as_view({'get': 'retrieve', 'put': 'update'}), name='admin-users-retrieve'),
    path('admin/users/<int:pk>/send_new_password/', views.UserAdminAPI.as_view({'post': 'send_new_password'}), name='admin-users-send-new-password'),
    path('admin/users/<int:pk>/impersonate/', views.UserAdminAPI.as_view({'post': 'impersonate'}), name='admin-users-impersonate'),
    path('admin/users/<int:pk>/change_email/', views.UserAdminSuperUserAPI.as_view({'post': 'change_email'}), name='admin-users-change-email'),
    path('admin/users/<int:pk>/refund_request/', views.UserAdminAPI.as_view({'post': 'refund_request'}), name='admin-users-refund-request'),
    path('admin/users/<int:pk>/reset_mfa_throttle/', views.UserAdminAPI.as_view({'post': 'reset_mfa_throttle'}), name='admin-reset-mfa-throttle'),

    # Admin - Awards
    path('admin/awards/', views.AwardAdminList.as_view({'get': 'list', 'post': 'create'}), name='admin-awards'),
    path('admin/awards/<str:pk>/', views.AwardAdminAPI.as_view(), name='admin-award-retrieve'),
    path('admin/user-awards/', views.UserAwardAdminList.as_view({'get': 'list', 'post': 'create'}), name='admin-user-awards'),
    path('admin/user-awards/<str:pk>/', views.UserAwardAdminAPI.as_view(), name='admin-user-award'),

    # NPS
    path('user/nps/', views.NPSSurveyAPI.as_view({'post': 'post'}), name='new-nps-survey'),
    path('user/nps/skip/', views.NPSSurveyAPI.as_view({'post': 'skip_survey'}), name='nps-survey-skip'),

    # Request Email Login Link
    path("user/request_login_link_email/", views.RequestLoginLinkEmailView.as_view(), name="request-login-link-email"),

    # Store review
    path('user/app-review/', views.AppReviewAPI.as_view(), name='user-app-review'),

    # Rank list
    path('ranks/', views.RankList.as_view({'get': 'list'}), name='rank-list'),
]
