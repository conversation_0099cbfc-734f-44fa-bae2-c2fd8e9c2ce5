# https://taskfile.dev

version: '3'

tasks:
  create-superuser:
    cmds:
      - DJANGO_SUPERUSER_PASSWORD=abc123 poetry run python manage.py createsuperuser --no-input --username teste --email <EMAIL>
    silent: false

  run-dev:
    cmds:
      - poetry run python manage.py runserver 0.0.0.0:8000
    silent: false

  run-dev-checkout:
    cmds:
      - poetry run fastapi dev main.py --port 8080
    silent: false

  test:
    cmds:
      - poetry run pytest -W ignore --reuse-db --show-capture=stderr {{.CLI_ARGS}}
    silent: false

  drop-test-db:
    cmds:
      - psql ********************************************/postgres -c "DROP DATABASE IF EXISTS test_caktodb;"
    silent: false

  test-clean:
    cmds:
      - task: drop-test-db
      - task: test
        vars: { CLI_ARGS: '--create-db {{.CLI_ARGS}}'}
    silent: false

  clean:
    cmds:
      - task: drop-test-db

  run-all-workers:
    deps:
      - run-rqworker
      - run-cakto_pixel-rqworker
      - run-metrics-rqworker
      - run-runapscheduler

  run-rqworker:
    cmds:
      - poetry run python manage.py rqworker default

  run-cakto_pixel-rqworker:
    cmds:
      - poetry run python manage.py rqworker pixel_events cakto_pixel

  run-metrics-rqworker:
    cmds:
      - poetry run python manage.py rqworker metrics

  run-runapscheduler:
    cmds:
      - poetry run python manage.py runapscheduler

  run-all:
    deps:
      - run-dev
      - run-dev-checkout
      - run-all-workers

