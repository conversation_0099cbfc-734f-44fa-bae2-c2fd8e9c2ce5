# General
DJANGO_SETTINGS_MODULE=cakto.settings_devcontainer
SECRET_KEY=django-insecure-i(^4b$@4lj5%wucdq#qls19tp9z-@4ab$ez^+vpgcb2v%8xs36
DEBUG=false
ALLOWED_HOSTS=backend.dev.teste,frontend.dev.teste,localhost
CORS_ORIGIN_WHITELIST=http://localhost:3000,http://localhost:3001,http://localhost:3002,https://frontend.dev.teste,https://backend.dev.teste
SESSION_COOKIE_DOMAIN=localhost
CSRF_TRUSTED_ORIGINS=http://localhost:3000,http://localhost:8000,http://localhost:3001,http://localhost:3002,https://frontend.dev.teste,https://backend.dev.teste
REDIRECT_DOMAIN=backend.dev.teste
BACKEND_BASE_URL=localhost:8000/
FRONT_END_BASE_URL=https://app.cakto.com.br
CHECKOUT_BASE_URL=http://localhost:3001
WHATSAPP_VALIDATION_ENABLED=false

# INFOBIP
INFOBIP_BASE_URL=
INFOBIP_API_TOKEN=
INFOBIP_WHATSAPP_NUMBER=
INFOBIP_AUTH_TEMPLATE_NAME=autenticacao_cakto

# Database
DB_NAME=caktodb
DB_USER=caktouser
DB_PASSWORD=caktopassword
DB_HOST=db
DB_PORT=5432
DB_SSL_MODE=disable

# redis
REDIS_URL=redis://redis:6379
REDIS_URL_QUEUE=redis://redis:6379

# aws
AWS_ACCESS_KEY_ID=aws_s3_access_key_id
AWS_SECRET_ACCESS_KEY=aws_secret_access_key
AWS_STORAGE_BUCKET_NAME=caktobackend
AWS_S3_REGION_NAME=eu-west-1
AWS_S3_ENDPOINT_URL='http://localstack:4566'
AWS_S3_CUSTOM_DOMAIN=''

# email
EMAIL_HOST=
EMAIL_PORT=
EMAIL_USE_TLS=
EMAIL_HOST_USER=
EMAIL_HOST_PASSWORD=

# PROMETHEUS
PROMETHEUS_TOKEN=


WEBHOOK_SECRETS=webhooktest

# manychat
MANYCHAT_TOKEN=manychatTest
MANYCHAT_AUTO_PHONE_ID=123456

# instagram
INSTAGRAM_BASE_URL=https://testing-instagram.com/
INSTAGRAM_AUTH_TOKEN=TESTING_TOKEN!@#$*(&)


# members
MEMBERS_URL=
MEMBERS_ADMIN_PASSWORD=
COURSE_ACCESS_BASE_URL=https://members.cakto.com.br/
VIDEO_HOST_APIKEY=
VIDEO_CDN_HOST_NAME=
VIDEO_LIBRARY_ID=

# members V2
MEMBERS_V2_BASE_URL=https://testing-members.com
MEMBERS_V2_API_TOKEN=TESTING_TOKEN

# Facebook Pixel for cakto custom conversion events (login)
FACEBOOK_PIXEL_ID=
FACEBOOK_ACCESS_TOKEN=

# MailChimp Settings
MAILCHIMP_API_KEY=
MAILCHIMP_SERVER_PREFIX=
MAILCHIMP_AUDIENCE_ID=

# Register producer webhook - url to receive producer registration events
REGISTER_PRODUCER_WEBHOOK_URL=

# TheMembers
THEMEMBERS_DEV_TOKEN=
