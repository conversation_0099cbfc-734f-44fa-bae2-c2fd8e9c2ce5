from rest_framework import serializers
from rest_flex_fields import FlexFieldsModelSerializer

from .models import SystemLog
from user.serializers import UserAdminSerializer
from product.serializers import ProductSerializer


class SystemLogSerializer(FlexFieldsModelSerializer):
    class Meta:
        model = SystemLog
        fields = '__all__'
        expandable_fields = {
            'user': (UserAdminSerializer, {'read_only': True}),
            'affected_user': (UserAdminSerializer, {'read_only': True}),
            'product': (ProductSerializer , {'read_only': True}),
        }
