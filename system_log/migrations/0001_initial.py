# Generated by Django 4.2.5 on 2025-04-07 21:51

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('product', '0126_product_pixexpiresin'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='SystemLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.CharField(choices=[('transaction_update', 'Alteração em transações'), ('change_permissions', 'Alteração de permissões'), ('user_login', 'Login de usuário'), ('custom_fee_update', 'Alteração de taxa e reserva customizada'), ('withdrawal_approval', 'Aprovação de saque'), ('gateway_request', 'Solicitação de gateway'), ('account_reset', 'Reset de conta/verificação'), ('product_block', 'Bloqueio de produtos'), ('support_user_login', 'Login no usuário do suporte'), ('send_new_password', 'Envio de nova senha'), ('manual_note', 'Observação manual'), ('manual_withdrawal', 'Saque manual'), ('withdrawals', 'Saques'), ('balance_recovery', 'Reaver o saldo'), ('med_applied', 'Med aplicado')], max_length=50)),
                ('details', models.TextField(blank=True, null=True)),
                ('createdAt', models.DateTimeField(auto_now_add=True)),
                ('affected_user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='affected_logs', to=settings.AUTH_USER_MODEL)),
                ('product', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='product.product')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'System Log',
                'verbose_name_plural': 'System Logs',
            },
        ),
    ]
