from django.db import models


class SystemLog(models.Model):
    ACTIONS = [
        ('transaction_update', 'Alteração em transações'),
        ('change_permissions', 'Alteração de permissões'),
        ('user_login', 'Login de usuário'),
        ('custom_fee_update', 'Alteração de taxa e reserva customizada'),
        ('withdrawal_approval', 'Aprovação de saque'),
        ('gateway_request', 'Solicitação de gateway'),
        ('account_reset', 'Reset de conta/verificação'),
        ('product_block', 'Bloqueio de produtos'),
        ('support_user_login', 'Login no usuário do suporte'),
        ('send_new_password', 'Envio de nova senha'),
        ('manual_note', 'Observação manual'),
        ('manual_withdrawal', 'Saque manual'),
        ('withdrawals', 'Saques'),
        ('balance_recovery', 'Reaver o saldo'),
        ('med_applied', 'Med aplicado'),
    ]
    user = models.ForeignKey('user.User', on_delete=models.SET_NULL, null=True, blank=True)
    action = models.CharField(max_length=50, choices=ACTIONS)
    details = models.TextField(null=True, blank=True)
    createdAt = models.DateTimeField(auto_now_add=True)

    # Campos de associação
    affected_user = models.ForeignKey('user.User', null=True, blank=True, on_delete=models.SET_NULL, related_name='affected_logs')
    product = models.ForeignKey('product.Product', null=True, blank=True, on_delete=models.SET_NULL)

    class Meta:
        verbose_name = 'System Log'
        verbose_name_plural = 'System Logs'
        ordering = ['-createdAt']

    def __str__(self):
        return f"{self.user} - {self.get_action_display()} - {self.createdAt}"
