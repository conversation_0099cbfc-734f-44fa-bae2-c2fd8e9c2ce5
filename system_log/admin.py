from django.contrib import admin

from .models import SystemLog


@admin.register(SystemLog)
class SystemLogAdmin(admin.ModelAdmin):
    model = SystemLog
    list_display = ('user', 'action', 'createdAt')
    search_fields = ('user__email', 'details')
    list_filter = ('action', 'createdAt')
    ordering = ('-createdAt',)

    def has_add_permission(self, request):
        return False

    def has_delete_permission(self, request, obj=None):
        return False

    def get_readonly_fields(self, request, obj=None):
        return [f.name for f in self.model._meta.concrete_fields]
