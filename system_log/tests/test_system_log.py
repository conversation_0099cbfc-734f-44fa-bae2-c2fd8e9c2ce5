from datetime import timedelta

from django.urls import reverse
from django.utils import timezone
from rest_framework import status

from cakto.tests.base import BaseTestCase
from product.models import Product
from system_log.models import SystemLog
from system_log.utils import log_action


class SystemLogAPITestCase(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.staff_user = cls.create_user(
            email='<EMAIL>',
            is_superuser=False,
            is_staff=True
        )
        cls.product = cls.create_product(user=cls.staff_user)
        cls.log = SystemLog.objects.create(
            user=cls.staff_user,
            action='product_block',
            details='Produto bloqueado manualmente',
            product=cls.product,
            affected_user=cls.user,
        )

    @property
    def staff_headers(self):
        return self.create_headers(self.get_user_access_token(self.staff_user))

    @property
    def user_headers(self):
        return self.create_headers(self.get_user_access_token(self.user))

    def test_list_logs_as_staff_user(self):
        url = reverse('system_log:list-system-logs')
        response = self.client.get(url, headers=self.staff_headers)
        self.assertEqual(response.status_code, 200)
        self.assertGreaterEqual(len(response.json()), 1)

    def test_list_logs_as_normal_user(self):
        url = reverse('system_log:list-system-logs')
        response = self.client.get(url, headers=self.user_headers)
        self.assertEqual(response.status_code, 403)

    def test_retrieve_log_as_staff_user_with_expand(self):
        url = reverse('system_log:retrieve-system-log', kwargs={'id': self.log.id}) + '?expand=user,affected_user,product'
        response = self.client.get(url, headers=self.staff_headers)
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertIn('user', data)
        self.assertIn('affected_user', data)
        self.assertIn('product', data)

    def test_retrieve_log_as_normal_user(self):
        url = reverse('system_log:retrieve-system-log', kwargs={'id': self.log.id})
        response = self.client.get(url, headers=self.user_headers)
        self.assertEqual(response.status_code, 403)

    def test_list_logs_filter_by_action(self):
        SystemLog.objects.all().delete()
        SystemLog.objects.create(
            user=self.staff_user,
            action='product_block',
            details='Teste filtro',
            affected_user=self.user,
        )
        url = reverse('system_log:list-system-logs') + '?action=product_block'
        response = self.client.get(url, headers=self.build_user_auth_headers(self.staff_user))
        self.assertEqual(response.status_code, 200)

        results = response.json().get('results', [])
        self.assertTrue(all(log['action'] == 'product_block' for log in results))

    def test_create_log_via_log_action(self):
            response = self.client.get(
                reverse('system_log:list-system-logs'),
                headers=self.build_user_auth_headers(self.staff_user)
            )
            self.assertEqual(response.status_code, 200)

    def test_log_action_utils_creates_log(self):
        from system_log.utils import log_action
        log_action(
            user=self.staff_user,
            action='product_block',
            details='Log criado via utilitário',
            affected_user=self.user,
            product=self.product,
        )
        self.assertEqual(SystemLog.objects.filter(action='product_block').count(), 2)

    def test_filter_by_date_range(self):
        # Log 1 (ontem)
        log_ontem = SystemLog.objects.create(
            user=self.staff_user,
            action='manual_withdrawal',
            details='Log de ontem'
        )
        log_ontem.createdAt = timezone.now() - timedelta(days=1)
        log_ontem.save()

        # Log 2 (hoje)
        log_hoje = SystemLog.objects.create(
            user=self.staff_user,
            action='withdrawals',
            details='Log de hoje'
        )

        url = reverse('system_log:list-system-logs')

        # Filtra logs criados hoje em diante
        hoje_str = timezone.now().strftime('%Y-%m-%dT%H:%M:%S')
        response = self.client.get(f'{url}?created_at__gte={hoje_str}', headers=self.staff_headers)
        self.assertEqual(response.status_code, 200)
        results = response.json().get('results', [])
        self.assertTrue(all(r['details'] != 'Log de ontem' for r in results))

        # Filtra logs criados antes de hoje
        response = self.client.get(f'{url}?created_at__lte={hoje_str}', headers=self.staff_headers)
        self.assertEqual(response.status_code, 200)
        results = response.json().get('results', [])
        self.assertTrue(any(r['details'] == 'Log de ontem' for r in results))

    def test_list_logs_no_results(self):
        SystemLog.objects.all().delete()
        SystemLog.objects.create(
            user=self.staff_user,
            action='gateway_request',
            details='Teste no results'
        )
        url = reverse('system_log:list-system-logs') + '?action=change_permissions'
        response = self.client.get(url, headers=self.staff_headers)
        self.assertEqual(response.status_code, 200)
        data = response.json()
        results = data.get('results', [])
        self.assertEqual(len(results), 0)

    def test_retrieve_non_existent_log(self):
        fake_id = 999999
        url = reverse('system_log:retrieve-system-log', kwargs={'id': fake_id})
        response = self.client.get(url, headers=self.staff_headers)
        self.assertEqual(response.status_code, 404)

    def test_log_with_null_fields(self):
        log_null = SystemLog.objects.create(
            action='manual_note',
            details='Log sem campos relacionais'
        )
        list_url = reverse('system_log:list-system-logs')
        response_list = self.client.get(list_url, headers=self.staff_headers)
        self.assertEqual(response_list.status_code, 200)

        results = response_list.json().get('results', [])
        self.assertTrue(any(r['id'] == log_null.id for r in results))

        detail_url = reverse('system_log:retrieve-system-log', kwargs={'id': log_null.id})
        response_detail = self.client.get(detail_url, headers=self.staff_headers)
        self.assertEqual(response_detail.status_code, 200)
        data = response_detail.json()

        self.assertEqual(data['id'], log_null.id)

        self.assertIsNone(data['user'])
        self.assertIsNone(data['product'])
        self.assertIsNone(data['affected_user'])
