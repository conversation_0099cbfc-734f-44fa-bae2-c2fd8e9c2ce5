from rest_framework import generics
from rest_framework.permissions import IsAdminUser
from django_filters.rest_framework import DjangoFilterBackend
from rest_flex_fields.views import FlexFieldsMixin

from .models import SystemLog
from .serializers import SystemLogSerializer
from .filters import SystemLogFilter


class SystemLogListAPIView(generics.ListAPIView):
    serializer_class = SystemLogSerializer
    permission_classes = [IsAdminUser]
    queryset = SystemLog.objects.all()
    filter_backends = [DjangoFilterBackend]
    filterset_class = SystemLogFilter


class SystemLogRetrieveAPIView(FlexFieldsMixin, generics.RetrieveAPIView):
    queryset = SystemLog.objects.all()
    serializer_class = SystemLogSerializer
    permission_classes = [IsAdminUser]
    lookup_field = 'id'
