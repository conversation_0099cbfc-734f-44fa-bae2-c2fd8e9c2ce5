from django.db.models import Q
import django_filters
from django_filters import rest_framework as filters
from rest_framework.filters import Ordering<PERSON>ilter

from product.models import Affiliate, Coupon, Offer, Product


class CharFieldInFilter(filters.BaseInFilter, filters.CharFilter):
    pass

class ProductFilter(filters.FilterSet):
    name = filters.CharFilter(lookup_expr='icontains')
    status = CharFieldInFilter()
    statusAdmin = filters.CharFilter()
    userEmail = filters.CharFilter(field_name='user__email', lookup_expr='icontains')
    type = CharFieldInFilter()
    contentDelivery = filters.CharFilter(method='filter_contentDeliveries')
    category = CharFieldInFilter()
    threeDsRetryEnabled = filters.BooleanFilter()
    displayedInShowcase = filters.BooleanFilter(method='filter_displayedInShowcase')

    class Meta:
        model = Product
        fields = {
            'id': ['exact'],
            'price': ['exact', 'gt', 'lt', 'lte', 'gte'],
            'createdAt': ['exact', 'gt', 'lt', 'lte', 'gte'],
            'updatedAt': ['exact', 'gt', 'lt', 'lte', 'gte'],
        }

    def filter_displayedInShowcase(self, queryset, name, value):
        if value:
            return queryset.filter(Product.get_showcase_filters())
        return queryset

    def filter_contentDeliveries(self, queryset, name, value):
        return queryset.filter(contentDeliveries__type__in=value.split(','))

class AffiliateAdminFilter(filters.FilterSet):
    short_id = filters.CharFilter(field_name='short_id', lookup_expr='exact')
    product = filters.CharFilter(field_name='product__id', lookup_expr='exact')
    productName = filters.CharFilter(field_name='product__name', lookup_expr='icontains')
    user = filters.NumberFilter(field_name='user', lookup_expr='exact')
    userEmail = filters.CharFilter(field_name='user__email', lookup_expr='icontains')
    status = CharFieldInFilter()
    endTime__gte = filters.DateTimeFilter(field_name='endTime', method='filter_endTime_gte')
    endTime__gt = filters.DateTimeFilter(field_name='endTime', method='filter_endTime_gt')

    class Meta:
        model = Affiliate
        fields = {
            'commission': ['exact', 'gt', 'lt', 'lte', 'gte'],
            'startTime': ['exact', 'gt', 'lt', 'lte', 'gte'],
            'endTime': ['exact', 'lt', 'lte']
        }

    def filter_endTime_gte(self, queryset, name, value):
        return queryset.filter(Q(**{f'{name}__gte': value}) | Q(endTime__isnull=True))

    def filter_endTime_gt(self, queryset, name, value):
        return queryset.filter(Q(**{f'{name}__gt': value}) | Q(endTime__isnull=True))

class CouponFilter(filters.FilterSet):
    status = CharFieldInFilter()
    endTime__gte = filters.DateTimeFilter(field_name='endTime', method='filter_endTime_gte')
    endTime__gt = filters.DateTimeFilter(field_name='endTime', method='filter_endTime_gt')

    class Meta:
        model = Coupon
        fields = {
            'code': ['exact', 'icontains'],
            'discount': ['exact', 'gt', 'lt', 'lte', 'gte'],
            'applyOnBumps': ['exact'],
            'startTime': ['exact', 'gt', 'lt', 'lte', 'gte'],
            'endTime': ['exact', 'lt', 'lte'],
            'createdAt': ['exact', 'gt', 'lt', 'lte', 'gte'],
            'updatedAt': ['exact', 'gt', 'lt', 'lte', 'gte'],
        }

    def filter_endTime_gte(self, queryset, name, value):
        return queryset.filter(Q(**{f'{name}__gte': value}) | Q(endTime__isnull=True))

    def filter_endTime_gt(self, queryset, name, value):
        return queryset.filter(Q(**{f'{name}__gt': value}) | Q(endTime__isnull=True))

class OfferFilter(filters.FilterSet):
    id = CharFieldInFilter()
    product = CharFieldInFilter()
    name = filters.CharFilter(lookup_expr='icontains')
    intervalType = CharFieldInFilter()
    type = CharFieldInFilter()
    status = CharFieldInFilter()
    default = filters.BooleanFilter()

    class Meta:
        model = Offer
        fields = {
            'price': ['exact', 'gt', 'lt', 'lte', 'gte'],
            'interval': ['exact', 'gt', 'lt', 'lte', 'gte'],
            'createdAt': ['exact', 'gt', 'lt', 'lte', 'gte'],
            'updatedAt': ['exact', 'gt', 'lt', 'lte', 'gte'],
        }

class ProductShowcaseFilter(django_filters.FilterSet):
    temperature = django_filters.NumberFilter(field_name='temperature')
    temperature__gte = django_filters.NumberFilter(field_name='temperature', lookup_expr='gte')
    temperature__lte = django_filters.NumberFilter(field_name='temperature', lookup_expr='lte')
    temperature__gt = django_filters.NumberFilter(field_name='temperature', lookup_expr='gt')
    temperature__lt = django_filters.NumberFilter(field_name='temperature', lookup_expr='lt')

    class Meta:
        model = Product
        fields = {
            'affiliate': ['exact'],
            'affiliateCommission': ['lt', 'gt', 'lte', 'gte'],
            'affiliateRequest': ['exact'],
            'affiliateContact': ['exact'],
            'affiliateShareBump': ['exact'],
            'affiliateShareUpsell': ['exact'],
            'price': ['lt', 'gt', 'lte', 'gte'],
            'type': ['exact'],
            'language': ['exact'],
            'category': ['exact'],
        }

class ShowcaseOrderingFilter(OrderingFilter):
    def get_ordering(self, request, queryset, view):
        ordering = super().get_ordering(request, queryset, view)
        if not ordering:
            return ordering

        has_id = any(term.lstrip('-') == 'id' for term in ordering)
        if not has_id:
            sign = '-' if ordering[0].startswith('-') else ''
            ordering = list(ordering) + [f'{sign}id']

        return ordering
