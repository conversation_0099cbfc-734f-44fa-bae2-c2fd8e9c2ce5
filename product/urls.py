from django.urls import path

from product import views

urlpatterns = [
    # Affiliates
    path('affiliates/', views.AffiliateListCreateAPIView.as_view({'get': 'list', 'post': 'create'}), name='affiliates'),
    path('affiliates/accept_many/', views.AffiliateListCreateAPIView.as_view({'post': 'accept_many'}), name='affiliate-accept-many'),
    path('affiliates/reject_many/', views.AffiliateListCreateAPIView.as_view({'post': 'reject_many'}), name='affiliate-reject-many'),
    path('affiliates/block_many/', views.AffiliateListCreateAPIView.as_view({'post': 'block_many'}), name='affiliate-block-many'),
    path('affiliates/unblock_many/', views.AffiliateListCreateAPIView.as_view({'post': 'unblock_many'}), name='affiliate-unblock-many'),
    path('affiliates/products/', views.AffiliateProductAPIView.as_view({'get': 'list'}), name='affiliate-products'),
    path('affiliates/products/<str:short_id>/', views.AffiliateProductAPIView.as_view({'get': 'retrieve'}), name='affiliate-product'),
    path('affiliates/total_sales/', views.AffiliateTotalSalesAPIView.as_view(), name='affiliate-total-sales'),
    path('affiliates/<str:pk>/', views.AffiliateRetrieveUpdateDestroyAPIView.as_view({'get': 'retrieve', 'delete': 'destroy', 'put': 'update'}), name='affiliate'),
    path('affiliate/<uuid:id>', views.AffiliateDetailAPIView.as_view(), name='affiliate-detail'),

    # Links
    path('<str:product>/affiliate_links/', views.LinkAPIView.as_view({'get': 'affiliate_links'}), name='affiliate-links'),
    path('<str:product>/links/', views.LinkAPIView.as_view({'get': 'list', 'post': 'create'}), name='links'),
    path('link/<int:pk>/', views.LinkAPIView.as_view({'delete': 'destroy', 'get': 'retrieve', 'put': 'update'}), name='link'),
    path('link/<int:pk>/disable/', views.LinkAPIView.as_view({'post': 'disable'}), name='link-disable'),
    path('link/<int:pk>/enable/', views.LinkAPIView.as_view({'post': 'enable'}), name='link-enable'),
    path('link/<int:pk>/show_affiliates/', views.LinkAPIView.as_view({'post': 'show_affiliates'}), name='link-show-affiliates'),
    path('link/<int:pk>/hide_affiliates/', views.LinkAPIView.as_view({'post': 'hide_affiliates'}), name='link-hide-affiliates'),
    path('link/enable_many/', views.LinkAPIView.as_view({'post': 'enable_many'}), name='link-enable-many'),
    path('link/disable_many/', views.LinkAPIView.as_view({'post': 'disable_many'}), name='link-disable-many'),

    # Product Delivery
    path('product/<str:pk>/delivery/', views.ProductDeliveryAPI.as_view({'get': 'list', 'post': 'create'}), name='product-delivery'),

    # Product Delivery Access
    path('product/delivery_access/', views.DeliveryAccessAPI.as_view(), name='product-delivery-access-list'),
    path('product/delivery_access/<str:delivery_access_id>/', views.DeliveryAccessAPI.as_view(), name='product-delivery-access'),

    # Products
    path('products/', views.ProductAPI.as_view({'get': 'list', 'post': 'create'}), name='products'),
    path('products/payment_methods/', views.PaymentMethodAPIView.as_view(), name='products-payment-methods'),
    path('products/content_delivery/', views.ContentDeliveryAPIView.as_view(), name='products-content-delivery-methods'),
    path('products/showcase', views.ShowcaseAPIView.as_view(), name='products-showcase'),
    path("products/showcase/v2/", views.ShowcaseV2APIView.as_view(), name="products-showcase-v2"),
    path('products/showcase/register_events/', views.ShowcaseEventView.as_view(), name='showcase-register-event'),
    path('product/categories/', views.categories, name='categories'),
    path('product/<str:pk>/', views.ProductRetrieveAPI.as_view({'get': 'retrieve', 'put': 'update', 'delete': 'destroy'}), name='product'),
    path('product/<str:pk>/duplicate/', views.ProductRetrieveAPI.as_view({'post': 'duplicate'}), name='product-duplicate'),
    path('product/<str:pk>/public/', views.ProductPublicApi.as_view(), name='product-public'),
    path('product/upsell/<str:short_id>/', views.OfferUpsellInstallmentsAPIView.as_view(), name='product-upsell-installments'),
    path('product/<str:pk>/image/', views.ProductImageAPIView.as_view(), name='productImage'),
    path('product/<str:product>/bump/', views.OrderBumpAPI.as_view({'post': 'create'}), name='product-bump'),
    path('product/<str:product>/bump/position_update/', views.OrderBumpAPI.as_view({'post': 'position_update'}), name='product-bump-position-update'),
    path('bump/<str:pk>/', views.OrderBumpRetrieveAPI.as_view(), name='bump'),

    # Offers
    path('offers/', views.OfferAPIView.as_view({'get': 'list', 'post': 'create'}), name='offers'),
    path('offers/<str:pk>/', views.OfferAPIView.as_view({'get': 'retrieve', 'put': 'partial_update', 'delete': 'destroy'}), name='offer'),
    path('offers/<str:pk>/block_product/', views.BlockProductByOfferAPIView.as_view({'patch': 'patch'}), name='block-product-by-offer'),

    # Admin
    path('admin/products/', views.ProductAdminList.as_view(), name='admin-products-list'),
    path('admin/products/<str:product_pk>/', views.ProductAdminAPI.as_view({'get': 'retrieve', 'put': 'update'}), name='admin-products-retrieve'),
    path('admin/products/<str:product_pk>/approve/', views.ProductAdminAPI.as_view({'post': 'approve'}), name='admin-products-approve'),
    path('admin/products/<str:product_pk>/block/', views.ProductAdminAPI.as_view({'post': 'block'}), name='admin-products-block'),
    path('admin/products/<str:product_pk>/block_card/', views.ProductAdminAPI.as_view({'post': 'block_card'}), name='admin-products-block-card'),
    path('admin/products/<str:product_pk>/unblock_card/', views.ProductAdminAPI.as_view({'post': 'unblock_card'}), name='admin-products-unblock-card'),
    path('admin/products/<str:product_pk>/unblock/', views.ProductAdminAPI.as_view({'post': 'unblock'}), name='admin-products-unblock'),
    path('admin/products/<str:product_pk>/refund_request/', views.ProductAdminAPI.as_view({'post': 'refund_request'}), name='admin-products-refund-request'),
    path('admin/affiliates/', views.AffiliateAdminList.as_view(), name='admin-affiliates'),
    path('admin/products/<str:product_pk>/hideshowcase/', views.ProductAdminAPI.as_view({'post': 'hideshowcase'}), name='admin-products-hideshowcase'),
    path('admin/products/<str:product_pk>/showcase_sales_filter/', views.ProductAdminAPI.as_view({'post': 'showcase_sales_filter'}), name='admin-products-showcase-sales-filter'),

    # Coproductions
    path('coproductions/', views.CoproductionListView.as_view(), name='coproductions'),
    path('coproduction/', views.CoproductionCreateView.as_view(), name='coproduction-create'),
    path('coproduction/<int:pk>/', views.CoproductionAPIView.as_view({'get': 'retrieve', 'put': 'update'}), name='coproduction'),
    path('coproduction/<int:pk>/accept/', views.CoproductionAPIView.as_view({'post': 'accept'}), name='coproduction-accept'),
    path('coproduction/<int:pk>/reject/', views.CoproductionAPIView.as_view({'post': 'reject'}), name='coproduction-reject'),
    path('coproduction/<int:pk>/delete/', views.CoproductionAPIView.as_view({'delete': 'destroy'}), name='coproduction-delete'),
    path('coproduction/<int:pk>/cancel_delete/', views.CoproductionAPIView.as_view({'post': 'cancel_delete'}), name='coproduction-cancel-delete'),

    # Pixels
    path('pixels/tracking_pixels/<str:pk>/', views.TrackingPixelAPIView.as_view(), name='tracking-pixels'),
    path('pixels/pixel_domain/', views.PixelDomainAPIView.as_view({'post': 'create', 'get': 'list'}), name='pixel-domain'),
    path('pixels/pixel_domain/<int:pk>/', views.PixelDomainAPIView.as_view({'get': 'retrieve', 'put': 'update', 'delete': 'destroy'}), name='pixel-domain-detail'),
    path('pixels/pixel_domain/<int:pk>/verify/', views.PixelDomainAPIView.as_view({'post': 'verify_pixel_domain'}), name='verify-pixel-domain'),

    # Cupons
    path('coupons/', views.CouponListCreateAPIView.as_view(), name='coupons'),
    path('coupons/validate/', views.CouponAllowAnyAPI.as_view({'post': 'validate_coupon'}), name='coupon-validate'),
    path('coupons/<str:pk>/', views.CouponAPI.as_view({'get': 'retrieve', 'patch': 'update', 'delete': 'destroy'}), name='coupon'),

    path('suppliers/', views.ProductSuppliers.as_view(), name='suppliers'),
]
