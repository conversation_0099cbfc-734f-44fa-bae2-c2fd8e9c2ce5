from typing import Any, Mapping, Sequence
from urllib.parse import urljoin

import requests
from django.conf import settings

from financial.enums import CompanyStatus
from product.models import Offer


class ComplianceService:
    """
    Service responsible for sending offer data to the AI compliance analysis system.
    """

    def __init__(self):
        self.compliance_url = settings.AI_COMPLIANCE_URL
        self.api_key = settings.AI_COMPLIANCE_API_KEY

    def _get_headers(self) -> Mapping[str, str]:
        return {'x-api-token': self.api_key}

    def _filter_out_deleted_offers(self, offers: Sequence[Offer]) -> Sequence[Offer]:
        return [offer for offer in offers if offer.status != 'deleted']

    def _is_producer_blocked(self, offers: Sequence[Offer]) -> bool:
        if not offers:
            return False
        # Assuming all offers belong to the same producer
        company = offers[0].product.user._company
        return company and company.status == CompanyStatus.BLOCKED.id

    def _prepare_bulk_payload(self, offers: Sequence[Offer]) -> Mapping[str, Any]:
        products_data = []
        for offer in offers:
            products_data.append({
                'id': offer.id,
                'produto_short_id': offer.id,  # Id used to get checkout data
                'produto_nome': offer.product.name,
                'produto_descricao': offer.product.description,
                'produto_imagem': offer.product.image.url if offer.product.image else '',
                'vendedor_nome': offer.product.producerName,
                'vendedor_email': offer.product.user.email,
            })
        return {'products': products_data}

    def _send_single_offer_analysis(self, active_offers):
        headers = self._get_headers()
        offer = active_offers[0]
        url = urljoin(self.compliance_url, f'/v1/compliance/analyze/single/{offer.id}')
        requests.post(url=url, headers=headers, timeout=2)

    def _send_bulk_compliance_analysis(self, active_offers):
        headers = self._get_headers()
        payload = self._prepare_bulk_payload(active_offers)
        url = urljoin(self.compliance_url, '/v1/compliance/analyze/bulk')
        requests.post(url=url, json=payload, headers=headers, timeout=2)

    def analyze_offers(self, offers: Sequence[Offer]) -> None:
        """
        Sends offer data for compliance analysis.
        It handles single or bulk offer analysis based on the number of offers.
        """
        if not self.compliance_url or not self.api_key:
            return

        offers = self._filter_out_deleted_offers(offers)

        if not offers or self._is_producer_blocked(offers):
            return

        try:
            if len(offers) > 1:
                self._send_bulk_compliance_analysis(offers)
            elif len(offers) == 1:
                self._send_single_offer_analysis(offers)
        except Exception as e:
            print(f"Erro ao enviar dados de ofertas para análise de conformidade: {e}")
