import json
import re
import uuid
from decimal import Decimal
from typing import Iterable, Literal

from django.conf import settings
from django.core.cache import cache
from django.core.files import File
from django.core.files.temp import NamedTemporaryFile
from django.core.validators import MaxValueValidator, MinValueValidator
from django.db import models
from django.db.models import Max, Q
from django.utils import timezone
from django_lifecycle import AFTER_CREATE, AFTER_DELETE, AFTER_SAVE, LifecycleModelMixin, hook
from shortuuid.django_fields import ShortUUID, ShortUUIDField
from phonenumber_field.modelfields import <PERSON>NumberField

from gateway.models import Order
from gateway.sdk.splitpay import SplitPay
from product.enums import AffiliateStatus, LanguageOptions, PaymentMethodStatus, ProductType, ShowcaseEventType
from user.models import User


def generate_uuid():
    return str(uuid.uuid4())

class Category(models.Model):
    class Meta:
        verbose_name = 'Categoria'
        verbose_name_plural = 'Categorias'
        ordering = ['name']

    # uuid v4 hex
    id = models.CharField(max_length=255, primary_key=True, unique=True, default=generate_uuid)
    name = models.CharField(max_length=255)

    def __str__(self):
        return self.name

    def serialize(self):
        return {
            'id': self.id,
            'name': self.name
        }

class PaymentMethod(LifecycleModelMixin, models.Model):
    type = models.CharField(
        max_length=255,
        primary_key=True,
        unique=True,
        help_text='Identificador do método de pagamento, ex.: pix, credit_card...',
    )
    name = models.CharField(
        max_length=255,
        help_text='Nome do método de pagamento, ex.: Pix, Cartão de Crédito...',
    )
    status = models.CharField(
        max_length=255,
        choices=PaymentMethodStatus.choices(),
        default=PaymentMethodStatus.ACTIVE.id,
        help_text='Status do método de pagamento.'
    )

    class Meta:
        verbose_name = 'Método de pagamento'
        verbose_name_plural = 'Métodos de pagamento'

    def __str__(self):
        return self.name or self.type

    cache_key = 'payment_method_instances'

    @hook(AFTER_SAVE)
    @hook(AFTER_CREATE)
    @hook(AFTER_DELETE)
    def invalidate_cache(self):
        cache.delete(self.cache_key)

    def _serialize(self):
        return {
            'type': self.type,
            'name': self.name,
        }

    @classmethod
    def _fetch_payment_methods(cls):
        return cls.objects.all()

    @classmethod
    def _filter_instances(
        cls,
        payment_methods: Iterable['PaymentMethod'],
        types: Iterable[str] | Literal['all'] = 'all',
        statuses: list[PaymentMethodStatus] | Literal['all'] = 'all',
        user: User | None = None,
    ) -> Iterable['PaymentMethod']:
        if statuses != 'all':
            statuses = [status.id for status in statuses]
            payment_methods = [method for method in payment_methods if method.status in statuses]

        if statuses == 'all' or PaymentMethodStatus.TESTER_USER_ACCESS.id in statuses:
            user_betas = []

            if user:
                user_betas = user.experimental_features.values_list('id', flat=True)

            payment_methods = [
                method for method in payment_methods
                if method.status != PaymentMethodStatus.TESTER_USER_ACCESS.id
                or method.type in user_betas and method.status == PaymentMethodStatus.TESTER_USER_ACCESS.id
            ]

        if types != 'all':
            payment_methods = [method for method in payment_methods if method.type in types]

        return payment_methods

    @classmethod
    def get_payment_methods(
        cls,
        types: Iterable[str] | Literal['all'] = 'all',
        statuses: list[PaymentMethodStatus] | Literal['all'] = [PaymentMethodStatus.ACTIVE, PaymentMethodStatus.TESTER_USER_ACCESS],
        user: User | None = None,
    ) -> Iterable['PaymentMethod']:
        """
        Gets cached payment methods filtered by types, statuses and user beta programs.

        Args:
            types (list or string): List of payment methods types or "all" for return all types.
            statuses (list or string): List of PaymentMethodStatus enums or string "all" for return all status.
            user (User): User instance. Used to check if user has access to payment methods with beta status.

        Returns:
            list: List of payment methods instances.
        """
        cached_payment_methods = cls._get_cached_payment_methods()

        if not cached_payment_methods:
            return []

        return cls._filter_instances(
            payment_methods=cached_payment_methods,
            statuses=statuses,
            types=types,
            user=user,
        )

    @classmethod
    def _get_cached_payment_methods(cls) -> Iterable['PaymentMethod'] | None:
        cached_payment_methods = cache.get_or_set(
            cls.cache_key,
            cls._fetch_payment_methods,
            None,
        )

        return cached_payment_methods

    @classmethod
    def get_payment_method_instance(
        cls,
        payment_method_type: str,
        user: User | None = None,
    ) -> 'PaymentMethod | None':
        """
        Gets a payment method instance by type.
        If user is provided, it will also get payment methods in beta status if user has access to it.
        """
        payment_methods = cls.get_payment_methods(user=user)
        if not payment_methods:
            return None
        return next((method for method in payment_methods if method.type == payment_method_type), None)

    @classmethod
    def get_serialized_payment_methods(
        cls,
        user: User | None = None,
    ) -> list[dict] | None:
        """
        Gets serialized payment methods.
        If user is provided, it will also get payment methods in beta status if user has access to it.

        Args:
            user (User): User instance. Used to check if user has access to payment methods with beta status.

        Returns:
            list: List of serialized payment methods.

        Example:
            [
                {
                    'type': 'pix',
                    'name': 'Pix',
                },
                {
                    'type': 'credit_card',
                    'name': 'Cartão de Crédito',
                },
            ]
        """
        payment_methods = cls.get_payment_methods(user=user)
        if not payment_methods:
            return None
        return [
            method._serialize() for method in payment_methods
            if method.type != 'threeDs'
        ]

    @classmethod
    def get_valid_payment_methods_types(
        cls,
        user: User | None = None,
    ) -> list[str]:
        """
        Gets valid payment methods types.
        If user is provided, it will also get payment methods in beta status if user has access to it.

        Args:
            user (User): User instance. Used to check if user has access to payment methods with beta status.

        Returns:
            list: List of payment methods types.

        Example:
            Calling --> PaymentMethod.get_valid_payment_methods_types() --> will return something like --> ['pix', 'credit_card']
        """
        return [method.type for method in cls.get_payment_methods(user=user)]

    @classmethod
    def get_payment_display(
        cls,
        payment_method_type: str,
        user: User | None = None,
    ) -> str | None:
        """
        Gets payment method display name by type.
        If user is provided, it will also get payment methods in beta status if user has access to it.

        Args:
            payment_method_type (str): Payment method type.
            user (User): User instance. Used to check if user has access to payment methods with beta status.

        Returns:
            str: Payment method display name.

        Example:
            Calling --> PaymentMethod.get_payment_display(type='pix') --> will return --> 'Pix'
        """
        return next(
            (method.name for method in cls.get_payment_methods(user=user) if method.type == payment_method_type),
            payment_method_type
        )

class ContentDelivery(LifecycleModelMixin, models.Model):
    class Meta:
        verbose_name = 'Entrega de conteúdo'
        verbose_name_plural = 'Entregas de conteúdo'
        ordering = ['order']

    type = models.CharField(max_length=255, primary_key=True, unique=True)
    name = models.CharField(max_length=255)
    status = models.CharField(max_length=255, choices=(
        ('active', 'Ativo'),
        ('disabled', 'Desabilitado'),
    ), default='active')
    order = models.IntegerField(default=0)

    def __str__(self):
        return self.name

    def serialize(self):
        return {
            'type': self.type,
            'name': self.name,
        }

    cache_key = 'contentDelivery_instances'

    @hook(AFTER_SAVE)
    @hook(AFTER_CREATE)
    @hook(AFTER_DELETE)
    def invalidate_cache(self):
        cache.delete(self.cache_key)

    @classmethod
    def fetch_contentDeliveries(cls):
        return cls.objects.filter(status='active')

    @classmethod
    def get_cached_contentDeliveries(cls):
        contentDeliveries: Iterable['ContentDelivery'] | None = cache.get_or_set(cls.cache_key, cls.fetch_contentDeliveries, None)
        return contentDeliveries

    @classmethod
    def serialize_contentDeliveries(cls, contentDeliveries: Iterable['ContentDelivery']):
        return [contentDelivery.serialize() for contentDelivery in contentDeliveries]

    @classmethod
    def get_contentDelivery_instance(cls, contentDelivery_type: str) -> 'ContentDelivery | None':
        contentDeliveries = cls.get_cached_contentDeliveries()
        if not contentDeliveries:
            return None
        return next((contentDelivery for contentDelivery in contentDeliveries if contentDelivery.type == contentDelivery_type), None)

    @classmethod
    def filter_contentDelivery_instances(cls, contentDelivery_types: Iterable[str]) -> list['ContentDelivery']:
        """Filter cached contentDeliveries instances by contentDelivery_types"""
        contentDeliveries = cls.get_cached_contentDeliveries()
        if not contentDeliveries:
            return []
        return [contentDelivery for contentDelivery in contentDeliveries if contentDelivery.type in contentDelivery_types]

    @classmethod
    def get_contentDeliveries(cls) -> list[dict] | None:
        contentDeliveries = cls.get_cached_contentDeliveries()
        if not contentDeliveries:
            return None
        return cls.serialize_contentDeliveries(contentDeliveries)

    @classmethod
    def get_valid_contentDelivery_types(cls) -> list[str]:
        contentDeliveries = cls.get_contentDeliveries()
        if not contentDeliveries:
            return []
        return [contentDelivery['type'] for contentDelivery in contentDeliveries]

    @classmethod
    def get_contentDeliveries_display(cls, contentDelivery_type: str) -> str | None:
        contentDeliveries = cls.get_contentDeliveries()
        if not contentDeliveries:
            return None
        return next((contentDelivery['name'] for contentDelivery in contentDeliveries if contentDelivery['type'] == contentDelivery_type), contentDelivery_type)

class Product(models.Model):
    CONTENT_DELIVERY_CHOICES = (
        ('cakto', 'Área de membros cakto'),
        ('files', 'E-book/Arquivos via E-mail'),
        ('emailAccess', 'Link de acesso via E-mail'),
        ('external', 'Área de membros externa'),
        ('disabled', 'Desabilitado')
    )

    class Meta:
        verbose_name = 'Produto'
        verbose_name_plural = 'Produtos'
        ordering = ['-id']
        permissions = [
            ('block_product', 'Can block product'),
        ]

    # Basic info
    id = models.CharField(max_length=255, primary_key=True, unique=True, default=generate_uuid)
    short_id = ShortUUIDField(length=7, max_length=40, unique=True)  # type:ignore
    membersId = models.CharField(max_length=255, verbose_name='Id da área de membros', null=True, blank=True)
    membersV2Id = models.CharField(max_length=255, verbose_name='Id da área de membros V2', null=True, blank=True)
    user = models.ForeignKey('user.User', on_delete=models.CASCADE)

    # Product info
    image = models.ImageField(upload_to='products', null=True, blank=True)
    name = models.CharField(max_length=255)
    description = models.TextField()
    category = models.ForeignKey(Category, on_delete=models.PROTECT, null=True, blank=True)
    status = models.CharField(max_length=255, choices=(
        ('active', 'Ativo'),
        ('blocked', 'Bloqueado'),
        ('deleted', 'Deletado')
    ), default='active', db_index=True)
    type = models.CharField(max_length=255, choices=ProductType.choices(), default=ProductType.UNIQUE.id)
    language = models.CharField(max_length=255, choices=LanguageOptions.choices(), default=LanguageOptions.PT.id)

    # Content Delivery
    contentDelivery = models.CharField(max_length=255, choices=CONTENT_DELIVERY_CHOICES, default='cakto')
    contentDeliveries = models.ManyToManyField('product.ContentDelivery', related_name='products')
    emailAccessLink = models.URLField(max_length=2048, null=True, blank=True)

    # Payment
    paymentMethods = models.ManyToManyField(PaymentMethod, related_name='products')
    paymentMethod = models.CharField(max_length=255, choices=(
        ('credit_card_ticket_pix', 'Cartão de crédito, boleto e pix'),
        ('credit_card_ticket', 'Cartão de crédito e boleto'),
        ('credit_card_pix', 'Cartão de crédito e pix'),
        ('credit_card', 'Apenas cartão de crédito'),
        ('pix', 'Apenas pix'),
    ), default='credit_card_ticket_pix')
    paymentsOrder = models.JSONField(default=list)
    invoiceDescription = models.CharField(max_length=255, null=True, blank=True, verbose_name='Descrição na fatura')
    ticketExpiration = models.IntegerField(default=7, verbose_name='Vencimento do boleto')
    pixExpiresIn = models.IntegerField(default=3600, verbose_name='Vencimento do pix', help_text='Vencimento do pix em segundos.')
    twoCardPayment = models.BooleanField(default=False, verbose_name='Aceitar dois cartões')
    confirmEmail = models.BooleanField(default=False, verbose_name='Pedir para confirmar e-mail')
    defaultPaymentMethod = models.ForeignKey('product.PaymentMethod', on_delete=models.SET_NULL, null=True, blank=True)
    showCouponField = models.BooleanField(default=False, verbose_name='Mostrar campo de cupom no checkout')
    showAddressFields = models.BooleanField(
        default=False,
        verbose_name='Mostrar campos de endereço no checkout',
        help_text='Exibe campos de endereço no checkout, como CEP, rua, número, etc.',
    )

    # Pricing
    price = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal("5"), db_index=True)
    guarantee = models.IntegerField(default=7)
    installments = models.IntegerField(default=12)

    # Upsell
    upsell = models.BooleanField(default=False)
    upsellPage = models.URLField(max_length=2048, null=True, blank=True)
    redirectUpsellWithBumpFail = models.BooleanField(default=True)

    # Pixel
    purchaseOnPix = models.BooleanField(default=False)
    purchaseOnTicket = models.BooleanField(default=False)

    # Support
    salesPage = models.URLField(max_length=2048, null=True, blank=True)
    supportEmail = models.EmailField(null=True, blank=True)
    supportWhatsapp = PhoneNumberField(
        null=True,
        blank=True,
        verbose_name='WhatsApp de suporte',
        help_text='Número de WhatsApp para suporte ao cliente, '
        'em formato ITU E.164, ex.: +5511999993333',
    )
    producerName = models.CharField(max_length=255, null=True, blank=True)

    # Affiliate
    affiliate = models.BooleanField(default=False, db_index=True)
    affiliateRequest = models.BooleanField(default=False)
    affiliateContact = models.BooleanField(default=False)
    affiliateMarketplace = models.BooleanField(default=False, db_index=True)
    affiliateDescription = models.TextField(null=True, blank=True)
    affiliateSupportEmail = models.EmailField(null=True, blank=True)
    affiliateShareBump = models.BooleanField(default=True)
    affiliateShareUpsell = models.BooleanField(default=False)
    affiliateCloneQuiz = models.BooleanField(default=False, verbose_name='Habilitar clonagem do quiz')
    affiliateCloneQuizUrl = models.URLField(max_length=2048, null=True, blank=True, verbose_name='Link para clonagem do quiz')
    affiliateSalesPage = models.URLField(max_length=2048, null=True, blank=True, verbose_name='Página de vendas do afiliado')

    # Click
    affiliateClick = models.CharField(
        max_length=255,
        choices=(
            ('first', 'Primeiro clique'),
            ('last', 'Último clique'),
        ),
        default='last'
    )
    cookieTime = models.IntegerField(default=30)

    # Commission
    affiliateCommission = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True
    )

    # Admin
    refundRequest = models.BooleanField(default=False, verbose_name='Solicitação de reembolso')
    scriptSuspicious = models.BooleanField(default=False)
    blockReason = models.TextField(null=True, blank=True)
    statusAdmin = models.CharField(max_length=40, choices=(
        ('approved', 'Aprovado'),
        ('pending', 'Pendente'),
        ('block_analisis', 'Análise de bloqueio'),
        ('stand_by', 'Aguardando Requisitos'),
    ), default='stand_by')
    hideShowcase = models.BooleanField(default=False, verbose_name='Esconder da vitrine')
    bypassShowcaseSalesFilter = models.BooleanField(
        default=False,
        verbose_name='Ignorar filtro de total de vendas da vitrine',
        help_text='Ignorar filtro de total de vendas da vitrine',
    )
    additionalInfo = models.JSONField(default=dict, blank=True)
    threeDsRetryEnabled = models.BooleanField(
        default=True,
        verbose_name='Habilitar retentativas de pagamento 3DS',
        help_text='Caso o pagamento falhe no cartão de crédito, será tentado novamente via 3DS',
    )

    # Metrics
    checkout_visits = models.IntegerField(default=0, verbose_name='Visitas ao checkout', help_text='Número de visitas ao checkout do produto')

    # all time
    total_sales = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal("0"))
    total_sales_count = models.IntegerField(default=0)
    total_paid = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal("0"))
    total_paid_count = models.IntegerField(default=0)
    total_refunded = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal("0"))
    total_refunded_count = models.IntegerField(default=0)
    total_chargeback = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal("0"))
    total_chargeback_count = models.IntegerField(default=0)
    # last 30 days
    sales_30_days = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal("0"))
    sales_30_days_count = models.IntegerField(default=0)
    refunded_30_days = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal("0"))
    refunded_30_days_count = models.IntegerField(default=0)
    chargeback_30_days = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal("0"))
    chargeback_30_days_count = models.IntegerField(default=0)
    # last 7 days
    sales_7_days = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal("0"))
    sales_7_days_count = models.IntegerField(default=0)
    refunded_7_days = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal("0"))
    refunded_7_days_count = models.IntegerField(default=0)
    chargeback_7_days = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal("0"))
    chargeback_7_days_count = models.IntegerField(default=0)
    # last 24h
    sales_24h = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal("0"))
    sales_24h_count = models.IntegerField(default=0)
    refunded_24h = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal("0"))
    refunded_24h_count = models.IntegerField(default=0)
    chargeback_24h = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal("0"))
    chargeback_24h_count = models.IntegerField(default=0)

    # Dates
    createdAt = models.DateTimeField(auto_now_add=True, db_index=True)
    updatedAt = models.DateTimeField(auto_now=True)
    unblockedAt = models.DateTimeField(null=True, blank=True)
    analisedAt = models.DateTimeField(null=True, blank=True)
    blockedAt = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return self.name

    def get_cache_keys(self) -> list[str]:
        cache_keys: list[str] = []
        offers = self.offers.filter(status__in=['active', 'disabled']).prefetch_related('bumps')

        # Offers
        for offer in offers:
            cache_keys.extend(offer.get_cache_keys())

        # Affiliates
        cache_keys.extend([affiliate.cache_key for affiliate in self.affiliates.all()])

        # Bump cache
        for offer in offers:
            bumps = offer.bumps.all()
            for bump in bumps:
                for offer in bump.product.offers.filter(status__in=['active', 'disabled']):
                    cache_keys.extend(offer.get_cache_keys())

        # Tracking Pixels
        for pixel in self.pixels.all():
            cache_keys.extend(pixel.get_cache_keys())

        return cache_keys

    def invalidate_cache(self) -> None:
        cache.delete_many(self.get_cache_keys())

    @staticmethod
    def bulk_invalidate_cache(product_list: Iterable['Product']) -> None:
        cache_keys: list[str] = []
        for product in product_list:
            cache_keys.extend(product.get_cache_keys())
        cache.delete_many(cache_keys)

    def save(self, *args, invalidate_cache=True, **kwargs) -> None:
        super_save = super().save(*args, **kwargs)
        if invalidate_cache:
            self.invalidate_cache()
        return super_save

    def get_refused_percentages(self):
        from django.db.models import Case, Count, Q, When

        from gateway.models import Order

        now = timezone.now()
        orders = Order.objects.filter(product=self, paymentMethod__type='credit_card').values('id', 'createdAt').annotate(
            is_refused=Case(When(status='refused', then=1), default=0, output_field=models.IntegerField()),
        ).aggregate(
            total=Count('id'),
            total_refused=Count('is_refused'),
            count_90=Count(Case(When(createdAt__gte=now - timezone.timedelta(days=90), then=1))),
            count_90_refused=Count(Case(When(Q(createdAt__gte=now - timezone.timedelta(days=90)) & Q(is_refused=1), then=1))),
            count_30=Count(Case(When(createdAt__gte=now - timezone.timedelta(days=30), then=1))),
            count_30_refused=Count(Case(When(Q(createdAt__gte=now - timezone.timedelta(days=30)) & Q(is_refused=1), then=1))),
            count_7=Count(Case(When(createdAt__gte=now - timezone.timedelta(days=7), then=1))),
            count_7_refused=Count(Case(When(Q(createdAt__gte=now - timezone.timedelta(days=7)) & Q(is_refused=1), then=1))),
            count_24h=Count(Case(When(createdAt__gte=now - timezone.timedelta(hours=24), then=1))),
            count_24h_refused=Count(Case(When(Q(createdAt__gte=now - timezone.timedelta(hours=24)) & Q(is_refused=1), then=1))),
        )
        if self.unblockedAt:
            since_block_orders = Order.objects.filter(product=self, paymentMethod__type='credit_card', createdAt__gte=self.unblockedAt).values('id', 'createdAt').annotate(
                is_refused=Case(When(status='refused', then=1), default=0, output_field=models.IntegerField()),
            ).aggregate(total=Count('id'), total_refused=Count('is_refused'))

        return {
            'total': round((orders['total_refused'] / orders['total']) * 100, 2) if orders['total'] else 0,
            '90_days': round((orders['count_90_refused'] / orders['count_90']) * 100, 2) if orders['count_90'] else 0,
            '30_days': round((orders['count_30_refused'] / orders['count_30']) * 100, 2) if orders['count_30'] else 0,
            '7_days': round((orders['count_7_refused'] / orders['count_7']) * 100, 2) if orders['count_7'] else 0,
            '24h': round((orders['count_24h_refused'] / orders['count_24h']) * 100, 2) if orders['count_24h'] else 0,
            'sinceBlock': round((since_block_orders['total_refused'] / since_block_orders['total']) * 100, 2) if (self.unblockedAt and since_block_orders['total']) else 0,  # type:ignore
        }

    def get_refused_percentage(self):
        from django.db.models import Case, Count, When

        from gateway.models import Order

        now = timezone.now()
        search_time = min((self.unblockedAt or now), now - timezone.timedelta(days=30))

        orders = Order.objects.filter(product=self, paymentMethod__type='credit_card', createdAt__gte=search_time).values('id', 'createdAt').annotate(
            is_refused=Case(When(status='refused', then=1), default=0, output_field=models.IntegerField()),
        ).aggregate(total=Count('id'), total_refused=Count('is_refused'))

        return round((orders['total_refused'] / orders['total']) * 100, 2) if orders['total'] else 0

    @property
    def metrics(self):
        from product.utils import get_product_metrics_percentages
        return get_product_metrics_percentages(self)

    def has_contentDelivery(self, delivery_type: str) -> bool:
        return self.contentDeliveries.filter(type__in=delivery_type.split(',')).exists()

    def has_internal_contentDelivery(self) -> bool:
        internal_deliveries = ['cakto', 'instagram_pp', 'instagram_cf', 'discord', 'telegram']
        return self.contentDeliveries.filter(type__in=internal_deliveries).exists()

    def get_affiliate_commission_amount(self, user, product):
        split = SplitPay()
        user_fees = split.getUserFees(user.company.externalId).json()
        pix_percentage = user_fees.get('pixPercentage') if user_fees.get('pixPercentage') / 100 != 0 else 1
        pix_percentage_discount = product.price * Decimal(pix_percentage / 100)
        price_with_payment_discounts = (product.price - pix_percentage_discount) - Decimal(user_fees.get('pixFixed'))
        affiliate_commission_amount = price_with_payment_discounts * (product.affiliateCommission / 100)
        return affiliate_commission_amount

    @staticmethod
    def get_showcase_filters() -> models.Q:
        """Returns the filter conditions for that the product appears in the showcase."""
        return Q(
            Q(total_sales__gt=5000) | Q(bypassShowcaseSalesFilter=True),
            affiliate=True,
            affiliateMarketplace=True,
            hideShowcase=False,
            status='active'
        )

class Coproduction(models.Model):
    user = models.ForeignKey('user.User', on_delete=models.CASCADE)
    product = models.ForeignKey('product.Product', on_delete=models.CASCADE, related_name='coproductions')
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    startTime = models.DateTimeField(auto_now_add=True)
    endTime = models.DateTimeField(blank=True, null=True)
    _status = models.CharField(max_length=255, choices=(
        ('accepted', 'Aceito'),
        ('pending', 'Pendente'),
        ('rejected', 'Rejeitado'),
        ('expired', 'Expirado'),
    ), default='pending')
    deletionApprovedByOwner = models.BooleanField(default=False, verbose_name='Deletado pelo dono')
    deletionApprovedByUser = models.BooleanField(default=False, verbose_name='Deletado pelo usuário')
    receiveSalesFromProducer = models.BooleanField(default=True, verbose_name='Receber vendas do produtor')
    receiveSalesFromAffiliate = models.BooleanField(default=True, verbose_name='Receber vendas de afiliados')
    createdAt = models.DateTimeField(auto_now_add=True)
    updatedAt = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'Coprodução'
        verbose_name = 'Coproduções'
        ordering = ['-startTime']

    def __str__(self) -> str:
        return f'{self.user} -> {self.product}'

    @property
    def status(self):
        if self._status != 'accepted':
            return self._status

        now = timezone.now()
        if self.endTime is None:
            return 'active'
        elif now > self.endTime:
            self._status = 'expired'
            self.save()
            return 'expired'
        elif now <= self.endTime:
            return 'active'

    @status.setter
    def status(self, value):
        self._status = value

class Affiliate(models.Model):
    user = models.ForeignKey('user.User', on_delete=models.CASCADE)
    product = models.ForeignKey('product.Product', on_delete=models.CASCADE, related_name='affiliates')
    short_id = ShortUUIDField(length=8, max_length=40, unique=True)  # type:ignore
    commission = models.DecimalField(max_digits=10, decimal_places=2)
    startTime = models.DateTimeField(auto_now_add=True)
    endTime = models.DateTimeField(null=True, blank=True)
    status = models.CharField(max_length=255, choices=AffiliateStatus.choices(), default=AffiliateStatus.WAITING.id)

    cache_prefix = 'affiliate_checkout_'

    @property
    def cache_key(self) -> str:
        return self.cache_prefix + self.short_id

    @staticmethod
    def bulk_invalidate_cache(affiliate_list: Iterable['Affiliate']) -> None:
        cache.delete_many([affiliate.cache_key for affiliate in affiliate_list])

    def invalidate_cache(self) -> None:
        cache.delete(self.cache_key)
        TrackingPixels.bulk_invalidate_cache(self.pixels.all())  # type:ignore

    def save(self, *args, **kwargs) -> None:
        response = super().save(*args, **kwargs)
        self.invalidate_cache()
        return response

    def __str__(self) -> str:
        return f'{self.user} -> {self.product}'

    @classmethod
    def validate_short_id(cls, short_id: str | None = None) -> str | None:
        if not short_id:
            return

        short_id_field = cls._meta.get_field('short_id')

        allowed_alphabet = ''.join(ShortUUID(alphabet=short_id_field.alphabet)._alphabet)

        short_id = re.sub(rf'[^{allowed_alphabet}]', '', short_id or '')

        if short_id_field.length <= len(short_id) <= short_id_field.max_length:
            return short_id

class Link(models.Model):
    shortId = ShortUUIDField(length=7, max_length=40, unique=True, db_index=True)  # type:ignore
    product = models.ForeignKey('product.Product', on_delete=models.CASCADE, related_name='links')
    name = models.CharField(max_length=255)
    showAffiliates = models.BooleanField(default=True)
    url = models.URLField(max_length=2048)
    offer = models.ForeignKey('product.Offer', on_delete=models.CASCADE, null=True, blank=True)
    checkout = models.ForeignKey(
        'product.Checkout',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        help_text='this field allows us to use a custom checkout for this link'
    )
    type = models.CharField(max_length=255, choices=(
        ('sales', 'Página de vendas'),
        ('checkout', 'Checkout'),
    ), default='sales')
    status = models.CharField(max_length=255, choices=(
        ('active', 'Ativo'),
        ('disabled', 'Desabilitado'),
    ), default='active')
    default = models.BooleanField(default=False)

    createdAt = models.DateTimeField(auto_now_add=True)
    updatedAt = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-default', '-createdAt']

    def __str__(self):
        return self.name

    cache_prefix = 'link_redirect_data_'

    @property
    def cache_key(self) -> str:
        return self.cache_prefix + self.shortId

    def invalidate_cache(self) -> None:
        cache.delete(self.cache_key)

    def save(self, *args, **kwargs):
        response = super().save(*args, **kwargs)
        self.invalidate_cache()
        return response

    def delete(self, *args, **kwargs):
        self.invalidate_cache()
        return super().delete(*args, **kwargs)

    @classmethod
    def generate_link_url(cls, offer, checkout_id: str | None = None) -> str:
        # The ending of the checkout url is used to cache the checkout data
        # so when changing the format of the url, make sure to check if the cache is invalidated
        base_link = f'{settings.CHECKOUT_BASE_URL}/{offer.id}'
        if checkout_id:
            base_link += f"_{checkout_id}"
        return base_link

    @classmethod
    def validate_short_id(cls, short_id: str | None = None) -> str | None:
        if not short_id:
            return

        short_id_field = cls._meta.get_field('shortId')

        allowed_alphabet = ''.join(ShortUUID(alphabet=short_id_field.alphabet)._alphabet)

        short_id = re.sub(rf'[^{allowed_alphabet}]', '', short_id or '')

        if short_id_field.length <= len(short_id) <= short_id_field.max_length:
            return short_id

class Offer(models.Model):
    cache_prefix = 'offer_checkout_info_'
    alphabet = '23456789abcdefghijkmnopqrstuvwxyz'

    class Meta:
        verbose_name = 'Oferta'
        verbose_name_plural = 'Ofertas'
        ordering = ['-default', '-createdAt']

    # Basic info
    id = ShortUUIDField(length=7, max_length=40, unique=True, primary_key=True, alphabet=alphabet)  # type:ignore
    externalId = models.CharField(max_length=255, null=True, blank=True)
    name = models.CharField(max_length=255)
    image = models.ImageField(
        upload_to='offers/',
        null=True,
        blank=True,
        verbose_name='Imagem da oferta',
        help_text='Imagem que poderá aparecer em vários locais, como checkout'
        ', opções do produto, etc.',
    )
    price = models.DecimalField(max_digits=10, decimal_places=2)
    type = models.CharField(verbose_name='Tipo de pagamento', max_length=255, choices=ProductType.choices(), default=ProductType.UNIQUE.id)
    status = models.CharField(max_length=40, choices=(
        ('active', 'Ativo'),
        ('disabled', 'Desabilitado'),
        ('deleted', 'Deletado'),
    ), default='active')
    default = models.BooleanField(default=False)
    cost_per_item = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=None,
        null=True,
        blank=True,
        verbose_name='Custo por item',
        help_text='Custo que o produtor tem por item vendido.',
    )
    profit_per_item = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=None,
        null=True,
        blank=True,
        verbose_name='Lucro por item',
        help_text='Lucro que o produtor tem por item vendido.',
    )

    # Relations
    product = models.ForeignKey('product.Product', on_delete=models.CASCADE, related_name='offers')

    # Interval info, for type=unique
    intervalType = models.CharField(verbose_name='Tipo de intervalo', max_length=255, choices=(
        ('week', 'Semana'),
        ('month', 'Mês'),
        ('year', 'Ano'),
        ('lifetime', 'Vitalício'),
    ), default='lifetime')
    interval = models.IntegerField(verbose_name='Quantidade de intervalos', default=1)

    # Recurrence info, for type=subscription
    recurrence_period = models.IntegerField(verbose_name='Período de recorrência (em dias)', default=30)
    quantity_recurrences = models.IntegerField(verbose_name='Quantidade de recorrências', default=-1)
    trial_days = models.IntegerField(verbose_name='Dias de teste', default=0)
    max_retries = models.IntegerField(verbose_name='Quantidade de tentativas de cobrança', default=3)
    retry_interval = models.IntegerField(verbose_name='Intervalo entre tentativas (em dias)', default=1)

    # Checkout
    cdn_checkout_json = models.FileField(upload_to='checkout/', null=True, blank=True)
    checkout_visits = models.IntegerField(default=0, verbose_name='Visitas ao checkout', help_text='Número de visitas ao checkout desta oferta')
    sales_count = models.IntegerField(default=0, verbose_name='Quantidade de Vendas', help_text='Número de vendas desta oferta')

    # Dates
    createdAt = models.DateTimeField(auto_now_add=True)
    updatedAt = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f'{self.name} R${self.price}'

    def checkout(self, checkout_id: str | None = None) -> 'Checkout | None':
        checkout = None

        if checkout_id:
            # If checkout_id is provided, return the custom checkout
            checkout = Checkout.objects.filter(pk=checkout_id).first()

        if not checkout:
            # Try to get the checkout that is applied to this offer
            checkout = Checkout.objects.filter(product=self.product, offers=self).first()

        if not checkout:
            # Get product's default checkout
            checkout = Checkout.objects.filter(product=self.product, default=True).first()

        return checkout

    @property
    def cache_key(self) -> str:
        return self.cache_prefix + self.pk.lower()

    def get_cache_keys(self) -> list[str]:
        checkout_cache_keys = []
        for checkout in self.checkouts.all():
            checkout_cache_keys.extend(checkout.get_cache_keys())
        return [self.cache_key, *checkout_cache_keys]

    def invalidate_cache(self) -> None:
        cache.delete_many(self.get_cache_keys())

    @staticmethod
    def bulk_invalidate_cache(offer_list: Iterable['Offer']) -> None:
        cache_keys = []
        for offer in offer_list:
            cache_keys.extend(offer.get_cache_keys())
        cache.delete_many(cache_keys)

    def save(self, *args, **kwargs) -> None:
        response = super().save(*args, **kwargs)
        self.invalidate_cache()
        return response

    def calculate_access_time(self, current_expiresAt: timezone.datetime | None = None) -> str | timezone.datetime:
        now = timezone.now()

        if current_expiresAt is None:
            current_expiresAt = now
        current_expiresAt = max(current_expiresAt, now)

        if self.type == 'subscription':
            return current_expiresAt + timezone.timedelta(days=self.recurrence_period)

        interval = self.get_interval_days()
        if interval == 'lifetime':
            return str(interval)

        return current_expiresAt + timezone.timedelta(**interval)

    def get_interval_days(self) -> dict[str, int] | Literal['lifetime']:
        if self.intervalType == 'lifetime':
            return 'lifetime'

        if self.intervalType == 'week':
            interval = {'days': self.interval * 7}
        elif self.intervalType == 'month':
            interval = {'days': self.interval * 30}
        else:
            interval = {'days': self.interval * 365}
        return interval

    def calculatedInstallments(self, installments_fees: dict | None = None, total_price: Decimal | None = None) -> list[dict]:
        from product.utils import calculateInstallments
        if installments_fees is None:
            from gateway.utils import get_split_installments_fees
            company_id = self.product.user._company.externalId
            installments_fees = cache.get_or_set(
                f'split_installments_fee_{company_id}',
                lambda: get_split_installments_fees(company_id),
                60
            )
        return calculateInstallments(
            installments_fees,  # type:ignore
            price=total_price or self.price,
            max_installments=self.product.installments,
            offer_type=self.type,
            recurrence_period=self.recurrence_period,
        )

    def to_cdn_checkout_file(self) -> None:
        offer_data, _ = cache.get_or_set(  # type:ignore
            key=self.cache_key,
            default=lambda: self.get_checkout_data(),
            timeout=None,
        )

        self.cdn_checkout_json.delete()

        with NamedTemporaryFile(mode='w+', delete=True) as temp_file:
            json.dump(offer_data, temp_file)
            temp_file.seek(0)  # Move the pointer to the beginning of the file
            self.cdn_checkout_json.save(self.id, File(temp_file), save=True)

    def get_checkout_data(self):
        from product.serializers import OfferCheckoutSerializer

        checkout = self.checkout()
        checkout_id = checkout.pk if checkout else None

        offer_data = OfferCheckoutSerializer(self, context={"checkout_id": checkout_id}).data

        offer_data['tracking_pixels'].pop('id', None)  # type:ignore

        self.remove_duplicated_bumps(offer_data)

        return offer_data, self

    @staticmethod
    def remove_duplicated_bumps(checkout_data):
        offers_ids = [checkout_data['id']]
        bumps_to_remove = []
        for i, bump in enumerate(checkout_data['product']['bumps']):
            if bump['offer']['id'] in offers_ids:
                bumps_to_remove.append(i)
                continue
            offers_ids.append(bump['offer']['id'])
        checkout_data['product']['bumps'] = [bump for i, bump in enumerate(checkout_data['product']['bumps']) if i not in bumps_to_remove]

class Checkout(models.Model):
    product = models.ForeignKey('product.Product', on_delete=models.CASCADE, related_name='checkouts')
    name = models.CharField(max_length=255)
    offers = models.ManyToManyField('product.Offer', related_name='checkouts')
    config = models.JSONField(null=True, blank=True)
    default = models.BooleanField(default=False)
    updatedAt = models.DateTimeField(auto_now=True)
    createdAt = models.DateTimeField(auto_now_add=True)
    visits = models.IntegerField(default=0, help_text='Quantidade de visitas ao checkout')
    sales_count = models.IntegerField(default=0, help_text='Quantidade de vendas realizadas pelo checkout')

    class Meta:
        ordering = ['-default', '-createdAt']

    def __str__(self):
        return self.name

    def get_cache_keys(self) -> list[str]:
        cache_keys = []
        for offer in self.offers.all():
            key = '{}{}_{}'.format(Offer.cache_prefix, offer.id, self.pk)
            cache_keys.append(key.lower())
        return cache_keys

    def invalidate_cache(self):
        cache_keys = self.get_cache_keys()

        for offer in self.offers.all():
            cache_keys.extend(offer.get_cache_keys())

        cache.delete_many(cache_keys)

    def getConfig(self):
        if self.config:
            return self.config
        else:
            checkout = Checkout.objects.filter(product=self.product, default=True).first()
            return checkout.config  # type:ignore

class OrderBump(models.Model):
    id = ShortUUIDField(length=7, max_length=40, unique=True, primary_key=True)  # type:ignore
    product = models.ForeignKey('product.Product', on_delete=models.CASCADE, related_name='bumps')
    referencePrice = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='Preço de referência', blank=True, null=True)
    offer = models.ForeignKey('product.Offer', on_delete=models.CASCADE, null=True, blank=True, related_name='bumps')
    cta = models.CharField(max_length=255, null=True, blank=True, default='Sim, eu aceito essa oferta especial!')
    title = models.CharField(max_length=255, null=True, blank=True, default='Nome do seu produto')
    description = models.TextField(null=True, blank=True, default='Descrição do seu produto')
    position = models.IntegerField(default=0)
    showImage = models.BooleanField(default=False)
    createdAt = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = 'Order bump'
        verbose_name_plural = 'Order bumps'
        ordering = ['position', '-createdAt']

    def __str__(self):
        return f'Order bump de {self.product.name}'

    def save(self, *args, **kwargs) -> None:
        self.position = self.calculate_position()
        super_save = super().save(*args, **kwargs)
        Offer.bulk_invalidate_cache(self.product.offers.all())
        return super_save

    def delete(self, *args, **kwargs) -> tuple[int, dict[str, int]]:
        super_delete = super().delete(*args, **kwargs)
        Offer.bulk_invalidate_cache(self.product.offers.all())
        return super_delete

    def calculate_position(self):
        if self.position:
            return self.position
        current_max_position = self.product.bumps.aggregate(Max('position'))['position__max']
        return (current_max_position or 0) + 1

class TrackingPixels(models.Model):
    product = models.ForeignKey('product.Product', on_delete=models.CASCADE, related_name='pixels')
    affiliate = models.ForeignKey('product.Affiliate', on_delete=models.CASCADE, related_name='pixels', null=True, blank=True)

    # Facebook
    fbPixPurchaseTrigger = models.BooleanField(default=True)
    fbPixConversionValue = models.IntegerField(default=100, validators=[MaxValueValidator(100), MinValueValidator(1)])
    fbBoletoPurchaseTrigger = models.BooleanField(default=True)
    fbBoletoConversionValue = models.IntegerField(default=100, validators=[MaxValueValidator(100), MinValueValidator(1)])
    fbPicpayPurchaseTrigger = models.BooleanField(default=True)
    fbPicpayConversionValue = models.IntegerField(default=100, validators=[MaxValueValidator(100), MinValueValidator(1)])
    fbNubankPurchaseTrigger = models.BooleanField(default=True)
    fbNubankConversionValue = models.IntegerField(default=100, validators=[MaxValueValidator(100), MinValueValidator(1)])

    # Google Analytics
    googleAnalyticsTrackingId = models.CharField(max_length=255, null=True, blank=True)

    # TikTok
    tiktokPixPurchaseTrigger = models.BooleanField(default=True)
    tiktokPixConversionValue = models.IntegerField(default=100, validators=[MaxValueValidator(100), MinValueValidator(1)])
    tiktokBoletoPurchaseTrigger = models.BooleanField(default=True)
    tiktokBoletoConversionValue = models.IntegerField(default=100, validators=[MaxValueValidator(100), MinValueValidator(1)])
    tiktokPicpayPurchaseTrigger = models.BooleanField(default=True)
    tiktokPicpayConversionValue = models.IntegerField(default=100, validators=[MaxValueValidator(100), MinValueValidator(1)])
    tiktokNubankPurchaseTrigger = models.BooleanField(default=True)
    tiktokNubankConversionValue = models.IntegerField(default=100, validators=[MaxValueValidator(100), MinValueValidator(1)])

    # Kwai
    kwaiPixPurchaseTrigger = models.BooleanField(default=True)
    kwaiPixConversionValue = models.IntegerField(default=100, validators=[MaxValueValidator(100), MinValueValidator(1)])
    kwaiBoletoPurchaseTrigger = models.BooleanField(default=True)
    kwaiBoletoConversionValue = models.IntegerField(default=100, validators=[MaxValueValidator(100), MinValueValidator(1)])
    kwaiPicpayPurchaseTrigger = models.BooleanField(default=True)
    kwaiPicpayConversionValue = models.IntegerField(default=100, validators=[MaxValueValidator(100), MinValueValidator(1)])
    kwaiNubankPurchaseTrigger = models.BooleanField(default=True)
    kwaiNubankConversionValue = models.IntegerField(default=100, validators=[MaxValueValidator(100), MinValueValidator(1)])

    createdAt = models.DateTimeField(auto_now_add=True)
    udpatedAt = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f'{self.product}'

    instance_cache_prefix = 'pixels_'
    data_cache_prefix = 'pixels_data_'

    def get_cache_keys(self) -> list[str]:
        # Offers
        cache_keys = []
        for offer in self.product.offers.all():
            cache_keys.extend(offer.get_cache_keys())

        # Tracking Pixel instance
        if self.affiliate:
            cache_keys.append(self.instance_cache_prefix + f'{self.product.short_id}_{self.affiliate.short_id}')
        else:
            cache_keys.append(self.instance_cache_prefix + self.product.short_id)

        # Tracking Pixel data
        cache_keys.append(self.data_cache_prefix + f'{self.product.short_id}_{self.pk}')

        return cache_keys

    def invalidate_cache(self) -> None:
        cache.delete_many(self.get_cache_keys())

    @staticmethod
    def bulk_invalidate_cache(tracking_pixels_list: Iterable['TrackingPixels']) -> None:
        cache_keys: list[str] = []
        for tracking_pixels in tracking_pixels_list:
            cache_keys.extend(tracking_pixels.get_cache_keys())
        cache.delete_many(cache_keys)

    def save(self, *args, **kwargs) -> None:
        super_save = super().save(*args, **kwargs)
        self.invalidate_cache()
        return super_save

class InvalidateTrackingPixelsCacheMixin:
    def invalidate_cache(self) -> None:
        self.tracking_pixels.invalidate_cache()  # type:ignore

    def save(self, *args, **kwargs):
        self.invalidate_cache()
        return super().save(*args, **kwargs)  # type:ignore

    def delete(self, *args, **kwargs):
        self.invalidate_cache()
        return super().delete(*args, **kwargs)  # type:ignore

class FacebookPixel(models.Model, InvalidateTrackingPixelsCacheMixin):
    tracking_pixels = models.ForeignKey('product.TrackingPixels', on_delete=models.CASCADE, related_name='facebook_pixels')
    pixelId = models.CharField(max_length=255)
    error = models.TextField(null=True, blank=True)
    domain = models.CharField(max_length=255, null=True, blank=True)
    apiToken = models.CharField(max_length=255, null=True, blank=True)
    createdAt = models.DateTimeField(auto_now_add=True)

class PixelDomain(models.Model):
    user = models.ForeignKey('user.User', on_delete=models.CASCADE, related_name='pixel_domains')
    domain = models.CharField(max_length=255)
    isVerified = models.BooleanField(default=False)
    lastVerified = models.DateTimeField(null=True, blank=True)

    def invalidate_cache(self) -> None:
        tracking_pixels_list = TrackingPixels.objects.filter(Q(affiliate__user=self.user) | Q(product__user=self.user))
        TrackingPixels.bulk_invalidate_cache(tracking_pixels_list)

    def save(self, *args, **kwargs):
        self.invalidate_cache()
        return super().save(*args, **kwargs)

    def delete(self, *args, **kwargs):
        self.invalidate_cache()
        return super().delete(*args, **kwargs)

class GoogleAdsPixel(models.Model, InvalidateTrackingPixelsCacheMixin):
    tracking_pixels = models.ForeignKey('product.TrackingPixels', on_delete=models.CASCADE, related_name='google_ads_pixels')
    name = models.CharField(max_length=255)
    pixelId = models.CharField(max_length=255)
    conversionLabel = models.CharField(max_length=255)
    checkoutVisitTrigger = models.BooleanField(default=False)
    cardPixApprovalTrigger = models.BooleanField(default=False)
    boletoTrigger = models.BooleanField(default=False)
    createdAt = models.DateTimeField(auto_now_add=True)

class TaboolaPixel(models.Model, InvalidateTrackingPixelsCacheMixin):
    tracking_pixels = models.ForeignKey('product.TrackingPixels', on_delete=models.CASCADE, related_name='taboola_pixels')
    eventName = models.CharField(max_length=255)
    accountId = models.CharField(max_length=255)
    checkoutVisitTrigger = models.BooleanField(default=False)
    cardPixApprovalTrigger = models.BooleanField(default=False)
    boletoTrigger = models.BooleanField(default=False)
    createdAt = models.DateTimeField(auto_now_add=True)

class OutbrainPixel(models.Model, InvalidateTrackingPixelsCacheMixin):
    tracking_pixels = models.ForeignKey('product.TrackingPixels', on_delete=models.CASCADE, related_name='outbrain_pixels')
    eventName = models.CharField(max_length=255)
    pixelId = models.CharField(max_length=255)
    checkoutVisitTrigger = models.BooleanField(default=False)
    cardPixApprovalTrigger = models.BooleanField(default=False)
    boletoTrigger = models.BooleanField(default=False)
    createdAt = models.DateTimeField(auto_now_add=True)

class TikTokPixel(models.Model, InvalidateTrackingPixelsCacheMixin):
    tracking_pixels = models.ForeignKey('product.TrackingPixels', on_delete=models.CASCADE, related_name='tiktok_pixels')
    pixelId = models.CharField(max_length=255)
    apiToken = models.CharField(max_length=255, null=True, blank=True)
    error = models.TextField(null=True, blank=True)
    createdAt = models.DateTimeField(auto_now_add=True)

class KwaiPixel(models.Model, InvalidateTrackingPixelsCacheMixin):
    tracking_pixels = models.ForeignKey('product.TrackingPixels', on_delete=models.CASCADE, related_name='kwai_pixels')
    pixelId = models.CharField(max_length=255)
    createdAt = models.DateTimeField(auto_now_add=True)

class Coupon(models.Model):
    class Meta:
        verbose_name = 'Cupom'
        verbose_name_plural = 'Cupons'
        ordering = ['-createdAt']

    # Basic info
    id = models.UUIDField(primary_key=True, unique=True, default=uuid.uuid4, editable=False)
    code = models.CharField(max_length=30)
    discount = models.DecimalField(max_digits=2, decimal_places=0, validators=[MinValueValidator(1), MaxValueValidator(80)])
    applyOnBumps = models.BooleanField(default=False)
    status = models.CharField(max_length=255, choices=(
        ('active', 'Ativo'),
        ('disabled', 'Desabilitado'),
        ('deleted', 'Deletado'),
    ), default='active')

    # Relations
    products = models.ManyToManyField('product.Product', db_index=True, related_name='cupons')

    # Dates
    startTime = models.DateTimeField()
    endTime = models.DateTimeField(blank=True, null=True, db_index=True)
    createdAt = models.DateTimeField(auto_now_add=True)
    updatedAt = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f'{self.code} - {self.discount}%'

    def validate_coupon(self, offerType: str = 'main') -> bool:
        if self.status != 'active':
            return False

        if offerType != 'main' and not self.applyOnBumps:
            return False

        now = timezone.now()
        return self.startTime <= now and (self.endTime is None or self.endTime >= now)

    def calculate_price(self, price: float | Decimal) -> Decimal:
        return Decimal(price) - (Decimal(price) * (Decimal(self.discount) / 100))

    def get_usage_quantity(self):
        return Order.objects.filter(coupon=self, offer_type='main').count()

    def save(self, *args, **kwargs):
        if self.code:
            self.code = self.code.lower()
        super().save(*args, **kwargs)

class ProductDelivery(models.Model):
    id = models.UUIDField(primary_key=True, unique=True, default=uuid.uuid4, editable=False)
    product = models.ForeignKey('product.Product', on_delete=models.CASCADE, related_name='deliveries')
    contentDelivery = models.ForeignKey('product.ContentDelivery', on_delete=models.CASCADE, related_name='deliveries')

    name = models.CharField(max_length=255)
    status = models.CharField(max_length=255, choices=(
        ('active', 'Ativo'),
        ('disabled', 'Desabilitado'),
        ('waiting_config', 'Aguardando configuração'),
        ('deleted', 'Deletado'),
    ), default='active')
    fields = models.JSONField(default=dict, blank=True)

    # Dates
    createdAt = models.DateTimeField(auto_now_add=True)
    updatedAt = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-createdAt']
        constraints = [
            models.UniqueConstraint(
                fields=['product', 'contentDelivery'],
                condition=Q(status='active'),
                name='unique_active_product_content_delivery',
            )
        ]

    def __str__(self):
        return self.name

class DeliveryAccess(models.Model):
    id = models.UUIDField(primary_key=True, unique=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey('user.User', on_delete=models.CASCADE)
    productDelivery = models.ForeignKey('product.ProductDelivery', on_delete=models.CASCADE, related_name='accesses')
    order = models.ForeignKey('gateway.Order', on_delete=models.CASCADE, related_name='productDeliveryAccesses')

    status = models.CharField(max_length=255, choices=(
        ('waiting_config', 'Aguardando configuração'),
        ('active', 'Ativo'),
        ('canceled', 'Cancelado'),
        ('expired', 'Expirado'),
    ), default='waiting_config')
    fields = models.JSONField(default=dict)

    # Dates
    configuredAt = models.DateTimeField(null=True, blank=True)
    expiresAt = models.DateTimeField(null=True, blank=True)
    createdAt = models.DateTimeField(auto_now_add=True)
    updatedAt = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-createdAt']

    def __str__(self):
        return f'{self.user} - {self.productDelivery}'

    def calculate_access_time(self):
        return self.order.offer.calculate_access_time(current_expiresAt=self.expiresAt)

class InstragramApiHistory(models.Model):
    deliveryAccess = models.ForeignKey('product.DeliveryAccess', on_delete=models.CASCADE, related_name='instagramApiHistories', blank=True, null=True)
    action = models.CharField(max_length=255)
    payload = models.JSONField()
    response = models.JSONField()
    response_status_code = models.IntegerField()
    createdAt = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-createdAt']

    def __str__(self):
        return f'{self.action} - {self.createdAt}'

class ShowcaseEvent(models.Model):
    product = models.ForeignKey(
        Product,
        on_delete=models.CASCADE,
        related_name='showcaseEvents',
        help_text='Produto alvo do evento (ex: produto que foi visualizado, clicado ou afiliado).',
    )

    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='showcaseEvents',
        help_text='Usuário que gerou o evento (ex: usuário que visualizou, clicou ou afiliou-se ao produto).',
    )

    type = models.CharField(
        max_length=40,
        choices=ShowcaseEventType.choices(),
        help_text='Tipo do evento (ex: visualização, clique ou afiliação).',
        db_index=True,
    )

    createdAt = models.DateTimeField(
        auto_now_add=True,
        help_text='Data e hora em que o evento foi gerado.',
        db_index=True,
    )

    class Meta:
        verbose_name = 'Evento de vitrine'
        verbose_name_plural = 'Eventos de vitrine'
        ordering = ['-createdAt']

class CheckoutVisit(models.Model):
    checkout = models.ForeignKey(
        Checkout,
        on_delete=models.SET_NULL,
        help_text='Checkout visitado.',
        blank=True,
        null=True,
    )

    offer = models.ForeignKey(
        Offer,
        on_delete=models.SET_NULL,
        help_text='Oferta relacionada ao checkout visitado.',
        blank=True,
        null=True,
    )

    product = models.ForeignKey(
        Product,
        on_delete=models.SET_NULL,
        help_text='Produto relacionado ao checkout visitado.',
        blank=True,
        null=True,
    )

    createdAt = models.DateTimeField(
        auto_now_add=True,
        help_text='Data e hora da visita ao checkout.',
        db_index=True,
    )

    class Meta:
        verbose_name = 'Visita ao checkout'
        verbose_name_plural = 'Visitas ao checkout'
        ordering = ['-createdAt']
