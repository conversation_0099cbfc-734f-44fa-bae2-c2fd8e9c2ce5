from decimal import ROUND_CEILING, ROUND_FLOOR, Decimal
from enum import Enum
from typing import Sequence
from statistics import mean

from django.db import transaction
from django.db.models import Case, Count, Decimal<PERSON>ield, F, Integer<PERSON>ield, Q, Sum, When, ExpressionWrapper, FloatField
from django.db.models.functions import Coalesce
from django.utils import timezone
from django_rq import job
from django.core.cache import cache

from gateway.models import Order, PaymentStatus
from product.enums import ProductType
from product.models import Affiliate, Checkout, CheckoutVisit, Coupon, Offer, Product
from product.services import ComplianceService
from gateway.utils import get_split_installments_fees


def is_domain_with_cname_record(domain, cname='pixels.cakto.com.br') -> bool:
    # cnames = []
    # try:
    #     answers = dns.resolver.query(domain, 'CNAME')
    #     for rdata in answers:  # type:ignore
    #         cnames.append(rdata.target)
    # except (dns.resolver.NoAnswer, dns.resolver.NXDOMAIN):
    #     return False
    # return cname in cnames
    return True

def calculateInstallments(
    installments_fees: dict,
    price: Decimal,
    max_installments: int,
    offer_type: str,
    recurrence_period: int | None = None,
) -> list[dict[str, Decimal]]:
    installments = []

    if offer_type == ProductType.SUBSCRIPTION.id and recurrence_period:
        recurrence_months = max(recurrence_period // 30, 1)
        max_installments = min(recurrence_months, max_installments)

    for i in range(1, max_installments + 1):
        installmentFeePercentage = Decimal(installments_fees.get(f'creditCard{i}x', 0) / 100)  # type:ignore

        # Add installment rate to price
        new_price = price
        if i > 1:
            new_price = price + (price * installmentFeePercentage)

        # Divide price by installment
        pricePerInstallment = new_price / i

        # Ceil number only on decimals
        if new_price % i == 0:
            pricePerInstallment = pricePerInstallment.quantize(Decimal('0.01'), rounding=ROUND_FLOOR)
        else:
            pricePerInstallment = pricePerInstallment.quantize(Decimal('0.01'), rounding=ROUND_CEILING)

        # Check if it is above minimum installment value
        if not pricePerInstallment >= 5 and i > 1:
            break

        installments.append({
            'installment': i,
            'value': pricePerInstallment
        })

    return list(reversed(installments))

def get_affiliate_cookie_info(afid_shortId: str, product_id: str) -> dict | None:
    affiliate = (
        Affiliate.objects
        .filter(short_id=afid_shortId, product=product_id, status='active')
        .select_related('user___company', 'product')
        .only('short_id', 'product__cookieTime', 'user___company__status')
        .first()
    )

    if not affiliate:
        return None

    return {
        'affiliateShortId': affiliate.short_id,
        'cookieTime': affiliate.product.cookieTime,
        'companyStatus': affiliate.user._company.status,
    }

def get_coupon(coupon_code: str, product_id: str | None = None, offer_id: str | None = None) -> Coupon | None:
    if not coupon_code or (not product_id and not offer_id):
        return None

    coupon = Coupon.objects.filter(Q(products=product_id) | Q(products__offers__pk=offer_id), code__iexact=coupon_code).first()
    if not coupon or not coupon.validate_coupon():
        return None

    return coupon

@job('metrics')
def calculate_product_metrics(product: Product):
    now = timezone.now()
    BACK_30_DAYS = now - timezone.timedelta(days=30)
    BACK_7_DAYS = now - timezone.timedelta(days=7)
    BACK_24H = now - timezone.timedelta(hours=24)
    ONCE_PAID_STATUSES = ['paid', 'refunded', 'chargedback']

    aggregated_data = Order.objects.filter(product=product).only('status', 'createdAt').annotate(
        is_recent_30_days=Case(
            When(createdAt__gte=BACK_30_DAYS, status__in=ONCE_PAID_STATUSES, then=1),
            default=0,
            output_field=IntegerField(),
        ),
        is_recent_7_days=Case(
            When(createdAt__gte=BACK_7_DAYS, status__in=ONCE_PAID_STATUSES, then=1),
            default=0,
            output_field=IntegerField(),
        ),
        is_recent_24h=Case(
            When(createdAt__gte=BACK_24H, status__in=ONCE_PAID_STATUSES, then=1),
            default=0,
            output_field=IntegerField(),
        ),
        is_refunded=Case(
            When(status='refunded', then=1),
            default=0,
            output_field=IntegerField(),
        ),
        is_paid=Case(
            When(status='paid', then=1),
            default=0,
            output_field=IntegerField(),
        ),
        is_chargedback=Case(
            When(status='chargedback', then=1),
            default=0,
            output_field=IntegerField(),
        ),
    ).aggregate(
        total_sales=Sum(Case(When(Q(is_paid=1) | Q(is_refunded=1) | Q(is_chargedback=1), then='amount'), output_field=DecimalField())),
        total_sales_count=Count(Case(When(Q(is_paid=1) | Q(is_refunded=1) | Q(is_chargedback=1), then='id'))),
        total_paid=Sum(Case(When(is_paid=1, then='amount'), output_field=DecimalField())),
        total_paid_count=Sum('is_paid'),
        total_refunded=Sum(Case(When(is_refunded=1, then='amount'), output_field=DecimalField())),
        total_refunded_count=Sum('is_refunded'),
        total_chargeback=Sum(Case(When(is_chargedback=1, then='amount'), output_field=DecimalField())),
        total_chargeback_count=Sum('is_chargedback'),

        sales_30_days=Sum(Case(When(is_recent_30_days=1, then='amount'), output_field=DecimalField())),
        sales_30_days_count=Sum('is_recent_30_days'),
        refunded_30_days=Sum(Case(When(is_recent_30_days=1, is_refunded=1, then='amount'), output_field=DecimalField())),
        refunded_30_days_count=Sum(Case(When(is_recent_30_days=1, then='is_refunded'), output_field=IntegerField())),
        chargeback_30_days=Sum(Case(When(is_recent_30_days=1, is_chargedback=1, then='amount'), output_field=DecimalField())),
        chargeback_30_days_count=Sum(Case(When(is_recent_30_days=1, then='is_chargedback'), output_field=IntegerField())),

        sales_7_days=Sum(Case(When(is_recent_7_days=1, then='amount'), output_field=DecimalField())),
        sales_7_days_count=Sum('is_recent_7_days'),
        refunded_7_days=Sum(Case(When(is_recent_7_days=1, is_refunded=1, then='amount'), output_field=DecimalField())),
        refunded_7_days_count=Sum(Case(When(is_recent_7_days=1, then='is_refunded'), output_field=IntegerField())),
        chargeback_7_days=Sum(Case(When(is_recent_7_days=1, is_chargedback=1, then='amount'), output_field=DecimalField())),
        chargeback_7_days_count=Sum(Case(When(is_recent_7_days=1, then='is_chargedback'), output_field=IntegerField())),

        sales_24h=Sum(Case(When(is_recent_24h=1, then='amount'), output_field=DecimalField())),
        sales_24h_count=Sum('is_recent_24h'),
        refunded_24h=Sum(Case(When(is_recent_24h=1, is_refunded=1, then='amount'), output_field=DecimalField())),
        refunded_24h_count=Sum(Case(When(is_recent_24h=1, then='is_refunded'), output_field=IntegerField())),
        chargeback_24h=Sum(Case(When(is_recent_24h=1, is_chargedback=1, then='amount'), output_field=DecimalField())),
        chargeback_24h_count=Sum(Case(When(is_recent_24h=1, then='is_chargedback'), output_field=IntegerField())),
    )

    # Update product metrics with aggregated data
    for key, value in aggregated_data.items():
        setattr(product, key, value or 0)
    product.save(invalidate_cache=False)

class MetricType(Enum):
    SALES = 'sales'
    REFUNDED = 'refunded'
    CHARGEBACK = 'chargeback'

class TimeRange(Enum):
    TOTAL = 'total'
    DAYS_30 = '30_days'
    DAYS_7 = '7_days'
    HOURS_24 = '24h'

def get_total_percentage_count(product: Product, metric_type: MetricType, time_range: TimeRange):
    amount = getattr(product, f'{metric_type.value}_count_{time_range.value}')
    return round((amount / product.sales_count_total) * 100 if product.sales_count_total > 0 else 0, 2)

def get_total_percentage_value(product: Product, metric_type: MetricType, time_range: TimeRange):
    metric_value = getattr(product, f'{metric_type.value}_{time_range.value}')
    return round((metric_value / product.sales_total) * 100 if product.sales_total > 0 else 0, 2)

def get_period_relative_percentage_count(product: Product, metric_type: MetricType, time_range: TimeRange):
    metric_count = getattr(product, f'{metric_type.value}_count_{time_range.value}')
    period_sales_count = getattr(product, f'sales_count_{time_range.value}')
    return round((metric_count / period_sales_count) * 100 if period_sales_count > 0 else 0, 2)

def get_period_relative_percentage_value(product: Product, metric_type: MetricType, time_range: TimeRange):
    metric_value = getattr(product, f'{metric_type.value}_{time_range.value}')
    period_sales_value = getattr(product, f'sales_{time_range.value}')
    return round((metric_value / period_sales_value) * 100 if period_sales_value > 0 else 0, 2)

def get_product_metrics_percentages(product: Product):
    return {
        'sales_percentage_count_30_days': get_total_percentage_count(product, MetricType.SALES, TimeRange.DAYS_30),
        'sales_percentage_count_7_days': get_total_percentage_count(product, MetricType.SALES, TimeRange.DAYS_7),
        'sales_percentage_count_24h': get_total_percentage_count(product, MetricType.SALES, TimeRange.HOURS_24),
        'sales_percentage_value_30_days': get_total_percentage_value(product, MetricType.SALES, TimeRange.DAYS_30),
        'sales_percentage_value_7_days': get_total_percentage_value(product, MetricType.SALES, TimeRange.DAYS_7),
        'sales_percentage_value_24h': get_total_percentage_value(product, MetricType.SALES, TimeRange.HOURS_24),

        'refunded_percentage_count_total': get_period_relative_percentage_count(product, MetricType.REFUNDED, TimeRange.TOTAL),
        'refunded_percentage_count_30_days': get_period_relative_percentage_count(product, MetricType.REFUNDED, TimeRange.DAYS_30),
        'refunded_percentage_count_7_days': get_period_relative_percentage_count(product, MetricType.REFUNDED, TimeRange.DAYS_7),
        'refunded_percentage_count_24h': get_period_relative_percentage_count(product, MetricType.REFUNDED, TimeRange.HOURS_24),
        'refunded_percentage_value_total': get_period_relative_percentage_value(product, MetricType.REFUNDED, TimeRange.TOTAL),
        'refunded_percentage_value_30_days': get_period_relative_percentage_value(product, MetricType.REFUNDED, TimeRange.DAYS_30),
        'refunded_percentage_value_7_days': get_period_relative_percentage_value(product, MetricType.REFUNDED, TimeRange.DAYS_7),
        'refunded_percentage_value_24h': get_period_relative_percentage_value(product, MetricType.REFUNDED, TimeRange.HOURS_24),

        'chargeback_percentage_count_total': get_period_relative_percentage_count(product, MetricType.CHARGEBACK, TimeRange.TOTAL),
        'chargeback_percentage_count_30_days': get_period_relative_percentage_count(product, MetricType.CHARGEBACK, TimeRange.DAYS_30),
        'chargeback_percentage_count_7_days': get_period_relative_percentage_count(product, MetricType.CHARGEBACK, TimeRange.DAYS_7),
        'chargeback_percentage_count_24h': get_period_relative_percentage_count(product, MetricType.CHARGEBACK, TimeRange.HOURS_24),
        'chargeback_percentage_value_total': get_period_relative_percentage_value(product, MetricType.CHARGEBACK, TimeRange.TOTAL),
        'chargeback_percentage_value_30_days': get_period_relative_percentage_value(product, MetricType.CHARGEBACK, TimeRange.DAYS_30),
        'chargeback_percentage_value_7_days': get_period_relative_percentage_value(product, MetricType.CHARGEBACK, TimeRange.DAYS_7),
        'chargeback_percentage_value_24h': get_period_relative_percentage_value(product, MetricType.CHARGEBACK, TimeRange.HOURS_24),
    }

def send_offer_data_to_compliance_analysis(offers: Sequence[Offer]) -> None:
    compliance_service = ComplianceService()
    compliance_service.analyze_offers(offers)

def get_is_user_affiliate(user, product):
    if user and user.is_authenticated:
        return Affiliate.objects.filter(user=user, product=product).first()
    return None

@job('metrics')
def increase_checkout_visits(
    offer: Offer,
    checkout: Checkout | None = None,
    checkout_id: str | None = None,
    product: Product | None = None,
) -> None:
    if not checkout:
        checkout = offer.checkout(checkout_id=checkout_id)
        if not checkout:
            raise ValueError(f'Checkout not found for offer {offer.id} and checkout_id {checkout_id}')

    product = product or offer.product

    with transaction.atomic():
        CheckoutVisit.objects.create(
            checkout=checkout,
            offer=offer,
            product=product,
        )

        Checkout.objects.filter(pk=checkout.pk).update(visits=F('visits') + 1)
        Offer.objects.filter(pk=offer.pk).update(checkout_visits=F('checkout_visits') + 1)
        Product.objects.filter(pk=product.pk).update(checkout_visits=F('checkout_visits') + 1)

@job('metrics')
def update_checkout_sales_count(
    offer: Offer,
    checkout: Checkout | None = None,
    checkout_id: str | None = None,
) -> None:
    if not checkout:
        checkout = offer.checkout(checkout_id=checkout_id)
        if not checkout:
            raise ValueError(f'Checkout not found for offer {offer.id} and checkout_id {checkout_id}')

    with transaction.atomic():
        # Increase the sales count for the checkout
        checkout.sales_count += Order.objects.filter(checkout=checkout, status=PaymentStatus.PAID.id).count()
        checkout.save(update_fields=['sales_count'])

        # Increase the sales count for the offer
        offer.sales_count += Order.objects.filter(offer=offer, status=PaymentStatus.PAID.id).count()
        offer.save(update_fields=['sales_count'])

        # The product's metrics is updated in the calculate_product_metrics job

def get_payment_fees_from_cache(offer) -> tuple[Decimal, Decimal]:
    """
    Obtém as taxas de pagamento do cache da split fee.

    Args:
        offer: Instância da oferta para obter as taxas da empresa

    Returns:
        Tuple com (taxa_fixa, taxa_percentual)
    """
    company_id = offer.product.user._company.externalId
    cache_key = f'split_installments_fee{company_id}'

    fees_data = cache.get(cache_key)
    if not fees_data:
        fees_data = get_split_installments_fees(company_id)
        cache.set(cache_key, fees_data, 60 * 60 * 24)

    fixed_fee = Decimal(fees_data.get('pix_fixed_fee', '2.49'))
    percentage_fee = Decimal(fees_data.get('pix_percentage_fee', '0.15'))

    return fixed_fee, percentage_fee

def calculate_net_amount_after_coupon_and_fees(
    original_price: Decimal,
    coupon_discount_percentage: Decimal,
    fixed_fee: Decimal = Decimal('2.49'),
    percentage_fee: Decimal = Decimal('0.15')
) -> Decimal:
    """
    Calcula o valor líquido que o produtor receberá após aplicar cupom e taxas.

    Args:
        original_price: Preço original do produto
        coupon_discount_percentage: Percentual de desconto do cupom (ex: 10 para 10%)
        fixed_fee: Taxa fixa cobrada pelo gateway (padrão: R$ 2,49)
        percentage_fee: Taxa percentual cobrada pelo gateway (padrão: 0.15%)

    Returns:
        Valor líquido que o produtor receberá
    """
    original_price = Decimal(original_price)
    coupon_discount_percentage = Decimal(coupon_discount_percentage)
    fixed_fee = Decimal(fixed_fee)
    percentage_fee = Decimal(percentage_fee)

    discounted_price = original_price - (original_price * (coupon_discount_percentage / Decimal('100')))

    percentage_fee_amount = discounted_price * (percentage_fee / Decimal('100'))
    total_fees = fixed_fee + percentage_fee_amount

    net_amount = discounted_price - total_fees

    return net_amount

def validate_coupon_net_amount(
    original_price: Decimal,
    coupon_discount_percentage: Decimal,
    fixed_fee: Decimal = Decimal('2.49'),
    percentage_fee: Decimal = Decimal('0.15'),
    minimum_net_amount: Decimal = Decimal('0.01')
) -> bool:
    """
    Valida se o valor líquido após cupom e taxas é suficiente.

    Args:
        original_price: Preço original do produto
        coupon_discount_percentage: Percentual de desconto do cupom
        fixed_fee: Taxa fixa cobrada pelo gateway
        percentage_fee: Taxa percentual cobrada pelo gateway
        minimum_net_amount: Valor mínimo líquido que o produtor deve receber

    Returns:
        True se o valor líquido é suficiente, False caso contrário
    """
    net_amount = calculate_net_amount_after_coupon_and_fees(
        original_price=original_price,
        coupon_discount_percentage=coupon_discount_percentage,
        fixed_fee=fixed_fee,
        percentage_fee=percentage_fee
    )

    return net_amount >= minimum_net_amount

def validate_coupon_with_dynamic_fees(offer, coupon) -> bool:
    """
    Valida cupom usando as taxas dinâmicas do cache.

    Args:
        offer: Instância da oferta
        coupon: Instância do cupom

    Returns:
        True se o cupom é válido, False caso contrário
    """
    try:
        fixed_fee, percentage_fee = get_payment_fees_from_cache(offer)
        return validate_coupon_net_amount(
            original_price=offer.price,
            coupon_discount_percentage=coupon.discount,
            fixed_fee=fixed_fee,
            percentage_fee=percentage_fee
        )
    except Exception:
        return validate_coupon_net_amount(
            original_price=offer.price,
            coupon_discount_percentage=coupon.discount
        )

def showcase_get_top_averages() -> dict[str, float]:
    """
    Retorna a média dos 20 maiores valores de volume, crescimento e conversão
    entre os produtos da vitrine, com cache de 1 hora.
    """
    TOP_N = 20
    CACHE_KEY = "showcase_products_v2:avg_values"
    CACHE_TIMEOUT = 60 * 60

    cached = cache.get(CACHE_KEY)
    if cached:
        return cached

    base_qs = (
        Product.objects.filter(Product.get_showcase_filters())
        .annotate(
            previous=Coalesce(F("sales_30_days_count") - F("sales_7_days_count"), 0),
            growth=Case(
                When(previous=0, then=0.0),
                default=ExpressionWrapper(
                    (F("sales_7_days_count") - F("previous")) * 1.0 / F("previous"),
                    output_field=FloatField(),
                ),
                output_field=FloatField(),
            ),
            total_visits=Coalesce(Sum("checkouts__visits"), 0),
            conversion=Case(
                When(total_visits=0, then=0.0),
                default=ExpressionWrapper(
                    F("total_sales_count") * 1.0 / F("total_visits"),
                    output_field=FloatField(),
                ),
                output_field=FloatField(),
            ),
        )
    )

    volume_vals = list(
        base_qs.order_by("-sales_7_days_count").values_list("sales_7_days_count", flat=True)[:TOP_N]
    )
    growth_vals = list(base_qs.order_by("-growth").values_list("growth", flat=True)[:TOP_N])
    conv_vals = list(base_qs.order_by("-conversion").values_list("conversion", flat=True)[:TOP_N])

    avg_volume = float(mean(volume_vals)) if volume_vals else 1.0
    avg_growth = float(mean(growth_vals)) if growth_vals else 1.0
    avg_conversion = float(mean(conv_vals)) if conv_vals else 1.0

    data = {
        "avg_volume": avg_volume,
        "avg_growth": avg_growth,
        "avg_conversion": avg_conversion,
    }
    cache.set(CACHE_KEY, data, CACHE_TIMEOUT)
    return data
