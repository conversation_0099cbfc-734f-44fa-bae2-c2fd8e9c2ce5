import hashlib
import re

import requests
from django.utils import timezone
from rest_framework import status

from gateway.models import Order
from product.pixel_strategies.base import PaymentTriggerMapping, PixelEvents, PixelProcessingError

TIKTOK_BASE_URL = "https://business-api.tiktok.com/open_api/v1.3/event/track/"

class TikTokPixelEvents(PixelEvents):
    # doc: https://business-api.tiktok.com/portal/docs?id=1771100865818625

    def pix_gerado(self) -> dict:
        order = self.kwargs['order']

        if self.tracking_pixels.tiktokPixPurchaseTrigger:
            amount = float(order.offer.price) * (self.tracking_pixels.tiktokPixConversionValue / 100)
            return self.purchase_approved(amount=amount)

        data = self.get_data(order=order, amount=order.offer.price, event_name='pix_gerado')
        res = self.send_event([data])

        return self.get_log_data(order, [data], res)

    def boleto_gerado(self) -> dict:
        order = self.kwargs['order']

        if self.tracking_pixels.tiktokBoletoPurchaseTrigger:
            amount = float(order.offer.price) * (self.tracking_pixels.tiktokBoletoConversionValue / 100)
            return self.purchase_approved(amount=amount)

        data = self.get_data(order=order, amount=order.offer.price, event_name='CompletePayment')
        res = self.send_event([data])

        return self.get_log_data(order, [data], res)

    def picpay_gerado(self) -> dict:
        order = self.kwargs['order']

        if self.tracking_pixels.tiktokPicpayPurchaseTrigger:
            amount = float(order.offer.price) * (self.tracking_pixels.tiktokPicpayConversionValue / 100)
            return self.purchase_approved(amount=amount)

        data = self.get_data(order=order, amount=order.offer.price, event_name='CompletePayment')
        res = self.send_event([data])

        return self.get_log_data(order, [data], res)

    def openfinance_nubank_gerado(self) -> dict:
        order = self.kwargs['order']

        if self.tracking_pixels.tiktokNubankPurchaseTrigger:
            amount = float(order.offer.price) * (self.tracking_pixels.tiktokNubankConversionValue / 100)
            return self.purchase_approved(amount=amount)

        data = self.get_data(order=order, amount=order.offer.price, event_name='openfinance_nubank')
        res = self.send_event([data])

        return self.get_log_data(order, [data], res)

    def purchase_approved(self, amount=None) -> dict:
        order = self.kwargs['order']
        paymentMethod = order.paymentMethodType

        event_triggered = self.get_already_triggered_event(order=order)
        if event_triggered:
            return {'event_sent': False, 'reason': f'Event already sent by {event_triggered}'}

        purchase_event_data = self.get_data(order=order, amount=amount or order.offer.price, event_name='CompletePayment')

        additional_event_data = self.get_additional_event_data(amount, order, paymentMethod)

        data = [purchase_event_data]

        if additional_event_data:
            data.append(additional_event_data)  # type:ignore

        res = self.send_event(data)

        return self.get_log_data(order, data, res)

    def initiate_checkout(self) -> dict:
        # Payload helper https://business-api.tiktok.com/portal/docs?id=*********4155649&lang=en
        # Supported Events https://business-api.tiktok.com/portal/docs?id=1771101186666498

        offer = self.kwargs['offer']
        product = self.kwargs['offer'].product

        pixelEventId = self.kwargs['pixelEventId']

        data = {
            'event': 'InitiateCheckout',
            'properties': {
                'value': float(offer.price),
                'currency': 'BRL',
                'contents': [
                    {
                            'content_id': offer.id,
                            'content_type': 'product',
                            'content_name': product.name,
                            'content_category': product.category.name if product.category else '',
                            'price': float(offer.price),
                            'quantity': 1,
                    },
                ],
                'description': product.description,
                'status': product.status,
            },
            'event_id': re.sub(r'[^0-9a-zA-Z]', '', pixelEventId),
            'event_time': int(timezone.now().timestamp()),
            'user': {
                'ip': self.kwargs['client_ip'] or '',
                'user_agent': self.kwargs['client_user_agent'] or '',
            },
        }

        res = self.send_event([data])

        return {
            'event_sent': status.is_success(res.status_code),
            'offer': offer.id,
            'apiToken_used': self.pixel.apiToken,
            'tiktok_response': res.json(),
            'payload_sended': data,
        }

    def get_data(self, order: Order, amount, event_name: str) -> dict:
        # Payload helper https://business-api.tiktok.com/portal/docs?id=*********4155649&lang=en
        # Supported Events https://business-api.tiktok.com/portal/docs?id=1771101186666498

        customer_email = hashlib.sha256(order.customer.email.strip().lower().encode()).hexdigest()
        customer_phone = hashlib.sha256(('55' + order.customer.phone).strip().lower().encode()).hexdigest()
        product = order.product

        data = {
            'event': event_name,
            'properties': {
                'value': float(amount),
                'currency': 'BRL',
                'contents': [
                    {
                        'content_id': order.offer.id,
                        'content_type': 'product',
                        'content_name': product.name,
                        'content_category': product.category.name if product.category else '',
                        'price': float(amount),
                        'quantity': 1,
                    },
                ],
                'description': product.description,
                'status': product.status,
            },
            'event_id': order.id,
            'event_time': int(order.createdAt.timestamp()),
            'page': {
                'url': order.checkoutUrl or '',
            },
            'user': {
                'email': customer_email,
                'phone': customer_phone,
                'ip': order.customer.ip or '',
                'user_agent': order.client_user_agent or '',
            },
        }

        return data

    def send_event(self, data: list[dict]) -> requests.Response:
        payload = {
            'event_source': 'web',
            'event_source_id': self.pixel.pixelId,
            'data': data
        }

        headers = {
            "Content-Type": "application/json",
            "Access-Token": self.pixel.apiToken
        }

        response = requests.post(TIKTOK_BASE_URL, json=payload, headers=headers)

        if not status.is_success(response.status_code):
            self.process_pixel_errors(data, response)

        return response

    def process_pixel_errors(self, data: list[dict], response: requests.Response) -> None:
        product = self.pixel.tracking_pixels.product

        if response.status_code == 401:
            self.set_pixel_error(
                error='Token de API inválido.',
                notification_title=f'Erro no pixel TikTok do {product.name[:30]}',
            )
        elif response.status_code == 404:
            self.set_pixel_error(
                error='Pixel não encontrado. Verifique os dados do pixel.',
                notification_title=f'Erro no pixel TikTok do {product.name[:30]}',
            )

        self.raise_pixel_error(data, response)

    def raise_pixel_error(self, payload_sended: list[dict], response: requests.Response) -> None:
        order = self.kwargs.get('order')
        payment = self.kwargs.get('payment')

        raise PixelProcessingError({
            'product_name': self.pixel.tracking_pixels.product.name,
            'product': self.pixel.tracking_pixels.product.pk,
            'pixel_id': self.pixel.pk,
            'tracking_pixel_id': self.pixel.tracking_pixels.pk,
            'order': order.refId if order else None,
            'payment': payment.pk if payment else None,
            'apiToken_used': self.pixel.apiToken,
            'tiktok__response_status_code': response.status_code,
            'tiktok_response': response.content.decode(),
            'payload_sended': payload_sended,
        })

    def get_log_data(self, order: Order, data: list[dict], res: requests.Response) -> dict:
        return {
            'event_sent': status.is_success(res.status_code),
            'order': order.refId,
            'payment': self.kwargs['payment'].pk,
            'apiToken_used': self.pixel.apiToken,
            'tiktok_response': res.json(),
            'payload_sended': data,
        }

    def get_already_triggered_event(self, order: Order) -> str | bool:
        payment_trigger_mapping: PaymentTriggerMapping = {
            'pix': (self.tracking_pixels.tiktokPixPurchaseTrigger, 'pix_gerado'),
            'openfinance_nubank': (self.tracking_pixels.tiktokNubankPurchaseTrigger, 'openfinance_nubank_gerado'),
            'picpay': (self.tracking_pixels.tiktokPicpayPurchaseTrigger, 'picpay_gerado'),
            'boleto': (self.tracking_pixels.tiktokBoletoPurchaseTrigger, 'boleto_gerado'),
        }

        return super()._get_already_triggered_event(order, payment_trigger_mapping)
