from typing import TypedDict, Union

from apps.services.types import InitiateCheckoutEventKwargs, PaymentEventKwargs
from product.models import FacebookPixel, TikTokPixel, TrackingPixels


class PixelPaymentEventKwargs(PaymentEventKwargs):
    tracking_pixels: TrackingPixels
    pixel: Union[FacebookPixel, TikTokPixel]

class PixelInitiateCheckoutEventKwargs(InitiateCheckoutEventKwargs):
    tracking_pixels: TrackingPixels
    pixel: Union[FacebookPixel, TikTokPixel]

class PaymentTriggerMapping(TypedDict, total=False):
    # paymentMethod: (trigger attr, event_name)
    pix: tuple[bool, str]
    openfinance_nubank: tuple[bool, str]
    picpay: tuple[bool, str]
    boleto: tuple[bool, str]
