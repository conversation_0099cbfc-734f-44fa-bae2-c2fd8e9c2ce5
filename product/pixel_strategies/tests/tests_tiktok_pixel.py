import hashlib
import json
from unittest import mock

import responses

from cakto.tests.base import BaseTestCase
from gateway.models import Payment
from product.models import TikTokPixel, TrackingPixels
from product.pixel_strategies.base import PixelProcessingError
from product.pixel_strategies.tiktok import TIKTOK_BASE_URL, TikTokPixelEvents
from product.pixel_strategies.types import PixelInitiateCheckoutEventKwargs


class TestTikTokPixelEvents(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        cls.create_paymentMethods()

        cls.order = cls.create_order()

        cls.product = cls.order.product

        cls.offer = cls.product.offers.filter(default=True).first()

        cls.payment = mock.Mock(spec=Payment)

        cls.pixel = mock.Mock(
            spec=TikTokPixel,
            apiToken='TokenMock',
            pixelId='PixelIdMock'
        )

        cls.tracking_pixels = cls.get_tracking_pixel_mock(product=cls.product)

        cls.pixel.tracking_pixels = cls.tracking_pixels

        cls.event_class = TikTokPixelEvents(
            order=cls.order,
            payment=cls.payment,
            pixel=cls.pixel,
            tracking_pixels=cls.tracking_pixels,
        )

    @classmethod
    def get_tracking_pixel_mock(
        cls,
        tiktokPixPurchaseTrigger=False,
        tiktokPixConversionValue=100,
        tiktokBoletoPurchaseTrigger=False,
        tiktokBoletoConversionValue=100,
        tiktokPicpayPurchaseTrigger=False,
        tiktokPicpayConversionValue=100,
        tiktokNubankPurchaseTrigger=False,
        tiktokNubankConversionValue=100,
        product=None
    ) -> TrackingPixels:
        tracking_pixel = mock.MagicMock(spec=TrackingPixels)

        tracking_pixel.tiktokBoletoPurchaseTrigger = tiktokBoletoPurchaseTrigger
        tracking_pixel.tiktokBoletoConversionValue = tiktokBoletoConversionValue

        tracking_pixel.tiktokPixPurchaseTrigger = tiktokPixPurchaseTrigger
        tracking_pixel.tiktokPixConversionValue = tiktokPixConversionValue

        tracking_pixel.tiktokPicpayPurchaseTrigger = tiktokPicpayPurchaseTrigger
        tracking_pixel.tiktokPicpayConversionValue = tiktokPicpayConversionValue

        tracking_pixel.tiktokNubankPurchaseTrigger = tiktokNubankPurchaseTrigger
        tracking_pixel.tiktokNubankConversionValue = tiktokNubankConversionValue

        tracking_pixel.product = product

        return tracking_pixel

    @classmethod
    def get_initiate_checkout_kwargs(
        cls,
        pixel: TikTokPixel | None = None,
        tracking_pixels: TrackingPixels | None = None,
    ):

        _pixel = pixel  # type: ignore
        if _pixel is None:
            _pixel: TikTokPixel = cls.pixel

        _tracking_pixels = tracking_pixels  # type: ignore
        if _tracking_pixels is None:
            _tracking_pixels = cls.tracking_pixels

        kwargs: PixelInitiateCheckoutEventKwargs = {
            'offer': cls.offer,
            'client_ip': '*********',
            'client_user_agent': 'Mozilla/5.0',
            'pixelEventId': '**********',
            'checkoutUrl': 'https://www.example.com/checkout',
            'refererUrl': 'https://www.example.com',
            'fbc': 'fbc',
            'fbp': 'fbp',
            'email': '<EMAIL>',
            'phone': '*********',
            'name': 'Teste',
            'tracking_pixels': _tracking_pixels,
            'pixel': _pixel,
        }

        return kwargs

    def test_get_already_triggered_event(self):
        payment_trigger_mapping = {
            'pix': (self.tracking_pixels.tiktokPixPurchaseTrigger, 'pix_gerado'),
            'openfinance_nubank': (self.tracking_pixels.tiktokNubankPurchaseTrigger, 'openfinance_nubank_gerado'),
            'picpay': (self.tracking_pixels.tiktokPicpayPurchaseTrigger, 'picpay_gerado'),
            'boleto': (self.tracking_pixels.tiktokBoletoPurchaseTrigger, 'boleto_gerado'),
        }

        _get_already_triggered_event_mock = mock.patch(
            'product.pixel_strategies.tiktok.PixelEvents._get_already_triggered_event'
        ).start()

        # Call
        self.event_class.get_already_triggered_event(self.order)

        _get_already_triggered_event_mock.assert_called_once_with(
            self.order,
            payment_trigger_mapping
        )

    def test_get_log_data_failed_response(self):
        tiktok_response_mock = self.get_response_mock(status=400)

        data_mock = mock.Mock()

        expected_result = {
            'event_sent': False,
            'order': self.order.refId,
            'payment': self.payment.pk,
            'apiToken_used': self.pixel.apiToken,
            'tiktok_response': tiktok_response_mock.json(),
            'payload_sended': data_mock,
        }

        # Call
        result = self.event_class.get_log_data(
            self.order,
            data_mock,
            tiktok_response_mock
        )

        # Assert
        self.assertEqual(result, expected_result)

    def test_get_log_data(self):
        tiktok_response_mock = self.get_response_mock(status=200)

        data_mock = mock.Mock()

        expected_result = {
            'event_sent': True,
            'order': self.order.refId,
            'payment': self.payment.pk,
            'apiToken_used': self.pixel.apiToken,
            'tiktok_response': tiktok_response_mock.json(),
            'payload_sended': data_mock,
        }

        # Call
        result = self.event_class.get_log_data(
            self.order,
            data_mock,
            tiktok_response_mock
        )

        # Assert
        self.assertEqual(result, expected_result)

    def test_raise_pixel_error(self):
        payload_sended = [{}]

        response_mock = self.get_response_mock(status=400)

        with self.assertRaises(
            PixelProcessingError,
            msg={
                'product_name': self.tracking_pixels.product.name,
                'product': self.tracking_pixels.product.pk,
                'pixel_id': self.pixel.pk,
                'tracking_pixel_id': self.tracking_pixels.pk,
                'order': self.order.refId,
                'payment': self.payment.pk,
                'apiToken_used': self.pixel.apiToken,
                'tiktok__response_status_code': response_mock.status_code,
                'tiktok_response': response_mock.content.decode(),
                'payload_sended': payload_sended,
            }
        ):
            self.event_class.raise_pixel_error(payload_sended, response_mock)

    def test_process_pixel_errors_401(self):
        response_mock = self.get_response_mock(status=401)

        set_pixel_error_mock = mock.patch(
            'product.pixel_strategies.tiktok.TikTokPixelEvents.set_pixel_error'
        ).start()

        mock.patch(
            'product.pixel_strategies.tiktok.TikTokPixelEvents.raise_pixel_error'
        ).start()

        # Call
        self.event_class.process_pixel_errors([], response_mock)

        # Assert
        set_pixel_error_mock.assert_called_once_with(
            error='Token de API inválido.',
            notification_title=f'Erro no pixel TikTok do {self.tracking_pixels.product.name[:30]}',
        )

    def test_process_pixel_errors_404(self):
        response_mock = self.get_response_mock(status=404)

        set_pixel_error_mock = mock.patch(
            'product.pixel_strategies.tiktok.TikTokPixelEvents.set_pixel_error'
        ).start()

        mock.patch(
            'product.pixel_strategies.tiktok.TikTokPixelEvents.raise_pixel_error'
        ).start()

        # Call
        self.event_class.process_pixel_errors([], response_mock)

        # Assert
        set_pixel_error_mock.assert_called_once_with(
            error='Pixel não encontrado. Verifique os dados do pixel.',
            notification_title=f'Erro no pixel TikTok do {self.tracking_pixels.product.name[:30]}',
        )

    def test_process_pixel_errors_unespected_response_status_code(self):
        response_mock = self.get_response_mock(status=500)

        set_pixel_error_mock = mock.patch(
            'product.pixel_strategies.tiktok.TikTokPixelEvents.set_pixel_error'
        ).start()

        raise_pixel_error_mock = mock.patch(
            'product.pixel_strategies.tiktok.TikTokPixelEvents.raise_pixel_error'
        ).start()

        # Call
        self.event_class.process_pixel_errors([], response_mock)

        # Assert
        set_pixel_error_mock.assert_not_called()
        raise_pixel_error_mock.assert_called_once_with([], response_mock)

    @responses.activate
    def test_send_event(self):
        tiktok_endpoint = responses.add(
            method=responses.POST,
            url=TIKTOK_BASE_URL,
            status=200,
        )

        data_mock = [{'testing': 'tiktok'}]

        expected_payload = {
            'event_source': 'web',
            'event_source_id': self.pixel.pixelId,
            'data': data_mock,
        }

        # Call
        result = self.event_class.send_event(data_mock)

        # Assert
        self.assertEqual(result, tiktok_endpoint.calls[0].response)
        self.assertEqual(
            tiktok_endpoint.calls[0].request.body.decode(),  # type: ignore
            json.dumps(expected_payload),
        )

        self.assertEqual(
            tiktok_endpoint.calls[0].request.headers['Access-Token'],
            self.pixel.apiToken,
        )

    @responses.activate
    def test_send_event_with_failed_request(self):
        tiktok_endpoint = responses.add(
            method=responses.POST,
            url=TIKTOK_BASE_URL,
            status=400,
        )

        data_mock = [{'testing': 'tiktok'}]

        process_pixel_errors_mock = mock.patch(
            'product.pixel_strategies.tiktok.TikTokPixelEvents.process_pixel_errors'
        ).start()

        # Call
        self.event_class.send_event(data_mock)

        # Assert
        process_pixel_errors_mock.assert_called_once_with(
            data_mock,
            tiktok_endpoint.calls[0].response,
        )

    def test_get_data(self):
        customer_email = hashlib.sha256(self.order.customer.email.strip().lower().encode()).hexdigest()
        customer_phone = hashlib.sha256(('55' + self.order.customer.phone).strip().lower().encode()).hexdigest()
        product = self.order.product

        event_name = 'pix_gerado'
        amount = 100

        expected_result = {
            'event': event_name,
            'properties': {
                'value': float(amount),
                'currency': 'BRL',
                'contents': [
                    {
                        'content_id': self.order.offer.id,
                        'content_type': 'product',
                        'content_name': product.name,
                        'content_category': product.category.name,
                        'price': float(amount),
                        'quantity': 1,
                    },
                ],
                'description': product.description,
                'status': product.status,
            },
            'event_id': self.order.id,
            'event_time': int(self.order.createdAt.timestamp()),
            'page': {
                'url': self.order.checkoutUrl or '',
            },
            'user': {
                'email': customer_email,
                'phone': customer_phone,
                'ip': self.order.customer.ip or '',
                'user_agent': self.order.client_user_agent or '',
            },
        }

        # Call
        result = self.event_class.get_data(self.order, amount, event_name)

        # Assert
        self.assertEqual(result, expected_result)

    def test_initiate_checkout(self):
        tiktok_response_mock = self.get_response_mock(status=200)

        send_event_mock = mock.patch(
            'product.pixel_strategies.tiktok.TikTokPixelEvents.send_event',
            return_value=tiktok_response_mock
        ).start()

        kwargs = self.get_initiate_checkout_kwargs()

        self.event_class = TikTokPixelEvents(**kwargs)

        expected_data_call = [{
            'event': 'InitiateCheckout',
            'properties': {
                'value': float(self.offer.price),
                'currency': 'BRL',
                'contents': [
                    {
                            'content_id': self.offer.id,
                            'content_type': 'product',
                            'content_name': self.product.name,
                            'content_category': self.product.category.name,
                            'price': float(self.offer.price),
                            'quantity': 1,
                    },
                ],
                'description': self.product.description,
                'status': self.product.status,
            },
            'event_id': kwargs['pixelEventId'],
            'event_time': mock.ANY,
            'user': {
                'ip': kwargs['client_ip'] or '',
                'user_agent': kwargs['client_user_agent'] or '',
            },
        }]

        expected_result = {
            'event_sent': True,
            'offer': self.offer.id,
            'apiToken_used': self.pixel.apiToken,
            'tiktok_response': tiktok_response_mock.json(),
            'payload_sended': expected_data_call,
        }

        # Call
        result = self.event_class.initiate_checkout()  # type: ignore

        # Assert
        send_event_mock.assert_called_once_with(expected_data_call)
        self.assertEqual(
            sorted(result, key=lambda x: x),
            sorted(expected_result, key=lambda x: x),
        )

    def test_initiate_checkout_with_failed_request(self):
        mock.patch(
            'product.pixel_strategies.tiktok.TikTokPixelEvents.send_event',
            return_value=self.get_response_mock(status=400)
        ).start()

        kwargs = self.get_initiate_checkout_kwargs()

        self.event_class = TikTokPixelEvents(**kwargs)

        # Call
        result = self.event_class.initiate_checkout()  # type: ignore

        # Assert
        self.assertEqual(result['event_sent'], False)

    def test_purchase_approved(self):
        get_data_mock = mock.patch(
            'product.pixel_strategies.tiktok.TikTokPixelEvents.get_data'
        ).start()

        get_additional_event_data_mock = mock.patch(
            'product.pixel_strategies.tiktok.TikTokPixelEvents.get_additional_event_data'
        ).start()

        send_event_response_mock = mock.Mock()
        mock.patch(
            'product.pixel_strategies.tiktok.TikTokPixelEvents.send_event',
            return_value=send_event_response_mock
        ).start()

        get_log_data_mock = mock.patch(
            'product.pixel_strategies.tiktok.TikTokPixelEvents.get_log_data'
        ).start()

        # Call
        self.event_class.purchase_approved()

        # Assert
        get_log_data_mock.assert_called_once_with(
            self.order,
            [get_data_mock.return_value, get_additional_event_data_mock.return_value],
            send_event_response_mock
        )

    def test_purchase_approved_with_trigger_enabled(self):
        self.order.paymentMethod = self.pix
        self.order.status = 'paid'
        self.order.save(update_fields=['paymentMethod'])

        self.tracking_pixels.tiktokPixPurchaseTrigger = True

        self.event_class = TikTokPixelEvents(
            order=self.order,
            payment=self.payment,
            pixel=self.pixel,
            tracking_pixels=self.tracking_pixels,
        )

        # Call
        result = self.event_class.purchase_approved()

        # Assert
        self.assertEqual(
            result,
            {
                'event_sent': False,
                'reason': 'Event already sent by pix_gerado'
            }
        )

    def test_openfinance_nubank_gerado(self):
        self.tracking_pixels.tiktokNubankPurchaseTrigger = True

        purchase_approved_mock = mock.patch(
            'product.pixel_strategies.tiktok.TikTokPixelEvents.purchase_approved'
        ).start()

        # Call
        result = self.event_class.openfinance_nubank_gerado()

        # Assert
        self.assertEqual(
            result,
            purchase_approved_mock.return_value
        )

    def test_openfinance_nubank_gerado_with_trigger_disabled(self):
        self.tracking_pixels.tiktokNubankPurchaseTrigger = False

        get_data_mock = mock.patch(
            'product.pixel_strategies.tiktok.TikTokPixelEvents.get_data',
            return_value={'data': 'testing_tiktok'}
        ).start()

        send_event_mock = mock.patch(
            'product.pixel_strategies.tiktok.TikTokPixelEvents.send_event',
        ).start()

        get_log_data_mock = mock.patch(
            'product.pixel_strategies.tiktok.TikTokPixelEvents.get_log_data'
        ).start()

        # Call
        self.event_class.openfinance_nubank_gerado()

        # Assert
        get_log_data_mock.assert_called_once_with(
            self.order,
            [get_data_mock.return_value],
            send_event_mock.return_value,
        )

    def test_picpay_gerado(self):
        self.tracking_pixels.tiktokPicpayPurchaseTrigger = True

        purchase_approved_mock = mock.patch(
            'product.pixel_strategies.tiktok.TikTokPixelEvents.purchase_approved'
        ).start()

        # Call
        result = self.event_class.picpay_gerado()

        # Assert
        self.assertEqual(
            result,
            purchase_approved_mock.return_value
        )

    def test_picpay_gerado_with_trigger_disabled(self):
        self.tracking_pixels.tiktokPicpayPurchaseTrigger = False

        get_data_mock = mock.patch(
            'product.pixel_strategies.tiktok.TikTokPixelEvents.get_data',
            return_value={'data': 'testing_tiktok'}
        ).start()

        send_event_mock = mock.patch(
            'product.pixel_strategies.tiktok.TikTokPixelEvents.send_event',
        ).start()

        get_log_data_mock = mock.patch(
            'product.pixel_strategies.tiktok.TikTokPixelEvents.get_log_data'
        ).start()

        # Call
        self.event_class.picpay_gerado()

        # Assert
        get_log_data_mock.assert_called_once_with(
            self.order,
            [get_data_mock.return_value],
            send_event_mock.return_value,
        )

    def test_boleto_gerado(self):
        self.tracking_pixels.tiktokBoletoPurchaseTrigger = True

        purchase_approved_mock = mock.patch(
            'product.pixel_strategies.tiktok.TikTokPixelEvents.purchase_approved'
        ).start()

        # Call
        result = self.event_class.boleto_gerado()

        # Assert
        self.assertEqual(
            result,
            purchase_approved_mock.return_value
        )

    def test_boleto_gerado_with_trigger_disabled(self):
        self.tracking_pixels.tiktokBoletoPurchaseTrigger = False

        get_data_mock = mock.patch(
            'product.pixel_strategies.tiktok.TikTokPixelEvents.get_data',
            return_value={'data': 'testing_tiktok'}
        ).start()

        send_event_mock = mock.patch(
            'product.pixel_strategies.tiktok.TikTokPixelEvents.send_event',
        ).start()

        get_log_data_mock = mock.patch(
            'product.pixel_strategies.tiktok.TikTokPixelEvents.get_log_data'
        ).start()

        # Call
        self.event_class.boleto_gerado()

        # Assert
        get_log_data_mock.assert_called_once_with(
            self.order,
            [get_data_mock.return_value],
            send_event_mock.return_value,
        )

    def test_pix_gerado(self):
        self.tracking_pixels.tiktokPixPurchaseTrigger = True

        purchase_approved_mock = mock.patch(
            'product.pixel_strategies.tiktok.TikTokPixelEvents.purchase_approved'
        ).start()

        # Call
        result = self.event_class.pix_gerado()

        # Assert
        self.assertEqual(
            result,
            purchase_approved_mock.return_value
        )

    def test_pix_gerado_with_trigger_disabled(self):
        self.tracking_pixels.tiktokPixPurchaseTrigger = False

        get_data_mock = mock.patch(
            'product.pixel_strategies.tiktok.TikTokPixelEvents.get_data',
            return_value={'data': 'testing_tiktok'}
        ).start()

        send_event_mock = mock.patch(
            'product.pixel_strategies.tiktok.TikTokPixelEvents.send_event',
        ).start()

        get_log_data_mock = mock.patch(
            'product.pixel_strategies.tiktok.TikTokPixelEvents.get_log_data'
        ).start()

        # Call
        self.event_class.pix_gerado()

        # Assert
        get_log_data_mock.assert_called_once_with(
            self.order,
            [get_data_mock.return_value],
            send_event_mock.return_value,
        )

    def test_event_class_init(self):
        # Call
        result = TikTokPixelEvents(
            order=self.order,
            payment=self.payment,
            pixel=self.pixel,
            tracking_pixels=self.tracking_pixels,
        )

        # Assert
        self.assertEqual(result.pixel, self.pixel)
        self.assertEqual(result.tracking_pixels, self.tracking_pixels)
