from cakto.enums import LabeledEnum


class ProductType(LabeledEnum):
    # name = (id, description)
    UNIQUE = ('unique', 'Pagamento único')
    SUBSCRIPTION = ('subscription', 'Assinatura recorrente')

class AffiliateStatus(LabeledEnum):
    ACTIVE = ('active', 'Ativo')
    WAITING = ('waiting', 'Analisando')
    REFUSED = ('refused', 'Rejeitado')
    BLOCKED = ('blocked', 'Bloqueado')
    CANCELED = ('canceled', 'Cancelado')

class PaymentMethodStatus(LabeledEnum):
    ACTIVE = ('active', 'Ativo')
    TESTER_USER_ACCESS = ('tester_user_access', 'Ativo para usuários testers')
    DISABLED = ('disabled', 'Desabilitado')

class LanguageOptions(LabeledEnum):
    PT = ('pt', 'Português')
    EN = ('en', 'English')
    ES = ('es', 'Español')

class ShowcaseEventType(LabeledEnum):
    VIEW = ('view', 'Visualização do Produto')
    CLICK = ('click', 'Clique no Produto')
    AFFILIATE = ('affiliate', 'Afiliar-se ao Produto')
