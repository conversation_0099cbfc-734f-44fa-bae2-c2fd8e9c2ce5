# Generated by Django 4.2.5 on 2024-10-11 17:57

from django.core.cache import cache
from django.db import migrations

cache_key = 'payment_method_instances'

def add_googlepay_payment_method(apps, schema_editor):
    PaymentMethod = apps.get_model('product', 'PaymentMethod')
    PaymentMethod.objects.get_or_create(type='googlepay', name='GooglePay', status='active')
    cache.delete(cache_key)

def remove_googlepay_payment_method(apps, schema_editor):
    PaymentMethod = apps.get_model('product', 'PaymentMethod')
    PaymentMethod.objects.filter(type='googlepay').delete()
    cache.delete(cache_key)


class Migration(migrations.Migration):

    dependencies = [
        ('product', '0103_add_applepay_payment_method'),
    ]

    operations = [
        migrations.RunPython(add_googlepay_payment_method, remove_googlepay_payment_method),
    ]
