# Generated by Django 4.2.5 on 2024-09-04 16:38

from django.db import connection, migrations

BATCH_SIZE = 50_000

def populate_product_paymentMethods(apps, schema_editor):
    with connection.cursor() as cursor:
        # Create payment methods if they don't exist
        cursor.execute("""
            INSERT INTO product_paymentmethod (type, name, status)
            VALUES
                ('pix', 'Pix', 'active'),
                ('boleto', 'Boleto', 'active'),
                ('credit_card', 'Cartão de Crédito', 'active')
            ON CONFLICT (type) DO NOTHING;
        """)

        # Primary keys for the payment methods
        paymentMap: dict[str, str] = {
            "pix": "%pix%",
            "boleto": "%ticket%",
            "credit_card": "%credit_card%",
        }

        # Process products in batches
        cursor.execute("SELECT COUNT(*) FROM product_product")
        total_count = cursor.fetchone()[0]  # type:ignore

        for paymentMethod_type, paymentMethodText in paymentMap.items():
            for start in range(0, total_count, BATCH_SIZE):
                cursor.execute(
                    f"""
                    INSERT INTO "product_product_paymentMethods" (product_id, paymentMethod_id)
                        SELECT id, %s
                        FROM product_product
                        WHERE "paymentMethod"
                        LIKE %s
                        LIMIT {BATCH_SIZE} OFFSET {start}
                        ON CONFLICT DO NOTHING""",
                    [paymentMethod_type, paymentMethodText]
                )

def remove_product_paymentMethods(apps, schema_editor):
    with connection.cursor() as cursor:
        cursor.execute("DELETE FROM product_product_paymentMethods")

class Migration(migrations.Migration):

    dependencies = [
        ('product', '0093_paymentmethod_product_paymentmethods'),
    ]

    operations = [
        migrations.RunPython(populate_product_paymentMethods, remove_product_paymentMethods),
    ]
