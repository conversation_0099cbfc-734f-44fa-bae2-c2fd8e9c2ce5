# Generated by Django 4.2.5 on 2025-05-15 19:29

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('product', '0130_product_hideshowcase'),
    ]

    operations = [
        migrations.CreateModel(
            name='ShowcaseEvent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('type', models.CharField(choices=[('view', 'Visualização do Produto'), ('click', 'Clique no Produto'), ('affiliate', 'Afiliar-se ao Produto')], db_index=True, help_text='Tipo do evento (ex: visualização, clique ou afiliação).', max_length=40)),
                ('createdAt', models.DateTimeField(auto_now_add=True, db_index=True, help_text='Data e hora em que o evento foi gerado.')),
                ('product', models.ForeignKey(help_text='Produto alvo do evento (ex: produto que foi visualizado, clicado ou afiliado).', on_delete=django.db.models.deletion.CASCADE, related_name='showcaseEvents', to='product.product')),
                ('user', models.ForeignKey(help_text='Usuário que gerou o evento (ex: usuário que visualizou, clicou ou afiliou-se ao produto).', on_delete=django.db.models.deletion.CASCADE, related_name='showcaseEvents', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Evento de vitrine',
                'verbose_name_plural': 'Eventos de vitrine',
                'ordering': ['-createdAt'],
            },
        ),
    ]
