# Generated by Django 4.2.5 on 2024-02-15 13:13

from django.db import migrations
from shortuuid.django_fields import ShortUUID


def gen_short_id(apps, schema_editor):
    Link = apps.get_model('product', 'Link')
    for link in Link.objects.all():
        link.shortId = ShortUUID().random(length=7)
        link.save(update_fields=['shortId'])

class Migration(migrations.Migration):

    dependencies = [
        ('product', '0050_link_shortid'),
    ]

    operations = [
        migrations.RunPython(gen_short_id, migrations.RunPython.noop),
    ]
