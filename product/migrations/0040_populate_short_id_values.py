# Generated by Django 4.2.5 on 2023-11-28 13:39

from django.db import migrations
import shortuuid.django_fields


def gen_short_id(apps, schema_editor):
    Affiliate = apps.get_model('product', 'Affiliate')
    for row in Affiliate.objects.all():
        row.short_id = shortuuid.django_fields.ShortUUIDField(length=8, max_length=40, prefix='', unique=True)._generate_uuid()  # type: ignore
        row.save(update_fields=["short_id"])


class Migration(migrations.Migration):

    dependencies = [
        ('product', '0039_affiliate_short_id'),
    ]

    operations = [
        migrations.RunPython(gen_short_id, reverse_code=migrations.RunPython.noop),
    ]
