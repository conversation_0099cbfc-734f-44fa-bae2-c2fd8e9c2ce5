# Generated by Django 4.2.5 on 2024-08-16 00:40

from django.db import migrations, models
import django_lifecycle.mixins


class Migration(migrations.Migration):

    dependencies = [
        ('product', '0092_product_emailaccesslink_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='PaymentMethod',
            fields=[
                ('type', models.CharField(help_text='Identificador do método de pagamento, ex.: pix, credit_card...', max_length=255, primary_key=True, serialize=False, unique=True)),
                ('name', models.CharField(help_text='Nome do método de pagamento, ex.: Pix, Cartão de Crédito...', max_length=255)),
                ('status', models.CharField(choices=[('active', 'Ativo'), ('disabled', 'Desabilitado')], default='active', max_length=255)),
            ],
            options={
                'verbose_name': 'Método de pagamento',
                'verbose_name_plural': 'Métodos de pagamento',
            },
            bases=(django_lifecycle.mixins.LifecycleModelMixin, models.Model),
        ),
        migrations.AddField(
            model_name='product',
            name='paymentMethods',
            field=models.ManyToManyField(related_name='products', to='product.paymentmethod'),
        ),
    ]
