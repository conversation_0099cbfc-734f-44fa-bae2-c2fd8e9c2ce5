# Generated by Django 4.2.5 on 2023-09-26 21:03

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('product', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='product',
            name='status',
            field=models.CharField(choices=[('active', 'Ativo'), ('waiting', '<PERSON><PERSON><PERSON><PERSON>'), ('refused', 'Re<PERSON><PERSON><PERSON>'), ('draft', 'Ras<PERSON><PERSON>o'), ('deleted', 'Deletado')], default='draft', max_length=255),
        ),
        migrations.AddField(
            model_name='product',
            name='type',
            field=models.CharField(choices=[('unique', 'Pagamento único'), ('subscription', 'Assinatura recorrente')], default='unique', max_length=255),
        ),
    ]
