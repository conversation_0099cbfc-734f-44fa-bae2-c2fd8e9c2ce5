# Generated by Django 4.2.5 on 2024-09-07 12:43

from django.db import migrations, models
import django_lifecycle.mixins


class Migration(migrations.Migration):

    dependencies = [
        ('product', '0095_product_paymentsorder'),
    ]

    operations = [
        migrations.CreateModel(
            name='ContentDelivery',
            fields=[
                ('type', models.CharField(max_length=255, primary_key=True, serialize=False, unique=True)),
                ('name', models.CharField(max_length=255)),
                ('status', models.CharField(choices=[('active', 'Ativo'), ('disabled', 'Desabilitado')], default='active', max_length=255)),
            ],
            options={
                'verbose_name': 'Entrega de conteúdo',
                'verbose_name_plural': 'Entregas de conteúdo',
                'ordering': ['name'],
            },
            bases=(django_lifecycle.mixins.LifecycleModelMixin, models.Model),
        ),
        migrations.AddField(
            model_name='product',
            name='contentDeliveries',
            field=models.ManyToManyField(related_name='products', to='product.contentdelivery'),
        ),

        # Adds blank=True to additionalInfo field
        migrations.AlterField(
            model_name='product',
            name='additionalInfo',
            field=models.JSONField(blank=True, default=dict),
        ),
    ]
