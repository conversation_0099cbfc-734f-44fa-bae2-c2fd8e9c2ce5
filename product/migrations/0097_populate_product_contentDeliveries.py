# Generated by Django 4.2.5 on 2024-09-07 13:30

from django.db import connection, migrations

BATCH_SIZE = 50_000

def populate_product_contentDeliveries(apps, schema_editor):
    with connection.cursor() as cursor:
        # Create contentDeliveries if they don't exist
        cursor.execute("""
            INSERT INTO "product_contentdelivery" (type, name, status)
            VALUES
                ('cakto', 'Área de membros cakto', 'active'),
                ('files', 'E-book/Arquivos via E-mail', 'active'),
                ('emailAccess', 'Link de acesso via E-mail', 'active'),
                ('external', 'Área de membros externa', 'active'),
                ('disabled', 'Desabilitado', 'active')
            ON CONFLICT (type) DO NOTHING;
        """)

        # Primary keys for the contentDeliveries
        contentDelivery_map: dict[str, str] = {
            "cakto": "%cakto%",
            "files": "%files%",
            "emailAccess": "%emailAccess%",
            "external": "%external%",
            "disabled": "%disabled%",
        }

        # Process products in batches
        cursor.execute("SELECT COUNT(*) FROM product_product")
        total_count = cursor.fetchone()[0]  # type:ignore

        for contentDelivery_type, contentDelivery_text in contentDelivery_map.items():
            for start in range(0, total_count, BATCH_SIZE):
                cursor.execute(
                    f"""
                    INSERT INTO "product_product_contentDeliveries" (product_id, contentdelivery_id)
                        SELECT id, %s
                        FROM product_product
                        WHERE "contentDelivery"
                        LIKE %s
                        LIMIT {BATCH_SIZE} OFFSET {start}
                        ON CONFLICT DO NOTHING""",
                    [contentDelivery_type, contentDelivery_text]
                )

def remove_product_contentDeliveries(apps, schema_editor):
    with connection.cursor() as cursor:
        cursor.execute("""
                       DELETE FROM "product_product_contentDeliveries"
                       """)

class Migration(migrations.Migration):

    dependencies = [
        ('product', '0096_add_product_contentDeliveries'),
    ]

    operations = [
        migrations.RunPython(populate_product_contentDeliveries, remove_product_contentDeliveries),
    ]
