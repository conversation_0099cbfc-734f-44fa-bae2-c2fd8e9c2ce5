# Generated by Django 4.2.5 on 2023-11-14 15:49

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('product', '0025_alter_coproduction_endtime_alter_coproduction_status'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='coproduction',
            name='status',
        ),
        migrations.AddField(
            model_name='coproduction',
            name='accept_status',
            field=models.CharField(choices=[('accepted', 'Aceito'), ('pending', 'Pendente'), ('rejected', 'Rejeitado')], default='pending', max_length=255),
        ),
        migrations.AddField(
            model_name='coproduction',
            name='deletionApprovedByOwner',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='coproduction',
            name='deletionApprovedByUser',
            field=models.BooleanField(default=False),
        ),
        migrations.AlterField(
            model_name='coproduction',
            name='endTime',
            field=models.DateTimeField(),
        ),
    ]
