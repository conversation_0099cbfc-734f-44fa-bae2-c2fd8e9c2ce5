# Generated by Django 4.2.5 on 2024-11-27 17:00

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('product', '0107_productdelivery_unique_active_product_content_delivery'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='offer',
            options={'verbose_name': 'Oferta', 'verbose_name_plural': 'Ofertas'},
        ),
        migrations.AddField(
            model_name='offer',
            name='max_retries',
            field=models.IntegerField(default=3, verbose_name='Quantidade de tentativas de cobrança'),
        ),
        migrations.AddField(
            model_name='offer',
            name='quantity_recurrences',
            field=models.IntegerField(default=-1, verbose_name='Quantidade de recorrências'),
        ),
        migrations.AddField(
            model_name='offer',
            name='recurrence_period',
            field=models.IntegerField(default=30, verbose_name='Período de recorrência (em dias)'),
        ),
        migrations.AddField(
            model_name='offer',
            name='retry_interval',
            field=models.IntegerField(default=1, verbose_name='Intervalo entre tentativas (em dias)'),
        ),
        migrations.AddField(
            model_name='offer',
            name='trial_days',
            field=models.IntegerField(default=0, verbose_name='Dias de teste'),
        ),
        migrations.AlterField(
            model_name='offer',
            name='interval',
            field=models.IntegerField(default=1, verbose_name='Quantidade de intervalos'),
        ),
        migrations.AlterField(
            model_name='offer',
            name='intervalType',
            field=models.CharField(choices=[('week', 'Semana'), ('month', 'Mês'), ('year', 'Ano'), ('lifetime', 'Vitalício')], default='lifetime', max_length=255, verbose_name='Tipo de intervalo'),
        ),
        migrations.AlterField(
            model_name='offer',
            name='type',
            field=models.CharField(choices=[('unique', 'Pagamento único'), ('subscription', 'Assinatura')], default='unique', max_length=255, verbose_name='Tipo de pagamento'),
        ),
    ]
