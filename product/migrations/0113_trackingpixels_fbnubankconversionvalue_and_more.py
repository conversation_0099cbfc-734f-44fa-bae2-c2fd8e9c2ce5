# Generated by Django 4.2.5 on 2025-02-21 12:54

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('product', '0112_merge_0109_alter_offer_options_0111_alter_offer_id'),
    ]

    operations = [
        migrations.AddField(
            model_name='trackingpixels',
            name='fbNubankConversionValue',
            field=models.IntegerField(default=100, validators=[django.core.validators.MaxValueValidator(100), django.core.validators.MinValueValidator(1)]),
        ),
        migrations.AddField(
            model_name='trackingpixels',
            name='fbNubankPurchaseTrigger',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='trackingpixels',
            name='fbPicpayConversionValue',
            field=models.IntegerField(default=100, validators=[django.core.validators.MaxValueValidator(100), django.core.validators.MinValueValidator(1)]),
        ),
        migrations.AddField(
            model_name='trackingpixels',
            name='fbPicpayPurchaseTrigger',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='trackingpixels',
            name='kwaiNubankConversionValue',
            field=models.IntegerField(default=100, validators=[django.core.validators.MaxValueValidator(100), django.core.validators.MinValueValidator(1)]),
        ),
        migrations.AddField(
            model_name='trackingpixels',
            name='kwaiNubankPurchaseTrigger',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='trackingpixels',
            name='kwaiPicpayConversionValue',
            field=models.IntegerField(default=100, validators=[django.core.validators.MaxValueValidator(100), django.core.validators.MinValueValidator(1)]),
        ),
        migrations.AddField(
            model_name='trackingpixels',
            name='kwaiPicpayPurchaseTrigger',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='trackingpixels',
            name='tiktokNubankConversionValue',
            field=models.IntegerField(default=100, validators=[django.core.validators.MaxValueValidator(100), django.core.validators.MinValueValidator(1)]),
        ),
        migrations.AddField(
            model_name='trackingpixels',
            name='tiktokNubankPurchaseTrigger',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='trackingpixels',
            name='tiktokPicpayConversionValue',
            field=models.IntegerField(default=100, validators=[django.core.validators.MaxValueValidator(100), django.core.validators.MinValueValidator(1)]),
        ),
        migrations.AddField(
            model_name='trackingpixels',
            name='tiktokPicpayPurchaseTrigger',
            field=models.BooleanField(default=True),
        ),
    ]
