# Generated by Django 4.2.5 on 2023-10-18 20:41

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('product', '0014_alter_product_id'),
    ]

    operations = [
        migrations.CreateModel(
            name='Domain',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('url', models.URLField()),
            ],
        ),
        migrations.AddField(
            model_name='product',
            name='confirmEmail',
            field=models.BooleanField(default=False, verbose_name='Pedir para confirmar e-mail'),
        ),
        migrations.AddField(
            model_name='product',
            name='invoiceDescription',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Descriçao da fatura'),
        ),
        migrations.AddField(
            model_name='product',
            name='paymentMethod',
            field=models.CharField(choices=[('credit_card_ticket_pix', 'Cart<PERSON> de crédito, boleto e pix'), ('credit_card_ticket', 'Cartão de crédito e boleto'), ('credit_card_pix', 'Cartão de crédito e pix'), ('credit_card', 'Apenas cartão de crédito')], default='credit_card_boleto_pix', max_length=255),
        ),
        migrations.AddField(
            model_name='product',
            name='purchaseOnPix',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='product',
            name='purchaseOnTicket',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='product',
            name='ticketExpiration',
            field=models.IntegerField(default=7, verbose_name='Vencimento do boleto'),
        ),
        migrations.AddField(
            model_name='product',
            name='twoCardPayment',
            field=models.BooleanField(default=False, verbose_name='Aceitar dois cartões'),
        ),
        migrations.AddField(
            model_name='product',
            name='upsellPage',
            field=models.URLField(blank=True, null=True),
        ),
        migrations.CreateModel(
            name='Pixel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('pixelId', models.CharField(max_length=255)),
                ('createdAt', models.DateTimeField(auto_now_add=True)),
                ('domain', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='product.domain')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='pixels', to='product.product')),
            ],
        ),
    ]
