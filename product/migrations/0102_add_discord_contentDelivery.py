# Generated by Django 4.2.5 on 2024-10-05 19:10

from django.core.cache import cache
from django.db import migrations

cache_key = 'contentDeliveries'

def adds_discord_contentDelivery(apps, schema_editor):
    ContentDelivery = apps.get_model('product', 'ContentDelivery')
    ContentDelivery.objects.get_or_create(type='discord', name='Discord')
    cache.delete(cache_key)

def remove_discord_contentDelivery(apps, schema_editor):
    ContentDelivery = apps.get_model('product', 'ContentDelivery')
    ContentDelivery.objects.filter(type='discord').delete()
    cache.delete(cache_key)


class Migration(migrations.Migration):

    dependencies = [
        ('product', '0101_alter_productdelivery_status'),
    ]

    operations = [
        migrations.RunPython(adds_discord_contentDelivery, remove_discord_contentDelivery),
    ]
