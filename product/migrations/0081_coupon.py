# Generated by Django 4.2.5 on 2024-07-04 14:19

import uuid

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('product', '0080_merge_20240603_1733'),
    ]

    operations = [
        migrations.CreateModel(
            name='Coupon',
            fields=[
                ('id', models.UUIDField(db_index=True, default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('code', models.CharField(max_length=30)),
                ('discount', models.DecimalField(decimal_places=0, max_digits=2, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(80)])),
                ('applyOnBumps', models.BooleanField(default=False)),
                ('startTime', models.DateTimeField()),
                ('endTime', models.DateTimeField(blank=True, db_index=True, null=True)),
                ('createdAt', models.DateTimeField(auto_now_add=True)),
                ('updatedAt', models.DateTimeField(auto_now=True)),
                ('products', models.ManyToManyField(db_index=True, related_name='cupons', to='product.product')),
            ],
            options={
                'verbose_name': 'Cupom',
                'verbose_name_plural': 'Cupons',
                'ordering': ['-createdAt'],
            },
        ),
    ]
