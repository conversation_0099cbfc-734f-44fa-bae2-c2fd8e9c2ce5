# Generated by Django 4.2.5 on 2024-09-26 18:01

from django.core.cache import cache
from django.db import migrations

cache_key = 'contentDeliveries'

def adds_telegram_contentDelivery(apps, schema_editor):
    ContentDelivery = apps.get_model('product', 'ContentDelivery')
    ContentDelivery.objects.get_or_create(type='telegram', name='Telegram')
    cache.delete(cache_key)

def remove_telegram_contentDelivery(apps, schema_editor):
    ContentDelivery = apps.get_model('product', 'ContentDelivery')
    ContentDelivery.objects.filter(type='telegram').delete()
    cache.delete(cache_key)


class Migration(migrations.Migration):

    dependencies = [
        ('product', '0099_deliveryaccess_productdelivery_instragramapihistory_and_more'),
    ]

    operations = [
        migrations.RunPython(adds_telegram_contentDelivery, remove_telegram_contentDelivery),
    ]
