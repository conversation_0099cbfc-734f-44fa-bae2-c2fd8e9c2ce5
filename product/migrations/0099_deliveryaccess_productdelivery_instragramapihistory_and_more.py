# Generated by Django 4.2.5 on 2024-09-20 20:30

import uuid

import django.db.models.deletion
from django.conf import settings
from django.core.cache import cache
from django.db import migrations, models

cache_key = 'contentDeliveries'

def adds_instagram_deliveries(apps, schema_editor):
    ContentDelivery = apps.get_model('product', 'ContentDelivery')

    ContentDelivery.objects.get_or_create(type='instagram_pp', name='Instagram Perfil Privado')
    ContentDelivery.objects.get_or_create(type='instagram_cf', name='Instagram Close Friends')
    cache.delete(cache_key)

def remove_instagram_deliveries(apps, schema_editor):
    ContentDelivery = apps.get_model('product', 'ContentDelivery')

    ContentDelivery.objects.filter(type='instagram_pp').delete()
    ContentDelivery.objects.filter(type='instagram_cf').delete()
    cache.delete(cache_key)

class Migration(migrations.Migration):

    dependencies = [
        ('gateway', '0058_split_unique_split'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('product', '0098_alter_product_defaultpaymentmethod'),
    ]

    operations = [
        migrations.CreateModel(
            name='DeliveryAccess',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, unique=True)),
                ('status', models.CharField(choices=[('waiting_config', 'Aguardando configuração'), ('active', 'Ativo'),
                 ('canceled', 'Cancelado'), ('expired', 'Expirado')], default='waiting_config', max_length=255)),
                ('fields', models.JSONField(default=dict)),
                ('configuredAt', models.DateTimeField(blank=True, null=True)),
                ('expiresAt', models.DateTimeField(blank=True, null=True)),
                ('createdAt', models.DateTimeField(auto_now_add=True)),
                ('updatedAt', models.DateTimeField(auto_now=True)),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='productDeliveryAccesses', to='gateway.order')),
            ],
            options={
                'ordering': ['-createdAt'],
            },
        ),
        migrations.CreateModel(
            name='ProductDelivery',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, unique=True)),
                ('name', models.CharField(max_length=255)),
                ('status', models.CharField(choices=[('active', 'Ativo'), ('disabled', 'Desabilitado')], default='active', max_length=255)),
                ('fields', models.JSONField(blank=True, default=dict)),
                ('createdAt', models.DateTimeField(auto_now_add=True)),
                ('updatedAt', models.DateTimeField(auto_now=True)),
                ('contentDelivery', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='deliveries', to='product.contentdelivery')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='deliveries', to='product.product')),
            ],
            options={
                'ordering': ['-createdAt'],
            },
        ),
        migrations.CreateModel(
            name='InstragramApiHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.CharField(max_length=255)),
                ('payload', models.JSONField()),
                ('response', models.JSONField()),
                ('response_status_code', models.IntegerField()),
                ('createdAt', models.DateTimeField(auto_now_add=True)),
                ('deliveryAccess', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='instagramApiHistories', to='product.deliveryaccess')),
            ],
            options={
                'ordering': ['-createdAt'],
            },
        ),
        migrations.AddField(
            model_name='deliveryaccess',
            name='productDelivery',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='accesses', to='product.productdelivery'),
        ),
        migrations.AddField(
            model_name='deliveryaccess',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.RunPython(adds_instagram_deliveries, remove_instagram_deliveries),
    ]
