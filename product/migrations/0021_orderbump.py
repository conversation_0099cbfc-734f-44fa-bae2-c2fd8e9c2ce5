# Generated by Django 4.2.5 on 2023-11-08 20:04

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('product', '0020_product_bumps_product_upsell_alter_offer_id'),
    ]

    operations = [
        migrations.CreateModel(
            name='OrderBump',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('cta', models.CharField(blank=True, default='Sim, eu aceito essa oferta especial!', max_length=255, null=True)),
                ('title', models.CharField(blank=True, default='Nome do seu produto', max_length=255, null=True)),
                ('description', models.TextField(blank=True, default='Descrição do seu produto', null=True)),
                ('showImage', models.BooleanField(default=False)),
                ('createdAt', models.DateTimeField(auto_now_add=True)),
                ('offer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='product.offer')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='orderBumps', to='product.product')),
            ],
        ),
    ]
