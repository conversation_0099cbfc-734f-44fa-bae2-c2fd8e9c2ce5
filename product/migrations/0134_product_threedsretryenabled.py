# Generated by Django 4.2.5 on 2025-06-04 21:55

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('product', '0133_checkout_sales_count_offer_checkout_visits_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='product',
            name='threeDsRetryEnabled',
            field=models.BooleanField(default=True, help_text='Caso o pagamento falhe no cartão de crédito, será tentado novamente via 3DS', verbose_name='Habilitar retentativas de pagamento 3DS'),
        ),
    ]
