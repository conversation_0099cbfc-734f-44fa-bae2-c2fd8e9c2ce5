# Generated by Django 4.2.5 on 2025-05-27 17:44

from decimal import Decimal

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('product', '0132_product_bypassshowcasesalesfilter'),
    ]

    operations = [
        migrations.AddField(
            model_name='checkout',
            name='sales_count',
            field=models.IntegerField(default=0, help_text='Quantidade de vendas realizadas pelo checkout'),
        ),
        migrations.AddField(
            model_name='offer',
            name='checkout_visits',
            field=models.IntegerField(default=0, help_text='Número de visitas ao checkout desta oferta', verbose_name='Visitas ao checkout'),
        ),
        migrations.AddField(
            model_name='offer',
            name='sales_count',
            field=models.IntegerField(default=0, help_text='Número de vendas desta oferta', verbose_name='Quantidade de Vendas'),
        ),
        migrations.AddField(
            model_name='product',
            name='checkout_visits',
            field=models.IntegerField(default=0, help_text='Número de visitas ao checkout do produto', verbose_name='Visitas ao checkout'),
        ),
        migrations.AlterField(
            model_name='checkout',
            name='visits',
            field=models.IntegerField(default=0, help_text='Quantidade de visitas ao checkout'),
        ),
        migrations.AddField(
            model_name='product',
            name='total_paid',
            field=models.DecimalField(decimal_places=2, default=Decimal('0'), max_digits=12),
        ),
        migrations.AddField(
            model_name='product',
            name='total_paid_count',
            field=models.IntegerField(default=0),
        ),
        migrations.CreateModel(
            name='CheckoutVisit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('createdAt', models.DateTimeField(auto_now_add=True, db_index=True, help_text='Data e hora da visita ao checkout.')),
                ('checkout', models.ForeignKey(blank=True, help_text='Checkout visitado.', null=True, on_delete=django.db.models.deletion.SET_NULL, to='product.checkout')),
                ('offer', models.ForeignKey(blank=True, help_text='Oferta relacionada ao checkout visitado.', null=True, on_delete=django.db.models.deletion.SET_NULL, to='product.offer')),
                ('product', models.ForeignKey(blank=True, help_text='Produto relacionado ao checkout visitado.', null=True, on_delete=django.db.models.deletion.SET_NULL, to='product.product')),
            ],
            options={
                'verbose_name': 'Visita ao checkout',
                'verbose_name_plural': 'Visitas ao checkout',
                'ordering': ['-createdAt'],
            },
        ),
    ]
