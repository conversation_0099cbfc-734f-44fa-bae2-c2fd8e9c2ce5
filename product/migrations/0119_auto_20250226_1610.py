# Generated by Django 4.2.5 on 2025-02-26 19:10

from django.core.cache import cache
from django.db import migrations

cache_key = 'payment_methods'

def update_payment_method_names(apps, schema_editor):
    PaymentMethod = apps.get_model('product', 'PaymentMethod')
    PaymentMethod.objects.filter(type='googlepay').update(name='Google Pay')
    PaymentMethod.objects.filter(type='applepay').update(name='Apple Pay')
    cache.delete(cache_key)

def revert_payment_method_names(apps, schema_editor):
    PaymentMethod = apps.get_model('product', 'PaymentMethod')
    PaymentMethod.objects.filter(type='googlepay').update(name='GooglePay')
    PaymentMethod.objects.filter(type='applepay').update(name='ApplePay')
    cache.delete(cache_key)

class Migration(migrations.Migration):

    dependencies = [
        ('product', '0118_merge_20250221_1049'),
    ]

    operations = [
        migrations.RunPython(update_payment_method_names, revert_payment_method_names),
    ]
