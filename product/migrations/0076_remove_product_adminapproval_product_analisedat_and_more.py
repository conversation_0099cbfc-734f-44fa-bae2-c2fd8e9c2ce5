# Generated by Django 4.2.5 on 2024-05-27 15:36

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('product', '0075_alter_product_paymentmethod'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='product',
            name='adminApproval',
        ),
        migrations.AddField(
            model_name='product',
            name='analisedAt',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='product',
            name='blockReason',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='product',
            name='statusAdmin',
            field=models.CharField(choices=[('approved', 'Aprovado'), ('pending', 'Pendente'), ('block_analisis', 'Análise de bloqueio'), ('stand_by', 'Aguardando Requisitos')], default='stand_by', max_length=40),
        ),
    ]
