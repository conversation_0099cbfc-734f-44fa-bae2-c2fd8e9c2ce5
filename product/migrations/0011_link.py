# Generated by Django 4.2.5 on 2023-10-02 23:21

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('product', '0010_alter_product_category'),
    ]

    operations = [
        migrations.CreateModel(
            name='Link',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('url', models.URLField()),
                ('type', models.CharField(choices=[('sales', 'Página de vendas'), ('checkout', 'Checkout')], default='sales', max_length=255)),
                ('status', models.CharField(choices=[('active', 'Ativo'), ('disabled', 'Desabilitado')], default='active', max_length=255)),
                ('createdAt', models.DateTimeField(auto_now_add=True)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='links', to='product.product')),
            ],
        ),
    ]
