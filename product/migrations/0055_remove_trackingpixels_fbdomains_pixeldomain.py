# Generated by Django 4.2.5 on 2024-02-27 00:01

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('product', '0054_kwaipixel_createdat_tiktokpixel_createdat_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='trackingpixels',
            name='fbDomains',
        ),
        migrations.CreateModel(
            name='PixelDomain',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('domain', models.CharField(max_length=255)),
                ('isVerified', models.BooleanField(default=False)),
                ('lastVerified', models.DateTimeField(blank=True, null=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='pixel_domains', to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
