# Generated by Django 4.2.5 on 2024-08-15 21:49

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('product', '0091_tiktokpixel_apitoken_tiktokpixel_error'),
    ]

    operations = [
        migrations.AddField(
            model_name='product',
            name='emailAccessLink',
            field=models.URLField(blank=True, max_length=2048, null=True),
        ),
        migrations.AlterField(
            model_name='product',
            name='contentDelivery',
            field=models.CharField(choices=[('cakto', 'Área de membros cakto'), ('files', 'E-book/Arquivos via E-mail'), ('emailAccess', 'Link de acesso via E-mail'), ('external', 'Área de membros externa'), ('disabled', 'Desabilitado')], default='cakto', max_length=255),
        ),
    ]
