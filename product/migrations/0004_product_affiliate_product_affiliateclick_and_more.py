# Generated by Django 4.2.5 on 2023-09-28 22:16

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('product', '0003_alter_category_id'),
    ]

    operations = [
        migrations.AddField(
            model_name='product',
            name='affiliate',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='product',
            name='affiliateClick',
            field=models.CharField(choices=[('first', 'Primeiro clique'), ('last', 'Último clique')], default='last', max_length=255),
        ),
        migrations.AddField(
            model_name='product',
            name='affiliateCommission',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='product',
            name='affiliateContact',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='product',
            name='affiliateDescription',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='product',
            name='affiliateMarketplace',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='product',
            name='affiliateRequest',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='product',
            name='affiliateSupportEmail',
            field=models.EmailField(blank=True, max_length=254, null=True),
        ),
        migrations.AddField(
            model_name='product',
            name='cookieTime',
            field=models.IntegerField(default=30),
        ),
        migrations.AddField(
            model_name='product',
            name='installments',
            field=models.IntegerField(default=12),
        ),
    ]
