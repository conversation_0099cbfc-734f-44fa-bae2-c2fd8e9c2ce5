# Generated by Django 4.2.5 on 2024-01-17 20:09

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('product', '0047_offer_status'),
    ]

    operations = [
        migrations.CreateModel(
            name='FacebookPixel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('pixelId', models.CharField(max_length=255)),
                ('domain', models.URLField(blank=True, max_length=255, null=True)),
                ('apiToken', models.CharField(blank=True, max_length=255, null=True)),
                ('createdAt', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='GoogleAdsPixel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('pixelId', models.CharField(max_length=255)),
                ('conversionLabel', models.CharField(max_length=255)),
                ('checkoutVisitTrigger', models.BooleanField(default=False)),
                ('cardPixApprovalTrigger', models.BooleanField(default=False)),
                ('boletoTrigger', models.BooleanField(default=False)),
                ('createdAt', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='KwaiPixel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('pixelId', models.CharField(max_length=255)),
            ],
        ),
        migrations.CreateModel(
            name='OutbrainPixel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('eventName', models.CharField(max_length=255)),
                ('pixelId', models.CharField(max_length=255)),
                ('checkoutVisitTrigger', models.BooleanField(default=False)),
                ('cardPixApprovalTrigger', models.BooleanField(default=False)),
                ('boletoTrigger', models.BooleanField(default=False)),
                ('createdAt', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='TaboolaPixel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('eventName', models.CharField(max_length=255)),
                ('accountId', models.CharField(max_length=255)),
                ('checkoutVisitTrigger', models.BooleanField(default=False)),
                ('cardPixApprovalTrigger', models.BooleanField(default=False)),
                ('boletoTrigger', models.BooleanField(default=False)),
                ('createdAt', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='TikTokPixel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('pixelId', models.CharField(max_length=255)),
            ],
        ),
        migrations.CreateModel(
            name='TrackingPixels',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('fbPixPurchaseTrigger', models.BooleanField(default=True)),
                ('fbPixConversionValue', models.IntegerField(default=100, validators=[django.core.validators.MaxValueValidator(100), django.core.validators.MinValueValidator(1)])),
                ('fbBoletoPurchaseTrigger', models.BooleanField(default=True)),
                ('fbBoletoConversionValue', models.IntegerField(default=100, validators=[django.core.validators.MaxValueValidator(100), django.core.validators.MinValueValidator(1)])),
                ('googleAnalyticsTrackingId', models.CharField(blank=True, max_length=255, null=True)),
                ('tiktokPixPurchaseTrigger', models.BooleanField(default=True)),
                ('tiktokPixConversionValue', models.IntegerField(default=100, validators=[django.core.validators.MaxValueValidator(100), django.core.validators.MinValueValidator(1)])),
                ('tiktokBoletoPurchaseTrigger', models.BooleanField(default=True)),
                ('tiktokBoletoConversionValue', models.IntegerField(default=100, validators=[django.core.validators.MaxValueValidator(100), django.core.validators.MinValueValidator(1)])),
                ('kwaiPixPurchaseTrigger', models.BooleanField(default=True)),
                ('kwaiPixConversionValue', models.IntegerField(default=100, validators=[django.core.validators.MaxValueValidator(100), django.core.validators.MinValueValidator(1)])),
                ('kwaiBoletoPurchaseTrigger', models.BooleanField(default=True)),
                ('kwaiBoletoConversionValue', models.IntegerField(default=100, validators=[django.core.validators.MaxValueValidator(100), django.core.validators.MinValueValidator(1)])),
                ('createdAt', models.DateTimeField(auto_now_add=True)),
                ('affiliate', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='pixel', to='product.affiliate')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='pixel', to='product.product')),
            ],
        ),
        migrations.RemoveField(
            model_name='pixel',
            name='domain',
        ),
        migrations.RemoveField(
            model_name='pixel',
            name='product',
        ),
        migrations.DeleteModel(
            name='Domain',
        ),
        migrations.DeleteModel(
            name='Pixel',
        ),
        migrations.AddField(
            model_name='tiktokpixel',
            name='tracking_pixels',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tiktok_pixels', to='product.trackingpixels'),
        ),
        migrations.AddField(
            model_name='taboolapixel',
            name='tracking_pixels',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='taboola_pixels', to='product.trackingpixels'),
        ),
        migrations.AddField(
            model_name='outbrainpixel',
            name='tracking_pixels',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='outbrain_pixels', to='product.trackingpixels'),
        ),
        migrations.AddField(
            model_name='kwaipixel',
            name='tracking_pixels',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='kwai_pixels', to='product.trackingpixels'),
        ),
        migrations.AddField(
            model_name='googleadspixel',
            name='tracking_pixels',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='google_ads_pixels', to='product.trackingpixels'),
        ),
        migrations.AddField(
            model_name='facebookpixel',
            name='tracking_pixels',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='facebook_pixels', to='product.trackingpixels'),
        ),
    ]
