# Generated by Django 4.2.5 on 2023-10-31 13:41

from django.db import migrations, models
import shortuuid.django_fields


class Migration(migrations.Migration):

    dependencies = [
        ('product', '0019_alter_offer_options_link_default'),
    ]

    operations = [
        migrations.AddField(
            model_name='product',
            name='bumps',
            field=models.ManyToManyField(blank=True, related_name='bumps', to='product.offer', verbose_name='Bumps'),
        ),
        migrations.AddField(
            model_name='product',
            name='upsell',
            field=models.BooleanField(default=False),
        ),
        migrations.AlterField(
            model_name='offer',
            name='id',
            field=shortuuid.django_fields.ShortUUIDField(alphabet=None, length=7, max_length=40, prefix='', primary_key=True, serialize=False, unique=True),
        ),
    ]
