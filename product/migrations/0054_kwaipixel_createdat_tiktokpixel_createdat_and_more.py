# Generated by Django 4.2.5 on 2024-02-24 02:33

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('product', '0053_trackingpixels_fbdomains'),
    ]

    operations = [
        migrations.AddField(
            model_name='kwaipixel',
            name='createdAt',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='tiktokpixel',
            name='createdAt',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='facebookpixel',
            name='domain',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
    ]
