# Generated by Django 4.2.5 on 2024-04-07 20:02

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('product', '0062_alter_product_contentdelivery'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='coproduction',
            options={'ordering': ['-startTime'], 'verbose_name': 'Coproduções'},
        ),
        migrations.AddField(
            model_name='coproduction',
            name='createdAt',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='coproduction',
            name='updatedAt',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AlterField(
            model_name='coproduction',
            name='deletionApprovedByOwner',
            field=models.BooleanField(default=False, verbose_name='Deletado pelo dono'),
        ),
        migrations.Alter<PERSON>ield(
            model_name='coproduction',
            name='deletionApprovedByUser',
            field=models.BooleanField(default=False, verbose_name='Deletado pelo usuário'),
        ),
        migrations.AlterField(
            model_name='coproduction',
            name='receiveSalesFromAffiliate',
            field=models.BooleanField(default=True, verbose_name='Receber vendas de afiliados'),
        ),
        migrations.AlterField(
            model_name='coproduction',
            name='receiveSalesFromProducer',
            field=models.BooleanField(default=True, verbose_name='Receber vendas do produtor'),
        ),
    ]
