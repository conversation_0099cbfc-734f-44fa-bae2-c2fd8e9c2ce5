# Generated by Django 4.2.5 on 2024-10-11 00:23

from django.core.cache import cache
from django.db import migrations

cache_key = 'payment_method_instances'

def add_applepay_payment_method(apps, schema_editor):
    PaymentMethod = apps.get_model('product', 'PaymentMethod')
    PaymentMethod.objects.get_or_create(type='applepay', name='ApplePay', status='active')
    cache.delete(cache_key)

def remove_applepay_payment_method(apps, schema_editor):
    PaymentMethod = apps.get_model('product', 'PaymentMethod')
    PaymentMethod.objects.filter(type='applepay').delete()
    cache.delete(cache_key)


class Migration(migrations.Migration):

    dependencies = [
        ('product', '0102_add_discord_contentDelivery'),
    ]

    operations = [
        migrations.RunPython(add_applepay_payment_method, remove_applepay_payment_method),
    ]
