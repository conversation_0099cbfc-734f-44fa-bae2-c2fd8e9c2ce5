from django.db import migrations, connection

def remove_field(apps, schema_editor):
    with connection.cursor() as cursor:
        # For PostgreSQL
        if connection.vendor == 'postgresql':
            try:
                cursor.execute("SELECT column_name FROM information_schema.columns WHERE table_name='product_product' AND column_name='membersV2Id'")
                if cursor.fetchone():
                    cursor.execute("""
                                   ALTER TABLE product_product DROP COLUMN "membersV2Id"
                                   """)
            except Exception as e:
                print(e)

        # For SQLite
        if connection.vendor == 'sqlite':
            try:
                cursor.execute("PRAGMA table_info(product_product)")
                columns = [row[1] for row in cursor.fetchall()]
                if 'membersV2Id' in columns:
                    cursor.execute("ALTER TABLE product_product DROP COLUMN membersV2Id")
            except Exception as e:
                print(e)

class Migration(migrations.Migration):

    dependencies = [
        ('product', '0108_coupon_status'),
    ]

    operations = [
        migrations.RunPython(remove_field, migrations.RunPython.noop),
    ]
