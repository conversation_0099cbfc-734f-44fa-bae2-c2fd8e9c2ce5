import re
from typing import Iterable

from django.utils import timezone
from facebook_business.adobjects.adspixel import AdsPixel
from facebook_business.api import FacebookAdsApi
from rest_flex_fields import FlexFieldsModelSerializer
from rest_framework import serializers
from rest_framework.exceptions import ValidationError

from cakto.serializers import CustomFlexFieldsSerializer
from gateway.models import PaymentMethodType, Subscription
from product.enums import ProductType, ShowcaseEventType
from product.models import (Affiliate, Category, Checkout, ContentDelivery, Coproduction, Coupon, DeliveryAccess,
                            FacebookPixel, GoogleAdsPixel, KwaiPixel, Link, Offer, OrderBump, OutbrainPixel,
                            PaymentMethod, PixelDomain, Product, ProductDelivery, ShowcaseEvent, TaboolaPixel,
                            TikTokPixel, TrackingPixels)
from product.utils import get_is_user_affiliate
from user.serializers import UserAdminFlexSerializer, UserAdminSerializer, UserAffiliateSerializer


class CategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = Category
        fields = [
            'id',
            'name',
        ]
        ordering = ['name']

class CategoryAdminFlexSerializer(CustomFlexFieldsSerializer):
    class Meta:
        model = Category
        fields = '__all__'

class OfferProductRetrieveSerializer(serializers.ModelSerializer):
    price = serializers.FloatField()
    default = serializers.BooleanField(read_only=True)
    checkout = serializers.SerializerMethodField()

    class Meta:
        model = Offer
        fields = [
            'id',
            'name',
            'image',
            'price',
            'default',
            'status',
            'cost_per_item',
            'profit_per_item',

            'type',

            'intervalType',
            'interval',

            'recurrence_period',
            'quantity_recurrences',
            'trial_days',
            'max_retries',
            'retry_interval',

            'checkout',
        ]
        ordering = ['-createdAt']

    def get_checkout(self, offer: Offer):
        checkout = offer.checkout()
        return checkout.id if checkout else None

class OfferSerializer(serializers.ModelSerializer):
    price = serializers.FloatField()
    default = serializers.BooleanField(read_only=True)
    product = serializers.CharField(source='product.id', read_only=True)

    class Meta:
        model = Offer
        fields = [
            'id',
            'name',
            'image',
            'price',
            'default',
            'product',
            'status',
            'cost_per_item',
            'profit_per_item',

            'type',

            'intervalType',
            'interval',

            'recurrence_period',
            'quantity_recurrences',
            'trial_days',
            'max_retries',
            'retry_interval',
        ]
        ordering = ['-createdAt']
        read_only_fields = ['id', 'default']
        extra_kwargs = {
            'image': {'required': False},
            'cost_per_item': {'required': False},
            'profit_per_item': {'required': False},
            'recurrence_period': {'required': False},
            'quantity_recurrences': {'required': False},
            'trial_days': {'required': False},
            'max_retries': {'required': False},
            'retry_interval': {'required': False},
            'type': {'required': False},
        }

    def to_internal_value(self, data):
        data = data.copy()
        data.pop('product', None)
        return super().to_internal_value(data)

    def create(self, validated_data):
        product_id = self.initial_data.get('product')
        if product_id:
            validated_data['product_id'] = product_id
        return super().create(validated_data)

    def update(self, instance, validated_data):
        if 'image' in validated_data and instance.image:
            instance.image.delete(save=False)

        return super().update(instance, validated_data)

    def validate_price(self, value):
        if value < 5:
            raise ValidationError('O preço mínimo do produto é de R$ 5,00')
        return value

    def validate_interval(self, value):
        if value < 1:
            raise ValidationError('O intervalo de tempo deve ser maior que 0.')
        return value

    def validate_status(self, value):
        if value not in ['active', 'disabled']:
            raise ValidationError('O status da oferta deve ser "active" ou "disabled".')
        return value

    def validate_trial_days(self, value):
        if value and (value < 0 or value > 365):
            raise serializers.ValidationError('O número de dias de teste deve estar entre 0 e 365.')
        return value

    def validate_quantity_recurrences(self, value):
        is_int = isinstance(value, int)
        is_higher_than_zero = value > 0
        is_lower_than_100 = value <= 100

        if is_int and ((is_higher_than_zero and is_lower_than_100) or value == -1):
            return value

        raise serializers.ValidationError('O número de recorrências deve estar entre 1 e 100 ou ser igual a -1 para recorrência infinita.')

    def validate(self, data):
        self._validate_product_paymentMethods(data)
        return data

    def _validate_product_paymentMethods(self, data):
        product: Product = self.context.get('product')  # type:ignore
        assert product is not None, 'product should be in the context'

        if self.instance:
            error_sufix = 'para alterar oferta para o tipo assinatura.'
        else:
            error_sufix = 'para adicionar uma oferta do tipo assinatura.'

        if data.get('type') == ProductType.SUBSCRIPTION.id:
            for method in product.paymentMethods.all():
                if method.type not in Subscription.get_supported_paymentMethodTypes():
                    paymentType: PaymentMethodType = PaymentMethodType.get_member(method.type)  # type:ignore
                    raise ValidationError({
                        'product':
                        f'Remova o método de pagamento {paymentType.description}'
                        f' do {product.name[:30]} {error_sufix}'
                    })

class OfferOwnerFlexSerializer(CustomFlexFieldsSerializer):
    class Meta:
        model = Offer
        fields = '__all__'
        expandable_fields = {
            'product': 'product.serializers.ProductOwnerFlexSerializer',
        }

class OfferPublicSerializer(serializers.ModelSerializer):
    price = serializers.FloatField()

    class Meta:
        model = Offer
        fields = [
            'id',
            'name',
            'price',
        ]
        ordering = ['-createdAt']

class OfferAdminSerialzier(serializers.ModelSerializer):
    class Meta:
        model = Offer
        fields = [
            'id',
            'name',
            'image',
            'price',
            'default',
            'status',

            'type',

            'intervalType',
            'interval',

            'recurrence_period',
            'quantity_recurrences',
            'trial_days',
            'max_retries',
            'retry_interval',

            'createdAt',
            'updatedAt',
        ]
        ordering = ['-createdAt']

class OrderBumpSerializer(serializers.ModelSerializer):
    offer = serializers.SerializerMethodField()
    image = serializers.SerializerMethodField()
    installments = serializers.IntegerField(source='offer.product.installments', read_only=True)
    referencePrice = serializers.FloatField(required=False, allow_null=True)

    class Meta:
        model = OrderBump
        fields = [
            'id',
            'product',
            'installments',
            'referencePrice',
            'offer',
            'cta',
            'title',
            'description',
            'position',
            'image',
            'showImage',
        ]
        read_only_fields = ['product', 'offer']

    def get_offer(self, order_bump):
        data = OfferSerializer(order_bump.offer).data
        data.update(product=order_bump.offer.product.pk)  # type:ignore
        return data

    def get_image(self, order_bump):
        if order_bump.offer.image:
            return order_bump.offer.image.url
        elif order_bump.offer.product.image:
            return order_bump.offer.product.image.url
        return None

class OrderBumpValidatePaymentMethodsMixin:
    def validate(self, data):
        product: Product = data.get('product') or self.instance.product  # type:ignore
        offer: Offer | None = data.get('offer')

        if offer and offer.type == ProductType.SUBSCRIPTION.id:
            for method in product.paymentMethods.all():
                if method.type not in Subscription.get_supported_paymentMethodTypes():
                    paymentType: PaymentMethodType = PaymentMethodType.get_member(method.type)  # type:ignore
                    raise ValidationError({
                        'product':
                        f'Remova o método de pagamento {paymentType.description}'
                        f' do {product.name[:30]} para adicionar o '
                        'orderbump do tipo assinatura.'
                    })

        return data

class OrderBumpUpdateSerializer(OrderBumpValidatePaymentMethodsMixin, serializers.ModelSerializer):
    referencePrice = serializers.FloatField(required=False, allow_null=True)

    class Meta:
        model = OrderBump
        fields = [
            'id',
            'product',
            'referencePrice',
            'offer',
            'cta',
            'title',
            'description',
            'position',
            'showImage',
        ]
        read_only_fields = ['product', 'id']

class OrderBumpCreateSerializer(OrderBumpValidatePaymentMethodsMixin, serializers.ModelSerializer):
    referencePrice = serializers.FloatField(required=False, allow_null=True)

    class Meta:
        model = OrderBump
        fields = [
            'id',
            'product',
            'referencePrice',
            'offer',
            'cta',
            'title',
            'description',
            'position',
            'showImage',
        ]

class FacebookPixelSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(required=False)

    class Meta:
        model = FacebookPixel
        fields = [
            'id',
            'pixelId',
            'error',
            'domain',
            'apiToken',
            'createdAt',
        ]

    def validate(self, data):
        # Fake event test
        if data.get('apiToken'):
            FacebookAdsApi.init(access_token=data.get('apiToken'))
            try:
                response = AdsPixel(data.get('pixelId')).create_event(params=self.get_fake_data())
                assert response.export_all_data()['events_received'] == 1  # type:ignore
            except Exception:
                raise ValidationError('Erro ao criar evento no Facebook. Verifique os dados do pixel e tente novamente.')

        # Removes error from pixel
        if data.get('id'):
            facebook_pixel = FacebookPixel.objects.filter(id=data['id']).first()
            if facebook_pixel:
                facebook_pixel.error = None
                facebook_pixel.save()
        data.pop('error', None)

        return data

    def get_fake_data(self):
        return {'data': [{
            'action_source': 'website',
            'event_name': 'teste_integração_cakto_facebook',
            'event_id': '1234',
            'event_time': int(timezone.now().timestamp()),
            'user_data': {
                'client_ip_address': '*******',
                'client_user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            },
            'custom_data': {
                'content_type': 'product',
                'contents': [{'id': '123123', 'quantity': 1}],
                'currency': 'BRL',
                'value': float(12),
            }
        }]}

class PixelDomainSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(required=False)
    user = serializers.CharField(source='user.email', read_only=True)

    class Meta:
        model = PixelDomain
        fields = [
            'id',
            'user',
            'domain',
            'isVerified',
            'lastVerified',
        ]

class GoogleAdsPixelSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(required=False)

    class Meta:
        model = GoogleAdsPixel
        fields = [
            'id',
            'name',
            'pixelId',
            'conversionLabel',
            'checkoutVisitTrigger',
            'cardPixApprovalTrigger',
            'boletoTrigger',
            'createdAt',
        ]

class TaboolaPixelSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(required=False)

    class Meta:
        model = TaboolaPixel
        fields = [
            'id',
            'eventName',
            'accountId',
            'checkoutVisitTrigger',
            'cardPixApprovalTrigger',
            'boletoTrigger',
            'createdAt',
        ]

class OutbrainPixelSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(required=False)

    class Meta:
        model = OutbrainPixel
        fields = [
            'id',
            'eventName',
            'pixelId',
            'checkoutVisitTrigger',
            'cardPixApprovalTrigger',
            'boletoTrigger',
            'createdAt',
        ]

class TikTokPixelSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(required=False)

    class Meta:
        model = TikTokPixel
        fields = [
            'id',
            'pixelId',
            'apiToken',
            'error',
            'createdAt',
        ]

class KwaiPixelSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(required=False)

    class Meta:
        model = KwaiPixel
        fields = [
            'id',
            'pixelId',
            'createdAt',
        ]

class FacebookPixelPublicSerializer(serializers.ModelSerializer):
    class Meta:
        model = FacebookPixel
        fields = [
            'pixelId',
        ]

class PixelDomainPublicSerializer(serializers.ModelSerializer):
    class Meta:
        model = PixelDomain
        fields = [
            'domain',
            'isVerified',
            'lastVerified',
        ]

class GoogleAdsPixelPublicSerializer(serializers.ModelSerializer):
    class Meta:
        model = GoogleAdsPixel
        fields = [
            'name',
            'pixelId',
            'conversionLabel',
            'checkoutVisitTrigger',
            'cardPixApprovalTrigger',
            'boletoTrigger',
        ]

class TaboolaPixelPublicSerializer(serializers.ModelSerializer):

    class Meta:
        model = TaboolaPixel
        fields = [
            'eventName',
            'accountId',
            'checkoutVisitTrigger',
            'cardPixApprovalTrigger',
            'boletoTrigger',
        ]

class OutbrainPixelPublicSerializer(serializers.ModelSerializer):
    class Meta:
        model = OutbrainPixel
        fields = [
            'eventName',
            'pixelId',
            'checkoutVisitTrigger',
            'cardPixApprovalTrigger',
            'boletoTrigger',
        ]

class TikTokPixelPublicSerializer(serializers.ModelSerializer):
    class Meta:
        model = TikTokPixel
        fields = [
            'pixelId',
        ]

class KwaiPixelPublicSerializer(serializers.ModelSerializer):

    class Meta:
        model = KwaiPixel
        fields = [
            'pixelId',
        ]

class TrackingPixelPublicSerializer(serializers.ModelSerializer):
    facebook_pixels = FacebookPixelPublicSerializer(many=True)
    google_ads_pixels = GoogleAdsPixelPublicSerializer(many=True)
    taboola_pixels = TaboolaPixelPublicSerializer(many=True)
    outbrain_pixels = OutbrainPixelPublicSerializer(many=True)
    tiktok_pixels = TikTokPixelPublicSerializer(many=True)
    kwai_pixels = KwaiPixelPublicSerializer(many=True)
    pixel_domains = serializers.SerializerMethodField()

    class Meta:
        model = TrackingPixels
        fields = [
            'fbPixPurchaseTrigger',
            'fbPixConversionValue',
            'fbBoletoPurchaseTrigger',
            'fbBoletoConversionValue',
            'fbPicpayPurchaseTrigger',
            'fbPicpayConversionValue',
            'fbNubankPurchaseTrigger',
            'fbNubankConversionValue',

            'googleAnalyticsTrackingId',

            'tiktokPixPurchaseTrigger',
            'tiktokPixConversionValue',
            'tiktokBoletoPurchaseTrigger',
            'tiktokBoletoConversionValue',
            'tiktokPicpayPurchaseTrigger',
            'tiktokPicpayConversionValue',
            'tiktokNubankPurchaseTrigger',
            'tiktokNubankConversionValue',

            'kwaiPixPurchaseTrigger',
            'kwaiPixConversionValue',
            'kwaiBoletoPurchaseTrigger',
            'kwaiBoletoConversionValue',
            'kwaiPicpayPurchaseTrigger',
            'kwaiPicpayConversionValue',
            'kwaiNubankPurchaseTrigger',
            'kwaiNubankConversionValue',

            'facebook_pixels',
            'google_ads_pixels',
            'taboola_pixels',
            'outbrain_pixels',
            'tiktok_pixels',
            'kwai_pixels',
            'pixel_domains'
        ]
        ordering = ['-createdAt']

    def get_pixel_domains(self, tracking_pixels):
        user = tracking_pixels.affiliate.user if tracking_pixels.affiliate else tracking_pixels.product.user
        return PixelDomainPublicSerializer(user.pixel_domains, many=True).data

class TrackingPixelSerializer(serializers.ModelSerializer):
    facebook_pixels = FacebookPixelSerializer(many=True)
    google_ads_pixels = GoogleAdsPixelSerializer(many=True)
    taboola_pixels = TaboolaPixelSerializer(many=True)
    outbrain_pixels = OutbrainPixelSerializer(many=True)
    tiktok_pixels = TikTokPixelSerializer(many=True)
    kwai_pixels = KwaiPixelSerializer(many=True)
    pixel_domains = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = TrackingPixels
        fields = [
            'id',
            'fbPixPurchaseTrigger',
            'fbPixConversionValue',
            'fbBoletoPurchaseTrigger',
            'fbBoletoConversionValue',
            'fbPicpayPurchaseTrigger',
            'fbPicpayConversionValue',
            'fbNubankPurchaseTrigger',
            'fbNubankConversionValue',

            'googleAnalyticsTrackingId',

            'tiktokPixPurchaseTrigger',
            'tiktokPixConversionValue',
            'tiktokBoletoPurchaseTrigger',
            'tiktokBoletoConversionValue',
            'tiktokPicpayPurchaseTrigger',
            'tiktokPicpayConversionValue',
            'tiktokNubankPurchaseTrigger',
            'tiktokNubankConversionValue',

            'kwaiPixPurchaseTrigger',
            'kwaiPixConversionValue',
            'kwaiBoletoPurchaseTrigger',
            'kwaiBoletoConversionValue',
            'kwaiPicpayPurchaseTrigger',
            'kwaiPicpayConversionValue',
            'kwaiNubankPurchaseTrigger',
            'kwaiNubankConversionValue',

            'facebook_pixels',
            'google_ads_pixels',
            'taboola_pixels',
            'outbrain_pixels',
            'tiktok_pixels',
            'kwai_pixels',
            'pixel_domains'
        ]
        ordering = ['-createdAt']

    pixel_classes = {
        'facebook_pixels': FacebookPixelSerializer,
        'google_ads_pixels': GoogleAdsPixelSerializer,
        'taboola_pixels': TaboolaPixelSerializer,
        'outbrain_pixels': OutbrainPixelSerializer,
        'tiktok_pixels': TikTokPixelSerializer,
        'kwai_pixels': KwaiPixelSerializer,
    }

    def update(self, tracking_pixels, validated_data):
        # Update or create pixels
        for field, serializer_class in self.pixel_classes.items():
            pixel_model = serializer_class.Meta.model  # type:ignore

            # Get all pixels that are related to the tracking pixel
            pixels_with_same_tracking = list(pixel_model.objects.filter(tracking_pixels=tracking_pixels).values_list('id', flat=True))
            pixels_pool = []

            if field in validated_data.keys():
                # Pixels list from the request data
                pixels_list = validated_data.pop(field)
                for pixel_data in pixels_list:
                    # If the pixel has an id, update it if are related to the tracking pixel
                    if 'id' in pixel_data.keys():
                        pixel_instance = pixel_model.objects.filter(id=pixel_data['id'], tracking_pixels=tracking_pixels).first()
                        if pixel_instance is not None:
                            serializer = serializer_class(instance=pixel_instance, data=pixel_data)
                            if serializer.is_valid(raise_exception=True):  # type:ignore
                                serializer.save()  # type:ignore
                                pixels_pool.append(pixel_instance.id)
                    else:
                        pixel_model.objects.create(tracking_pixels=tracking_pixels, **pixel_data)

            # Remove pixels that are not in the request data
            for pixel in pixels_with_same_tracking:
                if pixel not in pixels_pool:
                    pixel_model.objects.filter(id=pixel).delete()

        # Update or create pixel_domains
        for domain in validated_data.pop('pixel_domains', []):
            if 'id' in domain.keys():
                fb_domain = PixelDomain.objects.filter(id=domain['id'], user=tracking_pixels.product.user).first()
                if fb_domain is not None:
                    domain['domain'] = 'pixels.' + domain['domain'].replace('https://', '').replace('http://', '').replace('www.', '').replace('pixels.', '')
                    fb_domain.domain = domain['domain']
                    fb_domain.save()
            else:
                domain['domain'] = 'pixels.' + domain['domain'].replace('https://', '').replace('http://', '').replace('www.', '').replace('pixels.', '')
                PixelDomain.objects.create(domain=domain['domain'], user=tracking_pixels.product.user)

        return super().update(tracking_pixels, validated_data)

    def get_pixel_domains(self, tracking_pixels):
        user = tracking_pixels.affiliate.user if tracking_pixels.affiliate else tracking_pixels.product.user
        return PixelDomainSerializer(user.pixel_domains, many=True).data

class ProductCreateUpdateSerializerMixin:
    def validate_paymentMethods(self, value):
        user = getattr(self.context.get('request'), 'user', None)

        valid_methods = PaymentMethod.get_valid_payment_methods_types(user=user)

        if not isinstance(value, list):
            raise ValidationError({"paymentMethods": "Deve ser uma lista."})
        if not all(isinstance(item, str) for item in value):
            raise ValidationError({"paymentMethods": "Todos os itens devem ser uma string."})
        if not all(item in valid_methods for item in value):
            raise ValidationError({"paymentMethods": "Métodos de pagamentos inválidos."})

        return value

    def validate_contentDeliveries(self, value):
        valid_types = ContentDelivery.get_valid_contentDelivery_types()
        if not isinstance(value, list):
            raise ValidationError({"contentDeliveries": "Deve ser uma lista."})
        if not all(isinstance(item, str) for item in value):
            raise ValidationError({"contentDeliveries": "Todos os itens devem ser uma string."})
        if not all(item in valid_types for item in value):
            raise ValidationError({"contentDeliveries": "Métodos de entrega de conteúdo inválidos."})
        return value

    def validate_price(self, value):
        if value < 5:
            raise serializers.ValidationError('O preço mínimo do produto é de R$ 5,00')
        return value

    def validate(self, data):
        from product.views import AFFILIATE_MAX_COMMISSION

        if getattr(self, 'instance', None):
            # Validate affiliateCommission field
            affiliate = data.get('affiliate', self.instance.affiliate)  # type:ignore
            if affiliate:
                affiliateCommission = data.get('affiliateCommission', self.instance.affiliateCommission)  # type:ignore
                if affiliateCommission > AFFILIATE_MAX_COMMISSION or affiliateCommission < 1:
                    raise serializers.ValidationError({'affiliateCommission': f'A comissão do afiliado deve ser entre 1% e {AFFILIATE_MAX_COMMISSION}%'})

        return super().validate(data)

    def to_internal_value(self, data):
        internal_value = super().to_internal_value(data)  # type:ignore

        if 'paymentMethods' in data:
            internal_value['paymentMethods'] = self.validate_paymentMethods(data.get('paymentMethods', []))

        if 'contentDeliveries' in data:
            internal_value['contentDeliveries'] = self.validate_contentDeliveries(data.get('contentDeliveries', []))

        return internal_value

    def update(self, product, validated_data):
        if 'paymentMethods' in validated_data:
            paymentMethods = validated_data.pop('paymentMethods', [])
            self._validate_subscription_paymentMethods(product, paymentMethods)
            paymentMethodsInstances = PaymentMethod.get_payment_methods(
                types=paymentMethods,
                user=product.user,
            )
            product.paymentMethods.set(paymentMethodsInstances)

        # Update product's default offer name to match product name
        if 'name' in validated_data:
            name = validated_data['name']
            if name != product.name:
                product.offers.filter(default=True).update(name=name)

        if 'contentDeliveries' in validated_data:
            contentDeliveries_types = validated_data.get('contentDeliveries', [])
            self._mark_deleted_product_deliveries(contentDeliveries_types)

        return super().update(product, validated_data)  # type:ignore

    def _mark_deleted_product_deliveries(self, contentDeliveries_types):
        (ProductDelivery.objects
         .filter(product=self.instance)  # type:ignore
         .exclude(contentDelivery__type__in=contentDeliveries_types)
         .update(status='deleted'))

    def _validate_subscription_paymentMethods(self, product, paymentMethods):
        product_is_subscription = product.type == ProductType.SUBSCRIPTION.id

        subscription_offers = product.offers.filter(
            type=ProductType.SUBSCRIPTION.id,
            status__in=['active', 'disabled']
        )

        subscription_bumps = product.bumps.filter(
            offer__type=ProductType.SUBSCRIPTION.id
        )

        if product_is_subscription or subscription_offers.exists() or subscription_bumps.exists():
            wrong_methods = []
            for method in paymentMethods:
                if method not in Subscription.get_supported_paymentMethodTypes():
                    paymentType = PaymentMethodType.get_member(method)
                    if paymentType:
                        wrong_methods.append(paymentType.description)

            if wrong_methods:
                error_sufix = '.'
                if product_is_subscription:
                    error_sufix = 'para produtos do tipo assinatura.'
                elif subscription_offers.exists():
                    error_sufix = 'para produtos com ofertas de assinatura.'
                elif subscription_bumps.exists():
                    error_sufix = 'para produtos com orderbumps de assinatura.'

                plural = len(wrong_methods) > 1
                if plural:
                    error = (
                        'Os métodos de pagamento '
                        f'{", ".join(wrong_methods)} não são permitidos '
                        + error_sufix
                    )
                else:
                    error = (
                        'O método de pagamento '
                        f'{", ".join(wrong_methods)} não é permitido '
                        + error_sufix
                    )

                raise ValidationError({
                    'paymentMethods': error
                })

class PhisicalProductCreateSerializer(ProductCreateUpdateSerializerMixin, serializers.ModelSerializer):
    class Meta:
        model = Product
        fields = [
            'name',
            'price',
            'description',
            'category',
            'guarantee',
            'salesPage',
        ]
        required_fields = fields

class DigitalProductCreateSerializer(ProductCreateUpdateSerializerMixin, serializers.ModelSerializer):
    class Meta:
        model = Product
        fields = [
            'name',
            'description',
            'price',
            'type',
            'salesPage',
            'status',
            'emailAccessLink',  # optional
        ]
        required_fields = [
            'name',
            'description',
            'price',
            'type',
            'salesPage',
        ]

class ProductSerializer(serializers.ModelSerializer):
    price = serializers.FloatField()
    paymentMethods = serializers.SerializerMethodField()
    contentDeliveries = serializers.SerializerMethodField()
    category = CategorySerializer(read_only=True)

    class Meta:
        model = Product
        fields = [
            'id',
            'name',
            'description',
            'price',
            'type',
            'contentDeliveries',
            'emailAccessLink',
            'salesPage',
            'status',
            'paymentMethods',
            'category',
        ]
        ordering = ['-createdAt']

    def get_paymentMethods(self, product):
        return product.paymentMethods.values_list('type', flat=True)

    def get_contentDeliveries(self, product):
        return product.contentDeliveries.values_list('type', flat=True)

class ProductSerializerFull(ProductCreateUpdateSerializerMixin, serializers.ModelSerializer):
    offers = serializers.SerializerMethodField()
    bumps = OrderBumpSerializer(many=True, read_only=True)
    tracking_pixels = serializers.SerializerMethodField(read_only=True)
    paymentMethods = serializers.SerializerMethodField()
    contentDeliveries = serializers.SerializerMethodField()

    class Meta:
        model = Product
        fields = [
            'id',
            'short_id',
            'name',
            'status',
            'type',
            'description',
            'category',
            'price',
            'installments',
            'image',
            'guarantee',
            'salesPage',
            'supportEmail',
            'supportWhatsapp',
            'contentDeliveries',
            'emailAccessLink',
            'confirmEmail',

            # Affiliate
            'affiliate',
            'affiliateRequest',
            'affiliateCommission',
            'affiliateContact',
            'affiliateDescription',
            'affiliateSupportEmail',
            'affiliateMarketplace',
            'affiliateClick',
            'cookieTime',
            'affiliateShareBump',
            'affiliateShareUpsell',
            'affiliateCloneQuiz',
            'affiliateCloneQuizUrl',
            'affiliateSalesPage',

            # Checkout
            'upsell',
            'upsellPage',
            'redirectUpsellWithBumpFail',
            'defaultPaymentMethod',
            'showCouponField',
            'showAddressFields',
            'threeDsRetryEnabled',

            # Payment
            'offers',
            'invoiceDescription',
            'paymentMethods',
            'paymentsOrder',
            'ticketExpiration',
            'pixExpiresIn',
            'twoCardPayment',
            'bumps',

            'tracking_pixels',

            'producerName',
            'createdAt',
            'updatedAt',
        ]
        ordering = ['-createdAt']

    def get_paymentMethods(self, product: Product):
        return product.paymentMethods.values_list('type', flat=True)

    def get_contentDeliveries(self, product: Product):
        return product.contentDeliveries.exclude(type='files').values_list('type', flat=True)

    def get_offers(self, product):
        return OfferProductRetrieveSerializer(
            product.offers.filter(status__in=['active', 'disabled']),
            many=True
        ).data

    def get_tracking_pixels(self, product):
        tracking_pixels = product.pixels.filter(affiliate__isnull=True).first() or TrackingPixels.objects.create(product=product)
        return TrackingPixelSerializer(tracking_pixels).data

    def to_representation(self, product):
        self.fields['bumps'].context.update(self.context)
        res = super().to_representation(product)
        return res

class ProductPublicSerializer(serializers.ModelSerializer):
    offers = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = Product
        fields = [
            'id',
            'name',
            'type',
            'description',
            'category',
            'price',
            'image',

            # Affiliate
            'affiliate',
            'affiliateCommission',
            'affiliateContact',
            'affiliateDescription',
            'affiliateSupportEmail',
            'affiliateMarketplace',
            'affiliateClick',
            'cookieTime',
            'affiliateShareBump',
            'affiliateShareUpsell',
            'affiliateCloneQuiz',
            'affiliateCloneQuizUrl',
            'affiliateSalesPage',

            # Payment
            'offers',

            # Producer
            'producerName',
            'salesPage',
            'supportEmail',
            'supportWhatsapp',
        ]
        ordering = ['-createdAt']

    def get_offers(self, product):
        links = Link.objects.filter(product=product, status='active', showAffiliates=True, offer__isnull=False).only('offer').select_related('offer')
        offers = [link.offer for link in links]
        return OfferSerializer(offers, many=True).data

class AffiliateProductSerializer(serializers.ModelSerializer):
    companyName = serializers.SerializerMethodField(read_only=True)
    affiliateSupportEmail = serializers.SerializerMethodField(read_only=True)
    commission = serializers.FloatField()
    productName = serializers.CharField(source='product.name', read_only=True)
    productId = serializers.CharField(source='product.id', read_only=True)
    affiliateDescription = serializers.CharField(source='product.affiliateDescription', read_only=True)
    affiliateShareBump = serializers.SerializerMethodField(read_only=True)
    affiliateShareUpsell = serializers.SerializerMethodField(read_only=True)
    tracking_pixels = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = Affiliate
        fields = [
            'short_id',
            'status',
            'commission',
            'startTime',
            'productName',
            'productId',
            'companyName',
            'affiliateShareBump',
            'affiliateShareUpsell',
            'affiliateSupportEmail',
            'affiliateDescription',
            'tracking_pixels',
        ]
        ordering = ['-startTime']

    def get_affiliateShareBump(self, affiliate):
        return affiliate.product.affiliateShareBump

    def get_affiliateShareUpsell(self, affiliate):
        return affiliate.product.affiliateShareUpsell

    def get_companyName(self, affiliate):
        company = affiliate.product.user.company
        return company.companyName or company.completeName or affiliate.product.user.first_name or affiliate.product.user.email

    def get_affiliateSupportEmail(self, affiliate):
        return affiliate.product.affiliateSupportEmail or affiliate.product.user.email

    def get_tracking_pixels(self, affiliate):
        tracking_pixels, _ = affiliate.product.pixels.get_or_create(  # type:ignore
            defaults={'product': affiliate.product, 'affiliate': affiliate},  # type:ignore
            product=affiliate.product,  # type:ignore
            affiliate=affiliate
        )
        return TrackingPixelSerializer(tracking_pixels).data

class PerProductExportSerializer(serializers.Serializer):
    productName = serializers.CharField(source='name', label='Nome do Produto')
    amount = serializers.FloatField(label='Total em vendas')
    salesPercentage = serializers.FloatField(label='Porcentagem das vendas')
    salesCount = serializers.IntegerField(label='Quantidade de vendas')

class AffiliateSerializer(serializers.ModelSerializer):
    product = ProductSerializer(read_only=True)
    user = UserAffiliateSerializer(read_only=True)
    commission = serializers.FloatField()

    class Meta:
        model = Affiliate
        fields = [
            'id',
            'short_id',
            'user',
            'product',
            'commission',
            'status',
            'startTime',
            'endTime',
        ]
        ordering = ['-startTime']
        read_only_fields = ['startTime', 'endTime']

class AffiliateExportSerializer(serializers.Serializer):
    email = serializers.CharField(label='Email do Afiliado')
    amount = serializers.FloatField(label='Total em Vendas')
    salesPercentage = serializers.FloatField(label='Porcentagem das Vendas')
    salesCount = serializers.IntegerField(label='Quantidade de Vendas')

class AffiliateAdminFlexSerializer(CustomFlexFieldsSerializer):
    class Meta:
        model = Affiliate
        fields = '__all__'
        expandable_fields = {
            'user': 'user.serializers.UserAdminFlexSerializer',
            'product': 'product.serializers.ProductAdminFlexSerializer',
        }

class AffiliateOwnerFlexSerializer(CustomFlexFieldsSerializer):
    class Meta:
        model = Affiliate
        fields = [
            'id',
            'short_id',
            'product',
            'commission',
            'status',
            'startTime',
            'endTime',
        ]
        expandable_fields = {
            'product': 'product.serializers.ProductOwnerFlexSerializer',
        }

class AffiliatePublicSerializer(serializers.ModelSerializer):
    class Meta:
        model = Affiliate
        fields = [
            'id',
            'short_id',
            'product',
            'commission',
            'status',
            'startTime',
            'endTime',
        ]
        expandable_fields = {
            'product': 'product.serializers.ProductPublicFlexSerializer',
        }

class CoproductionRetrieveSerializer(serializers.ModelSerializer):
    percentage = serializers.FloatField(source='amount')
    coProducer = serializers.CharField(source='user.email', read_only=True)
    product = ProductSerializer(read_only=True)
    productOwner = serializers.CharField(source='product.user.email', read_only=True)

    class Meta:
        model = Coproduction
        fields = [
            'id',
            'coProducer',
            'productOwner',
            'product',
            'percentage',
            'receiveSalesFromProducer',
            'receiveSalesFromAffiliate',
            'startTime',
            'endTime',
            'status',
            'deletionApprovedByOwner',
            'deletionApprovedByUser',
        ]
        ordering = ['-startTime']

class CoproductionCreateSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(read_only=True)
    percentage = serializers.FloatField(source='amount')

    class Meta:
        model = Coproduction
        fields = [
            'id',
            'user',
            'product',
            'percentage',
            'receiveSalesFromProducer',
            'receiveSalesFromAffiliate',
            'startTime',
            'endTime',
            'status'
        ]
        ordering = ['-startTime']

class CoproductionUpdateSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(read_only=True)
    percentage = serializers.FloatField(source='amount')

    class Meta:
        model = Coproduction
        fields = [
            'id',
            'user',
            'product',
            'percentage',
            'receiveSalesFromProducer',
            'receiveSalesFromAffiliate',
            'startTime',
            'endTime',
            'status'
        ]
        ordering = ['-startTime']
        read_only_fields = ['id', 'user', 'product']

class PerCoproductionSerializer(serializers.Serializer):
    date = serializers.DateTimeField(label='Data')
    value = serializers.FloatField(label='Total')

class LinkSerializer(serializers.ModelSerializer):
    offer = OfferSerializer(read_only=True)
    checkout = serializers.SerializerMethodField()

    class Meta:
        model = Link
        fields = [
            'id',
            'shortId',
            'name',
            'url',
            'checkout',
            'offer',
            'showAffiliates',
            'type',
            'status',
            'createdAt',
            'updatedAt',
        ]
        ordering = ['-createdAt']

    def get_checkout(self, link):
        checkout = link.offer.checkout() if link.offer else None
        return checkout.id if checkout else None

class ProductSerializerCheckout(serializers.ModelSerializer):
    price = serializers.FloatField()
    bumps = OrderBumpSerializer(many=True, read_only=True)
    paymentMethods = serializers.SerializerMethodField()

    class Meta:
        model = Product
        fields = [
            'short_id',
            'name',
            'description',
            'status',
            'price',
            'image',
            'invoiceDescription',
            'upsell',
            'upsellPage',
            'redirectUpsellWithBumpFail',
            'defaultPaymentMethod',
            'bumps',
            'paymentMethods',
            'paymentsOrder',
            'installments',
            'ticketExpiration',
            'pixExpiresIn',
            'showCouponField',
            'showAddressFields',
        ]
        ordering = ['-createdAt']

    def get_paymentMethods(self, product: Product):
        payment_methods: Iterable[PaymentMethod] = product.paymentMethods.all()
        return [method._serialize() for method in payment_methods]

    def to_representation(self, instance):
        self.fields['bumps'].context.update(self.context)
        representation = super().to_representation(instance)
        return representation

class CheckoutOfferSerializer(serializers.ModelSerializer):
    # getConfig property function on checkout
    config = serializers.ReadOnlyField(source='getConfig')

    class Meta:
        model = Checkout
        fields = [
            'config',
        ]
        ordering = ['-createdAt']

class OfferCheckoutSerializer(serializers.ModelSerializer):
    product = ProductSerializerCheckout(read_only=True)
    checkout = serializers.SerializerMethodField(read_only=True)
    price = serializers.FloatField(read_only=True)
    confirmEmail = serializers.BooleanField(source='product.confirmEmail', read_only=True)
    upsellPage = serializers.CharField(source='product.upsellPage', read_only=True)
    sellerName = serializers.SerializerMethodField(read_only=True)
    sellerEmail = serializers.SerializerMethodField(read_only=True)
    sellerWhatsapp = serializers.CharField(source='product.supportWhatsapp', read_only=True)
    tracking_pixels = serializers.SerializerMethodField(read_only=True)
    companyStatus = serializers.CharField(source='product.user.company.status', read_only=True)
    threeDsEnabled = serializers.SerializerMethodField()
    threeDsRetryEnabled = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = Offer
        fields = [
            'id',
            'name',
            'image',
            'price',
            'checkout',
            'default',
            'status',
            'product',
            'threeDsEnabled',
            'threeDsRetryEnabled',
            'confirmEmail',
            'upsellPage',
            'sellerEmail',
            'sellerWhatsapp',
            'sellerName',
            'companyStatus',
            'tracking_pixels',

            'recurrence_period',
            'quantity_recurrences',
            'type',
            'trial_days',
            'max_retries',
            'retry_interval',
        ]
        ordering = ['-createdAt']

    def is_threeds_payment_enabled(self, producer) -> bool:
        return PaymentMethodType.THREEDS.id in \
            PaymentMethod.get_valid_payment_methods_types(user=producer)

    def get_threeDsEnabled(self, offer: Offer) -> bool:
        producer = offer.product.user

        if not self.is_threeds_payment_enabled(producer):
            return False

        return bool(producer._company.threeDsEnabled)

    def get_threeDsRetryEnabled(self, offer: Offer) -> bool:
        producer = offer.product.user

        if not self.is_threeds_payment_enabled(producer):
            return False

        user_threeDsRetryEnabled = producer.threeDsRetryEnabled
        product_threeDsRetryEnabled = offer.product.threeDsRetryEnabled

        return bool(user_threeDsRetryEnabled or product_threeDsRetryEnabled)

    def get_sellerName(self, offer):
        return offer.product.producerName or offer.product.supportEmail or offer.product.user.email

    def get_sellerEmail(self, offer):
        return offer.product.supportEmail or offer.product.user.email

    def get_tracking_pixels(self, offer):
        tracking_pixels = offer.product.pixels.filter(affiliate__isnull=True).first()
        return TrackingPixelPublicSerializer(tracking_pixels).data

    def get_checkout(self, offer):
        checkout_id = self.context.get('checkout_id')

        return CheckoutOfferSerializer(offer.checkout(checkout_id=checkout_id)).data

class OfferAdminFlexSerializer(CustomFlexFieldsSerializer):
    class Meta:
        model = Offer
        fields = '__all__'
        expandable_fields = {
            'product': 'product.serializers.ProductAdminFlexSerializer',
        }

class ProductAdminSerializer(serializers.ModelSerializer):
    userEmail = serializers.CharField(source='user.email', read_only=True)
    userName = serializers.CharField(source='user._company.completeName', read_only=True)
    refusedPercentage = serializers.FloatField(source='get_refused_percentage', read_only=True)

    class Meta:
        model = Product
        fields = [
            'id',
            'short_id',
            'name',
            'price',
            'type',
            'status',
            'statusAdmin',
            'blockReason',
            'hideShowcase',
            'bypassShowcaseSalesFilter',
            'additionalInfo',
            'refusedPercentage',
            'refundRequest',
            'userEmail',
            'userName',
            'createdAt',
            'updatedAt',
        ]
        ordering = ['createdAt', 'status']

class ProductAdminSerializerFull(serializers.ModelSerializer):
    user = UserAdminSerializer(read_only=True)
    category = CategorySerializer(read_only=True)
    offers = OfferAdminSerialzier(many=True, read_only=True)
    links = LinkSerializer(many=True, read_only=True)
    refusedPercentages = serializers.DictField(source='get_refused_percentages', read_only=True)
    coproductions = CoproductionRetrieveSerializer(many=True, read_only=True)
    paymentMethods = serializers.SerializerMethodField()
    contentDeliveries = serializers.SerializerMethodField()

    class Meta:
        model = Product
        fields = [
            'id',
            'short_id',
            'image',
            'name',
            'description',
            'status',
            'statusAdmin',
            'blockReason',
            'hideShowcase',
            'bypassShowcaseSalesFilter',
            'scriptSuspicious',
            'refundRequest',
            'refusedPercentages',
            'type',
            'contentDeliveries',
            'emailAccessLink',
            'paymentMethods',
            'paymentsOrder',
            'invoiceDescription',
            'ticketExpiration',
            'pixExpiresIn',
            'twoCardPayment',
            'confirmEmail',
            'price',
            'guarantee',
            'installments',
            'upsell',
            'upsellPage',
            'redirectUpsellWithBumpFail',
            'defaultPaymentMethod',
            'showCouponField',
            'purchaseOnPix',
            'purchaseOnTicket',
            'salesPage',
            'supportEmail',
            'supportWhatsapp',
            'producerName',
            'additionalInfo',
            'threeDsRetryEnabled',

            # Affiliate
            'affiliate',
            'affiliateRequest',
            'affiliateContact',
            'affiliateMarketplace',
            'affiliateDescription',
            'affiliateSupportEmail',
            'affiliateShareBump',
            'affiliateShareUpsell',
            'affiliateClick',
            'cookieTime',
            'affiliateCommission',
            'affiliateCloneQuiz',
            'affiliateCloneQuizUrl',
            'affiliateSalesPage',

            # Relations
            'user',
            'category',
            'offers',
            'links',
            'coproductions',

            # Dates
            'createdAt',
            'updatedAt',

            # Metrics
            'metrics',
        ]
        read_only_fields = ['id', 'refundRequest', 'blockReason', 'statusAdmin', 'scriptSuspicious', 'refundPercentages']

    def get_paymentMethods(self, product: Product):
        return product.paymentMethods.values_list('type', flat=True)

    def get_contentDeliveries(self, product: Product):
        return product.contentDeliveries.values_list('type', flat=True)

class ProductAdminUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for admin update that has fields that can be updated directly
    and does not affect other product fields so we dont need to create a new action
    for every field that needs to be updated.
    """

    class Meta:
        model = Product
        fields = [
            'threeDsRetryEnabled',
            'refundRequest',
            'hideShowcase',
            'bypassShowcaseSalesFilter',
        ]

class ProductAdminFlexSerializer(CustomFlexFieldsSerializer):
    class Meta:
        model = Product
        fields = '__all__'
        expandable_fields = {
            'user': UserAdminFlexSerializer,
            'category': CategoryAdminFlexSerializer,
            'contentDeliveries': ('product.serializers.ContentDeliveryAdminFlexSerializer', {'many': True}),
            'paymentMethods': ('product.serializers.PaymentMethodAdminFlexSerializer', {'many': True}),
            'defaultPaymentMethod': 'product.serializers.PaymentMethodAdminFlexSerializer',
        }

class ProductOwnerFlexSerializer(CustomFlexFieldsSerializer):
    paymentMethods = serializers.SerializerMethodField()
    contentDeliveries = serializers.SerializerMethodField()

    class Meta:
        model = Product
        fields = [
            # Basic info
            'id',
            'short_id',

            # Product info
            'image',
            'name',
            'description',
            'category',
            'status',
            'type',

            # Content Delivery
            'contentDelivery',
            'contentDeliveries',
            'emailAccessLink',

            # Payment
            'paymentMethods',
            'paymentsOrder',
            'invoiceDescription',
            'ticketExpiration',
            'pixExpiresIn',
            'twoCardPayment',
            'confirmEmail',
            'defaultPaymentMethod',
            'showCouponField',
            'threeDsRetryEnabled',

            # Pricing
            'price',
            'guarantee',
            'installments',

            # Upsell
            'upsell',
            'upsellPage',
            'redirectUpsellWithBumpFail',

            # Pixel
            'purchaseOnPix',
            'purchaseOnTicket',

            # Support
            'salesPage',
            'supportEmail',
            'supportWhatsapp',
            'producerName',

            # Affiliate
            'affiliate',
            'affiliateRequest',
            'affiliateContact',
            'affiliateMarketplace',
            'affiliateDescription',
            'affiliateSupportEmail',
            'affiliateShareBump',
            'affiliateShareUpsell',

            # Affiliate click
            'affiliateClick',
            'cookieTime',

            # Commission
            'affiliateCommission',

            # Dates
            'createdAt',
            'updatedAt',
        ]
        expandable_fields = {
            'category': CategorySerializer,
        }

    def get_paymentMethods(self, product: Product):
        return product.paymentMethods.values_list('type', flat=True)

    def get_contentDeliveries(self, product: Product):
        return product.contentDeliveries.exclude(type='files').values_list('type', flat=True)

class ProductMinimalSerializer(serializers.ModelSerializer):
    class Meta:
        model = Product
        fields = [
            'id',
            'name',
        ]
        ordering = ['-createdAt']

class CouponCreateUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Coupon
        fields = [
            'code',
            'products',
            'status',
            'discount',
            'applyOnBumps',
            'startTime',
            'endTime',
        ]

    def validate_products(self, value):
        if not all(product.user == self.context['request'].user for product in value):
            raise serializers.ValidationError('Um ou mais produtos não pertencem ao usuário.')
        return value

    def validate_code(self, value):
        value = value.lower()
        regex = r'^[a-z0-9]*$'
        if not re.match(regex, value):
            raise serializers.ValidationError(
                'O código do cupom deve conter apenas letras e números.'
            )
        user = self.context['request'].user

        if Coupon.objects.filter(code__iexact=value,
                                 products__user=user).exists():
            raise serializers.ValidationError('Cupom com este código já existe.')
        return value

class CouponSerializer(serializers.ModelSerializer):
    products = ProductMinimalSerializer(many=True, read_only=True)
    discount = serializers.FloatField()
    usage_quantity = serializers.IntegerField(source='get_usage_quantity', read_only=True)

    class Meta:
        model = Coupon
        fields = [
            'id',
            'code',
            'status',
            'usage_quantity',
            'products',
            'discount',
            'applyOnBumps',
            'startTime',
            'endTime',
            'createdAt',
            'updatedAt',
        ]
        ordering = ['-createdAt']

class CouponPublicSerializer(serializers.ModelSerializer):
    discount = serializers.FloatField()

    class Meta:
        model = Coupon
        fields = [
            'code',
            'discount',
            'applyOnBumps',
            'startTime',
            'endTime',
        ]
        ordering = ['-createdAt']

class CouponOwnerSerializer(CustomFlexFieldsSerializer):
    class Meta:
        model = Coupon
        fields = '__all__'
        expandable_fields = {
            'products': ('product.serializers.ProductOwnerFlexSerializer', {'many': True}),
        }

class ContentDeliveryAdminFlexSerializer(CustomFlexFieldsSerializer):
    class Meta:
        model = ContentDelivery
        fields = '__all__'

class ProductDeliverySerializer(serializers.ModelSerializer):
    class Meta:
        model = ProductDelivery
        fields = [
            'id',
            'product',
            'contentDelivery',
            'name',
            'status',
            'fields',
            'createdAt',
            'updatedAt',
        ]
        read_only_fields = ['id', 'product', 'contentDelivery', 'createdAt', 'updatedAt']

class DeliveryAccessSerializer(serializers.ModelSerializer):
    contentDelivery_type = serializers.CharField(source='productDelivery.contentDelivery.type', read_only=True)

    class Meta:
        model = DeliveryAccess
        fields = [
            'id',
            'user',
            'productDelivery',
            'contentDelivery_type',
            'status',
            'fields',
            'expiresAt',
            'createdAt',
            'updatedAt',
        ]
        read_only_fields = ['user', 'productDelivery', 'createdAt', 'updatedAt']

class DeliveryAccessPublicSerializer(serializers.ModelSerializer):
    contentDelivery_type = serializers.CharField(source='productDelivery.contentDelivery.type', read_only=True)
    loginUrl = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = DeliveryAccess
        fields = [
            'id',
            'contentDelivery_type',
            'status',
            'loginUrl',
            'fields',
            'expiresAt',
            'createdAt',
        ]
        read_only_fields = ['productDelivery', 'createdAt']

    def get_loginUrl(self, delivery_access):
        if delivery_access.status == 'waiting_config' and delivery_access.productDelivery.contentDelivery.type == 'discord':
            return self.context.get('discord_login_url')

class PaymentMethodAdminFlexSerializer(CustomFlexFieldsSerializer):
    class Meta:
        model = PaymentMethod
        fields = '__all__'

class ProductShowcaseSerializer(FlexFieldsModelSerializer):
    author = serializers.CharField(source='producerName')
    sales_page = serializers.CharField(source='salesPage')
    affiliate_commission = serializers.DecimalField(source='affiliateCommission', max_digits=10, decimal_places=2)
    affiliate_click = serializers.CharField(source='affiliateClick')
    affiliate_support_email = serializers.EmailField(source='affiliateSupportEmail')
    affiliate_request = serializers.BooleanField(source='affiliateRequest')
    affiliate_share_bump = serializers.BooleanField(source='affiliateShareBump')
    affiliate_contact = serializers.BooleanField(source='affiliateContact')
    affiliate_share_upsell = serializers.BooleanField(source='affiliateShareUpsell')
    affiliate_clone_quiz = serializers.BooleanField(source='affiliateCloneQuiz')
    affiliate_clone_quiz_url = serializers.URLField(source='affiliateCloneQuizUrl')
    affiliate_sales_page = serializers.CharField(source='affiliateSalesPage')
    cookie_duration = serializers.IntegerField(source='cookieTime')
    offers = serializers.SerializerMethodField(read_only=True)
    affiliation = serializers.SerializerMethodField()
    affiliate_commission_amount = serializers.SerializerMethodField()
    temperature = serializers.FloatField(read_only=True)

    class Meta:
        model = Product
        fields = [
            'id', 'name', 'author', 'price', 'category', 'type', 'sales_page', 'description',
            'language', 'image', 'affiliate_commission', 'affiliate_click', 'affiliate_request',
            'affiliate_share_bump', 'affiliate_contact', 'affiliate_share_upsell', 'affiliate_clone_quiz',
            'affiliate_clone_quiz_url', 'affiliate_sales_page', 'cookie_duration', 'affiliate_support_email',
            'offers', 'affiliation', 'affiliate_commission_amount', 'temperature'
        ]

    def get_offers(self, product):
        return OfferSerializer(product.offers.filter(status__in=['active', 'disabled']), many=True).data

    def get_affiliation(self, product):
        request = self.context.get("request")
        if request and request.user:
            affiliate = get_is_user_affiliate(request.user, product)
            if affiliate:
                return {"status": affiliate.status}
        return None

    def get_affiliate_commission_amount(self, product: Product):
        request = self.context.get("request")
        if request and request.user:
            return product.get_affiliate_commission_amount(request.user, product)

class ShowcaseEventSerializer(serializers.ModelSerializer):
    class Meta:
        model = ShowcaseEvent
        fields = [
            'id',
            'type',
            'user',
            'product',
            'createdAt',
        ]
        read_only_fields = ['id', 'product', 'user', 'createdAt']

    def get_products(self, event):
        return ProductSerializer(event.products.all(), many=True).data

class ShowcaseEventRegisterSerializer(serializers.Serializer):
    type = serializers.ChoiceField(choices=ShowcaseEventType.choices())
    products = serializers.ListField(child=serializers.CharField(), required=True)

    def validate_products(self, products):
        products = set(products)
        products = Product.objects.filter(id__in=products).only('pk')
        if not products:
            raise ValidationError({'detail': 'Nenhum produto encontrado.'})

        return products
