import uuid
from decimal import Decimal
from typing import Sequence
from unittest import mock

from django.urls import reverse
from django.utils import timezone
from django.utils.dateparse import parse_datetime
from rest_framework import status

from cakto.tests.base import BaseTestCase
from product.models import Coupon, Product
from product.utils import get_coupon, validate_coupon_net_amount, calculate_net_amount_after_coupon_and_fees, validate_coupon_with_dynamic_fees


class CouponTests(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.create_paymentMethods()

        cls.product = cls.create_product(user=cls.user)

    @property
    def headers(self):
        return self.create_headers(self.get_user_access_token(self.user))

    @classmethod
    def create_coupon(
        cls,
        code: str = 'Test',
        discount: int | Decimal | float = 10,
        startTime: timezone.datetime = timezone.now(),
        endTime: timezone.datetime | None = timezone.now() + timezone.timedelta(days=30),
        products: Sequence[Product] | None = None,
        **kwargs
    ):
        if products is None:
            products = [cls.product]

        coupon = Coupon.objects.create(
            code=code,
            discount=discount,
            startTime=startTime,
            endTime=endTime,
            **kwargs
        )
        coupon.products.add(*products)

        return coupon

    def test_can_create_coupon(self):
        url = reverse('coupons')
        data = {
            'products': [self.product.pk],
            'code': 'CUPOM10',
            'discount': 10,
            'startTime': timezone.now(),
        }

        response = self.client.post(url, data, format='json', headers=self.headers)

        self.assertEqual(response.status_code, status.HTTP_201_CREATED, response.content)
        self.assertEqual(response.data['products'][0]['id'], self.product.pk)
        self.assertEqual(Coupon.objects.count(), 1)

    def test_delete_coupon(self):
        coupon = self.create_coupon()
        url = reverse('coupon', args=[coupon.pk])

        response = self.client.delete(url, headers=self.headers)

        coupon.refresh_from_db()
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT, response.content)
        self.assertEqual(coupon.status, 'deleted')

    def test_cant_create_coupon_with_more_than_80_discount(self):
        url = reverse('coupons')
        data = {
            'products': [self.product.pk],
            'code': 'CUPOM10',
            'discount': 81,
            'startTime': timezone.now(),
        }

        response = self.client.post(url, data, format='json', headers=self.headers)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST, response.content)
        self.assertEqual(Coupon.objects.count(), 0)

    def test_cant_create_coupon_with_less_than_1_discount(self):
        url = reverse('coupons')
        data = {
            'products': [self.product.pk],
            'code': 'CUPOM10',
            'discount': 0.5,
            'startTime': timezone.now(),
        }

        response = self.client.post(url, data, format='json', headers=self.headers)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST, response.content)
        self.assertEqual(Coupon.objects.count(), 0)

    def test_cant_create_coupon_with_wrong_code_chars(self):
        url = reverse('coupons')
        data = {
            'products': [self.product.pk],
            'code': '!@#$%^&*()_+-=,.<>/?~',
            'discount': 10,
            'startTime': timezone.now(),
        }

        response = self.client.post(url, data, format='json', headers=self.headers)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST, response.content)
        self.assertEqual(Coupon.objects.count(), 0)

    def test_can_add_product_to_coupon(self):
        coupon = self.create_coupon(endTime=None)
        url = reverse('coupon', args=[coupon.pk])
        product_2 = self.create_product(user=self.user)
        data = {
            'products': [self.product.pk, product_2.pk],
        }

        response = self.client.patch(url, data, headers=self.headers)
        coupon.refresh_from_db()

        product_2_exists = any([product_2.pk == product['id'] for product in response.data['products']])
        self.assertTrue(product_2_exists)
        self.assertEqual(coupon.products.count(), 2)

    def test_can_edit_coupon_code(self):
        coupon = self.create_coupon()
        url = reverse('coupon', args=[coupon.pk])
        data = {
            'code': 'NEWCODE',
        }

        response = self.client.patch(url, data, headers=self.headers)
        coupon.refresh_from_db()

        self.assertEqual(response.data['code'], 'newcode')
        self.assertEqual(coupon.code, 'newcode')

    def test_can_edit_coupon_applyOnBumps(self):
        coupon = self.create_coupon(applyOnBumps=True)
        url = reverse('coupon', args=[coupon.pk])
        data = {
            'applyOnBumps': False,
        }

        response = self.client.patch(url, data, headers=self.headers)
        coupon.refresh_from_db()

        self.assertEqual(response.data['applyOnBumps'], False)
        self.assertEqual(coupon.applyOnBumps, False)

    def test_can_edit_coupon_startTime(self):
        coupon = self.create_coupon()
        url = reverse('coupon', args=[coupon.pk])
        new_date = timezone.now() + timezone.timedelta(days=1)
        data = {
            'startTime': new_date.isoformat(),
        }

        response = self.client.patch(url, data, headers=self.headers)

        # Convert the ISO 8601 string from the response back to a datetime object
        response_startTime = parse_datetime(response.data['startTime'])
        coupon.refresh_from_db()

        # Compare the two datetime objects directly
        self.assertTrue(timezone.is_aware(response_startTime))  # type:ignore
        self.assertTrue(timezone.is_aware(new_date))
        self.assertEqual(response_startTime, new_date)
        self.assertEqual(coupon.startTime, new_date)

    def test_can_edit_coupon_endTime(self):
        coupon = self.create_coupon()
        url = reverse('coupon', args=[coupon.pk])

        new_date = timezone.now() + timezone.timedelta(days=30)
        data = {
            'endTime': new_date.isoformat(),
        }

        response = self.client.patch(url, data, headers=self.headers)

        # Convert the ISO 8601 string from the response back to a datetime object
        response_endTime = parse_datetime(response.data['endTime'])
        coupon.refresh_from_db()

        # Compare the two datetime objects directly
        self.assertTrue(timezone.is_aware(response_endTime))  # type:ignore
        self.assertTrue(timezone.is_aware(new_date))
        self.assertEqual(response_endTime, new_date)
        self.assertEqual(coupon.endTime, new_date)

    def test_can_remove_coupon_endTime(self):
        coupon = self.create_coupon()
        url = reverse('coupon', args=[coupon.pk])
        data = {'endTime': ''}

        response = self.client.patch(url, data, headers=self.headers)
        coupon.refresh_from_db()

        self.assertEqual(None, response.json()['endTime'])
        self.assertEqual(coupon.endTime, None)

    def test_can_edit_coupon_discount(self):
        coupon = self.create_coupon()
        url = reverse('coupon', args=[coupon.pk])
        data = {
            'discount': 20,
        }

        response = self.client.patch(url, data, headers=self.headers)
        coupon.refresh_from_db()

        self.assertEqual(20, response.json()['discount'])
        self.assertEqual(coupon.discount, 20)

    def test_cant_edit_coupon_id(self):
        coupon = self.create_coupon()
        coupon_id_before_patch = str(coupon.pk)
        url = reverse('coupon', args=[coupon.pk])
        data = {
            'id': uuid.uuid4(),
        }

        response = self.client.patch(url, data, headers=self.headers)
        coupon.refresh_from_db()

        self.assertEqual(response.json()['id'], coupon_id_before_patch)
        self.assertEqual(str(coupon.id), coupon_id_before_patch)

    def test_get_coupon_by_product_id(self):
        coupon = self.create_coupon()

        coupon_retrieved = get_coupon(coupon.code, product_id=self.product.pk)
        self.assertEqual(coupon, coupon_retrieved)

    def test_get_coupon_by_offer_id(self):
        coupon = self.create_coupon()
        offer = self.product.offers.first()

        coupon_retrieved = get_coupon(coupon.code, offer_id=offer.pk)
        self.assertEqual(coupon, coupon_retrieved)

    def test_coupon_validate(self):
        coupon = self.create_coupon()
        offerId = self.product.offers.first().pk
        url = reverse('coupon-validate')

        data = {
            'code': coupon.code,
            'offerId': offerId,
        }

        response = self.client.post(url, data, headers=self.headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content)
        self.assertEqual(response.data['discount'], coupon.discount)
        self.assertEqual(response.data['code'], coupon.code)
        self.assertEqual(response.data['applyOnBumps'], coupon.applyOnBumps)

    def test_coupon_validate_with_checkout_id(self):
        coupon = self.create_coupon()
        offerId = self.product.offers.first().pk
        url = reverse('coupon-validate')

        data = {
            'code': coupon.code,
            'offerId': offerId + '_7012973',
        }

        response = self.client.post(url, data, headers=self.headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content)
        self.assertEqual(response.data['discount'], coupon.discount)
        self.assertEqual(response.data['code'], coupon.code)
        self.assertEqual(response.data['applyOnBumps'], coupon.applyOnBumps)

    def test_coupon_validate_should_return_400_when_expired(self):
        coupon = self.create_coupon(endTime=timezone.now() - timezone.timedelta(days=30))
        offerId = self.product.offers.first().pk
        url = reverse('coupon-validate')

        data = {
            'code': coupon.code,
            'offerId': offerId,
        }

        response = self.client.post(url, data, headers=self.headers)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST, response.content)

    def test_validate_coupon_should_return_false_when_bump_offer_and_applyOnBumps_is_false(self):
        coupon = self.create_coupon(applyOnBumps=False)

        self.assertEqual(coupon.validate_coupon('bump'), False)

    def test_validate_coupon_should_return_true_when_bump_offer_and_applyOnBumps_is_true(self):
        coupon = self.create_coupon(applyOnBumps=True)

        self.assertEqual(coupon.validate_coupon('bump'), True)

    def test_coupon_calculate_price(self):
        coupon = self.create_coupon(applyOnBumps=False)
        self.product.price = 100
        self.product.save()

        calculated_price = coupon.calculate_price(self.product.price)
        self.assertEqual(calculated_price, Decimal('90.00'))

    def test_calculate_net_amount_after_coupon_and_fees(self):
        """Testa o cálculo do valor líquido após cupom e taxas"""
        original_price = Decimal('100.00')
        coupon_discount = Decimal('10')  # 10%

        net_amount = calculate_net_amount_after_coupon_and_fees(
            original_price=original_price,
            coupon_discount_percentage=coupon_discount
        )

        expected_net_amount = Decimal('87.375')

        self.assertEqual(net_amount, expected_net_amount)

    def test_validate_coupon_net_amount_sufficient(self):
        """Testa validação quando o valor líquido é suficiente"""
        original_price = Decimal('100.00')
        coupon_discount = Decimal('10')  # 10%

        is_valid = validate_coupon_net_amount(
            original_price=original_price,
            coupon_discount_percentage=coupon_discount
        )

        self.assertTrue(is_valid)

    def test_validate_coupon_net_amount_insufficient(self):
        """Testa validação quando o valor líquido é insuficiente"""
        original_price = Decimal('5.00')
        coupon_discount = Decimal('80')  # 80% de desconto

        is_valid = validate_coupon_net_amount(
            original_price=original_price,
            coupon_discount_percentage=coupon_discount
        )

        self.assertFalse(is_valid)

    def test_validate_coupon_net_amount_edge_case(self):
        """Testa caso limite onde o valor líquido é exatamente o mínimo"""
        original_price = Decimal('10.00')
        coupon_discount = Decimal('50')  # 50% de desconto

        is_valid = validate_coupon_net_amount(
            original_price=original_price,
            coupon_discount_percentage=coupon_discount
        )

        self.assertTrue(is_valid)

    def test_coupon_validation_with_insufficient_net_amount(self):
        """Testa que cupom com valor líquido insuficiente é rejeitado na validação"""
        self.product.price = Decimal('5.00')
        self.product.save()

        coupon = self.create_coupon(discount=80)  # 80% de desconto
        offer = self.product.offers.first()

        url = reverse('coupon-validate')
        data = {
            'code': coupon.code,
            'offerId': offer.pk,
        }

        response = self.client.post(url, data, headers=self.headers)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('valor final seria menor que as taxas', response.json()['detail'])

    def test_coupon_validation_with_sufficient_net_amount(self):
        """Testa que cupom com valor líquido suficiente é aceito na validação"""
        self.product.price = Decimal('100.00')
        self.product.save()

        coupon = self.create_coupon(discount=10)
        offer = self.product.offers.first()

        url = reverse('coupon-validate')
        data = {
            'code': coupon.code,
            'offerId': offer.pk,
        }

        response = self.client.post(url, data, headers=self.headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()['discount'], coupon.discount)

    def test_create_with_mulptiple_products_creates_only_one_coupon(self):
        product_2 = self.create_product(user=self.user)
        url = reverse('coupons')
        data = {
            "startTime": "2024-07-15",
            "discount": 10,
            "products": [
                self.product.pk,
                product_2.pk,
            ],
            "code": "CUPOM10",
        }

        response = self.client.post(url, data, format='json', headers=self.headers)

        self.assertEqual(response.status_code, status.HTTP_201_CREATED, response.content)
        self.assertEqual(Coupon.objects.count(), 1)
        self.assertEqual(Coupon.objects.first().products.count(), 2)  # type:ignore

    def test_list_with_mulptiple_products_returns_only_one_coupon(self):
        product_2 = self.create_product(user=self.user)
        url = reverse('coupons')
        data = {
            "startTime": "2024-07-15T00:00:00Z",
            "discount": 10,
            "code": "CUPOM10",
        }
        coupon = Coupon.objects.create(**data)
        coupon.products.set([self.product.pk, product_2.pk])

        response = self.client.get(url, headers=self.headers)

        self.assertEqual(len(response.data['results']), 1)

    def test_coupon_list_returns_correct_data(self):
        coupon = self.create_coupon()
        url = reverse('coupons')
        response = self.client.get(url, headers=self.headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content)

        coupon_data = response.json()['results'][0]

        self.assertEqual(coupon_data['id'], str(coupon.id))
        self.assertEqual(coupon_data['code'], coupon.code)
        self.assertEqual(coupon_data['status'], coupon.status)
        self.assertEqual(coupon_data['usage_quantity'], coupon.get_usage_quantity())
        self.assertEqual(coupon_data['products'][0]['id'], coupon.products.first().pk)  # type:ignore
        self.assertEqual(coupon_data['discount'], coupon.discount)
        self.assertEqual(coupon_data['applyOnBumps'], coupon.applyOnBumps)

        self.assertEqual(coupon_data['startTime'], coupon.startTime.astimezone(timezone.get_current_timezone()).isoformat())
        self.assertEqual(coupon_data['endTime'], coupon.endTime.astimezone(timezone.get_current_timezone()).isoformat())  # type:ignore
        self.assertEqual(coupon_data['createdAt'], coupon.createdAt.astimezone(timezone.get_current_timezone()).isoformat())
        self.assertEqual(coupon_data['updatedAt'], coupon.updatedAt.astimezone(timezone.get_current_timezone()).isoformat())
        self.assertEqual(coupon_data['startTime'], coupon.startTime.astimezone(timezone.get_current_timezone()).isoformat())

    def test_coupon_list_does_not_return_deleted_coupons(self):
        coupon = self.create_coupon()
        coupon.status = 'deleted'
        coupon.save(update_fields=['status'])

        url = reverse('coupons')
        response = self.client.get(url, headers=self.headers)

        self.assertEqual(len(response.data['results']), 0)

    def tes_coupon_list_filter_by_status(self):
        self.create_coupon(status='deleted')
        self.create_coupon(status='active')

        url = reverse('coupons')
        response = self.client.get(url + '?status=deleted', headers=self.headers)

        self.assertEqual(len(response.data['results']), 1)

        response = self.client.get(url + '?status=deleted,active', headers=self.headers)

        self.assertEqual(len(response.data['results']), 2)

    def test_coupon_list_filter_by_code(self):
        coupon = self.create_coupon()
        coupon1 = self.create_coupon(code='Another Coupon')
        coupon1.products.add(self.product)

        url = reverse('coupons')
        response = self.client.get(url + f'?code={coupon.code}', headers=self.headers)

        self.assertEqual(len(response.data['results']), 1)

        response = self.client.get(url + f'?code__icontains={coupon1.code[3:]}', headers=self.headers)

        self.assertEqual(len(response.data['results']), 1)

    def test_coupon_list_filter_by_discount(self):
        self.create_coupon(discount=Decimal('10'))
        self.create_coupon(code='Another Coupon', discount=Decimal('20'))

        url = reverse('coupons')
        response = self.client.get(url + '?discount=10', headers=self.headers)

        self.assertEqual(len(response.data['results']), 1)

        response = self.client.get(url + '?discount__gt=10', headers=self.headers)
        self.assertEqual(len(response.data['results']), 1)

        response = self.client.get(url + '?discount__gte=11', headers=self.headers)
        self.assertEqual(len(response.data['results']), 1)

        response = self.client.get(url + '?discount__lt=20', headers=self.headers)
        self.assertEqual(len(response.data['results']), 1)

        response = self.client.get(url + '?discount__lte=20', headers=self.headers)
        self.assertEqual(len(response.data['results']), 2)

    def test_coupon_list_filter_by_applyOnBumps(self):
        self.create_coupon(applyOnBumps=False)
        self.create_coupon(code='Another Coupon', applyOnBumps=True)

        url = reverse('coupons')
        response = self.client.get(url + '?applyOnBumps=false', headers=self.headers)

        self.assertEqual(len(response.data['results']), 1)

        response = self.client.get(url + '?applyOnBumps=true', headers=self.headers)

        self.assertEqual(len(response.data['results']), 1)

    def test_coupon_list_filter_by_startTime(self):
        now = timezone.now()
        coupon1_datetime = now - timezone.timedelta(days=1)
        coupon2_datetime = now + timezone.timedelta(days=1)
        self.create_coupon(startTime=coupon1_datetime)
        self.create_coupon(code='Another Coupon', startTime=coupon2_datetime)

        url = reverse('coupons')

        response = self.client.get(url + f'?startTime={coupon1_datetime.strftime("%Y-%m-%dT%H:%M:%S.%f-0000")}', headers=self.headers)
        self.assertEqual(len(response.data['results']), 1)

        response = self.client.get(url + f'?startTime__gt={coupon1_datetime.strftime("%Y-%m-%dT%H:%M:%S.%f-0000")}', headers=self.headers)
        self.assertEqual(len(response.data['results']), 1)

        response = self.client.get(url + f'?startTime__gte={coupon1_datetime.strftime("%Y-%m-%dT%H:%M:%S.%f-0000")}', headers=self.headers)
        self.assertEqual(len(response.data['results']), 2)

        response = self.client.get(url + f'?startTime__lt={coupon2_datetime.strftime("%Y-%m-%dT%H:%M:%S.%f-0000")}', headers=self.headers)
        self.assertEqual(len(response.data['results']), 1)

        response = self.client.get(url + f'?startTime__lte={coupon2_datetime.strftime("%Y-%m-%dT%H:%M:%S.%f-0000")}', headers=self.headers)
        self.assertEqual(len(response.data['results']), 2)

    def test_coupon_list_filter_by_createdAt(self):
        coupon1 = self.create_coupon()
        coupon2 = self.create_coupon(code='Another Coupon')

        url = reverse('coupons')

        response = self.client.get(url + f'?createdAt={coupon1.createdAt.strftime("%Y-%m-%dT%H:%M:%S.%f-0000")}', headers=self.headers)
        self.assertEqual(len(response.data['results']), 1)

        response = self.client.get(url + f'?createdAt__gt={coupon1.createdAt.strftime("%Y-%m-%dT%H:%M:%S.%f-0000")}', headers=self.headers)
        self.assertEqual(len(response.data['results']), 1)

        response = self.client.get(url + f'?createdAt__gte={coupon1.createdAt.strftime("%Y-%m-%dT%H:%M:%S.%f-0000")}', headers=self.headers)
        self.assertEqual(len(response.data['results']), 2)

        response = self.client.get(url + f'?createdAt__lt={coupon2.createdAt.strftime("%Y-%m-%dT%H:%M:%S.%f-0000")}', headers=self.headers)
        self.assertEqual(len(response.data['results']), 1)

        response = self.client.get(url + f'?createdAt__lte={coupon2.createdAt.strftime("%Y-%m-%dT%H:%M:%S.%f-0000")}', headers=self.headers)
        self.assertEqual(len(response.data['results']), 2)

    def test_coupon_list_filter_by_updatedAt(self):
        coupon1 = self.create_coupon()
        coupon2 = self.create_coupon(code='Another Coupon')

        url = reverse('coupons')

        response = self.client.get(url + f'?updatedAt={coupon1.updatedAt.strftime("%Y-%m-%dT%H:%M:%S.%f-0000")}', headers=self.headers)
        self.assertEqual(len(response.data['results']), 1)

        response = self.client.get(url + f'?updatedAt__gt={coupon1.updatedAt.strftime("%Y-%m-%dT%H:%M:%S.%f-0000")}', headers=self.headers)
        self.assertEqual(len(response.data['results']), 1)

        response = self.client.get(url + f'?updatedAt__gte={coupon1.updatedAt.strftime("%Y-%m-%dT%H:%M:%S.%f-0000")}', headers=self.headers)
        self.assertEqual(len(response.data['results']), 2)

        response = self.client.get(url + f'?updatedAt__lt={coupon2.updatedAt.strftime("%Y-%m-%dT%H:%M:%S.%f-0000")}', headers=self.headers)
        self.assertEqual(len(response.data['results']), 1)

        response = self.client.get(url + f'?updatedAt__lte={coupon2.updatedAt.strftime("%Y-%m-%dT%H:%M:%S.%f-0000")}', headers=self.headers)
        self.assertEqual(len(response.data['results']), 2)

    def test_coupon_list_filter_by_endTime(self):
        now = timezone.now()
        coupon1_datetime = now - timezone.timedelta(days=1)
        coupon2_datetime = now + timezone.timedelta(days=1)
        self.create_coupon(endTime=coupon1_datetime)
        self.create_coupon(code='Another Coupon', endTime=coupon2_datetime)
        self.create_coupon(code='Another Coupon 2', endTime=None)

        url = reverse('coupons')

        response = self.client.get(url + f'?endTime={coupon1_datetime.strftime("%Y-%m-%dT%H:%M:%S.%f-0000")}', headers=self.headers)
        self.assertEqual(len(response.data['results']), 1)

        response = self.client.get(url + f'?endTime__gt={coupon1_datetime.strftime("%Y-%m-%dT%H:%M:%S.%f-0000")}', headers=self.headers)
        self.assertEqual(len(response.data['results']), 2)

        response = self.client.get(url + f'?endTime__gte={coupon1_datetime.strftime("%Y-%m-%dT%H:%M:%S.%f-0000")}', headers=self.headers)
        self.assertEqual(len(response.data['results']), 3)

        response = self.client.get(url + f'?endTime__lt={coupon2_datetime.strftime("%Y-%m-%dT%H:%M:%S.%f-0000")}', headers=self.headers)
        self.assertEqual(len(response.data['results']), 1)

        response = self.client.get(url + f'?endTime__lte={coupon2_datetime.strftime("%Y-%m-%dT%H:%M:%S.%f-0000")}', headers=self.headers)
        self.assertEqual(len(response.data['results']), 2)

    def test_get_usage_quantity(self):
        coupon = self.create_coupon()

        self.create_order(product=self.product, coupon=coupon)
        # offer_type different from 'main' should not increment usage quantity
        self.create_order(product=self.product, coupon=coupon, offer_type='upsell')

        self.assertEqual(coupon.get_usage_quantity(), 1)

    def test_user_cant_create_coupon_for_another_users_product(self):
        url = reverse('coupons')

        another_user = self.create_user()

        data = {
            'products': [self.product.pk],
            'code': 'TESTCUPOM',
            'discount': 60,
            'startTime': timezone.now(),
        }

        response = self.client.post(url, data, format='json', headers=self.build_user_auth_headers(another_user))

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST, response.content)
        self.assertEqual(Coupon.objects.count(), 0)

    def test_user_cant_update_coupon_products_to_another_users_products(self):
        another_user = self.create_user()
        another_product = self.create_product(user=another_user)

        coupon = self.create_coupon(products=[another_product])

        url = reverse('coupon', args=[coupon.pk])

        data = {
            'products': [self.product.pk],
        }

        response = self.client.patch(url, data, headers=self.build_user_auth_headers(another_user))

        coupon.refresh_from_db()

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST, response.content)
        self.assertNotIn(self.product, coupon.products.all())

    def test_user_cant_update_another_users_products_coupon(self):
        coupon = self.create_coupon()
        url = reverse('coupon', args=[coupon.pk])

        another_user = self.create_user()

        data = {
            'code': 'NEWCODE',
        }

        response = self.client.patch(url, data, headers=self.build_user_auth_headers(another_user))

        coupon.refresh_from_db()

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND, response.content)
        self.assertEqual(coupon.code, 'test')

    def test_can_disable_coupon(self):
        coupon = self.create_coupon()
        url = reverse('coupon', args=[coupon.pk])

        response = self.client.patch(url, {'status': 'disabled'}, headers=self.headers)

        coupon.refresh_from_db()

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content)
        self.assertEqual(coupon.status, 'disabled')

    def test_can_enable_coupon(self):
        coupon = self.create_coupon(status='disabled')
        url = reverse('coupon', args=[coupon.pk])

        response = self.client.patch(url, {'status': 'active'}, headers=self.headers)

        coupon.refresh_from_db()

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content)
        self.assertEqual(coupon.status, 'active')

    def test_validate_coupon_with_dynamic_fees_success(self):
        """Testa validação com taxas dinâmicas quando o cupom é válido"""
        self.product.price = Decimal('100.00')
        self.product.save()

        coupon = self.create_coupon(discount=10)
        offer = self.product.offers.first()

        with mock.patch('product.utils.get_payment_fees_from_cache') as mock_get_fees:
            mock_get_fees.return_value = (Decimal('2.49'), Decimal('0.15'))

            is_valid = validate_coupon_with_dynamic_fees(offer, coupon)

            self.assertTrue(is_valid)
            mock_get_fees.assert_called_once_with(offer)

    def test_validate_coupon_with_dynamic_fees_insufficient(self):
        """Testa validação com taxas dinâmicas quando o valor líquido é insuficiente"""
        self.product.price = Decimal('5.00')
        self.product.save()

        coupon = self.create_coupon(discount=80)
        offer = self.product.offers.first()

        with mock.patch('product.utils.get_payment_fees_from_cache') as mock_get_fees:
            mock_get_fees.return_value = (Decimal('2.49'), Decimal('0.15'))

            is_valid = validate_coupon_with_dynamic_fees(offer, coupon)

            self.assertFalse(is_valid)
            mock_get_fees.assert_called_once_with(offer)

    def test_validate_coupon_with_dynamic_fees_fallback(self):
        """Testa que a validação usa fallback para taxas padrão em caso de erro"""
        self.product.price = Decimal('100.00')
        self.product.save()

        coupon = self.create_coupon(discount=10)
        offer = self.product.offers.first()

        with mock.patch('product.utils.get_payment_fees_from_cache') as mock_get_fees:
            mock_get_fees.side_effect = Exception("Erro ao obter taxas")

            is_valid = validate_coupon_with_dynamic_fees(offer, coupon)

            self.assertTrue(is_valid)
            mock_get_fees.assert_called_once_with(offer)

    def test_coupon_validation_endpoint_with_dynamic_fees(self):
        """Testa que o endpoint de validação usa taxas dinâmicas"""
        self.product.price = Decimal('5.00')
        self.product.save()

        coupon = self.create_coupon(discount=80)
        offer = self.product.offers.first()

        url = reverse('coupon-validate')
        data = {
            'code': coupon.code,
            'offerId': offer.pk,
        }

        response = self.client.post(url, data, headers=self.headers)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('valor final seria menor que as taxas', response.json()['detail'])

    def test_coupon_validate_accepts_uppercase_input(self):
        coupon = self.create_coupon(code='cupom10')
        offer_id = self.product.offers.first().pk
        url = reverse('coupon-validate')

        response = self.client.post(
            url,
            {'code': 'CUPOM10', 'offerId': offer_id},
            headers=self.headers
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_cannot_create_duplicate_coupon_with_different_case(self):
        self.client.post(
            reverse('coupons'),
            {
                'products': [self.product.pk],
                'code': 'CUPOM10',
                'discount': 10,
                'startTime': timezone.now(),
            },
            format='json',
            headers=self.headers
        )

        response = self.client.post(
            reverse('coupons'),
            {
                'products': [self.product.pk],
                'code': 'cupom10',
                'discount': 10,
                'startTime': timezone.now(),
            },
            format='json',
            headers=self.headers
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn(
            'Cupom com este código já existe.',
            response.json().get('code', []),
        )
