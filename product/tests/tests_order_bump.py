from django.urls import reverse
from rest_framework import status
from product.enums import ProductType
from product.models import Offer, OrderBump
from cakto.tests.base import BaseTestCase

class TestOrderBump(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.create_paymentMethods()

        cls.product = cls.create_product(user=cls.user, name='Test product bump')
        cls.extra_offer = Offer.objects.create(product=cls.product, price=10)
        cls.token = cls.get_user_access_token(cls.user)
        cls.headers = cls.create_headers(cls.token)
        cls.bump1 = OrderBump.objects.create(product=cls.product, position=1)
        cls.bump2 = OrderBump.objects.create(product=cls.product, position=2)
        cls.bump3 = OrderBump.objects.create(product=cls.product, position=3)

    def test_position_update(self):
        self.url = reverse('product-bump-position-update', kwargs={'product': self.product.id})

        data = [
            {'id': self.bump1.id, 'position': 3},
            {'id': self.bump2.id, 'position': 1},
            {'id': self.bump3.id, 'position': 2},
        ]

        response = self.client.post(self.url, data, format='json', headers=self.build_user_auth_headers(self.user))

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        self.bump1.refresh_from_db()
        self.bump2.refresh_from_db()
        self.bump3.refresh_from_db()

        self.assertEqual(self.bump1.position, 3)
        self.assertEqual(self.bump2.position, 1)
        self.assertEqual(self.bump3.position, 2)

    def test_create_order_bump_with_offer_subscription_returns_400_when_product_has_not_supported_paymentMethod(self):
        self.url = reverse('product-bump', args=[self.product.id])

        self.product.paymentMethods.set([self.googlepay])

        self.extra_offer.type = ProductType.SUBSCRIPTION.id
        self.extra_offer.save(update_fields=['type'])

        payload = {
            'product': self.product.id,
            'offer': self.extra_offer.id,
            'posisiton': 4,
            'cta': 'Test product subscription bump does not create'
        }

        # Call
        response = self.client.post(
            self.url,
            data=payload,
            format='json',
            headers=self.build_user_auth_headers(self.user)
        )

        # Assert
        self.assertEqual(
            response.status_code,
            status.HTTP_400_BAD_REQUEST,
            response.content.decode()
        )
        self.assertEqual(
            response.json(),
            {'product': [f'Remova o método de pagamento Google Pay do {self.product.name[:30]} para adicionar o orderbump do tipo assinatura.']}
        )

    def test_update_order_bump_with_offer_subscription_returns_400_when_product_has_not_supported_paymentMethod(self):
        self.url = reverse('bump', args=[self.bump1.id])

        self.product.type = ProductType.SUBSCRIPTION.id
        self.product.save(update_fields=['type'])
        self.product.paymentMethods.set([self.googlepay])

        self.extra_offer.type = ProductType.SUBSCRIPTION.id
        self.extra_offer.save(update_fields=['type'])

        self.bump1.offer = self.extra_offer
        self.bump1.save(update_fields=['offer'])

        payload = {
            'product': self.product.id,
            'offer': self.extra_offer.id,
            'posisiton': 4,
            'cta': 'Test product subscription bump does not create'
        }

        # Call
        response = self.client.put(
            self.url,
            data=payload,
            format='json',
            headers=self.build_user_auth_headers(self.user)
        )

        # Assert
        self.assertEqual(
            response.status_code,
            status.HTTP_400_BAD_REQUEST,
            response.content.decode()
        )
        self.assertEqual(
            response.json(),
            {'product': [f'Remova o método de pagamento Google Pay do {self.product.name[:30]} para adicionar o orderbump do tipo assinatura.']}
        )
