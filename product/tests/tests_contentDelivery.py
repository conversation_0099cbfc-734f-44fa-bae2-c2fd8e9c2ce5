from django.core.cache import cache
from django.urls import reverse

from cakto.tests.base import BaseTestCase
from product.models import ContentDelivery


class ContentDeliveryTest(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        cls.cakto, _ = ContentDelivery.objects.get_or_create(type='cakto', name='Área de membros cakto')
        cls.files, _ = ContentDelivery.objects.get_or_create(type='files', name='E-book/Arquivos via E-mail')
        cls.emailAccess, _ = ContentDelivery.objects.get_or_create(type='emailAccess', name='<PERSON> de acesso via E-mail')
        cls.external, _ = ContentDelivery.objects.get_or_create(type='external', name='Área de membros externa')
        cls.disabled, _ = ContentDelivery.objects.get_or_create(type='disabled', name='Desabilitado')
        cls.instagram_pp, _ = ContentDelivery.objects.get_or_create(type='instagram_pp', name='Instagram Perfil Privado')
        cls.instagram_cf, _ = ContentDelivery.objects.get_or_create(type='instagram_cf', name='Instagram Close Friends')
        cls.telegram, _ = ContentDelivery.objects.get_or_create(type='telegram', name='Telegram')
        cls.discord, _ = ContentDelivery.objects.get_or_create(type='discord', name='Discord')

    def test_get_contentDeliveries(self):
        expected_list = [
            {'type': 'cakto', 'name': 'Área de membros cakto'},
            {'type': 'files', 'name': 'E-book/Arquivos via E-mail'},
            {'type': 'emailAccess', 'name': 'Link de acesso via E-mail'},
            {'type': 'external', 'name': 'Área de membros externa'},
            {'type': 'disabled', 'name': 'Desabilitado'},
            {'type': 'telegram', 'name': 'Telegram'},
            {'name': 'Discord', 'type': 'discord'},
            {'name': 'Instagram Perfil Privado', 'type': 'instagram_pp'},
            {'name': 'Instagram Close Friends', 'type': 'instagram_cf'},
        ]

        response = self.client.get(reverse('products-content-delivery-methods'))

        self.assertEqual(response.status_code, 200, response.content)
        self.assertEqual(sorted(response.json(), key=lambda x: x['type']), sorted(expected_list, key=lambda x: x['type']))

    def test_contentDeliveries_invalidate_cache_on_save(self):
        cache.set(ContentDelivery.cache_key, 'old_value')

        self.cakto.save()

        self.assertIsNone(cache.get(ContentDelivery.cache_key))

    def test_contentDeliveries_invalidate_cache_on_create(self):
        cache.set(ContentDelivery.cache_key, 'old_value')

        ContentDelivery.objects.create(type='contentThatDoesNotExists', name='contentThatDoesNotExists')

        self.assertIsNone(cache.get(ContentDelivery.cache_key))

    def test_contentDeliveries_invalidate_cache_on_delete(self):
        cache.set(ContentDelivery.cache_key, 'old_value')

        self.cakto.delete()

        self.assertIsNone(cache.get(ContentDelivery.cache_key))

    def test_contentDeliveries_get_contentDeliveries_display(self):
        ContentDelivery.objects.create(type='test', name='Test')

        self.assertEqual(ContentDelivery.get_contentDeliveries_display('cakto'), 'Área de membros cakto')
        self.assertEqual(ContentDelivery.get_contentDeliveries_display('test'), 'Test')

        ContentDelivery.objects.filter(type='test', name='Test').delete()
        cache.clear()

    def test_contentDeliveries_is_cached(self):
        url = reverse('products-content-delivery-methods')

        cache.clear()

        with self.assertNumQueries(1):
            self.client.get(url)

        with self.assertNumQueries(0):
            self.client.get(url)

    def test_contentDeliveries_instances_is_cached(self):
        cache.clear()

        with self.assertNumQueries(1):
            ContentDelivery.get_contentDelivery_instance('emailAccess')

        with self.assertNumQueries(0):
            ContentDelivery.get_contentDelivery_instance('emailAccess')

    def test_contentDeliveries_get_valid_contentDelivery_types(self):
        expected_list = ['cakto', 'files', 'emailAccess', 'external', 'disabled', 'telegram', 'discord', 'instagram_pp', 'instagram_cf']
        result = ContentDelivery.get_valid_contentDelivery_types()

        self.assertEqual(sorted(result), sorted(expected_list))

    def test_contentDeliveries_filter_payment_method_instances(self):
        filter_types = ['cakto', 'files', 'emailAccess']

        expected_list = list(ContentDelivery.objects.filter(type__in=filter_types, status='active'))

        result = ContentDelivery.filter_contentDelivery_instances(filter_types)

        expected_list.sort(key=lambda x: x.type)
        result.sort(key=lambda x: x.type)

        self.assertEqual(result, expected_list)
