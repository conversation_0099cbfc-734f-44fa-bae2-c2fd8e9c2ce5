import pytest

from product.models import Checkout, Offer, Product


@pytest.mark.django_db
def test_checkout_with_valid_checkout_id(
    product: Product,
    checkout: Checkout,
    offer: Offer
):
    result = offer.checkout(checkout_id=checkout.id)
    assert result == checkout

@pytest.mark.django_db
def test_checkout_with_no_checkout_id_and_custom_checkout(
    product: Product,
    checkout: Checkout,
    offer: Offer
):
    assert Checkout.objects.filter(product=product, offers=offer).exists()
    result = offer.checkout()

    assert result == checkout

@pytest.mark.django_db
def checkout_with_no_checkout_id_and_default_checkout(
    product: Product,
    offer: Offer,
    default_checkout_without_offer: Checkout,
):
    assert offer.product == product
    assert default_checkout_without_offer.product == product
    assert offer.product == default_checkout_without_offer.product

    result = offer.checkout()
    assert result == default_checkout_without_offer

@pytest.mark.django_db
def test_checkout_with_no_checkout_id_and_no_custom_or_default_checkout(
    product: Product,
    offer: Offer,
):
    assert Checkout.objects.filter(product=product, offers=offer).exists() is False
    result = offer.checkout()
    assert result is None
