from decimal import Decimal

from django.urls import reverse
from django.utils import timezone

from cakto.tests.base import BaseTestCase
from product.models import Product


class AffiliateTotalSalesAPIViewTests(BaseTestCase):
    def test_get_total_sales_with_affiliates(self):
        now = timezone.datetime.strptime('2021-01-01T12:00Z+0000', '%Y-%m-%dT%H:%MZ%z')
        start_date = now.strftime('%d-%m-%Y')
        end_date = now.strftime('%d-%m-%Y')

        product = Product.objects.create(
            name='Product 1',
            price=50.00,
            status='active',
            user=self.user,
            affiliate=True,
            total_sales=100.00,
        )

        product.createdAt = now
        product.save()

        start_date = now.strftime('%d-%m-%Y')

        url = reverse('affiliate-total-sales') + f'?startDate={start_date}&endDate={end_date}'
        response = self.client.get(url, headers=self.build_user_auth_headers(self.user))

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['total_sales_with_affiliates'], Decimal('100.00'))

    def test_get_total_sales_with_affiliates_without_product(self):
        now = timezone.datetime.strptime('2021-01-01T12:00Z+0000', '%Y-%m-%dT%H:%MZ%z')
        start_date = now.strftime('%d-%m-%Y')
        end_date = now.strftime('%d-%m-%Y')

        url = reverse('affiliate-total-sales') + f'?startDate={start_date}&endDate={end_date}'
        response = self.client.get(url, headers=self.build_user_auth_headers(self.user))

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['total_sales_with_affiliates'], 0.0)

    def test_get_total_sales_without_affiliates(self):
        now = timezone.datetime.strptime('2021-01-01T12:00Z+0000', '%Y-%m-%dT%H:%MZ%z')
        start_date = now.strftime('%d-%m-%Y')
        end_date = now.strftime('%d-%m-%Y')

        product = Product.objects.create(
            name='Product 2',
            price=5.00,
            status='active',
            user=self.user,
            affiliate=False,
            total_sales=35.00,
        )

        product.createdAt = now
        product.save()

        url = reverse('affiliate-total-sales') + f'?startDate={start_date}&endDate={end_date}'
        response = self.client.get(url, headers=self.build_user_auth_headers(self.user))

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['total_sales_without_affiliates'], Decimal('35.00'))

    def test_get_total_sales_without_affiliates_without_product(self):
        now = timezone.datetime.strptime('2021-01-01T12:00Z+0000', '%Y-%m-%dT%H:%MZ%z')
        start_date = now.strftime('%d-%m-%Y')
        end_date = now.strftime('%d-%m-%Y')

        url = reverse('affiliate-total-sales') + f'?startDate={start_date}&endDate={end_date}'
        response = self.client.get(url, headers=self.build_user_auth_headers(self.user))

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['total_sales_without_affiliates'], 0.0)

    def test_affiliates_per_product(self):
        now = timezone.datetime.strptime('2021-01-01T12:00Z+0000', '%Y-%m-%dT%H:%MZ%z')
        start_date = now.strftime('%d-%m-%Y')
        end_date = now.strftime('%d-%m-%Y')

        for i in range(5):
            product = Product.objects.create(
                name=f'Product {i}',
                price=10.00,
                status='active',
                user=self.user,
                affiliate=True,
                total_sales=10.00 * i,
            )
            product.createdAt = now
            product.save()

        url = reverse('affiliate-total-sales') + f'?startDate={start_date}&endDate={end_date}'
        response = self.client.get(url, headers=self.build_user_auth_headers(self.user))

        self.assertEqual(response.status_code, 200)
        response_data = list(response.data['affiliates_per_product'])
        self.assertEqual(response_data[0]['total_sales'], Decimal('40.00'))
        self.assertEqual(response_data[0]['name'], 'Product 4')

    def test_affiliates_per_product_without_product(self):
        now = timezone.datetime.strptime('2021-01-01T12:00Z+0000', '%Y-%m-%dT%H:%MZ%z')
        start_date = now.strftime('%d-%m-%Y')
        end_date = now.strftime('%d-%m-%Y')

        url = reverse('affiliate-total-sales') + f'?startDate={start_date}&endDate={end_date}'
        response = self.client.get(url, headers=self.build_user_auth_headers(self.user))

        self.assertEqual(response.status_code, 200)
        response_data = list(response.data['affiliates_per_product'])
        self.assertEqual(len(response_data), 0)

    def test_get_top_3_affiliates(self):
        now = timezone.datetime.strptime('2021-01-01T12:00Z+0000', '%Y-%m-%dT%H:%MZ%z')
        start_date = now.strftime('%d-%m-%Y')
        end_date = now.strftime('%d-%m-%Y')

        for i in range(5):
            product = Product.objects.create(
                name=f'Product {i}',
                price=10.00,
                status='active',
                user=self.user,
                affiliate=True,
                total_sales=10.00 * i,
            )
            product.createdAt = now
            product.save()

        url = reverse('affiliate-total-sales') + f'?startDate={start_date}&endDate={end_date}'
        response = self.client.get(url, headers=self.build_user_auth_headers(self.user))

        response_data = list(response.data['top_3_affiliates'])
        total_sales_response = sum([Decimal(item['total_sales']) for item in response_data])
        total_percentage = round(sum([item['percentage'] for item in response_data]), 2)

        self.assertEqual(response.status_code, 200)
        self.assertEqual(total_sales_response, 90.00)
        self.assertEqual(len(response_data), 3)
        self.assertEqual(total_percentage, Decimal('99.99') or Decimal('100.00'))  # 100% is expected, but due to rounding, it is 99.99

    def test_get_top_3_affiliates_without_product(self):
        now = timezone.datetime.strptime('2021-01-01T12:00Z+0000', '%Y-%m-%dT%H:%MZ%z')
        start_date = now.strftime('%d-%m-%Y')
        end_date = now.strftime('%d-%m-%Y')

        url = reverse('affiliate-total-sales') + f'?startDate={start_date}&endDate={end_date}'
        response = self.client.get(url, headers=self.build_user_auth_headers(self.user))

        response_data = list(response.data['top_3_affiliates'])

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response_data), 0)
