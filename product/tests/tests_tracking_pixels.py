from django.core.cache import cache

from cakto.tests.base import BaseTestCase
from product.models import Affiliate, TrackingPixels


class TrackingPixelsTest(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()

        cls.product = cls.create_product(user=cls.user)

        cls.tracking_pixels = TrackingPixels.objects.create(product=cls.product)

        cls.headers = cls.build_user_auth_headers(cls.user)

    def test_tracking_pixels_get_cache_keys(self):
        offer = self.product.offers.first()

        expected_keys = [
            f'{TrackingPixels.instance_cache_prefix}{self.product.short_id}',
            offer.cache_key,
            f'{TrackingPixels.data_cache_prefix}{self.product.short_id}_{self.tracking_pixels.pk}'
        ]

        cache_keys = self.tracking_pixels.get_cache_keys()

        self.assertEqual(sorted(expected_keys), sorted(cache_keys))

    def test_tracking_pixels_get_cache_keys_with_affiliate(self):
        offer = self.product.offers.first()

        affiliate = Affiliate.objects.create(user=self.user, product=self.product, commission=1)
        self.tracking_pixels.affiliate = affiliate
        self.tracking_pixels.save()

        expected_keys = [
            f'{TrackingPixels.instance_cache_prefix}{self.product.short_id}_{affiliate.short_id}',
            offer.cache_key,
            f'{TrackingPixels.data_cache_prefix}{self.product.short_id}_{self.tracking_pixels.pk}'
        ]

        cache_keys = self.tracking_pixels.get_cache_keys()

        self.assertEqual(sorted(expected_keys), sorted(cache_keys))

    def test_tracking_pixels_invalidate_cache(self):
        cache_keys = self.tracking_pixels.get_cache_keys()

        for key in cache_keys:
            cache.set(key, 'Test invalidate cache', None)

        self.tracking_pixels.invalidate_cache()

        with self.subTest('Tracking Pixel cache should be empty'):
            for key in cache_keys:
                self.assertIsNone(cache.get(key))

    def test_tracking_pixels_cache_is_invalidated_when_save(self):
        cache_keys = self.tracking_pixels.get_cache_keys()

        for key in cache_keys:
            cache.set(key, 'Test invalidate cache', None)

        self.tracking_pixels.save()

        with self.subTest('Tracking Pixel cache should be empty'):
            for key in cache_keys:
                self.assertIsNone(cache.get(key))

    def test_tracking_pixels_cache_is_invalidated_when_save_with_affiliate(self):
        affiliate = Affiliate.objects.create(user=self.user, product=self.product, commission=1)
        self.tracking_pixels.affiliate = affiliate
        self.tracking_pixels.save()

        cache_keys = self.tracking_pixels.get_cache_keys()

        for key in cache_keys:
            cache.set(key, 'Test invalidate cache', None)

        self.tracking_pixels.save()

        with self.subTest('Tracking Pixel cache should be empty'):
            for key in cache_keys:
                self.assertIsNone(cache.get(key))

    def test_tracking_pixels_bulk_invalidate_cache(self):
        tracking_pixels_2 = TrackingPixels.objects.create(product=self.create_product())

        cache_keys = list(self.tracking_pixels.get_cache_keys())
        cache_keys.extend(tracking_pixels_2.get_cache_keys())

        for key in cache_keys:
            cache.set(key, 'Test invalidate cache', None)

        TrackingPixels.bulk_invalidate_cache([self.tracking_pixels, tracking_pixels_2])

        with self.subTest('Tracking Pixel cache should be empty'):
            for key in cache_keys:
                self.assertIsNone(cache.get(key))
