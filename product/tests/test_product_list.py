from django.urls import reverse
from django.utils import timezone
from rest_framework import status

from cakto.tests.base import BaseTestCase
from product.models import Affiliate, Category, ContentDelivery, Coproduction, Product


class ProductListTest(BaseTestCase):
    url = reverse('products')

    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()

        cls.category = Category.objects.create(name='Test Category')

        cls.product = cls.create_product(
            user=cls.user,
            id='testid',
            name='Test Product',
            price=100.0,
            description='desc',
            status='active',
            statusAdmin='approved',
            type='unique',
            total_sales=10_000,
            affiliate=True,
            affiliateMarketplace=True,
            hideShowcase=False,
            category=cls.category,
        )

        cakto_delivery, _ = ContentDelivery.objects.get_or_create(
            type='cakto',
            defaults={'name': 'Área de membros cakto'}
        )

        cls.product.contentDeliveries.add(cakto_delivery)

        cls.product2 = cls.create_product(
            user=cls.user,
            id='otherid',
            name='Other Product',
            price=200.0,
            description='otherdesc',
            statusAdmin='block_analisis',
            status='blocked',
            type='other',
        )
        cls.product2.createdAt = timezone.now() - timezone.timedelta(days=10)
        cls.product2.save()

        cls.headers = cls.build_user_auth_headers(cls.user)

    def test_filter_id(self):
        response = self.client.get(f'{self.url}?id={self.product.id}', headers=self.headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        data = response.json()

        self.assertEqual(data['results'][0]['id'], self.product.id)
        self.assertEqual(data['count'], 1)
        self.assertEqual(len(data['results']), 1)

    def test_filter_name(self):
        response = self.client.get(f'{self.url}?name=Test', headers=self.headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        data = response.json()

        self.assertEqual(data['results'][0]['id'], self.product.id)
        self.assertEqual(data['count'], 1)
        self.assertEqual(len(data['results']), 1)

    def test_filter_status(self):
        response = self.client.get(f'{self.url}?status=active', headers=self.headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        data = response.json()

        self.assertEqual(data['results'][0]['id'], self.product.id)
        self.assertEqual(data['count'], 1)
        self.assertEqual(len(data['results']), 1)

    def test_filter_statusAdmin(self):
        response = self.client.get(f'{self.url}?statusAdmin=approved', headers=self.headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        data = response.json()

        self.assertEqual(data['results'][0]['id'], self.product.id)
        self.assertEqual(data['count'], 1)
        self.assertEqual(len(data['results']), 1)

    def test_filter_userEmail(self):
        url = reverse('admin-products-list')

        self.user.is_staff = True
        self.user.save()

        self.product2.user = self.create_user()
        self.product2.save()

        response = self.client.get(f'{url}?userEmail={self.user.email}', headers=self.headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        data = response.json()

        self.assertEqual(data['results'][0]['id'], self.product.id)
        self.assertEqual(data['count'], 1)
        self.assertEqual(len(data['results']), 1)

    def test_filter_type(self):
        response = self.client.get(f'{self.url}?type=unique', headers=self.headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        data = response.json()

        self.assertEqual(data['results'][0]['id'], self.product.id)
        self.assertEqual(data['count'], 1)
        self.assertEqual(len(data['results']), 1)

    def test_filter_contentDelivery(self):
        response = self.client.get(f'{self.url}?contentDelivery=cakto', headers=self.headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        data = response.json()

        self.assertEqual(data['results'][0]['id'], self.product.id)
        self.assertEqual(data['count'], 1)
        self.assertEqual(len(data['results']), 1)

    def test_filter_category(self):
        response = self.client.get(f'{self.url}?category={self.category.id}', headers=self.headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        data = response.json()

        self.assertEqual(data['results'][0]['id'], self.product.id)
        self.assertEqual(data['count'], 1)
        self.assertEqual(len(data['results']), 1)

    def test_filter_displayedInShowcase(self):
        response = self.client.get(f'{self.url}?displayedInShowcase=true', headers=self.headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        data = response.json()

        self.assertEqual(data['results'][0]['id'], self.product.id)
        self.assertEqual(data['count'], 1)
        self.assertEqual(len(data['results']), 1)

    def test_filter_price(self):
        response = self.client.get(f'{self.url}?price={self.product.price}', headers=self.headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        data = response.json()

        self.assertEqual(data['results'][0]['id'], self.product.id)
        self.assertEqual(data['count'], 1)
        self.assertEqual(len(data['results']), 1)

    def test_filter_createdAt(self):
        start_date = self.product.createdAt.date() - timezone.timedelta(days=1)
        end_date = self.product.createdAt.date() + timezone.timedelta(days=1)

        response = self.client.get(f'{self.url}?createdAt__gte={start_date}&createdAt__lte={end_date}', headers=self.headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        data = response.json()

        self.assertEqual(data['results'][0]['id'], self.product.id)
        self.assertEqual(data['count'], 1)
        self.assertEqual(len(data['results']), 1)

    def test_filter_updatedAt(self):
        # Update the updatedAt field by filter().update() to does not trigger the auto_now
        Product.objects.filter(id=self.product2.id).update(updatedAt=self.product2.createdAt)

        start_date = self.product.updatedAt.date() - timezone.timedelta(days=1)
        end_date = self.product.updatedAt.date() + timezone.timedelta(days=1)

        response = self.client.get(f'{self.url}?updatedAt__gte={start_date}&updatedAt__lte={end_date}', headers=self.headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        data = response.json()

        self.assertEqual(data['results'][0]['id'], self.product.id)
        self.assertEqual(data['count'], 1)
        self.assertEqual(len(data['results']), 1)

    def test_filter_productionType_coproducer(self):
        product3 = self.create_product()

        Coproduction.objects.create(
            user=self.user,
            product=product3,
            status='active',
            amount=50.0,
        )

        response = self.client.get(f'{self.url}?productionType=coproducer', headers=self.headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        data = response.json()

        self.assertEqual(data['results'][0]['id'], product3.id)
        self.assertEqual(data['count'], 1)
        self.assertEqual(len(data['results']), 1)

    def test_filter_productionType_producer(self):
        product3 = self.create_product()

        Coproduction.objects.create(
            user=self.user,
            product=product3,
            status='active',
            amount=50.0,
        )

        response = self.client.get(f'{self.url}?productionType=producer', headers=self.headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        data = response.json()

        self.assertEqual(data['results'][0]['id'], self.product.id)
        self.assertEqual(data['results'][1]['id'], self.product2.id)
        self.assertEqual(data['count'], 2)
        self.assertEqual(len(data['results']), 2)

    def test_filter_productionType_affiliate(self):
        product3 = self.create_product()

        Affiliate.objects.create(
            user=self.user,
            product=product3,
            status='active',
            commission=50.0,
        )

        response = self.client.get(f'{self.url}?productionType=affiliate', headers=self.headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        data = response.json()

        self.assertEqual(data['results'][0]['id'], product3.id)
        self.assertEqual(data['count'], 1)
        self.assertEqual(len(data['results']), 1)

    def test_filter_productionType_empty(self):
        product3 = self.create_product()

        Coproduction.objects.create(
            user=self.user,
            product=product3,
            status='active',
            amount=50.0,
        )

        response = self.client.get(self.url, headers=self.headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        data = response.json()

        self.assertEqual(data['results'][0]['id'], self.product.id)
        self.assertEqual(data['results'][1]['id'], self.product2.id)
        self.assertEqual(data['count'], 2)
        self.assertEqual(len(data['results']), 2)

    def test_user_admin_can_get_all_products(self):
        Product.objects.all().delete()

        url = reverse('admin-products-list')

        self.user.is_staff = True
        self.user.save()

        # Create 4 products with other users
        for _ in range(4):
            self.create_product(user=self.create_user())

        response = self.client.get(f'{url}', headers=self.headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        data = response.json()

        self.assertEqual(data['count'], 4)
        self.assertEqual(len(data['results']), 4)
