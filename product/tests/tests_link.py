from cakto.tests.base import BaseTestCase
from product.models import Link


class TestLink(BaseTestCase):
    def test_link_validate_short_id_min_length(self):
        result = Link.validate_short_id("2" * 7)
        self.assertEqual(result, "2" * 7)

    def test_link_validate_short_id_max_length(self):
        result = Link.validate_short_id("2" * 40)
        self.assertEqual(result, "2" * 40)

    def test_link_validate_short_id_invalid_lengths(self):
        result = Link.validate_short_id("2" * 5)
        self.assertIsNone(result)

        result = Link.validate_short_id("2" * 41)
        self.assertIsNone(result)

    def test_link_validate_short_id_invalid_characters(self):
        result = Link.validate_short_id("2345 #$  BC->>999 \\x \n|\n")
        self.assertEqual(result, "2345BC999x")

    def test_link_validate_short_id_empty(self):
        result = Link.validate_short_id("")
        self.assertIsNone(result)
