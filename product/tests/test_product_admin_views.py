from django.urls import reverse
from rest_framework import status

from cakto.tests.base import BaseTestCase


class ProductAdminViewsTest(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()

        cls.create_paymentMethods()

        cls.product = cls.create_product(user=cls.user, paymentMethod='credit_card')
        cls.product.paymentMethods.set([cls.credit_card, cls.pix])

        cls.headers = cls.build_user_auth_headers(cls.user)

        cls.admin_user = cls.create_user(is_staff=True)

        cls.admin_headers = cls.build_user_auth_headers(cls.admin_user)

    def test_product_admin_card_unblock(self):
        url = reverse('admin-products-unblock-card', args=[self.product.pk])

        expected_payment_method = ['pix', 'credit_card']

        self.product.paymentMethods.set([self.pix])
        self.product.additionalInfo['paymentMethodBeforeBlock'] = expected_payment_method
        self.product.additionalInfo['cardBlocked'] = True
        self.product.save()

        response = self.client.post(url, headers=self.admin_headers)
        self.product.refresh_from_db()

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content)
        self.assertEqual(self.product.additionalInfo.get('cardBlocked'), False)
        self.assertEqual(sorted(list(self.product.paymentMethods.values_list('type', flat=True))), sorted(expected_payment_method))

    def test_product_admin_card_block(self):
        url = reverse('admin-products-block-card', args=[self.product.pk])

        response = self.client.post(url, headers=self.admin_headers)
        self.product.refresh_from_db()

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content)
        self.assertEqual(self.product.additionalInfo.get('cardBlocked'), True)

    def test_product_admin_hide_showcase_toggle(self):
        url = reverse('admin-products-hideshowcase', args=[self.product.pk])

        self.product.hideShowcase = False
        self.product.save()

        response = self.client.post(url, headers=self.admin_headers)
        self.product.refresh_from_db()

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content)
        self.assertTrue(self.product.hideShowcase)
        self.assertEqual(response.json(), {"detail": "Produto escondido da vitrine com sucesso"})

        response = self.client.post(url, headers=self.admin_headers)
        self.product.refresh_from_db()

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content)
        self.assertFalse(self.product.hideShowcase)
        self.assertEqual(response.json(), {"detail": "Produto exibido na vitrine com sucesso"})

    def test_product_admin_showcase_sales_filter_toggle(self):
        url = reverse('admin-products-showcase-sales-filter', args=[self.product.pk])

        self.product.bypassShowcaseSalesFilter = True
        self.product.save()

        response = self.client.post(url, headers=self.admin_headers)

        self.product.refresh_from_db()

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())
        self.assertFalse(self.product.bypassShowcaseSalesFilter)

        # Call
        response = self.client.post(url, headers=self.admin_headers)

        self.product.refresh_from_db()

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())
        self.assertTrue(self.product.bypassShowcaseSalesFilter)

    def test_common_user_cant_update_product_admin(self):
        url = reverse('admin-products-retrieve', args=[self.product.pk])

        response = self.client.put(url, data={'hideShowcase': True}, headers=self.headers)

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN, response.content.decode())

    def test_common_user_cant_get_product_admin(self):
        url = reverse('admin-products-retrieve', args=[self.product.pk])

        response = self.client.get(url, headers=self.headers)

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN, response.content.decode())

    def test_common_user_cant_list_product_admin(self):
        url = reverse('admin-products-list')

        response = self.client.get(url, headers=self.headers)

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN, response.content.decode())

    def test_product_admin_update_change_threeDsRetryEnabled(self):
        self.product.threeDsRetryEnabled = True
        self.product.save()
        self.assertTrue(self.product.threeDsRetryEnabled)

        url = reverse('admin-products-retrieve', args=[self.product.pk])

        payload = {
            'threeDsRetryEnabled': False,
        }

        response = self.client.put(url, data=payload, headers=self.admin_headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())

        result = response.json().get('threeDsRetryEnabled')

        self.assertTrue(isinstance(result, bool))
        self.assertFalse(result)

    def test_product_admin_update_change_refundRequest(self):
        self.product.refundRequest = True
        self.product.save()
        self.assertTrue(self.product.refundRequest)

        url = reverse('admin-products-retrieve', args=[self.product.pk])

        payload = {
            'refundRequest': False,
        }

        response = self.client.put(url, data=payload, headers=self.admin_headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())

        result = response.json().get('refundRequest')

        self.assertTrue(isinstance(result, bool))
        self.assertFalse(result)

    def test_product_admin_update_change_hideShowcase(self):
        self.product.hideShowcase = True
        self.product.save()
        self.assertTrue(self.product.hideShowcase)

        url = reverse('admin-products-retrieve', args=[self.product.pk])

        payload = {
            'hideShowcase': False,
        }

        response = self.client.put(url, data=payload, headers=self.admin_headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())

        result = response.json().get('hideShowcase')

        self.assertTrue(isinstance(result, bool))
        self.assertFalse(result)

    def test_product_admin_update_change_bypassShowcaseSalesFilter(self):
        self.product.bypassShowcaseSalesFilter = True
        self.product.save()
        self.assertTrue(self.product.bypassShowcaseSalesFilter)

        url = reverse('admin-products-retrieve', args=[self.product.pk])

        payload = {
            'bypassShowcaseSalesFilter': False,
        }

        response = self.client.put(url, data=payload, headers=self.admin_headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())

        result = response.json().get('bypassShowcaseSalesFilter')

        self.assertTrue(isinstance(result, bool))
        self.assertFalse(result)
