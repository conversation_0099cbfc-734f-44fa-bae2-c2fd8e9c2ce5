from django.core.cache import cache
from django.urls import reverse

from cakto.tests.base import BaseTestCase
from product.enums import PaymentMethodStatus
from product.models import PaymentMethod
from user.models import ExperimentalFeature


class TestPaymentMethod(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.create_paymentMethods()

        cls.tester_payment_method = PaymentMethod.objects.create(
            type='beta_method',
            name='Beta Method',
            status=PaymentMethodStatus.TESTER_USER_ACCESS.id,
        )

        cls.tester_feature = ExperimentalFeature.objects.create(
            id=cls.tester_payment_method.type,
            name=cls.tester_payment_method.name,
        )

    def test_get_payment_methods_without_authentication_returns_active_and_experimental_payments(self):
        cache.clear()

        self.applepay.status = PaymentMethodStatus.DISABLED
        self.applepay.save()

        expected_list = [
            # {'type': 'applepay', 'name': 'Apple Pay'},  # Disabled
            {'type': 'beta_method', 'name': 'Beta Method'},
            {'type': 'boleto', 'name': '<PERSON><PERSON><PERSON>'},
            {'type': 'credit_card', 'name': 'Cartão de Crédito'},
            {'type': 'googlepay', 'name': 'Google Pay'},
            {'type': 'openfinance_nubank', 'name': 'Nubank'},
            {'type': 'picpay', 'name': 'PicPay'}, {'type': 'pix', 'name': 'Pix'}
        ]

        response = self.client.get(reverse('products-payment-methods'))

        self.assertEqual(response.status_code, 200)
        self.assertEqual(
            sorted(response.json(), key=lambda x: x['type']),
            sorted(expected_list, key=lambda x: x['type']),
            msg=sorted(response.json(), key=lambda x: x['type']),
        )

    def test_get_payment_methods_with_authentication_returns_active_and_experimental_payments_that_user_has_access(self):
        cache.clear()

        self.user.experimental_features.add(self.tester_feature)

        expected_list = [
            {'type': 'applepay', 'name': 'Apple Pay'},  # Disabled
            {'type': 'beta_method', 'name': 'Beta Method'},
            {'type': 'boleto', 'name': 'Boleto'},
            {'type': 'credit_card', 'name': 'Cartão de Crédito'},
            {'type': 'googlepay', 'name': 'Google Pay'},
            {'type': 'openfinance_nubank', 'name': 'Nubank'},
            {'type': 'picpay', 'name': 'PicPay'}, {'type': 'pix', 'name': 'Pix'}
        ]

        response = self.client.get(
            reverse('products-payment-methods'),
            headers=self.build_user_auth_headers(self.user)
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(
            sorted(response.json(), key=lambda x: x['type']),
            sorted(expected_list, key=lambda x: x['type']),
            msg=sorted(response.json(), key=lambda x: x['type']),
        )

    def test_get_payment_methods_with_authentication_does_not_returns_experimental_payments_if_user_has_no_access(self):
        cache.clear()

        self.user.experimental_features.remove(self.tester_feature)

        expected_list = [
            {'type': 'applepay', 'name': 'Apple Pay'},  # Disabled
            {'type': 'boleto', 'name': 'Boleto'},
            {'type': 'credit_card', 'name': 'Cartão de Crédito'},
            {'type': 'googlepay', 'name': 'Google Pay'},
            {'type': 'openfinance_nubank', 'name': 'Nubank'},
            {'type': 'picpay', 'name': 'PicPay'}, {'type': 'pix', 'name': 'Pix'}
        ]

        response = self.client.get(
            reverse('products-payment-methods'),
            headers=self.build_user_auth_headers(self.user)
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(
            sorted(response.json(), key=lambda x: x['type']),
            sorted(expected_list, key=lambda x: x['type']),
            msg=sorted(response.json(), key=lambda x: x['type']),
        )

    def test_payment_methods_invalidate_cache_on_save(self):
        cache.set(PaymentMethod.cache_key, 'old_value')

        self.pix.save()

        self.assertIsNone(cache.get(PaymentMethod.cache_key))

    def test_payment_methods_invalidate_cache_on_create(self):
        cache.set(PaymentMethod.cache_key, 'old_value')

        PaymentMethod.objects.create(type='methodThatDoesNotExists', name='methodThatDoesNotExists')

        self.assertIsNone(cache.get(PaymentMethod.cache_key))

    def test_payment_methods_invalidate_cache_on_delete(self):
        cache.set(PaymentMethod.cache_key, 'old_value')

        self.pix.delete()

        self.assertIsNone(cache.get(PaymentMethod.cache_key))

    def test_payment_methods_get_payment_display(self):
        PaymentMethod.objects.create(type='test', name='Test')

        self.assertEqual(PaymentMethod.get_payment_display('pix'), 'Pix')
        self.assertEqual(PaymentMethod.get_payment_display('test'), 'Test')

        PaymentMethod.objects.filter(type='test', name='Test').delete()

    def test_products_payment_methods_endpoint_is_cached(self):
        url = reverse('products-payment-methods')

        cache.clear()

        with self.assertNumQueries(1):
            self.client.get(url)

        with self.assertNumQueries(0):
            self.client.get(url)

    def test_get_payment_methods_is_cache(self):
        cache.clear()

        with self.assertNumQueries(1):
            PaymentMethod.get_payment_methods()

        with self.assertNumQueries(0):
            PaymentMethod.get_payment_methods()

    def test_payment_methods_instances_is_cached(self):
        cache.clear()

        with self.assertNumQueries(1):
            PaymentMethod.get_payment_method_instance('pix')

        with self.assertNumQueries(0):
            PaymentMethod.get_payment_method_instance('pix')

    def test_get_serializer_payment_methods_is_cached(self):
        cache.clear()

        with self.assertNumQueries(1):
            PaymentMethod.get_serialized_payment_methods()

        with self.assertNumQueries(0):
            PaymentMethod.get_serialized_payment_methods()

    def test_get_valid_payment_methods_types_is_cached(self):
        cache.clear()

        with self.assertNumQueries(1):
            PaymentMethod.get_valid_payment_methods_types()

        with self.assertNumQueries(0):
            PaymentMethod.get_valid_payment_methods_types()

    def test_get_payment_display_is_cached(self):
        cache.clear()

        with self.assertNumQueries(1):
            PaymentMethod.get_payment_display('pix')

        with self.assertNumQueries(0):
            PaymentMethod.get_payment_display('pix')

    def test_payment_methods_get_valid_payment_methods_types(self):
        expected_list = [
            'pix', 'boleto', 'credit_card', 'picpay', 'googlepay', 'applepay',
            'openfinance_nubank', 'threeDs'
        ]
        result = PaymentMethod.get_valid_payment_methods_types()

        self.assertEqual(sorted(result), sorted(expected_list))

    def test_payment_methods_filter_payment_method_instances(self):
        filter_types = ['pix', 'boleto', 'credit_card', 'picpay', 'googlepay', 'applepay']

        expected_list = list(PaymentMethod.objects.filter(type__in=filter_types, status=PaymentMethodStatus.ACTIVE.id))

        result = PaymentMethod.get_payment_methods(types=filter_types)

        expected_list.sort(key=lambda x: x.type)
        result.sort(key=lambda x: x.type)

        self.assertEqual(result, expected_list)

    def test_get_payment_methods_all(self):
        expected_list = PaymentMethod.objects.filter(
            status=PaymentMethodStatus.ACTIVE.id,
        )

        result = PaymentMethod.get_payment_methods(types='all')

        self.assertEqual(
            sorted(result, key=lambda x: x.type),
            sorted(expected_list, key=lambda x: x.type),
        )

    def test_get_payment_methods_pix_boleto(self):
        expected_list = list(PaymentMethod.objects.filter(
            type__in=['pix', 'boleto'],
            status=PaymentMethodStatus.ACTIVE.id),
        )

        result = PaymentMethod.get_payment_methods(types=['pix', 'boleto'])

        self.assertEqual(
            sorted(result, key=lambda x: x.type),
            sorted(expected_list, key=lambda x: x.type),
        )

    def test_get_payment_methods_all_statuses(self):
        expected_list = PaymentMethod.objects.filter(
            status=PaymentMethodStatus.ACTIVE.id,
        )

        result = PaymentMethod.get_payment_methods(statuses='all')

        self.assertEqual(
            sorted(result, key=lambda x: x.type),
            sorted(expected_list, key=lambda x: x.type),
        )

    def test_get_payment_methods_disabled_active(self):
        self.pix.status = PaymentMethodStatus.DISABLED
        self.pix.save(update_fields=['status'])

        expected_list = PaymentMethod.objects.filter(
            status__in=[PaymentMethodStatus.DISABLED.id, PaymentMethodStatus.ACTIVE.id]
        )

        result = PaymentMethod.get_payment_methods(
            statuses=[PaymentMethodStatus.DISABLED, PaymentMethodStatus.ACTIVE]
        )

        self.assertEqual(
            sorted(expected_list, key=lambda x: x.type),
            sorted(result, key=lambda x: x.type),
        )

    def test_get_payment_methods_beta_user_access(self):
        self.user.experimental_features.add(self.tester_feature)

        expected_list = PaymentMethod.objects.filter(status=PaymentMethodStatus.TESTER_USER_ACCESS.id)

        result = PaymentMethod.get_payment_methods(
            statuses=[PaymentMethodStatus.TESTER_USER_ACCESS],
            user=self.user,
        )

        self.assertEqual(
            sorted(expected_list, key=lambda x: x.type),
            sorted(result, key=lambda x: x.type),
        )

    def test_get_payment_methods_beta_user_access_no_user_access(self):
        result = PaymentMethod.get_payment_methods(
            statuses=[PaymentMethodStatus.TESTER_USER_ACCESS],
            user=self.user,
        )

        self.assertEqual(result, [])

    def test_get_payment_methods_beta_user_access_no_user(self):
        result = PaymentMethod.get_payment_methods(
            statuses=[PaymentMethodStatus.TESTER_USER_ACCESS],
        )

        self.assertEqual(result, [])

    def test_get_payment_methods_no_paymentMethods(self):
        PaymentMethod.objects.all().delete()

        result = PaymentMethod.get_payment_methods()

        self.assertEqual(result, [])

    def test_get_payment_method_instance(self):
        result = PaymentMethod.get_payment_method_instance('pix')

        self.assertEqual(result, self.pix)

    def test_get_payment_method_instance_beta_access(self):
        self.user.experimental_features.add(self.tester_feature)

        result = PaymentMethod.get_payment_method_instance(
            self.tester_payment_method.type,
            user=self.user
        )

        self.assertEqual(result, self.tester_payment_method)
