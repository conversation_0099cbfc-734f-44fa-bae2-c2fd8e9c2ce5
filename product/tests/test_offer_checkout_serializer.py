from unittest import mock

from cakto.tests.base import BaseTestCase
from product.models import Checkout, Offer, Product
from product.serializers import OfferCheckoutSerializer


class TestOfferCheckoutSerializer(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.create_paymentMethods()

        cls.product: Product = cls.create_product(user=cls.user)
        cls.offer: Offer = cls.product.offers.first()  # type:ignore
        cls.checkout = Checkout.objects.create(
            product=cls.product,
            config={"TestingKey": "valueTesting"},
        )
        cls.checkout.offers.set([cls.offer])

    def test_get_checkout_with_valid_checkout_id(self):
        serializer = OfferCheckoutSerializer(self.offer, context={'checkout_id': self.checkout.id})

        result = serializer.get_checkout(self.offer)

        self.assertIsInstance(result, dict)

        self.assertEqual(result["config"], self.checkout.config)  # type:ignore

    def test_get_checkout_with_invalid_checkout_id(self):
        mock_offer = mock.Mock()
        mock_offer.checkout.return_value = None

        serializer = OfferCheckoutSerializer(context={'checkout_id': 'invalid_id'})

        result = serializer.get_checkout(mock_offer)

        self.assertEqual(result, {})

    def test_get_checkout_with_no_checkout_id(self):
        serializer = OfferCheckoutSerializer(self.offer, context={"checkout_id": None})

        result = serializer.get_checkout(self.offer)

        self.assertIsInstance(result, dict)

        self.assertEqual(result["config"], self.checkout.config)  # type:ignore

    def test_offer_checkout_serializer_returns_threeDsRetryEnabled_true_when_product_flag_is_on(self):
        self.product.threeDsRetryEnabled = True
        self.product.save()

        self.product.user.threeDsRetryEnabled = False
        self.product.user.save()

        serializer = OfferCheckoutSerializer(self.offer, context={'checkout_id': self.checkout.id})

        result = serializer.data.get('threeDsRetryEnabled')

        self.assertIsInstance(result, bool)
        self.assertTrue(result)

    def test_offer_checkout_serializer_returns_threeDsRetryEnabled_true_when_user_flag_is_on(self):
        self.product.threeDsRetryEnabled = False
        self.product.save()

        self.product.user.threeDsRetryEnabled = True
        self.product.user.save()

        serializer = OfferCheckoutSerializer(self.offer, context={'checkout_id': self.checkout.id})

        result = serializer.data.get('threeDsRetryEnabled')

        self.assertIsInstance(result, bool)
        self.assertTrue(result)

    def test_offer_checkout_serializer_returns_threeDsRetryEnabled_false_when_both_user_and_product_flag_is_off(self):
        self.product.threeDsRetryEnabled = False
        self.product.save()

        self.product.user.threeDsRetryEnabled = False
        self.product.user.save()

        serializer = OfferCheckoutSerializer(self.offer, context={'checkout_id': self.checkout.id})

        result = serializer.data.get('threeDsRetryEnabled')

        self.assertIsInstance(result, bool)
        self.assertFalse(result)

    def test_offer_checkout_serializer_returns_threeDsRetryEnabled_true_when_both_user_and_product_flag_is_on(self):
        self.product.threeDsRetryEnabled = True
        self.product.save()

        self.product.user.threeDsRetryEnabled = True
        self.product.user.save()

        serializer = OfferCheckoutSerializer(self.offer, context={'checkout_id': self.checkout.id})

        result = serializer.data.get('threeDsRetryEnabled')

        self.assertIsInstance(result, bool)
        self.assertTrue(result)

    def test_offer_checkout_serializer_returns_showAddressFields(self):
        self.product.showAddressFields = True
        self.product.save()

        serializer = OfferCheckoutSerializer(self.offer, context={'checkout_id': self.checkout.id})

        result = serializer.data.get('product', {}).get('showAddressFields')

        self.assertIsInstance(result, bool)
        self.assertTrue(result)

    def test_offer_checkout_serializer_returns_product_supportWhatsapp_as_sellerWhatsapp(self):
        self.product.supportWhatsapp = "+5511999993333"
        self.product.save()

        serializer = OfferCheckoutSerializer(self.offer, context={'checkout_id': self.checkout.id})

        result = serializer.data.get('sellerWhatsapp')

        self.assertIsInstance(result, str)
        self.assertEqual(result, "+5511999993333")

        # Test when product supportWhatsapp is None
        self.product.supportWhatsapp = None
        self.product.save()

        serializer = OfferCheckoutSerializer(self.offer, context={'checkout_id': self.checkout.id})

        result = serializer.data.get('sellerWhatsapp')

        self.assertIsNone(result)
