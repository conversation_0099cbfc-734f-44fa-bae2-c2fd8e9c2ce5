import hashlib
import json
from decimal import Decimal
from unittest import mock

import pytest
from django.conf import settings
from django.core.cache import cache
from django.urls import reverse
from django.utils import timezone
from rest_framework import status
from waffle.testutils import override_switch

from cakto.tests.base import BaseTestCase
from gateway.models import PaymentMethodType, Subscription
from product.enums import ProductType, ShowcaseEventType
from product.models import (Affiliate, Category, Checkout, CheckoutVisit, ContentDelivery, FacebookPixel,
                            GoogleAdsPixel, KwaiPixel, Link, Offer, OrderBump, OutbrainPixel, PaymentMethod, Product,
                            ProductDelivery, ShowcaseEvent, TaboolaPixel, TikTokPixel, TrackingPixels)
from product.pagination import ProductShowcasePagination
from product.serializers import ProductShowcaseSerializer
from product.utils import increase_checkout_visits, send_offer_data_to_compliance_analysis
from product.views import AFFILIATE_MAX_COMMISSION


class ProductAPITest(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()

        cls.create_paymentMethods()

        cls.category = Category.objects.create(name="test")

        cls.product = cls.create_product(
            user=cls.user,
            paymentMethod='credit_card',
            category=cls.category,
        )
        cls.product.paymentMethods.set([cls.credit_card, cls.pix])

        cls.headers = cls.build_user_auth_headers(cls.user)

    def test_create_product(self):
        url = reverse('products')
        self.product.delete()
        ContentDelivery.objects.get_or_create(type='emailAccess', name='Link de acesso via E-mail')

        data = {
            'name': 'New Product',
            'price': 20.0,
            'description': 'A new product' * 10,
            'contentDeliveries': ['emailAccess', ],
            'emailAccessLink': 'https://test.com',
        }

        response = self.client.post(url, data, headers=self.headers, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED, response.content)
        self.assertEqual(Product.objects.count(), 1)
        self.assertEqual(Product.objects.last().name, data['name'])  # type: ignore
        self.assertEqual(Product.objects.last().price, data['price'])  # type: ignore
        self.assertEqual(Product.objects.last().contentDeliveries.first().type, data['contentDeliveries'][0])  # type: ignore
        self.assertEqual(Product.objects.last().emailAccessLink, data['emailAccessLink'])  # type: ignore

        product_paymentMethods = Product.objects.last().paymentMethods.values_list('type', flat=True)  # type: ignore
        all_paymentMethods = PaymentMethod.objects.all().values_list('type', flat=True)

        self.assertEqual(sorted(product_paymentMethods), sorted(all_paymentMethods))

        self.assertEqual(Product.objects.last().offers.first().type, ProductType.UNIQUE.id)  # type: ignore

    def test_create_product_subscription(self):
        url = reverse('products')
        self.product.delete()
        ContentDelivery.objects.get_or_create(type='emailAccess', name='Link de acesso via E-mail')

        data = {
            'name': 'New Product',
            'price': 20.0,
            'description': 'A new product' * 10,
            'contentDeliveries': ['emailAccess', ],
            'emailAccessLink': 'https://test.com',
            'type': ProductType.SUBSCRIPTION.id,
        }

        response = self.client.post(url, data, headers=self.headers, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED, response.content)

        subscription_permited_paymentMethods = Subscription.get_supported_paymentMethodTypes()
        product_paymentMethods = Product.objects.last().paymentMethods.values_list('type', flat=True)  # type: ignore
        self.assertEqual(
            sorted(subscription_permited_paymentMethods),
            sorted(product_paymentMethods),
        )

        self.assertEqual(Product.objects.last().offers.first().type, ProductType.SUBSCRIPTION.id)  # type: ignore

    def test_cant_create_product_without_login(self):
        url = reverse('products')
        self.product.delete()

        data = {
            'name': 'New Product',
            'price': 20.0,
            'description': 'A new product' * 10,
        }

        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        self.assertEqual(Product.objects.count(), 0)

    def test_user_cant_edit_another_user_product(self):
        another_user = self.create_user(email='<EMAIL>')
        another_user_token = self.get_user_access_token(another_user)
        another_user_headers = self.create_headers(another_user_token)

        new_data = {'name': 'Updated'}
        url = reverse('product', args=[self.product.pk])

        response = self.client.put(url, new_data, headers=another_user_headers)

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.product.refresh_from_db()
        self.assertNotEqual(self.product.name, new_data['name'])

    def test_affiliate_invalidate_cache_when_product_save(self):
        affiliate = Affiliate.objects.create(user=self.user, product=self.product, commission=10)

        cache.set(affiliate.cache_key, 'Test Invalidate Cache', None)

        self.product.save()

        self.assertEqual(cache.get(affiliate.cache_key), None)

    def test_product_get_cache_keys(self):
        offer: Offer = self.product.offers.first()
        affiliate = Affiliate.objects.create(product=self.product, user=self.user, commission=10)
        tracking_pixels = TrackingPixels.objects.create(product=self.product)

        expected_cache_keys = [
            offer.cache_key,
            affiliate.cache_key,
        ]
        expected_cache_keys.extend(tracking_pixels.get_cache_keys())

        cache_keys = self.product.get_cache_keys()

        self.assertEqual(sorted(cache_keys), sorted(expected_cache_keys))

    def test_product_bulk_invalidate_cache(self):
        product_2 = self.create_product(user=self.user)

        cache_keys = self.product.get_cache_keys() + product_2.get_cache_keys()

        [cache.set(key, 'Test Invalidate Cache', None) for key in cache_keys]

        Product.bulk_invalidate_cache([self.product, product_2])

        with self.subTest('Invalidate cache for all products'):
            for key in cache_keys:
                self.assertIsNone(cache.get(key))

    def test_product_cant_update_paymentMethods_with_cardBlocked(self):
        url = reverse('product', args=[self.product.pk])
        self.product.additionalInfo['cardBlocked'] = True
        self.product.save()
        self.product.paymentMethods.set([self.pix])

        payload = {
            'name': 'Updated Product',
            'description': 'A new description',
            'price': 30.0,
            'paymentMethods': ['credit_card', 'pix']
        }

        response = self.client.put(url, headers=self.headers, data=payload)
        self.product.refresh_from_db()

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content)
        self.assertEqual(self.product.name, 'Updated Product')
        self.assertEqual(list(self.product.paymentMethods.values_list('type', flat=True)), ['pix'])

    def test_product_can_edit_paymentMethods(self):
        url = reverse('product', args=[self.product.pk])
        self.product.save()
        self.product.paymentMethods.set([self.pix])

        expected_payment_method = ['credit_card', ]

        payload = {
            'name': 'Updated Product',
            'description': 'A new description',
            'price': 30.0,
            'paymentMethods': expected_payment_method
        }

        response = self.client.put(url, headers=self.headers, data=payload, format='json')
        self.product.refresh_from_db()

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content)
        self.assertEqual(self.product.name, 'Updated Product')
        self.assertEqual(list(self.product.paymentMethods.values_list('type', flat=True)), expected_payment_method)

    def test_product_orderbumps_cache_invalidate(self):
        main_product_offer = self.product.offers.first()
        product_2 = self.create_product(user=self.user)
        OrderBump.objects.create(product=self.product, offer=product_2.offers.first())

        cache.set(main_product_offer.cache_key, 'Test Invalidate Cache', None)

        # When product_2 is saved, the cache of main_product_offer should be invalidated
        product_2.save()

        self.assertEqual(cache.get(main_product_offer.cache_key), None)

    def test_product_emailAccessLink_update(self):
        url = reverse('product', args=[self.product.pk])
        self.product.emailAccessLink = 'https://old.com'
        self.product.save()

        data = {
            'name': self.product.name,
            'price': self.product.price,
            'description': '1234567890' * 10,
            'emailAccessLink': 'https://test.com',
        }

        response = self.client.put(url, data=json.dumps(data), headers=self.headers, content_type='application/json')
        self.product.refresh_from_db()

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content)
        self.assertEqual(self.product.emailAccessLink, data['emailAccessLink'])  # type: ignore

    def test_create_product_sets_all_paymentMethods(self):
        Product.objects.all().delete()
        url = reverse('products')

        data = {
            'name': 'New Product',
            'price': 20.0,
            'description': 'A new product' * 10,
        }

        response = self.client.post(url, data, headers=self.headers, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED, response.content.decode())

        product: Product = Product.objects.first()  # type: ignore
        self.assertEqual(product.paymentMethods.count(), PaymentMethod.objects.count())  # type: ignore

    def test_create_product_with_wrong_paymentMethods(self):
        Product.objects.all().delete()
        url = reverse('products')

        data = {
            'name': 'New Product',
            'price': 20.0,
            'description': 'A new product' * 10,
            'paymentMethods': ['does_not_exist'],
        }

        response = self.client.post(url, data, headers=self.headers, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST, response.content.decode())

    def test_product_paymentMethods_update(self):
        url = reverse('product', args=[self.product.pk])

        self.product.paymentMethods.set([self.pix, self.boleto])

        data = {
            'name': self.product.name,
            'price': self.product.price,
            'description': '1234567890' * 10,
            'paymentMethods': ['boleto'],
        }

        response = self.client.put(url, data=json.dumps(data), headers=self.headers, content_type='application/json')
        self.product.refresh_from_db()

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())
        self.assertEqual(self.product.paymentMethods.count(), 1)  # type: ignore
        self.assertEqual(self.product.paymentMethods.first().type, 'boleto')  # type: ignore

    def test_product_paymentMethods_update_with_wrong_paymentMethod(self):
        url = reverse('product', args=[self.product.pk])

        self.product.paymentMethods.set([self.pix, self.boleto])

        data = {
            'name': self.product.name,
            'price': self.product.price,
            'description': '1234567890' * 10,
            'paymentMethods': ['does_not_exist'],
        }

        response = self.client.put(url, data=json.dumps(data), headers=self.headers, content_type='application/json')
        self.product.refresh_from_db()

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST, response.content.decode())

    def test_product_paymentMethods_update_without_paymentMethod(self):
        url = reverse('product', args=[self.product.pk])

        self.product.paymentMethods.set([self.pix, self.boleto])

        data = {
            'name': self.product.name,
            'price': self.product.price,
            'description': '1234567890' * 10,
        }

        response = self.client.put(url, data=json.dumps(data), headers=self.headers, content_type='application/json')
        self.product.refresh_from_db()

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())
        self.assertEqual(self.product.paymentMethods.count(), 2)  # type: ignore
        self.assertEqual(sorted(list(self.product.paymentMethods.values_list('type', flat=True))), sorted(['pix', 'boleto']))  # type: ignore

    def test_product_defaultPaymentMethod_update(self):
        url = reverse('product', args=[self.product.pk])

        self.product.defaultPaymentMethod = self.pix
        self.product.save()

        data = {
            'name': self.product.name,
            'price': self.product.price,
            'description': '1234567890' * 10,
            'paymentMethods': ['boleto'],
            'defaultPaymentMethod': 'boleto',
        }

        response = self.client.put(url, data=json.dumps(data), headers=self.headers, content_type='application/json')
        self.product.refresh_from_db()

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content)
        self.assertEqual(self.product.defaultPaymentMethod.type, 'boleto')  # type: ignore

    def test_create_product_with_contentDeliveries(self):
        self.cakto, _ = ContentDelivery.objects.get_or_create(type='cakto', name='Área de membros cakto')
        self.external, _ = ContentDelivery.objects.get_or_create(type='external', name='Área de membros externa')
        Product.objects.all().delete()

        url = reverse('products')

        data = {
            'name': 'New Product',
            'price': 20.0,
            'description': 'A new product' * 10,
            'contentDeliveries': ['cakto', 'external'],
        }

        response = self.client.post(url, data, headers=self.headers, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED, response.content)

        product: Product = Product.objects.first()  # type: ignore
        self.assertEqual(product.contentDeliveries.count(), 2)
        self.assertEqual(sorted(list(product.contentDeliveries.values_list('type', flat=True))), sorted(data['contentDeliveries']))  # type: ignore

    def test_product_contentDeliveries_update(self):
        self.cakto, _ = ContentDelivery.objects.get_or_create(type='cakto', name='Área de membros cakto')
        self.external, _ = ContentDelivery.objects.get_or_create(type='external', name='Área de membros externa')

        url = reverse('product', args=[self.product.pk])

        self.product.contentDeliveries.set([self.cakto, self.external])

        data = {
            'name': self.product.name,
            'price': self.product.price,
            'description': '1234567890' * 10,
            'contentDeliveries': ['cakto'],
        }

        response = self.client.put(url, data=json.dumps(data), headers=self.headers, content_type='application/json')
        self.product.refresh_from_db()

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content)
        self.assertEqual(self.product.contentDeliveries.count(), 1)  # type: ignore
        self.assertEqual(self.product.contentDeliveries.first().type, 'cakto')  # type: ignore

    def test_product_update_removing_contentDeliveries_updates_productDelivery_to_deleted(self):
        self.cakto, _ = ContentDelivery.objects.get_or_create(type='cakto', name='Área de membros cakto')
        self.external, _ = ContentDelivery.objects.get_or_create(type='external', name='Área de membros externa')

        productDelivery = ProductDelivery.objects.create(
            product=self.product,
            contentDelivery=self.external,
            status='active',
            name='Área de membros externa',
        )

        url = reverse('product', args=[self.product.pk])

        self.product.contentDeliveries.set([self.cakto, self.external])

        data = {
            'name': self.product.name,
            'price': self.product.price,
            'description': '1234567890' * 10,
            'contentDeliveries': ['cakto'],
        }

        response = self.client.put(url, data=json.dumps(data), headers=self.headers, content_type='application/json')
        self.product.refresh_from_db()

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content)
        self.assertEqual(self.product.deliveries.count(), 1)  # type:ignore
        productDelivery.refresh_from_db()
        self.assertEqual(productDelivery.status, 'deleted')  # type:ignore

    def test_product_duplicate(self):
        url = reverse('product-duplicate', args=[self.product.pk])

        self.assertEqual(Product.objects.count(), 1)

        response = self.client.post(url, headers=self.headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content)

        self.assertEqual(Product.objects.count(), 2)
        new_product: Product = Product.objects.exclude(pk=self.product.pk).first()  # type:ignore

        self.assertNotEqual(new_product.short_id, self.product.short_id)
        self.assertNotEqual(new_product.pk, self.product.pk)
        self.assertEqual(new_product.membersId, None)
        self.assertEqual(new_product.name, f'{self.product.name} - Cópia')
        self.assertEqual(new_product.refundRequest, False)
        self.assertEqual(new_product.scriptSuspicious, False)
        self.assertEqual(new_product.blockReason, None)
        self.assertEqual(new_product.statusAdmin, 'stand_by')
        self.assertEqual(new_product.additionalInfo, {})

        self.assertEqual(new_product.total_sales, 0)
        self.assertEqual(new_product.total_sales_count, 0)
        self.assertEqual(new_product.total_paid, 0)
        self.assertEqual(new_product.total_paid_count, 0)
        self.assertEqual(new_product.total_refunded, 0)
        self.assertEqual(new_product.total_refunded_count, 0)
        self.assertEqual(new_product.total_chargeback, 0)
        self.assertEqual(new_product.total_chargeback_count, 0)
        self.assertEqual(new_product.sales_30_days, 0)
        self.assertEqual(new_product.sales_30_days_count, 0)
        self.assertEqual(new_product.refunded_30_days, 0)
        self.assertEqual(new_product.refunded_30_days_count, 0)
        self.assertEqual(new_product.chargeback_30_days, 0)
        self.assertEqual(new_product.chargeback_30_days_count, 0)
        self.assertEqual(new_product.sales_7_days, 0)
        self.assertEqual(new_product.sales_7_days_count, 0)
        self.assertEqual(new_product.refunded_7_days, 0)
        self.assertEqual(new_product.refunded_7_days_count, 0)
        self.assertEqual(new_product.chargeback_7_days, 0)
        self.assertEqual(new_product.chargeback_7_days_count, 0)
        self.assertEqual(new_product.sales_24h, 0)
        self.assertEqual(new_product.sales_24h_count, 0)
        self.assertEqual(new_product.refunded_24h, 0)
        self.assertEqual(new_product.refunded_24h_count, 0)
        self.assertEqual(new_product.chargeback_24h, 0)
        self.assertEqual(new_product.chargeback_24h_count, 0)

        self.assertEqual(new_product.unblockedAt, None)
        self.assertEqual(new_product.analisedAt, None)
        self.assertEqual(new_product.blockedAt, None)

    def test_product_duplicate_offers_and_checkouts(self):
        url = reverse('product-duplicate', args=[self.product.pk])

        offer_2 = Offer.objects.create(product=self.product, price=20.0, name='Offer 2')
        checkout_2 = Checkout.objects.create(product=self.product, name='Checkout 2')
        checkout_2.offers.add(offer_2)

        response = self.client.post(url, headers=self.headers)
        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content)

        self.product.refresh_from_db()
        offer_2.refresh_from_db()
        checkout_2.refresh_from_db()

        new_product: Product = Product.objects.exclude(pk=self.product.pk).first()  # type:ignore

        self.assertEqual(new_product.offers.count(), 2)
        self.assertNotEqual(new_product.offers.filter(name='Offer 2').first(), self.product.offers.filter(name='Offer 2').first())
        self.assertEqual(new_product.checkouts.count(), 1)
        self.assertNotEqual(new_product.checkouts.first(), self.product.checkouts.first())

    def test_product_duplicate_links(self):
        url = reverse('product-duplicate', args=[self.product.pk])

        offer = self.product.offers.first()
        link = Link.objects.create(product=self.product, name='Test Link', type='checkout', url='http://test.com')
        link_2 = Link.objects.create(product=self.product, name='Test Link', type='checkout', url=Link.generate_link_url(offer), offer=offer)

        response = self.client.post(url, headers=self.headers)
        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content)

        self.product.refresh_from_db()
        link.refresh_from_db()
        new_product: Product = Product.objects.exclude(pk=self.product.pk).first()  # type:ignore

        self.assertEqual(Link.objects.count(), 4)

        new_link1: Link = Link.objects.filter(product=new_product, offer=None).first()  # type:ignore
        self.assertNotEqual(new_link1, link)
        self.assertNotEqual(new_link1.shortId, link.shortId)  # type:ignore
        self.assertNotEqual(new_link1.pk, link.pk)  # type:ignore

        new_link2: Link = Link.objects.filter(product=new_product, offer__isnull=False).first()  # type:ignore
        self.assertNotEqual(new_link2, link_2)
        self.assertNotEqual(new_link2.shortId, link_2.shortId)  # type:ignore
        self.assertNotEqual(new_link2.pk, link_2.pk)  # type:ignore
        self.assertNotEqual(new_link2.url, link_2.url)  # type:ignore

    def test_product_duplicate_orderbumps(self):
        url = reverse('product-duplicate', args=[self.product.pk])

        product_2 = self.create_product(user=self.user)
        bump = OrderBump.objects.create(product=self.product, offer=product_2.offers.first(), cta='Test Bump')

        product_3 = self.create_product(user=self.user)
        bump_2 = OrderBump.objects.create(product=self.product, offer=product_3.offers.first(), cta='Test Bump 2')

        response = self.client.post(url, headers=self.headers)
        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content)

        bump.refresh_from_db()
        bump_2.refresh_from_db()

        self.assertEqual(OrderBump.objects.count(), 4)
        self.assertNotEqual(OrderBump.objects.filter(cta='Test Bump', product=product_2).first(), bump)
        self.assertNotEqual(OrderBump.objects.filter(cta='Test Bump 2', product=product_3).first(), bump_2)

    def test_product_duplicate_tracking_pixels(self):
        url = reverse('product-duplicate', args=[self.product.pk])

        tracking_pixel = TrackingPixels.objects.create(product=self.product, fbPixConversionValue=88)

        affiliate = Affiliate.objects.create(product=self.product, user=self.create_user(), commission=10)
        tracking_pixel_affiliate = TrackingPixels.objects.create(product=self.product, affiliate=affiliate, fbPixConversionValue=55)

        response = self.client.post(url, headers=self.headers)
        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content)

        tracking_pixel.refresh_from_db()
        tracking_pixel_affiliate.refresh_from_db()

        self.assertEqual(TrackingPixels.objects.count(), 3)
        new_tracking_pixel: TrackingPixels = TrackingPixels.objects.filter(affiliate__isnull=True).exclude(id=tracking_pixel.id).first()  # type:ignore

        self.assertEqual(tracking_pixel.fbPixConversionValue, new_tracking_pixel.fbPixConversionValue)

    def test_product_duplicate_tracking_pixels_relations(self):
        url = reverse('product-duplicate', args=[self.product.pk])

        tracking_pixel = TrackingPixels.objects.create(product=self.product, fbPixConversionValue=88)

        fb = FacebookPixel.objects.create(tracking_pixels=tracking_pixel, pixelId='123456')
        google = GoogleAdsPixel.objects.create(tracking_pixels=tracking_pixel, pixelId='123456', name='Test Pixel')
        outbrain = OutbrainPixel.objects.create(tracking_pixels=tracking_pixel, pixelId='123456')
        tiktok = TikTokPixel.objects.create(tracking_pixels=tracking_pixel, pixelId='123456')
        kwai = KwaiPixel.objects.create(tracking_pixels=tracking_pixel, pixelId='123456')
        taboola = TaboolaPixel.objects.create(tracking_pixels=tracking_pixel, eventName='Test Event', accountId='123456')

        response = self.client.post(url, headers=self.headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content)

        for model in [TrackingPixels, FacebookPixel, GoogleAdsPixel, OutbrainPixel, TikTokPixel, KwaiPixel, TaboolaPixel]:
            with self.subTest('Check if all instances were duplicated'):
                self.assertEqual(model.objects.count(), 2)  # type:ignore

        for instance in [tracking_pixel, fb, google, taboola, outbrain, tiktok, kwai]:
            instance.refresh_from_db()

        new_tracking_pixel: TrackingPixels = TrackingPixels.objects.filter(affiliate__isnull=True).exclude(id=tracking_pixel.id).first()  # type:ignore
        self.assertEqual(tracking_pixel.fbPixConversionValue, new_tracking_pixel.fbPixConversionValue)

        for instance in [fb, google, outbrain, tiktok, kwai]:
            with self.subTest(f'Check if {instance.__class__.__name__} was duplicated'):
                new_instance = instance.__class__.objects.filter(tracking_pixels=new_tracking_pixel).exclude(id=instance.pk).first()
                self.assertEqual(new_instance.pixelId, instance.pixelId)

        new_taboola = TaboolaPixel.objects.filter(tracking_pixels=new_tracking_pixel).exclude(id=taboola.pk).first()
        self.assertEqual(new_taboola.accountId, taboola.accountId)  # type:ignore

    def test_list_product_contentDeliveries(self):
        url = reverse('product-delivery', args=[self.product.pk])

        cakto, _ = ContentDelivery.objects.get_or_create(type='cakto', name='Área de membros cakto')

        productDelivery = ProductDelivery.objects.create(
            product=self.product,
            contentDelivery=cakto,
            status='active',
            name='Área de membros cakto',
        )

        response = self.client.get(url, headers=self.headers)
        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode("utf-8"))
        self.assertEqual(
            response.json()['results'],
            [{
                'id': str(productDelivery.pk),
                'product': str(self.product.pk),
                'contentDelivery': 'cakto',
                'name': 'Área de membros cakto',
                'status': 'active',
                'fields': {},
                'createdAt': timezone.localtime(productDelivery.createdAt).isoformat(),
                'updatedAt': timezone.localtime(productDelivery.updatedAt).isoformat(),
            }]
        )

    @mock.patch('requests.post')
    def test_send_offer_data_to_compliance_analysis_single_offer(self, mock_post):
        offer = self.product.offers.first()
        with self.settings(AI_COMPLIANCE_URL='https://test.com', AI_COMPLIANCE_API_KEY='test'):
            send_offer_data_to_compliance_analysis([offer])

            mock_post.assert_called_once_with(
                url=f'{settings.AI_COMPLIANCE_URL}/v1/compliance/analyze/single/{offer.id}',
                headers={'x-api-token': settings.AI_COMPLIANCE_API_KEY},
                timeout=2
            )

    @mock.patch('requests.post')
    def test_send_offer_data_to_compliance_analysis_multiple_offers(self, mock_post):
        offer_1 = self.product.offers.first()
        offer_2 = Offer.objects.create(product=self.product, price=20.0, name='Offer 2')

        with self.settings(AI_COMPLIANCE_URL='https://test.com', AI_COMPLIANCE_API_KEY='test'):
            send_offer_data_to_compliance_analysis([offer_1, offer_2])

            mock_post.assert_called_once_with(
                url=f'{settings.AI_COMPLIANCE_URL}/v1/compliance/analyze/bulk',
                json={
                    'products': [
                        {
                            'id': offer_1.id,
                            'produto_short_id': offer_1.id,
                            'produto_nome': offer_1.product.name,
                            'produto_descricao': offer_1.product.description,
                            'produto_imagem': offer_1.product.image.url if offer_1.product.image else '',
                            'vendedor_nome': offer_1.product.producerName,
                            'vendedor_email': offer_1.product.user.email,
                        },
                        {
                            'id': offer_2.id,
                            'produto_short_id': offer_2.id,
                            'produto_nome': offer_2.product.name,
                            'produto_descricao': offer_2.product.description,
                            'produto_imagem': offer_2.product.image.url if offer_2.product.image else '',
                            'vendedor_nome': offer_2.product.producerName,
                            'vendedor_email': offer_2.product.user.email,
                        }
                    ]
                },
                headers={'x-api-token': settings.AI_COMPLIANCE_API_KEY},
                timeout=2
            )

    @mock.patch('requests.post')
    def test_send_offer_data_to_compliance_analysis_no_offers(self, mock_post):
        with self.settings(AI_COMPLIANCE_URL='https://test.com', AI_COMPLIANCE_API_KEY='test'):
            send_offer_data_to_compliance_analysis([])
            mock_post.assert_not_called()

    @mock.patch('requests.post')
    def test_send_offer_data_to_compliance_analysis_missing_settings(self, mock_post):
        with self.settings(AI_COMPLIANCE_URL=None, AI_COMPLIANCE_API_KEY='test'):
            offer = self.product.offers.first()
            send_offer_data_to_compliance_analysis([offer])
            mock_post.assert_not_called()

        with self.settings(AI_COMPLIANCE_API_KEY=None, AI_COMPLIANCE_URL='http://test.com'):
            offer = self.product.offers.first()
            send_offer_data_to_compliance_analysis([offer])
            mock_post.assert_not_called()

    def test_product_update_raises_error_when_subscription_type_and_not_suported_paymentMethod(self):
        url = reverse('product', args=[self.product.pk])

        expected_paymentMethods = [self.pix, self.credit_card]

        self.product.type = ProductType.SUBSCRIPTION.id
        self.product.save(update_fields=['type'])
        self.product.paymentMethods.set(expected_paymentMethods)

        data = {
            'name': self.product.name,
            'price': self.product.price,
            'description': '1234567890' * 10,
            'paymentMethods': [PaymentMethodType.GOOGLEPAY.value[0]],
        }

        response = self.client.put(url, data=json.dumps(data), headers=self.headers, content_type='application/json')

        self.product.refresh_from_db()
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST, response.content.decode())
        self.assertEqual(
            response.json(),
            {"paymentMethods": "O método de pagamento Google Pay não é permitido para produtos do tipo assinatura."}
        )

        with self.subTest('Check if paymentMethods were not updated'):
            product_paymentMethods = self.product.paymentMethods.all()
            for paymentMethod in expected_paymentMethods:
                self.assertIn(paymentMethod, product_paymentMethods)

    def test_product_update_raises_error_when_subscription_type_and_not_suported_paymentMethod_plural(self):
        url = reverse('product', args=[self.product.pk])

        expected_paymentMethods = [self.pix, self.credit_card]

        self.product.type = ProductType.SUBSCRIPTION.id
        self.product.save(update_fields=['type'])
        self.product.paymentMethods.set(expected_paymentMethods)

        data = {
            'name': self.product.name,
            'price': self.product.price,
            'description': '1234567890' * 10,
            'paymentMethods': [PaymentMethodType.GOOGLEPAY.value[0], PaymentMethodType.APPLEPAY.value[0]],
        }

        response = self.client.put(url, data=json.dumps(data), headers=self.headers, content_type='application/json')

        self.product.refresh_from_db()
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST, response.content.decode())
        self.assertEqual(
            response.json(),
            {"paymentMethods": "Os métodos de pagamento Google Pay, Apple Pay não são permitidos para produtos do tipo assinatura."}
        )

        with self.subTest('Check if paymentMethods were not updated'):
            product_paymentMethods = self.product.paymentMethods.all()
            for paymentMethod in expected_paymentMethods:
                self.assertIn(paymentMethod, product_paymentMethods)

    def test_product_update_raises_error_when_subscription_offer_and_not_suported_paymentMethod(self):
        url = reverse('product', args=[self.product.pk])

        expected_paymentMethods = [self.pix, self.credit_card]

        self.product.type = ProductType.UNIQUE.id
        self.product.save(update_fields=['type'])
        self.product.paymentMethods.set(expected_paymentMethods)

        offer = self.product.offers.first()
        offer.type = ProductType.SUBSCRIPTION.id
        offer.save(update_fields=['type'])

        data = {
            'name': self.product.name,
            'price': self.product.price,
            'description': '1234567890' * 10,
            'paymentMethods': [PaymentMethodType.GOOGLEPAY.value[0]],
        }

        response = self.client.put(url, data=json.dumps(data), headers=self.headers, content_type='application/json')

        self.product.refresh_from_db()
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST, response.content.decode())
        self.assertEqual(
            response.json(),
            {"paymentMethods": "O método de pagamento Google Pay não é permitido para produtos com ofertas de assinatura."}
        )

        with self.subTest('Check if paymentMethods were not updated'):
            product_paymentMethods = self.product.paymentMethods.all()
            for paymentMethod in expected_paymentMethods:
                self.assertIn(paymentMethod, product_paymentMethods)

    def test_product_update_raises_error_when_subscription_orderbump_and_not_suported_paymentMethod(self):
        url = reverse('product', args=[self.product.pk])

        expected_paymentMethods = [self.pix, self.credit_card]

        self.product.type = ProductType.UNIQUE.id
        self.product.save(update_fields=['type'])
        self.product.paymentMethods.set(expected_paymentMethods)

        offer = Offer.objects.create(
            product=self.create_product(user=self.user),
            price=20.0,
            name='Offer 2',
            type=ProductType.SUBSCRIPTION.id
        )

        OrderBump.objects.create(product=self.product, offer=offer)

        data = {
            'name': self.product.name,
            'price': self.product.price,
            'description': '1234567890' * 10,
            'paymentMethods': [PaymentMethodType.GOOGLEPAY.value[0]],
        }

        response = self.client.put(url, data=json.dumps(data), headers=self.headers, content_type='application/json')

        self.product.refresh_from_db()
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST, response.content.decode())
        self.assertEqual(
            response.json(),
            {"paymentMethods": "O método de pagamento Google Pay não é permitido para produtos com orderbumps de assinatura."}
        )

        with self.subTest('Check if paymentMethods were not updated'):
            product_paymentMethods = self.product.paymentMethods.all()
            for paymentMethod in expected_paymentMethods:
                self.assertIn(paymentMethod, product_paymentMethods)

    @mock.patch('product.serializers.ProductShowcaseSerializer.get_affiliate_commission_amount')
    def test_product_showcase(self, mock_commission_amount):
        mock_commission_amount.return_value = 24
        url = reverse('products-showcase')
        self.product.delete()
        self.product = self.create_product(
            user=self.user,
            affiliate=True,
            affiliateMarketplace=True,
            total_sales=60000
        )

        response = self.client.get(url, headers=self.headers)

        self.assertEqual(response.status_code, 200, response.content.decode("utf-8"))

        self.assertEqual(response.json().get('count'), 1)
        cache.clear()

        with self.subTest('Check if only enabled affiliations are shown'):
            self.product.delete()
            self.product = self.create_product(user=self.user, affiliate=False)

            response = self.client.get(url, headers=self.headers)

            self.assertEqual(response.status_code, 200, response.content.decode("utf-8"))

            self.assertEqual(response.json().get('count'), 0)
            cache.clear()

    @mock.patch('product.serializers.ProductShowcaseSerializer.get_affiliate_commission_amount')
    @mock.patch('product.utils.showcase_get_top_averages')
    def test_temperature_math_v2(self, mock_avgs, mock_commission):
        mock_avgs.return_value = {'avg_volume': 100, 'avg_growth': 1.0, 'avg_conversion': 0.05}
        mock_commission.return_value = 24

        p_top = self.create_product(
            user=self.user, affiliate=True, affiliateMarketplace=True,
            total_sales=10_000,
            total_sales_count=10,
            sales_7_days_count=200,
            sales_30_days_count=250,
            sales_24h_count=1,
        )

        p_top.checkouts.create(visits=100, name="Top Checkout")

        resp = self.client.get(reverse('products-showcase-v2'), headers=self.headers)
        first = resp.json()['results'][0]
        self.assertEqual(first['temperature'], 150)

    @mock.patch('product.serializers.ProductShowcaseSerializer.get_affiliate_commission_amount')
    def test_product_showcase_with_filters(self, mock_commission_amount):
        mock_commission_amount.return_value = 24
        url = reverse('products-showcase')
        self.product.delete()
        self.product = self.create_product(user=self.user, price=15.0, affiliate=True, affiliateMarketplace=True, total_sales=60000)
        self.product1 = self.create_product(user=self.user, price=20.0, affiliate=True, affiliateMarketplace=True, total_sales=60000)

        response = self.client.get(f'{url}?price__lt=20', headers=self.headers)

        self.assertEqual(response.status_code, 200, response.content.decode("utf-8"))

        product = response.json()['results'][0]

        self.assertEqual(product.get('price'), '15.00')

        with self.subTest('Check product with charfield filters'):
            self.product.delete()
            self.product1.delete()
            self.product = self.create_product(
                user=self.user,
                price=15.0,
                type='unique',
                affiliate=True,
                affiliateMarketplace=True,
                total_sales=60000
            )
            self.product1 = self.create_product(
                user=self.user,
                price=20.0,
                type='subscription',
                affiliate=True,
                affiliateMarketplace=True,
                total_sales=60000
            )

            response = self.client.get(f'{url}?type=subscription', headers=self.headers)

            self.assertEqual(response.status_code, 200, response.content.decode("utf-8"))

            product = response.json()['results'][0]

            self.assertEqual(product.get('type'), 'subscription')
            cache.clear()

        with self.subTest('Check filtering with invalid filter'):
            self.product.delete()
            self.product1.delete()
            self.product = self.create_product(
                user=self.user,
                price=15.0,
                type='unique',
                affiliate=True,
                affiliateMarketplace=True,
                total_sales=60000
            )
            self.product1 = self.create_product(
                user=self.user,
                price=20.0,
                type='subscription',
                affiliate=True,
                affiliateMarketplace=True,
                total_sales=60000
            )

            response = self.client.get(f'{url}?search=xptodasilva', headers=self.headers)

            self.assertEqual(response.status_code, 200, response.content.decode("utf-8"))
            self.assertEqual(response.json().get('results'), [])
            cache.clear()

    @mock.patch('product.serializers.ProductShowcaseSerializer.get_affiliate_commission_amount')
    def test_product_caching(self, mock_commission_amount):
        mock_commission_amount.return_value = 24
        url = reverse('products-showcase')
        self.product = self.create_product(user=self.user, price=15.0, affiliate=True, affiliateMarketplace=True, total_sales=60000)

        response = self.client.get(url, headers=self.headers)
        self.assertEqual(response.status_code, 200)

        cache_key = f'showcase_products:{hashlib.md5("".encode()).hexdigest()}'
        cached_data = cache.get(cache_key)
        self.assertIsNotNone(cached_data)
        cache.clear()

    @mock.patch('product.serializers.ProductShowcaseSerializer.get_affiliate_commission_amount')
    @mock.patch('product.views.cache')
    def test_cache_returns_cached_response(self, mocked_cache, mock_commission_amount):
        url = reverse('products-showcase')
        self.product = self.create_product(user=self.user, price=15.0, affiliate=True, affiliateMarketplace=True, total_sales=60000)
        mocked_cache.get.return_value = {
            "count": 1,
            "next": None,
            "previous": None,
            "results": [
                {
                    "id": self.product.id,
                    "name": self.product.name,
                    "price": self.product.price,
                    "affiliation": None,
                }
            ]
        }

        response = self.client.get(url, headers=self.headers)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()["count"], 1)

    @mock.patch('product.serializers.ProductShowcaseSerializer.get_affiliate_commission_amount')
    def test_cache_expiration(self, mock_commission_amount):
        mock_commission_amount.return_value = 24
        url = reverse('products-showcase')
        response = self.client.get(url, headers=self.headers)
        self.assertEqual(response.status_code, 200)
        cache_key = f'showcase_products:{hashlib.md5("".encode()).hexdigest()}'
        self.assertIsNotNone(cache.get(cache_key))
        cache.delete(cache_key)
        self.assertIsNone(cache.get(cache_key))

    @mock.patch('product.serializers.ProductShowcaseSerializer.get_affiliate_commission_amount')
    def test_cache_with_different_params(self, mocked_commission_amount):
        mocked_commission_amount.return_value = 24
        url = reverse('products-showcase')
        response = self.client.get(url, headers=self.headers)
        response1 = self.client.get(f'{url}?price__lt=20', headers=self.headers)

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response1.status_code, 200)

        cache_key1 = f'showcase_products:{hashlib.md5("".encode()).hexdigest()}'
        cache_key2 = f'showcase_products:{hashlib.md5("price__lt=20".encode()).hexdigest()}'

        self.assertIsNotNone(cache.get(cache_key1))
        self.assertIsNotNone(cache.get(cache_key2))

    @mock.patch('product.views.get_is_user_affiliate')
    def test_returns_affiliate_status_and_details_when_user_is_affiliated(self, mock_affiliate_func):
        self.product.delete()
        self.product = self.create_product(user=self.user, price=15.0, affiliate=True, affiliateMarketplace=True, total_sales=60000)

        affiliate = Affiliate.objects.create(user=self.user, product=self.product, commission=50, status='approved')
        mock_affiliate_func.return_value = affiliate

        url = reverse('affiliate-detail', kwargs={'id': self.product.id})
        self.client.force_authenticate(user=self.user)
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["affiliate"], "approved")
        self.assertIn("affiliate_details", response.data)
        self.assertEqual(response.data["affiliate_details"]["commission"], 50)

    @mock.patch('product.views.get_is_user_affiliate')
    def test_returns_affiliation_false_when_user_is_not_affiliated(self, mock_affiliate_func):
        self.product.delete()
        self.product = self.create_product(user=self.user, price=15.0, affiliate=True)

        mock_affiliate_func.return_value = None

        url = reverse('affiliate-detail', kwargs={'id': self.product.id})
        self.client.force_authenticate(user=self.user)
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data, {"affiliation": False})

    def test_returns_404_when_product_is_not_affiliable(self):
        product = self.create_product(user=self.user, affiliate=False)

        url = reverse('affiliate-detail', kwargs={'id': product.id})
        self.client.force_authenticate(user=self.user)
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    @mock.patch('product.serializers.ProductShowcaseSerializer.get_affiliate_commission_amount')
    def test_temperature_max_score(self, mocked_commission_amount):
        mocked_commission_amount.return_value = 24

        product = self.create_product(
            user=self.user,
            price=15.0,
            affiliate=True,
            affiliateMarketplace=True,
            total_sales=10000,
            sales_7_days_count=100,
            sales_30_days_count=150,
            sales_24h_count=10
        )
        product.checkouts.create(visits=1000)

        product.temperature = 150

        serializer = ProductShowcaseSerializer(instance=product)
        assert serializer.data['temperature'] == 150  # type:ignore

    @mock.patch('product.serializers.ProductShowcaseSerializer.get_affiliate_commission_amount')
    def test_temperature_half_score(self, mocked_commission_amount):
        mocked_commission_amount.return_value = 24

        product = self.create_product(
            user=self.user,
            price=15.0,
            affiliate=True,
            affiliateMarketplace=True,
            total_sales=5001,
            sales_7_days_count=25,
            sales_30_days_count=45.75,
            sales_24h_count=10
        )
        product.checkouts.create(visits=250)

        product.temperature = 75

        serializer = ProductShowcaseSerializer(instance=product)
        assert serializer.data['temperature'] == 75  # type:ignore

    @mock.patch('product.serializers.ProductShowcaseSerializer.get_affiliate_commission_amount')
    def test_temperature_min_score(self, mocked_commission_amount):
        mocked_commission_amount.return_value = 24

        product = self.create_product(
            user=self.user,
            price=15.0,
            affiliate=True,
            affiliateMarketplace=True,
            total_sales=0,
            sales_7_days_count=0,
            sales_30_days_count=0,
            sales_24h_count=0
        )
        product.checkouts.create(visits=0)

        product.temperature = 0

        serializer = ProductShowcaseSerializer(instance=product)
        assert serializer.data['temperature'] == 0  # type:ignore

    @mock.patch('product.serializers.ProductShowcaseSerializer.get_affiliate_commission_amount')
    def test_showcase_product_with_bypassShowcaseSalesFilter_is_returned(self, mock_commission_amount):
        mock_commission_amount.return_value = 24
        url = reverse('products-showcase')

        Product.objects.all().delete()
        product = self.create_product(
            user=self.user,
            affiliate=True,
            affiliateMarketplace=True,
            total_sales=0,
            bypassShowcaseSalesFilter=True,
            hideShowcase=False
        )

        # Call
        response = self.client.get(url, headers=self.headers)

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()["count"], 1, "Produto com bypass não aparece na vitrine")

        product.bypassShowcaseSalesFilter = False
        product.save(update_fields=["bypassShowcaseSalesFilter"])
        cache.clear()

        # Call
        response = self.client.get(url, headers=self.headers)

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()["count"], 0, "Produto sem vendas e sem bypass aparece na vitrine")

    @mock.patch('product.serializers.ProductShowcaseSerializer.get_affiliate_commission_amount')
    def test_showcase_updates_hideShowcase_from_db(self, mock_commission_amount):
        mock_commission_amount.return_value = 24
        url = reverse('products-showcase')

        self.product.delete()
        self.product = self.create_product(
            user=self.user,
            affiliate=True,
            affiliateMarketplace=True,
            total_sales=60000,
            hideShowcase=False
        )

        response = self.client.get(url, headers=self.headers)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()["count"], 1)

        self.product.hideShowcase = True
        self.product.save(update_fields=["hideShowcase"])

        response = self.client.get(url, headers=self.headers)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()["count"], 0, "Produto oculto ainda aparece na vitrine")

    def test_product_update_name_also_updates_default_offer_name(self):
        offer = self.product.offers.filter(default=True).first()

        another_offer = Offer.objects.create(product=self.product, price=20.0, name='Offer 2')

        new_name = 'Product Test Name UPDATED'

        url = reverse('product', args=[self.product.pk])

        data = {
            'name': new_name,
            'description': 'description',
            'price': self.product.price,
            'paymentMethods': [self.pix.pk, self.credit_card.pk],
        }

        response = self.client.put(url, data=json.dumps(data), headers=self.headers, content_type='application/json')

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())

        self.product.refresh_from_db()
        offer.refresh_from_db()
        another_offer.refresh_from_db()

        self.assertEqual(self.product.name, new_name)
        self.assertEqual(offer.name, new_name)
        self.assertEqual(another_offer.name, 'Offer 2')

    def test_product_update_affiliateCommission_raises_error_if_above_limit(self):
        commission_limit = AFFILIATE_MAX_COMMISSION
        new_name = 'Product Test Name UPDATED'

        # set affiliate for that the check actually runs
        self.product.affiliate = True
        self.product.save(update_fields=['affiliate'])

        data = {
            'name': new_name,
            'description': 'description',
            'price': self.product.price,
            'paymentMethods': [self.pix.pk, self.credit_card.pk],
            'affiliateCommission': commission_limit + 1,  # Set above the limit
        }

        url = reverse('product', args=[self.product.pk])
        response = self.client.put(url, data=json.dumps(data), headers=self.headers, content_type='application/json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST, response.content.decode())
        self.assertEqual(
            response.json(),
            {'affiliateCommission': [f'A comissão do afiliado deve ser entre 1% e {commission_limit}%']}
        )

        self.product.refresh_from_db()
        self.assertNotEqual(self.product.name, new_name)

    def test_showcase_event_create_success(self):

        url = reverse('showcase-register-event')

        user = self.create_user()

        another_product = self.create_product(user=user)

        for type in ShowcaseEventType.as_id_list():
            ShowcaseEvent.objects.all().delete()

            payload = {
                'type': type,
                'products': [self.product.pk, another_product.pk],
            }

            with self.subTest(type=type):
                result = self.client.post(url, data=payload, headers=self.build_user_auth_headers(user))

                self.assertEqual(result.status_code, status.HTTP_201_CREATED, result.content.decode())

                showcase_events = ShowcaseEvent.objects.all()

                self.assertEqual(showcase_events.count(), 2, 'Should have created 2 events')
                self.assertTrue(all(event.type == type for event in showcase_events), f'All events should be of type "{type}"')
                self.assertTrue(showcase_events.filter(product=self.product).exists())
                self.assertTrue(showcase_events.filter(product=another_product).exists())
                self.assertTrue(all(event.user == user for event in showcase_events), 'All events should be created by the same user')

    def test_showcase_event_create_missing_products(self):
        url = reverse('showcase-register-event')

        user = self.create_user()

        payload = {
            'type': ShowcaseEventType.AFFILIATE.id,
        }

        result = self.client.post(url, data=payload, headers=self.build_user_auth_headers(user))

        self.assertEqual(result.status_code, status.HTTP_400_BAD_REQUEST, result.content.decode())
        self.assertEqual(result.json(), {'products': ['Este campo é obrigatório.']})
        self.assertEqual(ShowcaseEvent.objects.count(), 0, 'Should not have created any events')

    def test_showcase_event_create_with_products_inexistent(self):
        url = reverse('showcase-register-event')

        user = self.create_user()

        payload = {
            'type': ShowcaseEventType.AFFILIATE.id,
            'products': ["wrong_id_1", "wrong_id_2"],
        }

        result = self.client.post(url, data=payload, headers=self.build_user_auth_headers(user))

        self.assertEqual(result.status_code, status.HTTP_400_BAD_REQUEST, result.content.decode())
        self.assertEqual(result.json(), {'products': {'detail': 'Nenhum produto encontrado.'}})
        self.assertEqual(ShowcaseEvent.objects.count(), 0, 'Should not have created any events')

    def test_showcase_event_create_with_type_inexistent(self):
        url = reverse('showcase-register-event')

        user = self.create_user()

        payload = {
            'type': "Wrong",
            'products': [self.product.pk],
        }

        result = self.client.post(url, data=payload, headers=self.build_user_auth_headers(user))

        self.assertEqual(result.status_code, status.HTTP_400_BAD_REQUEST, result.content.decode())
        self.assertEqual(result.json(), {'type': ['"Wrong" não é um escolha válido.']})
        self.assertEqual(ShowcaseEvent.objects.count(), 0, 'Should not have created any events')

    @mock.patch('product.serializers.ProductShowcaseSerializer.get_affiliate_commission_amount')
    def test_showcase_no_duplicate_products_across_pages(self, mock_commission_amount):
        mock_commission_amount.return_value = 24
        url = reverse('products-showcase')

        Product.objects.all().delete()

        page_size = ProductShowcasePagination.page_size or 10
        total_products = page_size * 3

        for i in range(total_products):
            self.create_product(
                user=self.user,
                name=f'Prod {i}',
                price=10,
                affiliate=True,
                affiliateMarketplace=True,
                total_sales=60000,
                sales_7_days_count=10,
                sales_30_days_count=20,
                sales_24h_count=0,
            )

        resp_p2 = self.client.get(f'{url}?ordering=temperature&page=2', headers=self.headers)
        resp_p3 = self.client.get(f'{url}?ordering=temperature&page=3', headers=self.headers)

        self.assertEqual(resp_p2.status_code, 200, resp_p2.content.decode())
        self.assertEqual(resp_p3.status_code, 200, resp_p3.content.decode())

        ids_p2 = {p['id'] for p in resp_p2.json()['results']}
        ids_p3 = {p['id'] for p in resp_p3.json()['results']}

        self.assertTrue(
            ids_p2.isdisjoint(ids_p3),
            'Um ou mais produtos aparecem em ambas as páginas (duplicados).'
        )

    def test_product_retrieve_returns_showAddressFields_field(self):
        url = reverse('product', args=[self.product.pk])

        response = self.client.get(url, headers=self.headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())
        self.assertIn('showAddressFields', response.json())
        self.assertIsInstance(response.json()['showAddressFields'], bool)

    def test_product_update_updates_showAddressFields_field(self):
        self.product.showAddressFields = False
        self.product.save(update_fields=['showAddressFields'])
        self.assertFalse(self.product.showAddressFields)

        url = reverse('product', args=[self.product.pk])

        data = {
            'name': self.product.name,
            'price': self.product.price,
            'description': "Some description" * 10,
            'showAddressFields': True,
        }

        response = self.client.put(url, data=json.dumps(data), headers=self.headers, content_type='application/json')

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())
        self.product.refresh_from_db()
        self.assertTrue(self.product.showAddressFields)

        # Test setting it to False
        data['showAddressFields'] = False
        response = self.client.put(url, data=json.dumps(data), headers=self.headers, content_type='application/json')

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())
        self.product.refresh_from_db()
        self.assertFalse(self.product.showAddressFields)

    def test_product_create_with_phisical_switch_active(self):
        Product.objects.all().delete()  # Clear existing products

        data = {
            'name': 'Test Physical Product',
            'price': 200,
            'description': 'This is a test physical product.',
            'category': self.category.pk,
            'guarantee': 30,
            'salesPage': 'https://example.com/access',
        }

        url = reverse('products')

        with override_switch('phisical_product_create_serializer', True):
            response = self.client.post(url, data, headers=self.headers, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED, response.content)
        self.assertEqual(Product.objects.count(), 1)

        product: Product = Product.objects.first()  # type:ignore

        self.assertEqual(product.name, data['name'])
        self.assertEqual(product.category, self.category)
        self.assertEqual(product.guarantee, data['guarantee'])
        self.assertEqual(product.description, data['description'])
        self.assertEqual(product.salesPage, data['salesPage'])
        self.assertEqual(product.price, Decimal(data['price']))

    def test_product_supportWhatsapp_update(self):
        url = reverse('product', args=[self.product.pk])
        self.product.supportWhatsapp = None
        self.product.save()

        data = {
            'name': self.product.name,
            'price': self.product.price,
            'description': '1234567890' * 10,
            'supportWhatsapp': '+5511999995555',
        }

        response = self.client.put(url, data=json.dumps(data), headers=self.headers, content_type='application/json')
        self.product.refresh_from_db()

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content)
        self.assertEqual(self.product.supportWhatsapp, data['supportWhatsapp'])  # type: ignore

    def test_product_supportWhatsapp_is_returned_on_retrieve_product(self):
        url = reverse('product', args=[self.product.pk])

        # Set supportWhatsapp
        self.product.supportWhatsapp = None
        self.product.save(update_fields=['supportWhatsapp'])

        response = self.client.get(url, headers=self.headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())
        self.assertIn('supportWhatsapp', response.json())
        self.assertIsNone(response.json()['supportWhatsapp'])

        # Set supportWhatsapp
        self.product.supportWhatsapp = '+5511999995555'
        self.product.save(update_fields=['supportWhatsapp'])

        response = self.client.get(url, headers=self.headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())
        self.assertEqual(response.json()['supportWhatsapp'], '+5511999995555')

@pytest.mark.django_db
def test_increase_checkout_visits_method(
    checkout: Checkout,
    offer: Offer
):
    assert checkout.visits == 0
    assert offer.checkout_visits == 0
    assert offer.product.checkout_visits == 0
    CheckoutVisit.objects.all().delete()

    # Call
    increase_checkout_visits(offer=offer, checkout_id=checkout.pk)

    checkout.refresh_from_db()
    assert checkout.visits == 1
    assert CheckoutVisit.objects.count() == 1

    checkout_visit = CheckoutVisit.objects.first()

    assert checkout_visit.checkout == checkout  # type:ignore
    assert checkout_visit.offer == offer  # type:ignore
    assert checkout_visit.product == offer.product  # type:ignore
