from unittest import mock

from django.core.cache import cache
from django.urls import reverse
from django.utils import timezone

from cakto.tests.base import BaseTestCase
from product.enums import AffiliateStatus, ShowcaseEventType
from product.models import Affiliate, ShowcaseEvent
from product.serializers import ProductSerializer
from product.utils import get_affiliate_cookie_info
from user.serializers import UserAffiliateSerializer


class AffiliateTest(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()

        cls.product = cls.create_product(user=cls.user, affiliate=True, affiliateCommission=50)

        cls.affiliate_1_user = cls.create_user(
            phoneCountryCode='55',
            cellphone='34988887777',
        )

        cls.affiliate = Affiliate.objects.create(
            user=cls.affiliate_1_user,
            product=cls.product,
            commission=10,
            status=AffiliateStatus.ACTIVE.id,
        )

        cls.affiliate_2 = Affiliate.objects.create(
            user=cls.create_user(),
            product=cls.product,
            commission=20.0,
            status=AffiliateStatus.BLOCKED.id,
        )

        cls.affiliate_3 = Affiliate.objects.create(
            user=cls.create_user(),
            product=cls.product,
            commission=20.0,
            status=AffiliateStatus.BLOCKED.id,
        )

        cls.headers = cls.build_user_auth_headers(cls.user)

    def test_affiliate_cache_key_property(self):
        self.assertEqual(self.affiliate.cache_key, f'{Affiliate.cache_prefix}{self.affiliate.short_id}')

    def test_affiliate_invalidate_cache_on_save(self):
        cache_value = 'Test Invalidate Cache'
        cache.set(self.affiliate.cache_key, cache_value, None)

        self.affiliate.save()

        self.assertEqual(cache.get(self.affiliate.cache_key), None)

    def test_affiliate_bulk_invalidade_cache(self):
        cache_value = 'Test Invalidate Cache'

        cache.set(self.affiliate.cache_key, cache_value, None)
        cache.set(self.affiliate_2.cache_key, cache_value, None)

        Affiliate.bulk_invalidate_cache([self.affiliate, self.affiliate_2])

        self.assertEqual(cache.get(self.affiliate.cache_key), None)
        self.assertEqual(cache.get(self.affiliate_2.cache_key), None)

    def test_affiliate_update_many_amount_of_queries(self):
        affiliates = [self.affiliate, self.affiliate_2, self.affiliate_3]

        url = reverse('affiliate-accept-many')

        payload = {'ids': [affiliate.id for affiliate in affiliates]}

        # Call
        with self.assertNumQueries(3):
            # 3 queries:
            # 1 for the logged user that django does automatically
            # 1 for the affiliate status update
            # 1 for the bulk cache invalidation, it needs to fetch the affiliates for the short_ids
            response = self.client.post(url, data=payload, format='json', headers=self.headers)

        # Assert
        self.assertEqual(response.status_code, 200)

    def test_affiliate_update_many_with_short_ids(self):
        affiliates = [self.affiliate, self.affiliate_2, self.affiliate_3]

        for affiliate in affiliates:
            affiliate.status = AffiliateStatus.WAITING.id
            affiliate.save(update_fields=['status'])

        bulk_invalidate_cache_mock = mock.patch(
            'product.models.Affiliate.bulk_invalidate_cache'
        ).start()

        url = reverse('affiliate-accept-many')

        payload = {'short_ids': [affiliate.short_id for affiliate in affiliates]}

        # Call
        with self.assertNumQueries(2):
            response = self.client.post(url, data=payload, format='json', headers=self.headers)

        # Assert
        self.assertEqual(response.status_code, 200)

        self.assertQuerySetEqual(
            bulk_invalidate_cache_mock.call_args[0][0],
            affiliates,
            ordered=False
        )

        for affiliate in affiliates:
            with self.subTest(affiliate=affiliate):
                affiliate.refresh_from_db()
                self.assertEqual(affiliate.status, AffiliateStatus.ACTIVE.id)

    def test_affiliate_accept_many(self):
        affiliates = [self.affiliate, self.affiliate_2, self.affiliate_3]

        for affiliate in affiliates:
            affiliate.status = AffiliateStatus.WAITING.id
            affiliate.save(update_fields=['status'])

        bulk_invalidate_cache_mock = mock.patch(
            'product.models.Affiliate.bulk_invalidate_cache'
        ).start()

        url = reverse('affiliate-accept-many')

        payload = {'ids': [affiliate.id for affiliate in affiliates]}

        # Call
        with self.assertNumQueries(2):
            response = self.client.post(url, data=payload, format='json', headers=self.headers)

        # Assert
        self.assertEqual(response.status_code, 200)

        self.assertQuerySetEqual(
            bulk_invalidate_cache_mock.call_args[0][0],
            affiliates,
            ordered=False
        )

        for affiliate in affiliates:
            with self.subTest(affiliate=affiliate):
                affiliate.refresh_from_db()
                self.assertEqual(affiliate.status, AffiliateStatus.ACTIVE.id)

    def test_affiliate_reject_many(self):
        affiliates = [self.affiliate, self.affiliate_2, self.affiliate_3]

        for affiliate in affiliates:
            affiliate.status = AffiliateStatus.WAITING.id
            affiliate.save(update_fields=['status'])

        bulk_invalidate_cache_mock = mock.patch(
            'product.models.Affiliate.bulk_invalidate_cache'
        ).start()

        url = reverse('affiliate-reject-many')

        payload = {'ids': [affiliate.id for affiliate in affiliates]}

        # Call
        with self.assertNumQueries(2):
            response = self.client.post(url, data=payload, format='json', headers=self.headers)

        # Assert
        self.assertEqual(response.status_code, 200)

        self.assertQuerySetEqual(
            bulk_invalidate_cache_mock.call_args[0][0],
            affiliates,
            ordered=False
        )

        for affiliate in affiliates:
            with self.subTest(affiliate=affiliate):
                affiliate.refresh_from_db()
                self.assertEqual(affiliate.status, AffiliateStatus.REFUSED.id)

    def test_affiliate_block_many(self):
        affiliates = [self.affiliate, self.affiliate_2, self.affiliate_3]

        for affiliate in affiliates:
            affiliate.status = AffiliateStatus.ACTIVE.id
            affiliate.save(update_fields=['status'])

        bulk_invalidate_cache_mock = mock.patch(
            'product.models.Affiliate.bulk_invalidate_cache'
        ).start()

        url = reverse('affiliate-block-many')

        payload = {'ids': [affiliate.id for affiliate in affiliates]}

        # Call
        with self.assertNumQueries(2):
            response = self.client.post(url, data=payload, format='json', headers=self.headers)

        # Assert
        self.assertEqual(response.status_code, 200)

        self.assertQuerySetEqual(
            bulk_invalidate_cache_mock.call_args[0][0],
            affiliates,
            ordered=False
        )

        for affiliate in affiliates:
            with self.subTest(affiliate=affiliate):
                affiliate.refresh_from_db()
                self.assertEqual(affiliate.status, AffiliateStatus.BLOCKED.id)

    def test_affiliate_unblock_many(self):
        affiliates = [self.affiliate, self.affiliate_2, self.affiliate_3]

        for affiliate in affiliates:
            affiliate.status = AffiliateStatus.BLOCKED.id
            affiliate.save(update_fields=['status'])

        bulk_invalidate_cache_mock = mock.patch(
            'product.models.Affiliate.bulk_invalidate_cache'
        ).start()

        url = reverse('affiliate-unblock-many')

        payload = {'ids': [affiliate.id for affiliate in affiliates]}

        # Call
        with self.assertNumQueries(2):
            response = self.client.post(url, data=payload, format='json', headers=self.headers)

        # Assert
        self.assertEqual(response.status_code, 200)

        self.assertQuerySetEqual(
            bulk_invalidate_cache_mock.call_args[0][0],
            affiliates,
            ordered=False
        )

        for affiliate in affiliates:
            with self.subTest(affiliate=affiliate):
                affiliate.refresh_from_db()
                self.assertEqual(affiliate.status, AffiliateStatus.ACTIVE.id)

    def test_affiliate_validate_short_id_min_length(self):
        result = Affiliate.validate_short_id("2" * 8)
        self.assertEqual(result, "2" * 8)

    def test_affiliate_validate_short_id_max_length(self):
        result = Affiliate.validate_short_id("2" * 40)
        self.assertEqual(result, "2" * 40)

    def test_affiliate_validate_short_id_invalid_lengths(self):
        result = Affiliate.validate_short_id("2" * 5)
        self.assertIsNone(result)

        result = Affiliate.validate_short_id("2" * 41)
        self.assertIsNone(result)

    def test_affiliate_validate_short_id_invalid_characters(self):
        result = Affiliate.validate_short_id("2345 #$  BC->>999 \\x \n|\n")
        self.assertEqual(result, "2345BC999x")

    def test_affiliate_validate_short_id_empty(self):
        result = Affiliate.validate_short_id("")
        self.assertIsNone(result)

    def test_affiliate_create_api_view(self):
        url = reverse('affiliates')

        user = self.create_user()

        payload = {'productPk': self.product.pk}

        Affiliate.objects.all().delete()
        ShowcaseEvent.objects.all().delete()

        result = self.client.post(url, data=payload, format='json', headers=self.build_user_auth_headers(user))

        self.assertEqual(result.status_code, 201, result.content.decode())
        self.assertEqual(Affiliate.objects.count(), 1)

        affiliate: Affiliate = Affiliate.objects.first()  # type:ignore
        self.assertEqual(affiliate.commission, self.product.affiliateCommission)
        self.assertEqual(affiliate.product, self.product)
        self.assertEqual(affiliate.status, 'active')
        self.assertEqual(ShowcaseEvent.objects.count(), 0)

    def test_affiliate_create_api_view_with_is_from_showcase_true(self):
        url = reverse('affiliates')

        user = self.create_user()

        payload = {
            'productPk': self.product.pk,
            'is_from_showcase': True,
        }

        Affiliate.objects.all().delete()
        ShowcaseEvent.objects.all().delete()

        result = self.client.post(url, data=payload, format='json', headers=self.build_user_auth_headers(user))

        self.assertEqual(result.status_code, 201, result.content.decode())
        self.assertEqual(Affiliate.objects.count(), 1)

        affiliate: Affiliate = Affiliate.objects.first()  # type:ignore
        self.assertEqual(affiliate.commission, self.product.affiliateCommission)
        self.assertEqual(affiliate.product, self.product)
        self.assertEqual(affiliate.status, 'active')

        showcase_event = ShowcaseEvent.objects.filter(
            user=user,
            product=self.product,
            type=ShowcaseEventType.AFFILIATE.id,
        )
        self.assertEqual(showcase_event.count(), 1)
        self.assertAlmostEqual(
            showcase_event.first().createdAt,  # type:ignore
            timezone.now(),
            delta=timezone.timedelta(seconds=1),
        )

    def test_affiliate_list_api_view(self):
        url = reverse('affiliates')

        expected_data = {
            'id': self.affiliate.pk,
            'short_id': self.affiliate.short_id,
            'user': UserAffiliateSerializer(self.affiliate.user).data,
            'product': ProductSerializer(self.affiliate.product).data,
            'commission': self.affiliate.commission,
            'status': self.affiliate.status,
            'startTime': self.affiliate.startTime.astimezone(timezone.get_current_timezone()).isoformat(),
            'endTime': self.affiliate.endTime,
        }
        expected_data['product']['paymentMethods'] = mock.ANY  # This will be a queryset that is not comparable, setting to ANY fixes it
        expected_data['product']['contentDeliveries'] = mock.ANY  # This will be a queryset that is not comparable, setting to ANY fixes it

        result = self.client.get(url, headers=self.headers)

        self.assertEqual(result.status_code, 200, result.content.decode())
        self.assertEqual(result.json()['count'], 3)

        affiliate_data = result.json()['results'][2]

        for key, value in expected_data.items():
            with self.subTest(key=key):
                self.assertEqual(affiliate_data[key], value, f'{key} is not equal')

    def test_affiliate_retrieve_api_view(self):
        url = reverse('affiliate', args=[self.affiliate.pk])

        expected_data = {
            'id': self.affiliate.pk,
            'short_id': self.affiliate.short_id,
            'user': dict(UserAffiliateSerializer(self.affiliate.user).data),
            'product': dict(ProductSerializer(self.affiliate.product).data),
            'commission': self.affiliate.commission,
            'status': self.affiliate.status,
            'startTime': self.affiliate.startTime.astimezone(timezone.get_current_timezone()).isoformat(),
            'endTime': self.affiliate.endTime,
        }
        expected_data['product']['paymentMethods'] = mock.ANY  # This will be a queryset that is not comparable, setting to ANY fixes it
        expected_data['product']['contentDeliveries'] = mock.ANY  # This will be a queryset that is not comparable, setting to ANY fixes it

        result = self.client.get(url, headers=self.headers)

        self.assertEqual(result.status_code, 200, result.content.decode())

        result_data = result.json()
        self.assertEqual(result_data['id'], expected_data['id'])
        self.assertEqual(result_data['short_id'], expected_data['short_id'])
        self.assertEqual(result_data['user'], expected_data['user'])
        self.assertEqual(result_data['product'], expected_data['product'])
        self.assertEqual(result_data['commission'], expected_data['commission'])
        self.assertEqual(result_data['status'], expected_data['status'])
        self.assertEqual(result_data['startTime'], expected_data['startTime'])
        self.assertEqual(result_data['endTime'], expected_data['endTime'])

    def test_get_affiliate_cookie_info(self):
        company = self.affiliate_1_user.company
        company.status = expected_company_status = 'active'
        company.save()

        self.product.cookieTime = expected_cookie_time = 3600
        self.product.save()

        result = get_affiliate_cookie_info(
            afid_shortId=self.affiliate.short_id,
            product_id=self.product.pk,
        )

        self.assertIsNotNone(result)
        self.assertEqual(result['affiliateShortId'], self.affiliate.short_id)  # type:ignore
        self.assertEqual(result['cookieTime'], expected_cookie_time)  # type:ignore
        self.assertEqual(result['companyStatus'], expected_company_status)  # type:ignore

    def test_query_count_on_get_affiliate_cookie_info(self):
        with self.assertNumQueries(1):
            get_affiliate_cookie_info(
                afid_shortId=self.affiliate.short_id,
                product_id=self.product.pk,
            )
