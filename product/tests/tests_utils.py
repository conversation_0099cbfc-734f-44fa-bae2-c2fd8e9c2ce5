from cakto.tests.base import BaseTestCase
from product.models import Checkout, CheckoutVisit
from product.utils import increase_checkout_visits


class ProductUtilsTest(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()

        cls.create_paymentMethods()

        cls.product = cls.create_product(user=cls.user, paymentMethod='credit_card')
        cls.product.paymentMethods.set([cls.credit_card, cls.pix])

        cls.headers = cls.build_user_auth_headers(cls.user)

    def test_increase_checkout_visits_method_with_offer_and_checkout_id(self):
        offer = self.product.offers.first()

        checkout = Checkout.objects.create(
            product=self.product,
            name='Test Checkout',
        )
        checkout.offers.set([offer])

        self.assertEqual(checkout.visits, 0)
        self.assertEqual(offer.checkout_visits, 0)
        self.assertEqual(offer.product.checkout_visits, 0)
        CheckoutVisit.objects.all().delete()

        # Call
        increase_checkout_visits(offer=offer, checkout_id=checkout.pk)

        checkout.refresh_from_db()
        self.assertEqual(checkout.visits, 1)
        self.assertEqual(CheckoutVisit.objects.count(), 1)

        checkout_visit = CheckoutVisit.objects.first()

        self.assertEqual(checkout_visit.checkout, checkout)  # type:ignore
        self.assertEqual(checkout_visit.offer, offer)  # type:ignore
        self.assertEqual(checkout_visit.product, offer.product)  # type:ignore

    def test_increase_checkout_visits_method_with_offer_and_checkout_instance(self):
        offer = self.product.offers.first()

        checkout = Checkout.objects.create(
            product=self.product,
            name='Test Checkout',
        )
        checkout.offers.set([offer])

        self.assertEqual(checkout.visits, 0)
        self.assertEqual(offer.checkout_visits, 0)
        self.assertEqual(offer.product.checkout_visits, 0)
        CheckoutVisit.objects.all().delete()

        # Call
        increase_checkout_visits(offer=offer, checkout=checkout)

        checkout.refresh_from_db()
        self.assertEqual(checkout.visits, 1)
        self.assertEqual(CheckoutVisit.objects.count(), 1)

        checkout_visit = CheckoutVisit.objects.first()

        self.assertEqual(checkout_visit.checkout, checkout)  # type:ignore
        self.assertEqual(checkout_visit.offer, offer)  # type:ignore
        self.assertEqual(checkout_visit.product, offer.product)  # type:ignore

    def test_increase_checkout_visits_method_with_offer_only(self):
        offer = self.product.offers.first()

        checkout = Checkout.objects.create(
            product=self.product,
            name='Test Checkout',
        )
        checkout.offers.set([offer])

        self.assertEqual(checkout.visits, 0)
        self.assertEqual(offer.checkout_visits, 0)
        self.assertEqual(offer.product.checkout_visits, 0)
        CheckoutVisit.objects.all().delete()

        # Call
        increase_checkout_visits(offer=offer)

        checkout.refresh_from_db()
        self.assertEqual(checkout.visits, 1)
        self.assertEqual(CheckoutVisit.objects.count(), 1)

        checkout_visit = CheckoutVisit.objects.first()

        self.assertEqual(checkout_visit.checkout, checkout)  # type:ignore
        self.assertEqual(checkout_visit.offer, offer)  # type:ignore
        self.assertEqual(checkout_visit.product, offer.product)  # type:ignore

    def test_increase_checkout_visits_method_with_offer_product_and_checkout(self):
        offer = self.product.offers.first()

        checkout = Checkout.objects.create(
            product=self.product,
            name='Test Checkout',
        )
        checkout.offers.set([offer])

        self.assertEqual(checkout.visits, 0)
        self.assertEqual(offer.checkout_visits, 0)
        self.assertEqual(offer.product.checkout_visits, 0)
        CheckoutVisit.objects.all().delete()

        # Call
        increase_checkout_visits(offer=offer, product=offer.product, checkout=checkout)

        checkout.refresh_from_db()
        self.assertEqual(checkout.visits, 1)
        self.assertEqual(CheckoutVisit.objects.count(), 1)

        checkout_visit = CheckoutVisit.objects.first()

        self.assertEqual(checkout_visit.checkout, checkout)  # type:ignore
        self.assertEqual(checkout_visit.offer, offer)  # type:ignore
        self.assertEqual(checkout_visit.product, offer.product)  # type:ignore
