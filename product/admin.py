from django.contrib import admin
from django.urls import reverse
from django.utils.html import format_html

from .models import (Affiliate, Category, Checkout, CheckoutVisit, ContentDelivery, Coproduction, Coupon, DeliveryAccess,
                     FacebookPixel, GoogleAdsPixel, InstragramApiHistory, KwaiPixel, Link, Offer, OrderBump, OutbrainPixel,
                     PaymentMethod, Product, ProductDelivery, ShowcaseEvent, TaboolaPixel, TikTokPixel, TrackingPixels)


class ReadOnlyInline:
    can_delete = False
    show_change_link = True

    def has_add_permission(self, request, obj=None):
        return False

    def get_readonly_fields(self, request, obj=None):
        return [f.name for f in self.model._meta.concrete_fields]

class FacebookPixelInline(ReadOnlyInline, admin.TabularInline):
    model = FacebookPixel
    extra = 0

class GoogleAdsPixelInline(ReadOnlyInline, admin.TabularInline):
    model = GoogleAdsPixel
    extra = 0

class TaboolaPixelInline(ReadOnlyInline, admin.TabularInline):
    model = TaboolaPixel
    extra = 0

class OutbrainPixelInline(ReadOnlyInline, admin.TabularInline):
    model = OutbrainPixel
    extra = 0

class TikTokPixelInline(ReadOnlyInline, admin.TabularInline):
    model = TikTokPixel
    extra = 0

class KwaiPixelInline(ReadOnlyInline, admin.TabularInline):
    model = KwaiPixel
    extra = 0


admin.site.register(OrderBump)
@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list = ('name')
    search_fields = ('name',)

@admin.register(PaymentMethod)
class PaymentMethodAdmin(admin.ModelAdmin):
    list_display = ('type', 'name', 'status')
    list_filter = ('type', 'name', 'status')
    search_fields = ('name', 'type')
    list_display_links = ('name', 'type')

@admin.register(ContentDelivery)
class ContentDeliveryAdmin(admin.ModelAdmin):
    list_display = ('type', 'name', 'status', 'order')
    list_filter = ('type', 'name', 'status')
    search_fields = ('name', 'type')
    list_display_links = ('name', 'type')

class OfferInline(ReadOnlyInline, admin.TabularInline):
    model = Offer
    verbose_name = 'Oferta Ativa'
    verbose_name_plural = 'Ofertas Ativas'
    extra = 0
    fields = ('id', 'name', 'price', 'status', 'type')

    def get_queryset(self, request):
        return super().get_queryset(request).filter(status='active')

    def has_change_permission(self, request, obj=None):
        # Prevents the inline from being editable and from causing validation errors
        return False

@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    list_display = ('name', 'short_id', 'status', 'price', 'category', 'user')
    list_filter = ('category', 'status', 'type', 'contentDeliveries__name', 'affiliate', 'createdAt', 'updatedAt')
    search_fields = (
        'name', 'id', 'short_id', 'membersId', 'supportEmail',
        'user__email', 'user__id', 'user__username', 'user__first_name', 'user__last_name',
        'offers__id',
    )
    list_display_links = ('name', 'short_id')
    readonly_fields = (
        'user', 'createdAt', 'updatedAt', 'tracking_pixels', 'total_sales', 'total_sales_count', 'total_refunded',
        'total_refunded_count', 'total_chargeback', 'total_chargeback_count', 'sales_30_days', 'sales_30_days_count',
        'refunded_30_days', 'refunded_30_days_count', 'chargeback_30_days', 'chargeback_30_days_count', 'sales_7_days',
        'sales_7_days_count', 'refunded_7_days', 'refunded_7_days_count', 'chargeback_7_days', 'chargeback_7_days_count',
        'sales_24h', 'sales_24h_count', 'refunded_24h', 'refunded_24h_count', 'chargeback_24h', 'chargeback_24h_count',
        'total_paid', 'total_paid_count', 'checkout_visits',
    )
    inlines = [OfferInline, ]

    def tracking_pixels(self, obj):
        url = reverse('admin:product_trackingpixels_change', args=[obj.pixels.first().id])
        return format_html('<a href="{}">Edit Tracking Pixels</a>', url)
    tracking_pixels.short_description = 'Tracking Pixels'  # type:ignore

@admin.register(TrackingPixels)
class TrackingPixelsAdmin(admin.ModelAdmin):
    list_display = ('id', 'product', 'affiliate',)
    list_filter = ('createdAt',)
    search_fields = ('product__name', 'affiliate__user__email', 'product__user__email')
    readonly_fields = ('createdAt', 'product', 'affiliate')
    inlines = [FacebookPixelInline, GoogleAdsPixelInline, TaboolaPixelInline, OutbrainPixelInline, TikTokPixelInline, KwaiPixelInline, ]

@admin.register(Affiliate)
class AffiliateAdmin(admin.ModelAdmin):
    list_display = ('short_id', 'user', 'product', 'status', 'startTime', 'endTime',)
    list_filter = ('status', 'startTime', 'endTime', )
    search_fields = ('short_id', 'user__email', 'user__id', 'product__name', 'product__short_id', 'product__id', 'product__user__email')
    readonly_fields = ('user', 'product', 'tracking_pixels')

    def tracking_pixels(self, obj):
        url = reverse('admin:product_trackingpixels_change', args=[obj.pixels.first().pk])
        return format_html('<a href="{}">Edit Tracking Pixels</a>', url)
    tracking_pixels.short_description = 'Tracking Pixels'  # type:ignore

@admin.register(Coproduction)
class CoproductionAdmin(admin.ModelAdmin):
    list_display = ('user', 'status', 'product', 'startTime', 'endTime')
    list_filter = (
        '_status', 'startTime', 'endTime', 'deletionApprovedByOwner', 'deletionApprovedByUser',
        'receiveSalesFromProducer', 'receiveSalesFromAffiliate', 'createdAt', 'updatedAt'
    )
    search_fields = (
        'product__name', 'product__short_id', 'product__id', 'product__user__email', 'product__supportEmail',
        'user__email', 'user__id', 'user__username', 'user__first_name', 'user__last_name',
    )
    readonly_fields = ('user', 'product', 'createdAt', 'updatedAt')

@admin.register(Offer)
class OfferAdmin(admin.ModelAdmin):
    list_display = ('id', 'product', 'name', 'status', 'price', 'type')
    list_filter = ('status', 'default', 'intervalType', 'type', 'createdAt', 'updatedAt')
    search_fields = (
        'id', 'externalId', 'name',
        'product__name', 'product__short_id', 'product__id', 'product__user__email',
    )
    readonly_fields = ('product', 'createdAt', 'updatedAt', 'checkout_visits', 'sales_count', )

class CheckoutOfferInline(ReadOnlyInline, admin.TabularInline):
    model = Checkout.offers.through  # type:ignore
    verbose_name = 'Oferta'
    verbose_name_plural = 'Ofertas'
    extra = 0

    def get_readonly_fields(self, request, obj=None):
        return [f.name for f in self.model._meta.concrete_fields] + ['product', 'default', 'status', 'type']

    def product(self, obj):
        return obj.offer.product
    product.short_description = 'Produto'  # type:ignore

    def default(self, obj):
        return obj.offer.default
    default.short_description = 'Oferta Padrão'  # type:ignore

    def status(self, obj):
        return obj.offer.status
    status.short_description = 'Status'  # type:ignore

    def type(self, obj):
        return obj.offer.type
    type.short_description = 'Tipo'  # type:ignore

@admin.register(Checkout)
class CheckoutAdmin(admin.ModelAdmin):
    list_display = ('product', 'name', 'default')
    search_fields = (
        'product__name', 'product__short_id', 'product__id', 'product__user__email',
        'name', 'id', 'offers__id'
    )
    list_filter = ('default', 'createdAt', 'updatedAt')
    readonly_fields = ('product', 'visits', 'sales_count', 'offers')
    inlines = [CheckoutOfferInline, ]

@admin.register(InstragramApiHistory)
class InstragramApiHistoryAdmin(admin.ModelAdmin):
    list_display = ('action', 'response_status_code', 'payload', 'createdAt')
    list_filter = ('action', 'response_status_code', 'createdAt')
    search_fields = ('payload', 'response')
    readonly_fields = ('id', 'action', 'payload', 'response', 'response_status_code', 'createdAt')
    list_display_links = ('action', 'response_status_code')

@admin.register(ProductDelivery)
class ProductDeliveryAdmin(admin.ModelAdmin):
    list_display = ('id', 'product', 'contentDelivery', 'name', 'status', 'createdAt', 'updatedAt')
    list_filter = ('contentDelivery', 'status', 'createdAt', 'updatedAt')
    search_fields = ('id', 'product__name', 'product__id', 'product__short_id', 'contentDelivery__type', 'contentDelivery__name', 'name', 'fields')
    readonly_fields = ('id', 'product', 'contentDelivery', 'name', 'createdAt', 'updatedAt')

@admin.register(DeliveryAccess)
class DeliveryAccessAdmin(admin.ModelAdmin):
    list_display = ('id', 'user', 'productDelivery', 'order', 'status', 'configuredAt', 'expiresAt', 'createdAt')
    list_filter = ('status', 'configuredAt', 'expiresAt', 'createdAt', 'updatedAt',)
    search_fields = ('id', 'user__id', 'user__email', 'productDelivery__product__id', 'productDelivery__product__short_id',
                     'productDelivery__product__user__email', 'order__id', 'order__refId', 'fields')
    readonly_fields = ('user', 'productDelivery', 'order', 'createdAt', 'updatedAt')

@admin.register(Coupon)
class CouponAdmin(admin.ModelAdmin):
    list_display = ('id', 'code', 'discount', 'createdAt')
    list_filter = ('applyOnBumps', 'startTime', 'endTime', 'createdAt', 'updatedAt')
    search_fields = ('id', 'code', 'products__name', 'products__short_id', 'products__id', 'products__user__email')
    readonly_fields = ('id', 'createdAt', 'updatedAt', 'products_list')
    exclude = ('products',)

    def products_list(self, obj):
        print('products')
        links = []
        for product in obj.products.all():
            url = reverse('admin:product_product_change', args=[product.id])
            link = format_html('<a href="{}">{}</a>', url, product.name)
            links.append(link)
        return format_html('<br/>'.join(links))

    products_list.short_description = 'Products'  # type:ignore

@admin.register(Link)
class LinkAdmin(admin.ModelAdmin):
    list_display = ('shortId', 'name', 'type', 'status', 'createdAt')
    list_filter = ('showAffiliates', 'type', 'status', 'default', 'createdAt', 'updatedAt')
    search_fields = (
        'id', 'shortId', 'product__id', 'product__short_id', 'url', 'createdAt',
        'updatedAt', 'product__user__email',
    )
    readonly_fields = (
        'id', 'product', 'offer', 'checkout', 'createdAt', 'updatedAt',
    )

@admin.register(ShowcaseEvent)
class ShowcaseEventAdmin(admin.ModelAdmin):
    list_display = ('type', 'product', 'user', 'createdAt')
    list_filter = ('type', 'createdAt')
    search_fields = ('products__short_id', 'products__id', 'product__user__email', 'user__email')
    readonly_fields = ('id', 'type', 'product', 'user', 'createdAt')
    list_display_links = ('type', 'product', 'user')

@admin.register(CheckoutVisit)
class CheckoutVisitAdmin(admin.ModelAdmin):
    list_display = ('id', 'checkout', 'offer', 'product', 'createdAt')
    list_display_links = ('id', 'checkout', 'offer', 'product')
    list_filter = ('createdAt', )
    search_fields = ('checkout__id', 'offer__id', 'product__id')
    readonly_fields = ('id', 'checkout', 'offer', 'product', 'createdAt')
